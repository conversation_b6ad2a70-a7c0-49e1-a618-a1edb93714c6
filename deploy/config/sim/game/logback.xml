<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE xml>
<configuration scan="true" scanPeriod="5 seconds">
	<property name="LOG_DIR" value="./logs"/>
	<property name="BiLOG_DIR" value="/home/<USER>/ga_log" />
	<define name="sid" class="com.lc.billion.icefire.game.biz.service.impl.bilog.ServerIdDefine" />
	<define name="hostname" class="com.lc.billion.icefire.game.biz.service.impl.bilog.HostnameDefine"/>
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>[%d{yyyy-MM-dd_HH:mm:ss.SSS}][%level][%thread][%logger][%X{msgTraceId}] - %msg%n</pattern>
		</encoder>
	</appender>


	<!-- game.log begin-->
	<!--	<appender name="SIFT_ROLEFILE" class="ch.qos.logback.classic.sift.SiftingAppender">-->
	<!--		<discriminator class="ThreadDiscriminator">-->
	<!--			<key>threadName</key>-->
	<!--		</discriminator>-->
	<!--		<sift>-->
	<!--			<appender name="ROLLFILE"-->
	<!--				class="ch.qos.logback.core.rolling.RollingFileAppender">-->
	<!--				<file>${LOG_DIR}/game.log</file>-->
	<!--				<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
	<!--					<fileNamePattern>${LOG_DIR}/game.%d{yyyyMMdd_HH}.log</fileNamePattern>-->
	<!--					<maxHistory>2160</maxHistory>-->
	<!--				</rollingPolicy>-->
	<!--				<encoder>-->
	<!--					<pattern>[%d{yyyy-MM-dd_HH:mm:ss.SSS}][%level][%thread][%logger][%L] - %msg%n</pattern>-->
	<!--				</encoder>-->
	<!--			</appender>-->
	<!--		</sift>-->
	<!--	</appender>-->
	<appender name="ROLEFILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/game.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/game.%d{yyyyMMdd_HH}.log</fileNamePattern>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>[%d{yyyy-MM-dd_HH:mm:ss.SSS}][%level][%thread][%logger][%X{msgTraceId}] - %msg%n</pattern>
		</encoder>
	</appender>
	<appender name="ASYNC_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="ROLEFILE" />
		<queueSize>102400</queueSize>
		<maxFlushTime>3000</maxFlushTime>
		<includeCallerData>false</includeCallerData>
	</appender>
	<!-- game.log begin-->

	<!-- resource.log begin-->
	<appender name="resourceRollFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/bizlogs/resource.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/bizlogs/resource.%d{yyyyMMdd}.log</fileNamePattern>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<maxHistory>365</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n</pattern>
		</encoder>
	</appender>

	<appender name="resourceRollFileAsync" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="resourceRollFile" />
		<queueSize>10240</queueSize>
		<maxFlushTime>3000</maxFlushTime>
	</appender>
	<!-- game.log begin-->

	<!-- asset.log begin-->
	<appender name="BiLOG_ASSET_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${BiLOG_DIR}/asset.bi.${hostname}.log.%d{yyyy_MM_dd}</fileNamePattern>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n%ex</pattern>
		</encoder>
	</appender>
	<appender name="ASYNC_BiLOG_ASSET_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="BiLOG_ASSET_ROLLING_FILE"/>
		<queueSize>102400</queueSize>
		<maxFlushTime>3000</maxFlushTime>
		<includeCallerData>false</includeCallerData>
	</appender>
	<!-- asset.log end-->

	<!-- game-perf.log begin-->
	<!-- 每小时产生一次game-perf.log，间隔为1分钟记录一次 -->
	<appender name="PERF4J_ROLLING_FILE_MIN" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/game-perf.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/game-perf.%d{yyyyMMdd_HH}.log</fileNamePattern>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg</pattern>
		</encoder>
	</appender>
	<appender name="PERF4J_LOG_MODIFIER_MIN" class="com.lc.billion.icefire.core.perf.Perf4jLogModifyToSlsAppender">
		<appender-ref ref="PERF4J_ROLLING_FILE_MIN" />
	</appender>
	<appender name="PERF4J_STATISTICS_MIN" class="org.perf4j.logback.AsyncCoalescingStatisticsAppender">
		<appender-ref ref="PERF4J_LOG_MODIFIER_MIN" />
		<createRollupStatistics>true</createRollupStatistics>
		<timeSlice>60000</timeSlice>
	</appender>
	<!-- game-perf.log end-->

	<!-- game-perf-day.log begin-->
	<!-- 每天产生一次game-perf-day.log，间隔为一天记录一次 -->
	<appender name="PERF4J_ROLLING_FILE_DAY" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/game-perf-day.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/game-perf-day.%d{yyyyMMdd}.log</fileNamePattern>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg</pattern>
		</encoder>
	</appender>
	<appender name="PERF4J_LOG_MODIFIER_DAY" class="com.lc.billion.icefire.core.perf.Perf4jLogModifyToSlsAppender">
		<appender-ref ref="PERF4J_ROLLING_FILE_DAY" />
	</appender>
	<appender name="PERF4J_STATISTICS_DAY" class="org.perf4j.logback.AsyncCoalescingStatisticsAppender">
		<appender-ref ref="PERF4J_LOG_MODIFIER_DAY" />
		<createRollupStatistics>true</createRollupStatistics>
		<timeSlice>86400000</timeSlice>
	</appender>
	<!-- game-perf-day.log end-->
	<appender name="BiLOG_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!--		<file>${BiLOG_DIR}/ga.bi.log</file>-->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${BiLOG_DIR}/Ga_${hostname}_%d{yyyyMMdd_HH}.log</fileNamePattern>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n%ex</pattern>
		</encoder>
	</appender>
	<appender name="ASYNC_BiLOG_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="BiLOG_ROLLING_FILE"/>
		<queueSize>102400</queueSize>
		<maxFlushTime>3000</maxFlushTime>
		<includeCallerData>false</includeCallerData>
	</appender>
	<appender name="SLOWLOG_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/slow.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/slow.%d{yyyyMMdd_HH}.log</fileNamePattern>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n</pattern>
		</encoder>
	</appender>
	<appender name="ASYNC_SLOWLOG_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="SLOWLOG_ROLLING_FILE"/>
		<queueSize>102400</queueSize>
		<maxFlushTime>3000</maxFlushTime>
		<includeCallerData>false</includeCallerData>
	</appender>
	<appender name="FIGHT_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/fight.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/fight.%d{yyyy-MM-dd_HH:mm:ss}.%i.log</fileNamePattern>
			<maxFileSize>10MB</maxFileSize>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n%ex</pattern>
		</encoder>
	</appender>
	<appender name="GAME_MSG_LOG_ROLLFILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/game_msg.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/game_msg.%d{yyyyMMdd_HH}.log</fileNamePattern>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n</pattern>
		</encoder>
	</appender>
	<appender name="ERROR_LOG_ROLLFILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/error.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/error.%d{yyyyMMdd_HH}.log</fileNamePattern>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="com.lc.billion.icefire.game.biz.service.impl.log.error.layout.LogLayout">
				<Pattern>%msg%n</Pattern>
			</layout>
		</encoder>
	</appender>
	<appender name="ASYNC_GAME_MSG_LOG_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="GAME_MSG_LOG_ROLLFILE" />
		<queueSize>102400</queueSize>
		<maxFlushTime>3000</maxFlushTime>
		<includeCallerData>false</includeCallerData>
	</appender>
	<appender name="ASYNC_ERROR_LOG_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="ERROR_LOG_ROLLFILE" />
		<queueSize>102400</queueSize>
		<maxFlushTime>3000</maxFlushTime>
		<includeCallerData>false</includeCallerData>
	</appender>
	<appender name="ASYNC_LOG_ROLLFILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/async.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/async.%d{yyyyMMdd_HH}.log</fileNamePattern>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n</pattern>
		</encoder>
	</appender>
	<appender name="COMMON_LOG_ROLLFILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/common.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/common.%d{yyyyMMdd_HH}.log</fileNamePattern>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n</pattern>
		</encoder>
	</appender>
	<appender name="CORE_LOG_ROLLFILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_DIR}/core.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/core.%d{yyyyMMdd_HH}.log</fileNamePattern>
			<maxHistory>2160</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%msg%n</pattern>
		</encoder>
	</appender>
	<appender name="ASYNC_ASYNC_LOG_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="ASYNC_LOG_ROLLFILE" />
		<queueSize>102400</queueSize>
		<maxFlushTime>3000</maxFlushTime>
		<includeCallerData>false</includeCallerData>
	</appender>
	<appender name="ASYNC_COMMON_LOG_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="COMMON_LOG_ROLLFILE" />
		<queueSize>10240</queueSize>
		<maxFlushTime>3000</maxFlushTime>
	</appender>
	<appender name="ASYNC_CORE_LOG_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="CORE_LOG_ROLLFILE" />
		<queueSize>10240</queueSize>
		<maxFlushTime>3000</maxFlushTime>
	</appender>
	<logger name="org.perf4j.TimingLogger" level="INFO" additivity="false">
		<appender-ref ref="PERF4J_STATISTICS_MIN" />
		<appender-ref ref="PERF4J_STATISTICS_DAY" />
	</logger>

	<logger name="org.apache" level="INFO" additivity="false" />
	<logger name="org.springframework" level="INFO" additivity="false" />
	<logger name="org.mybatis" level="INFO" additivity="false" />
	<logger name="org.mongodb" level="INFO" additivity="false"/>

	<!-- 临时关闭BiLogUtil	-->
	<logger name="com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil" level="WARN" additivity="false">
		<appender-ref ref="ASYNC_ROLLFILE" />
	</logger>
	<!-- 临时关闭GcMessageDispatcher	-->
	<logger name="com.lc.billion.icefire.game.net.GcMessageDispatcher" level="WARN" additivity="false">
		<appender-ref ref="ASYNC_ROLLFILE" />
	</logger>
	<logger name="ResourceLogUtil" level="INFO" additivity="false">
		<appender-ref ref="resourceRollFileAsync" />
	</logger>
	<logger name="BiLog" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_BiLOG_ROLLING_FILE"/>
	</logger>
	<logger name="BiAssetLog" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_BiLOG_ASSET_ROLLING_FILE"/>
	</logger>
	<logger name="FightLog" level="INFO">
		<appender-ref ref="FIGHT_ROLLING_FILE"/>
	</logger>
	<logger name="MessagePushLog" level="INFO">
		<appender-ref ref="ASYNC_ROLLFILE"/>
    </logger>
	<logger name="SlowLog" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_SLOWLOG_ROLLING_FILE"/>
	</logger>

	<logger name="GameMsgLog" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_GAME_MSG_LOG_ROLLFILE"/>
	</logger>
	<logger name="ErrorLog" level="INFO">
		<appender-ref ref="ASYNC_ERROR_LOG_ROLLFILE"/>
	</logger>
	<logger name="AsyncLog" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_ASYNC_LOG_ROLLFILE"/>
	</logger>
	<logger name="CommonLog" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_COMMON_LOG_ROLLFILE"/>
	</logger>
	<logger name="CoreLog" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_CORE_LOG_ROLLFILE"/>
	</logger>
	<root level="INFO">
		<appender-ref ref="ASYNC_ROLLFILE" />
		<appender-ref ref="STDOUT" />
	</root>
</configuration>