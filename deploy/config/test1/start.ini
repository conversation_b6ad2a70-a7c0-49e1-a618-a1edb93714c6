#===========================================================
# Jetty Startup
#
# Starting Jetty from this {jetty.home} is not recommended.
#
# A proper {jetty.base} directory should be configured, instead
# of making changes to this {jetty.home} directory.
#
# See documentation about {jetty.base} at
# https://jetty.org/docs/9/startup.html
#
# A demo-base directory has been provided as an example of
# this sort of setup.
#
# $ cd demo-base
# $ java -jar ../start.jar
#
#===========================================================

# To disable the warning message, comment the following line
# --module=home-base-warning

# ---------------------------------------
# Module: ext
# Adds all jar files discovered in $JETTY_HOME/lib/ext
# and $JETTY_BASE/lib/ext to the servers classpath.
# ---------------------------------------
# --module=ext


# ---------------------------------------
# Module: server
# Enables the core Jetty server on the classpath.
# ---------------------------------------
--module=server
#使用虚拟线程
#--module=threadpool-all-virtual
## Maximum number of current virtual threads.
#jetty.threadPool.maxThreads=20000

### Common HTTP configuration
## Scheme to use to build URIs for secure redirects
# jetty.httpConfig.secureScheme=https

## Port to use to build URIs for secure redirects
# jetty.httpConfig.securePort=8443

## Response content buffer size (in bytes)
# jetty.httpConfig.outputBufferSize=32768

## Max response content write length that is buffered (in bytes)
# jetty.httpConfig.outputAggregationSize=8192

## Max request headers size (in bytes)
# jetty.httpConfig.requestHeaderSize=8192

## Max response headers size (in bytes)
# jetty.httpConfig.responseHeaderSize=8192

## Whether to send the Server: header
# jetty.httpConfig.sendServerVersion=true

## Whether to send the Date: header
# jetty.httpConfig.sendDateHeader=false

## Max per-connection header cache size (in nodes)
# jetty.httpConfig.headerCacheSize=1024

## Whether, for requests with content, delay dispatch until some content has arrived
# jetty.httpConfig.delayDispatchUntilContent=true

## Maximum number of error dispatches to prevent looping
# jetty.httpConfig.maxErrorDispatches=10

## Cookie compliance mode for parsing request Cookie headers: RFC2965, RFC6265
# jetty.httpConfig.requestCookieCompliance=RFC6265

## Cookie compliance mode for generating response Set-Cookie: RFC2965, RFC6265
# jetty.httpConfig.responseCookieCompliance=RFC6265

## multipart/form-data compliance mode of: LEGACY(slow), RFC7578(fast)
# jetty.httpConfig.multiPartFormDataCompliance=LEGACY

## Relative Redirect Locations allowed
# jetty.httpConfig.relativeRedirectAllowed=false

### Server configuration
## Whether ctrl+c on the console gracefully stops the Jetty server
jetty.server.stopAtShutdown=true

## Timeout in ms to apply when stopping the server gracefully
jetty.server.stopTimeout=30000

## Dump the state of the Jetty server, components, and webapps after startup
# jetty.server.dumpAfterStart=false

## Dump the state of the Jetty server, components, and webapps before shutdown
# jetty.server.dumpBeforeStop=false

## Scheduler Configuration
# jetty.scheduler.name=
# jetty.scheduler.deamon=false
# jetty.scheduler.threads=-1

# ---------------------------------------
# Module: jsp
# Enables JSP for all webapplications deployed on the server.
# ---------------------------------------
--module=ee10-jsp

# ---------------------------------------
# Module: resources
# Adds the $JETTY_HOME/resources and/or $JETTY_BASE/resources
# directory to the server classpath. Useful for configuration
# property files (eg jetty-logging.properties)
# ---------------------------------------
--module=resources


# ---------------------------------------
# Module: deploy
# Enables webapplication deployment from the webapps directory.
# ---------------------------------------
--module=ee10-deploy

# Monitored directory name (relative to $jetty.base)
# jetty.deploy.monitoredDir=webapps
# - OR -
# Monitored directory path (fully qualified)
# jetty.deploy.monitoredPath=/var/www/webapps

# Defaults Descriptor for all deployed webapps
# jetty.deploy.defaultsDescriptorPath=${jetty.base}/etc/webdefault.xml

# Monitored directory scan period (seconds)
# jetty.deploy.scanInterval=1

# Whether to extract *.war files
# jetty.deploy.extractWars=true

# ---------------------------------------
# Module: jstl
# Enables JSTL for all webapplications deployed on the server
# ---------------------------------------
--module=ee10-jstl


# ---------------------------------------
# Module: websocket
# Enable websockets for deployed web applications
# ---------------------------------------
; --module=websocket


# ---------------------------------------
# Module: http
# Enables an HTTP connector on the server.
# By default HTTP/1 is support, but HTTP2C can
# be added to the connector with the http2c module.
# ---------------------------------------
--module=http

### HTTP Connector Configuration

## Connector host/address to bind to
# jetty.http.host=0.0.0.0

## Connector port to listen on
# jetty.http.port=8080

## Connector idle timeout in milliseconds
# jetty.http.idleTimeout=30000

## Number of acceptors (-1 picks default based on number of cores)
# jetty.http.acceptors=-1

## Number of selectors (-1 picks default based on number of cores)
# jetty.http.selectors=-1

## ServerSocketChannel backlog (0 picks platform default)
# jetty.http.acceptQueueSize=0

## Thread priority delta to give to acceptor threads
# jetty.http.acceptorPriorityDelta=0

## The requested maximum length of the queue of incoming connections.
# jetty.http.acceptQueueSize=0

## Enable/disable the SO_REUSEADDR socket option.
# jetty.http.reuseAddress=true

## Enable/disable TCP_NODELAY on accepted sockets.
# jetty.http.acceptedTcpNoDelay=true

## The SO_RCVBUF option to set on accepted sockets. A value of -1 indicates that it is left to its default value.
# jetty.http.acceptedReceiveBufferSize=-1

## The SO_SNDBUF option to set on accepted sockets. A value of -1 indicates that it is left to its default value.
# jetty.http.acceptedSendBufferSize=-1

## Connect Timeout in milliseconds
# jetty.http.connectTimeout=15000

## HTTP Compliance: RFC7230, RFC7230_LEGACY, RFC2616, RFC2616_LEGACY, LEGACY or CUSTOMn
# jetty.http.compliance=RFC7230_LEGACY
