#!/bin/bash
set -eox pipefail

docker_rmi() {
    TEMP_IMAGE_NAME=$1
    COUNT=$(docker image ls "$TEMP_IMAGE_NAME" | wc -l)

    if [ "$COUNT" -eq 2 ]; then
        echo "docker rmi $TEMP_IMAGE_NAME"
        docker rmi "$TEMP_IMAGE_NAME"
    else
        echo "$TEMP_IMAGE_NAME doesn't exist"
    fi
}

ENV=$1
BRANCH=$2
DEPLOY=$3
BATTLE_TAG=$4

OLD_DIR=$(pwd);BASE_DIR=$(cd "$(dirname ${BASH_SOURCE[0]})";pwd);cd "${OLD_DIR}";

echo -e "执行release脚本"
if [ "$ENV" == "sim" ]; then
    RELEASE_ENV="sim"
elif [ "$ENV" == "sim_canary" ]; then
    RELEASE_ENV="sim"
elif [ "$ENV" == "online" ]; then
    RELEASE_ENV="online"
elif [ "$ENV" == "online_canary" ]; then
    RELEASE_ENV="online"
else
    RELEASE_ENV=$ENV
fi
${BASE_DIR}/release.sh ${RELEASE_ENV} ${BRANCH} | tee release.log

release_name=`cat release.log | tail -n 1`
echo "release_name: ${release_name}"

if [[ ! "$release_name" =~ ^20[0-9]{2}-[0-9]{2}-[0-9]{2}_[0-9]{2}-[0-9]{2}-[0-9]{2}.[A-Za-z0-9_.-]{1,}$ ]]; then
    echo "编译出错！"
    exit 1;
fi
if [ ! -d "../../lib" ]; then
    echo "没有找到部署环境${ENV}的发布目录lib"
else
   echo "拷贝文件至发布目录"
   cp ${RELEASE_ENV}.GAME_SERVER.${release_name}.tar.gz ../../lib/${ENV}.GAME_SERVER.${release_name}.tar.gz
fi
DATETIME=`date +"%Y%m%d%H%M"`
TAG=${BRANCH##*/}.${DATETIME}

DOCKER_URL=tyhub.tuyoo.com
PROJECT_NAME=xingguang

# 检查 ENV 是否包含 online
if [ "$ENV" == "online" ]; then
    DOCKER_URL=harbor.ops.tuyoops.com
    PROJECT_NAME=sanguo2-138
    docker login https://harbor.ops.tuyoops.com -u 朱长彬 --password-stdin < /var/lib/jenkins/docker-password-new
else
    docker login https://tyhub.tuyoo.com -u caojian --password-stdin < /var/lib/jenkins/docker-password
fi

IMAGE_NAME="${DOCKER_URL}/${PROJECT_NAME}"

WEB_IMAGE_NAME="${IMAGE_NAME}/web_server_${ENV}"
GAME_IMAGE_NAME="${IMAGE_NAME}/game_server_${ENV}"
BATTLE_IMAGE_NAME="${IMAGE_NAME}/battle_server_${ENV}"

LOGIN_IMAGE_NAME="${IMAGE_NAME}/login_server_${ENV}"
GM_IMAGE_NAME="${IMAGE_NAME}/gm_server_${ENV}"


echo "开始构建 web image..."
ls -alh "${RELEASE_ENV}".WEB_SERVER*.tar.gz
rm -rf DOCKER_WEB_SERVER
mkdir DOCKER_WEB_SERVER
tar -zxf "${RELEASE_ENV}".WEB_SERVER*.tar.gz --strip-components 1 -C DOCKER_WEB_SERVER
cp -R DOCKER_WEB_SERVER/ROOT/meta/ DOCKER_WEB_SERVER/ROOT/web_meta_monitor
cp deploy/config/start.ini .
DOCKER_BUILDKIT=1 docker build --rm -f web_Dockerfile_21 -t ${WEB_IMAGE_NAME}:${TAG} --progress=plain .
docker push "${WEB_IMAGE_NAME}:${TAG}"

docker tag ${WEB_IMAGE_NAME}:${TAG} ${LOGIN_IMAGE_NAME}:${TAG}
docker push ${LOGIN_IMAGE_NAME}:${TAG}

docker tag ${WEB_IMAGE_NAME}:${TAG} ${GM_IMAGE_NAME}:${TAG}
docker push ${GM_IMAGE_NAME}:${TAG}

if [ "$ENV" == "docker" ]; then
  docker tag ${WEB_IMAGE_NAME}:${TAG} ${WEB_IMAGE_NAME}_${BRANCH}:latest
  docker push ${WEB_IMAGE_NAME}_${BRANCH}:latest
else
  docker tag ${WEB_IMAGE_NAME}:${TAG} ${WEB_IMAGE_NAME}:latest
  docker push ${WEB_IMAGE_NAME}:latest
fi


echo "开始构建 game image..."
ls -alh "${RELEASE_ENV}".GAME_SERVER*.tar.gz
rm -rf DOCKER_GAME_SERVER
mkdir DOCKER_GAME_SERVER
tar -zxf "${RELEASE_ENV}".GAME_SERVER*.tar.gz --strip-components 1 -C DOCKER_GAME_SERVER
cp -R DOCKER_GAME_SERVER/meta DOCKER_GAME_SERVER/game_meta_monitor
DOCKER_BUILDKIT=1 docker build --rm -f game_Dockerfile_21 -t ${GAME_IMAGE_NAME}:${TAG} --progress=plain .
docker push "${GAME_IMAGE_NAME}:${TAG}"

if [ "$ENV" == "docker" ]; then
  docker tag ${GAME_IMAGE_NAME}:${TAG} ${GAME_IMAGE_NAME}_${BRANCH}:latest
  docker push ${GAME_IMAGE_NAME}_${BRANCH}:latest
else
  docker tag ${GAME_IMAGE_NAME}:${TAG} ${GAME_IMAGE_NAME}:latest
  docker push ${GAME_IMAGE_NAME}:latest
fi

rm -f build.result.log  # 结果文件
set +x
echo "镜像构建完成！"
if [ "$ENV" == "online" ]; then
  echo "国内线上修改拉取的镜像名字"
  PULL_DOCKER_URL=alixj-registry-vpc.cn-beijing.cr.aliyuncs.com
  IMAGE_NAME="${PULL_DOCKER_URL}/${PROJECT_NAME}"

  WEB_IMAGE_NAME="${IMAGE_NAME}/web_server_${ENV}"
  GAME_IMAGE_NAME="${IMAGE_NAME}/game_server_${ENV}"
  BATTLE_IMAGE_NAME="${IMAGE_NAME}/battle_server_${ENV}"

  LOGIN_IMAGE_NAME="${IMAGE_NAME}/login_server_${ENV}"
  GM_IMAGE_NAME="${IMAGE_NAME}/gm_server_${ENV}"
fi
{
  echo "游戏服镜像名称：${GAME_IMAGE_NAME}:${TAG}"
  echo "战斗服镜像名称：${BATTLE_IMAGE_NAME}:${BATTLE_TAG}"
  echo "登录服镜像名称：${LOGIN_IMAGE_NAME}:${TAG}"
  echo "GM服镜像名称：${GM_IMAGE_NAME}:${TAG}"
} >> build.result.log
echo "游戏服镜像名称：${GAME_IMAGE_NAME}:${TAG}"
echo "战斗服镜像名称：${BATTLE_IMAGE_NAME}:${BATTLE_TAG}"
echo "登录服镜像名称：${LOGIN_IMAGE_NAME}:${TAG}"
echo "GM服镜像名称：${GM_IMAGE_NAME}:${TAG}"
set -x

# if [ -d "/home/<USER>/.jenkins/workspace/dockertags" ]; then
#     echo "将镜像名称以文件格式存入/home/<USER>/.jenkins/workspace/dockertags，作为jenkins的选项参数"

#     # NEW_WEB_IMAGE_NAME=${WEB_IMAGE_NAME#tyhub.tuyoo.com/xingguang/}
#     # echo "touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_WEB_IMAGE_NAME}:${TAG}"
#     # touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_WEB_IMAGE_NAME}:${TAG}

#     # NEW_GAME_IMAGE_NAME=${GAME_IMAGE_NAME#tyhub.tuyoo.com/xingguang/}
#     # echo "touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_GAME_IMAGE_NAME}:${TAG}"
#     # touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_GAME_IMAGE_NAME}:${TAG}

#     NEW_LOGIN_IMAGE_NAME=${LOGIN_IMAGE_NAME#tyhub.tuyoo.com/xingguang/}
#     echo "touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_LOGIN_IMAGE_NAME}:${TAG}"
#     touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_LOGIN_IMAGE_NAME}:${TAG}

#     NEW_GM_IMAGE_NAME=${GM_IMAGE_NAME#tyhub.tuyoo.com/xingguang/}
#     echo "touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_GM_IMAGE_NAME}:${TAG}"
#     touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_GM_IMAGE_NAME}:${TAG}

#     NEW_BATTLE_IMAGE_NAME=${BATTLE_IMAGE_NAME#tyhub.tuyoo.com/xingguang/}
#     echo "touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_BATTLE_IMAGE_NAME}:${BATTLE_TAG}"
#     touch /home/<USER>/.jenkins/workspace/dockertags/${NEW_BATTLE_IMAGE_NAME}:${BATTLE_TAG}
# fi


if [ "$DEPLOY" == "false" ]; then
    echo "不需要部署，任务结束！"
    exit 0;
fi
if [ -f "deploy/tools/deploy_${ENV}.sh" ]; then
    echo "部署脚本存在，执行部署脚本"
    source deploy/tools/deploy_${ENV}.sh ${TAG} ${BATTLE_TAG}

    # 测试环境删除本地镜像
    if [[ "$ENV" != *"online"* ]]; then
      echo "准备删除本地镜像"
      docker_rmi ${WEB_IMAGE_NAME}:${TAG}
      docker_rmi ${WEB_IMAGE_NAME}_${BRANCH}:latest
      docker_rmi ${GAME_IMAGE_NAME}:${TAG}
      docker_rmi ${GAME_IMAGE_NAME}_${BRANCH}:latest
      docker_rmi ${BATTLE_IMAGE_NAME}:${BATTLE_TAG}
      docker_rmi ${BATTLE_IMAGE_NAME}_${BRANCH}:latest
      echo "本地镜像删除成功"
    fi
    exit 0;
fi


if [ ! -d "deploy/config/${ENV}/k8s" ]; then
    echo "没有找到部署环境${ENV}的k8s目录！"

    # 测试环境删除本地镜像
    if [[ "$ENV" != *"online"* ]]; then
      echo "准备删除本地镜像"
      docker_rmi ${WEB_IMAGE_NAME}:${TAG}
      docker_rmi ${WEB_IMAGE_NAME}_${BRANCH}:latest
      docker_rmi ${GAME_IMAGE_NAME}:${TAG}
      docker_rmi ${GAME_IMAGE_NAME}_${BRANCH}:latest
      docker_rmi ${BATTLE_IMAGE_NAME}:${BATTLE_TAG}
      docker_rmi ${BATTLE_IMAGE_NAME}_${BRANCH}:latest
      echo "本地镜像删除成功"
    fi

    echo "没有找到部署环境${ENV}的k8s目录！，不执行部署，退出"
    exit
fi

echo "开始k8s部署"
#设置不同环境的k8s集群配置


if [ "$ENV" == "sim" ]; then
    K8S_NAME="sanguo2-sim"
    /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/login-server-sim"  "login-server-sim=${WEB_IMAGE_NAME}:${TAG}"
    /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/gm-server-sim"  "gm-server-sim=${WEB_IMAGE_NAME}:${TAG}"
    /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/battle-server-sim"  "battle-server-sim=${BATTLE_IMAGE_NAME}:${BATTLE_TAG}"
elif [ "$ENV" == "sim_canary" ]; then
    K8S_NAME="sanguo2-sim"
    # /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/login-server-sim"  "login-server-sim=${WEB_IMAGE_NAME}:${TAG}"
    # /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/gm-server-sim"  "gm-server-sim=${WEB_IMAGE_NAME}:${TAG}"
    # 灰度
    /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/battle-server-sim2"  "battle-server-sim=${BATTLE_IMAGE_NAME}:${BATTLE_TAG}"
elif [ "$ENV" == "oversea-online" ]; then
   K8S_NAME="sanguo-online"
   /usr/local/bin/kubectl set image -n "${K8S_NAME}"  "deploy/battle-server-online"  "battle-server-online=${BATTLE_IMAGE_NAME}:${BATTLE_TAG}"
   /usr/local/bin/kubectl set image -n "${K8S_NAME}"  "deploy/login-server-online"  "login-server-online=${WEB_IMAGE_NAME}:${TAG}"
   /usr/local/bin/kubectl set image -n "${K8S_NAME}"  "deploy/gm-server-online"  "gm-server-online=${WEB_IMAGE_NAME}:${TAG}"
elif [ "$ENV" == "online" ]; then
   K8S_NAME="sanguo2-china-online"
   /usr/local/bin/kubectl set image -n "${K8S_NAME}"  "deploy/battle-server-online"  "battle-server-online=${BATTLE_IMAGE_NAME}:${BATTLE_TAG}"
   /usr/local/bin/kubectl set image -n "${K8S_NAME}"  "deploy/login-server-online"  "login-server-online=${WEB_IMAGE_NAME}:${TAG}"
   /usr/local/bin/kubectl set image -n "${K8S_NAME}"  "deploy/gm-server-online"  "gm-server-online=${WEB_IMAGE_NAME}:${TAG}"
else
    K8S_NAME="sanguo2-${ENV}"
    /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/login-server-${ENV}"  "login-server-${ENV}=${WEB_IMAGE_NAME}:${TAG}"
    /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/gm-server-${ENV}"  "gm-server-${ENV}=${WEB_IMAGE_NAME}:${TAG}"
    /usr/bin/kubectl set image -n "${K8S_NAME}"  "deploy/battle-server-${ENV}"  "battle-server--${ENV}=${BATTLE_IMAGE_NAME}:${BATTLE_TAG}"
fi

echo -e "部署 ${BRANCH} 分支至 ${ENV} 环境"

cat build.result.log