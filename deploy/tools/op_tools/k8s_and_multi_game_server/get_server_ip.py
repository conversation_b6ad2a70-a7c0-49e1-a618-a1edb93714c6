#!/bin/python3
import sys
import os
from kazoo.client import KazooClient

server=sys.argv[1]
zk = KazooClient(hosts='mse-c14f8152-zk.mse.aliyuncs.com:2181')
zk.start()
path=f"/LS_V1/GameServers/{server}"
if zk.exists(path):
    #print('')
    pass
else:
    print(f'您输入的服务器{server}不存在', file=sys.stderr)
    exit(1)
data,stat = zk.get(f'{path}/rpc_ip')
#print('连接成功，获取数据')
host=data.decode('UTF-8')
if host == '':
    print(f'您要连接的是{server}服,ip不存在，无法跳转', file=sys.stderr)
else:
    # print(f'您要连接的是{server}服,ip是{host}正在为您跳转')
    print(f'{host}')
