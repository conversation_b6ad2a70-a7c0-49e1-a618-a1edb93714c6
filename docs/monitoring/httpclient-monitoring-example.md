# HttpClient 监控使用示例

## 快速开始

### 1. 添加依赖

在你的项目中添加 sgf-monitoring 依赖：

```xml
<dependency>
    <groupId>com.simfun.sgf</groupId>
    <artifactId>sgf-monitoring</artifactId>
    <version>1.2.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置应用

在 `application.properties` 中添加配置：

```properties
# 应用名称
monitoring.application.name=my-game-server

# Prometheus 导出器配置
monitoring.prometheus.port=9090
monitoring.prometheus.path=/metrics
monitoring.prometheus.host=127.0.0.1

# 启用 JVM 指标
monitoring.jvm.enabled=true
```

### 3. 启用监控

在 Spring Boot 应用中添加组件扫描：

```java
@SpringBootApplication
@ComponentScan(basePackages = {
    "com.lc.billion.icefire",
    "com.simfun.sgf.monitoring"  // 添加监控模块扫描
})
public class GameServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(GameServerApplication.class, args);
    }
}
```

## 使用示例

### 1. 使用 HttpUtil（自动监控）

```java
import com.lc.billion.icefire.core.support.HttpUtil;

public class GameService {
    
    public void callExternalAPI() {
        try {
            // 这些调用会自动被监控
            String response = HttpUtil.get("https://api.example.com/users");
            
            Map<String, String> params = new HashMap<>();
            params.put("username", "player123");
            String postResponse = HttpUtil.post("https://api.example.com/login", params);
            
            // JSON POST 请求
            String jsonData = "{\"action\":\"battle\",\"playerId\":123}";
            String jsonResponse = HttpUtil.postJSON("https://api.example.com/battle", jsonData);
            
        } catch (Exception e) {
            // 错误也会被监控记录
            log.error("API调用失败", e);
        }
    }
}
```

### 2. 使用 Utils 类（自动监控）

```java
import com.lc.billion.icefire.core.support.Utils;

public class DataService {
    
    public void fetchData() {
        try {
            // 简单 GET 请求
            String data = Utils.httpGet("https://api.example.com/data");
            
            // 带参数的 GET 请求
            Map<String, String> params = new HashMap<>();
            params.put("type", "player");
            params.put("limit", "100");
            String result = Utils.httpGet("https://api.example.com/search", params, true);
            
        } catch (Exception e) {
            log.error("数据获取失败", e);
        }
    }
}
```

### 3. 自定义 HttpClient 监控

```java
import com.lc.billion.icefire.core.monitoring.HttpClientMonitoring;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

@Service
public class CustomHttpService {
    
    private CloseableHttpClient httpClient;
    
    @PostConstruct
    public void init() {
        // 创建自定义的 HttpClient 并添加监控
        this.httpClient = HttpClientMonitoring.addMonitoring(
            HttpClients.custom()
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(20),
            "custom-service-client"  // 客户端名称
        ).build();
    }
    
    public String callService(String url) throws Exception {
        HttpGet request = new HttpGet(url);
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            return EntityUtils.toString(response.getEntity());
        }
    }
}
```

## 监控数据查看

### 1. 访问 Prometheus 指标

启动应用后，访问：`http://localhost:9090/metrics`

你会看到类似这样的指标：

```prometheus
# HELP http_client_requests_seconds Apache HttpClient 请求计时
# TYPE http_client_requests_seconds summary
http_client_requests_seconds_count{application="my-game-server",client_name="HttpUtil",method="GET",outcome="SUCCESS",status="200",uri="/api/users"} 5.0
http_client_requests_seconds_sum{application="my-game-server",client_name="HttpUtil",method="GET",outcome="SUCCESS",status="200",uri="/api/users"} 0.125

# HELP http_client_requests_total HTTP 请求总数
# TYPE http_client_requests_total counter
http_client_requests_total{application="my-game-server",client_name="HttpUtil",method="GET",outcome="SUCCESS",status="200"} 5.0
```

### 2. Grafana 仪表板

创建 Grafana 仪表板来可视化监控数据：

**请求速率面板：**
```promql
sum(rate(http_client_requests_total[5m])) by (client_name)
```

**平均响应时间面板：**
```promql
rate(http_client_requests_seconds_sum[5m]) / rate(http_client_requests_seconds_count[5m])
```

**错误率面板：**
```promql
sum(rate(http_client_requests_total{outcome!="SUCCESS"}[5m])) by (client_name) / sum(rate(http_client_requests_total[5m])) by (client_name)
```

**状态码分布面板：**
```promql
sum(rate(http_client_requests_total[5m])) by (status)
```

### 3. 告警规则

在 Prometheus 中设置告警规则：

```yaml
groups:
  - name: http_client_alerts
    rules:
      - alert: HighHttpErrorRate
        expr: |
          (
            sum(rate(http_client_requests_total{outcome!="SUCCESS"}[5m])) by (client_name) /
            sum(rate(http_client_requests_total[5m])) by (client_name)
          ) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "HTTP客户端错误率过高"
          description: "客户端 {{ $labels.client_name }} 的错误率为 {{ $value | humanizePercentage }}"

      - alert: HighHttpLatency
        expr: |
          (
            rate(http_client_requests_seconds_sum[5m]) /
            rate(http_client_requests_seconds_count[5m])
          ) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HTTP请求延迟过高"
          description: "平均响应时间为 {{ $value }}s"
```

## 最佳实践

### 1. 客户端命名规范

```java
// 好的命名方式
HttpClientMonitoring.addMonitoring(builder, "user-service-client");
HttpClientMonitoring.addMonitoring(builder, "payment-api-client");
HttpClientMonitoring.addMonitoring(builder, "game-data-client");

// 避免的命名方式
HttpClientMonitoring.addMonitoring(builder, "client-" + userId); // 高基数
HttpClientMonitoring.addMonitoring(builder, "temp-" + System.currentTimeMillis()); // 临时名称
```

### 2. 错误处理

```java
public class RobustHttpService {
    
    public String callAPI(String url) {
        try {
            return HttpUtil.get(url);
        } catch (Exception e) {
            // 监控会自动记录错误，但业务逻辑需要适当处理
            log.error("API调用失败: {}", url, e);
            
            // 返回默认值或重试
            return getDefaultResponse();
        }
    }
}
```

### 3. 性能优化

```java
@Service
public class OptimizedHttpService {
    
    // 复用 HttpClient 实例
    private final CloseableHttpClient httpClient;
    
    public OptimizedHttpService() {
        this.httpClient = HttpClientMonitoring.addMonitoring(
            HttpClients.custom()
                .setConnectionManager(createConnectionManager())
                .setDefaultRequestConfig(createRequestConfig()),
            "optimized-client"
        ).build();
    }
    
    private PoolingHttpClientConnectionManager createConnectionManager() {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setMaxTotal(200);
        cm.setDefaultMaxPerRoute(20);
        return cm;
    }
    
    private RequestConfig createRequestConfig() {
        return RequestConfig.custom()
            .setConnectTimeout(5000)
            .setSocketTimeout(10000)
            .build();
    }
}
```

## 故障排除

### 1. 监控未生效

检查日志中是否有以下信息：
```
INFO  c.s.s.m.i.HttpClientMonitoringInitializer - HttpClient 监控初始化完成
INFO  c.s.s.m.h.ApacheHttpClientInstrumentation - 为 Apache HttpClient 添加监控拦截器，客户端: HttpUtil
```

### 2. 指标未出现

确认以下几点：
- sgf-monitoring 模块已正确加载
- 应用配置中包含了组件扫描
- 已经执行了 HTTP 请求

### 3. 性能影响

监控对性能的影响极小（< 1%），如果发现性能问题：
- 检查是否有高基数的标签
- 确认 URI 模式提取是否正常工作
- 考虑调整监控采样率（如果需要）
