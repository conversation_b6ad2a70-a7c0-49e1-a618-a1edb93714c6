# HttpClient 监控集成指南

## 概述

本文档描述了如何在项目中为 Apache HttpComponents 4.x HttpClient 集成 Micrometer 监控指标。通过这个集成，所有的 HTTP 请求都将被自动监控，提供延迟、错误率、请求量等关键指标。

## 已集成的组件

### 1. HttpUtil (icefire-core)
位置：`icefire-core/src/main/java/com/lc/billion/icefire/core/support/HttpUtil.java`

**自动监控的方法：**
- `post()` - POST 请求
- `postJSON()` - JSON POST 请求  
- `get()` - GET 请求

**客户端名称：** `HttpUtil`

### 2. Utils (icefire-core)
位置：`icefire-core/src/main/java/com/lc/billion/icefire/core/support/Utils.java`

**自动监控的方法：**
- `httpGet(String url)` - 简单 GET 请求
- `httpGet(String url, Map<String, String> params, boolean encode)` - 带参数 GET 请求
- `httpsGet()` - HTTPS GET 请求

**客户端名称：** 
- `Utils-httpGet`
- `Utils-httpGet-params` 
- `Utils-httpsGet`

### 3. HttpUtils (icefire-web)
位置：`icefire-web/src/main/java/com/lc/billion/icefire/web/utils/HttpUtils.java`

**自动监控的方法：**
- `post()` - POST 请求
- `postJSON()` - JSON POST 请求
- `get()` - GET 请求

**客户端名称：** `HttpUtils`

## 监控指标

### 计时器指标 (Timer)
- **指标名称：** `http.client.requests`
- **描述：** Apache HttpClient 请求计时
- **单位：** 秒
- **标签：**
  - `client.name` - 客户端名称 (如 "HttpUtil", "Utils-httpGet")
  - `method` - HTTP 方法 (GET, POST, PUT, DELETE)
  - `status` - HTTP 状态码 (200, 404, 500 等)
  - `uri` - URI 模式 (如 "/api/users/{id}")
  - `outcome` - 请求结果 (SUCCESS, CLIENT_ERROR, SERVER_ERROR, ERROR)

### 计数器指标 (Counter)
- **指标名称：** `http.client.requests.total`
- **描述：** HTTP 请求总数
- **标签：** 与计时器相同

## 监控数据查看

### 1. Prometheus 端点
访问：`http://localhost:9090/metrics`

### 2. 示例指标输出
```prometheus
# HELP http_client_requests_seconds Apache HttpClient 请求计时
# TYPE http_client_requests_seconds summary
http_client_requests_seconds_count{application="your-app",client_name="HttpUtil",method="POST",outcome="SUCCESS",status="200",uri="/api/users"} 5.0
http_client_requests_seconds_sum{application="your-app",client_name="HttpUtil",method="POST",outcome="SUCCESS",status="200",uri="/api/users"} 0.125

# HELP http_client_requests_total HTTP 请求总数
# TYPE http_client_requests_total counter
http_client_requests_total{application="your-app",client_name="HttpUtil",method="POST",outcome="SUCCESS",status="200"} 5.0
```

### 3. Grafana 查询示例

**请求速率：**
```promql
rate(http_client_requests_total[5m])
```

**平均响应时间：**
```promql
rate(http_client_requests_seconds_sum[5m]) / rate(http_client_requests_seconds_count[5m])
```

**错误率：**
```promql
rate(http_client_requests_total{outcome!="SUCCESS"}[5m]) / rate(http_client_requests_total[5m])
```

**按客户端分组的请求量：**
```promql
sum(rate(http_client_requests_total[5m])) by (client_name)
```

## 技术实现

### 核心组件

1. **ApacheHttpClientInstrumentation** - Apache HttpComponents 监控装饰器
2. **HttpClientMonitoring** - 解耦的监控集成类
3. **HttpClientMonitoringInitializer** - 自动初始化监控功能

### 架构特点

- **解耦设计：** icefire-core 不直接依赖 Micrometer
- **自动集成：** 通过反射和回调函数实现自动监控
- **降级处理：** 监控失败时不影响业务功能
- **标签优化：** 自动提取 URI 模式，减少标签基数

### 监控流程

1. 应用启动时，`HttpClientMonitoringInitializer` 自动注入监控装饰器
2. HttpClient 创建时，自动添加请求/响应拦截器
3. 每次 HTTP 请求时，记录开始时间和请求信息
4. 请求完成时，计算耗时并记录指标到 Prometheus

## 配置选项

### 监控配置
```properties
# 应用名称（用于指标标签）
monitoring.application.name=your-app-name

# Prometheus 导出器配置
monitoring.prometheus.port=9090
monitoring.prometheus.path=/metrics
monitoring.prometheus.host=127.0.0.1
```

### JVM 指标
```properties
# 启用 JVM 指标收集
monitoring.jvm.enabled=true
```

## 故障排除

### 1. 指标未出现
- 检查 sgf-monitoring 模块是否正确加载
- 确认 `HttpClientMonitoringInitializer` 是否执行
- 查看日志中是否有监控相关的错误信息

### 2. 监控影响性能
- 监控使用虚拟线程异步处理，性能影响极小
- 如需禁用，可以移除 sgf-monitoring 依赖

### 3. 标签基数过高
- URI 自动进行模式提取（如 `/users/123` → `/users/{id}`）
- 避免在 URI 中包含用户 ID、时间戳等高基数值

## 扩展使用

### 为其他 HttpClient 添加监控
```java
// 创建 HttpClientBuilder
HttpClientBuilder builder = HttpClients.custom()
    .setConnectionManager(connectionManager)
    .setDefaultRequestConfig(requestConfig);

// 添加监控
builder = HttpClientMonitoring.addMonitoring(builder, "my-client");

// 构建客户端
CloseableHttpClient client = builder.build();
```

### 检查监控是否可用
```java
if (HttpClientMonitoring.isMonitoringAvailable()) {
    // 监控可用
    logger.info("HTTP 监控已启用");
} else {
    // 监控不可用
    logger.warn("HTTP 监控未启用");
}
```

## 最佳实践

1. **客户端命名：** 使用有意义的客户端名称，便于区分不同的 HTTP 客户端
2. **标签管理：** 避免使用高基数的标签值（如用户 ID、订单号）
3. **监控告警：** 基于错误率和响应时间设置合理的告警阈值
4. **性能监控：** 定期检查监控对应用性能的影响
5. **指标清理：** 定期清理不再使用的指标，避免内存泄漏
