package com.lc.billion.icefire.client.msg;

import com.lc.billion.icefire.client.net.CgTopHandler;
import com.lc.billion.icefire.client.net.ClientNetHelper;
import com.lc.billion.icefire.client.net.ClientNetSession;
import com.simfun.sgf.net.MsgPack;
import com.simfun.sgf.net.NetConfig;
import com.simfun.sgf.net.NetMessageHelper;
import com.simfun.sgf.net.codec.MessageBodyEncoder;
import com.simfun.sgf.net.msg.MessageConfig.MessageMeta;
import com.simfun.sgf.net.msg.MessageConfigManager;
import com.simfun.sgf.net.msg.NetMessage;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import io.netty.handler.codec.MessageToMessageEncoder;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * {@link NetMessage}消息编码器
 *
 * <AUTHOR>
 */
public class ClientNetMessageWebScoketEncoder extends MessageToMessageEncoder<MsgPack> {

	private static final Logger log = LoggerFactory.getLogger(ClientNetMessageWebScoketEncoder.class);

	private MessageBodyEncoder bodyEncoder;

	private MessageConfigManager<MessageMeta> messageConfigManager;

	private NetConfig netConfig;

	public ClientNetMessageWebScoketEncoder(NetConfig netConfig, MessageConfigManager<MessageMeta> messageConfigManager, MessageBodyEncoder bodyEncoder) {
		this.netConfig = netConfig;
		this.messageConfigManager = messageConfigManager;
		this.bodyEncoder = bodyEncoder;
	}

	@Override
	protected void encode(ChannelHandlerContext ctx, MsgPack msgPack, List<Object> out) throws Exception {
		int msgType = 0;
		Object body = msgPack.getMsg();
		if (!bodyEncoder.canEncode(body)) {
			// 不支持的类型，直接原样返回，调用层会继续发个下一个ChannelHandler
			return;
		}
		msgType = messageConfigManager.getMessageType(body.getClass());
		if (msgType < 0) {
			log.error("Not found message Type mapping: " + body.getClass().getName());
			return; // 编码失败
		}

		ClientNetSession session = CgTopHandler.getSession(ctx);
		if (!msgPack.isNotLog()) {
			log.info("RESPONSE -> [{}:{}]{}", ClientNetHelper.getSessionDesc(session), msgType, body);
		}

		//build body
		boolean compressed=false;
		ByteBuf bodyBuf = Unpooled.directBuffer(session.getEncodeBufferSize());
		bodyEncoder.encode(body, bodyBuf);
		int bodyLen = bodyBuf.readableBytes();

		//build head
		int headerSize = netConfig.isEnableSequenceMode() ? NetMessage.HEADER_SIZE + NetMessage.SEQUENCE_SIZE : NetMessage.HEADER_SIZE;
		ByteBuf headBuf = Unpooled.directBuffer(headerSize);
		headBuf.writeShort(NetMessageHelper.createFlag(compressed, msgPack.isVerifySequence(), false)); // flag
		if (netConfig.isEnableSequenceMode()) {
			headBuf.writeShort(msgPack.getSequence());
		}
		headBuf.writeShort(msgType);
		headBuf.writeInt(bodyLen); // bodyLen 占位

		//component bytebuf
		ByteBuf newOut = Unpooled.wrappedBuffer(headBuf, bodyBuf);
		BinaryWebSocketFrame binaryWebSocketFrame = new BinaryWebSocketFrame(newOut);
		out.add(binaryWebSocketFrame);
	}
}
