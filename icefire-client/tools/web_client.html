<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>协议模拟器</title>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .input-group input[type="text"],
        .input-group input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: #fff;
        }

        .params-table th, .params-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .params-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .params-table tr:hover {
            background-color: #f8f9fa;
        }

        button {
            padding: 10px 20px;
            margin-right: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #45a049;
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        #response {
            width: 100%;
            height: 300px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.6;
            background-color: #f8f9fa;
            resize: vertical;
        }

        .control-panel {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .auto-increment-group {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .auto-increment-group input {
            width: 150px;
        }

        .continuous-controls {
            display: flex;
            flex-direction: row;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            padding: 15px 0;
        }

        .input-item {
            flex: 0 1 auto;
            min-width: 150px;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .input-item label {
            margin-right: 8px;
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }

        .input-item input {
            width: 80px;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }

        .collapsible {
            background-color: #f8f9fa;
            color: #333;
            cursor: pointer;
            padding: 12px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .collapsible:hover {
            background-color: #eee;
        }

        .collapsible.active {
            background-color: #4CAF50;
            color: white;
        }

        .collapsible:after {
            content: '\002B';
            font-weight: bold;
            float: right;
            margin-left: 5px;
            color: inherit;
        }

        .active:after {
            content: "\2212";
        }

        .content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.2s ease-out;
            background-color: white;
        }

        .params-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .step-input {
            width: 80px !important;
            margin-left: 5px;
        }

        .continuous-panel {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        #progressBar {
            margin-top: 15px;
            width: 100%;
            height: 20px;
            background-color: #ddd;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        #progressFill {
            height: 100%;
            background-color: #4CAF50;
            width: 0;
            transition: width 0.3s ease;
        }

        #progressText {
            position: absolute;
            width: 100%;
            text-align: center;
            line-height: 20px;
            color: black;
            z-index: 1;
            font-size: 12px;
        }

        .stats-info {
            margin-top: 15px;
            display: flex;
            gap: 20px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .stats-info span {
            font-size: 14px;
        }

        #successCount {
            color: #4CAF50;
            font-weight: bold;
        }

        #failureCount {
            color: #f44336;
            font-weight: bold;
        }

        #stopContinuous {
            background-color: #f44336;
        }

        #stopContinuous:hover {
            background-color: #da190b;
        }

        .stats-charts {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .chart-container {
            position: relative;
            height: 200px;
            margin-bottom: 20px;
        }

        .stats-summary {
            display: flex;
            justify-content: space-around;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-top: 10px;
        }

        .stats-summary span {
            font-size: 14px;
        }

        .stats-charts-container,
        .response-container {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .stats-charts-container .content,
        .response-container .content {
            padding: 15px;
            background-color: white;
        }

        .stats-charts-container .collapsible,
        .response-container .collapsible {
            margin-bottom: 0;
            border-radius: 4px 4px 0 0;
        }

        @media (max-width: 1200px) {
            .continuous-controls {
                flex-wrap: wrap;
            }

            .input-item {
                flex: 1 1 calc(33.333% - 10px);
            }

            .button-group {
                flex: 1 1 100%;
                justify-content: flex-end;
                margin-top: 10px;
            }
        }

        @media (max-width: 768px) {
            .container {
                width: 100%;
                padding: 10px;
            }

            .input-item {
                flex: 1 1 calc(50% - 10px);
            }

            .auto-increment-group {
                flex-direction: column;
                align-items: stretch;
            }

            .auto-increment-group input {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .input-item {
                flex: 1 1 100%;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<div class="container">
    <h2>协议模拟器</h2>

    <div class="input-group">
        <label for="url">URL:</label>
        <input type="text" id="url" placeholder="输入完整URL">
    </div>

    <div class="input-group">
        <label>默认步长:</label>
        <div class="auto-increment-group">
            <input type="number" id="defaultStep" placeholder="默认步长" value="1">
        </div>
    </div>

    <div class="control-panel">
        <button onclick="parseUrl()">解析URL</button>
        <button onclick="sendRequest()">发送请求</button>
        <button onclick="clearResponse()">清空响应</button>
        <button onclick="copyResponse()">复制响应</button>
    </div>

    <div class="continuous-panel">
        <div class="continuous-controls">
            <div class="input-item">
                <label for="totalRequests">请求总次数:</label>
                <input type="number" id="totalRequests" placeholder="请求总次数" value="100" min="1">
            </div>
            <div class="input-item">
                <label for="concurrency">并发数:</label>
                <input type="number" id="concurrency" placeholder="并发数" value="10" min="1">
            </div>
            <div class="input-item">
                <label for="interval">间隔(ms):</label>
                <input type="number" id="interval" placeholder="请求间隔" value="0" min="0">
            </div>
            <div class="input-item">
                <label for="timeout">超时(ms):</label>
                <input type="number" id="timeout" placeholder="超时时间" value="5000" min="0">
            </div>
            <div class="input-item">
                <label for="retryCount">重试次数:</label>
                <input type="number" id="retryCount" placeholder="重试次数" value="0" min="0">
            </div>
            <div class="button-group">
                <button id="startContinuous" onclick="startSimulation()">开始发送</button>
                <button id="stopContinuous" onclick="stopSimulation()" disabled>停止发送</button>
            </div>
        </div>
        <div id="progressBar">
            <div id="progressFill"></div>
            <span id="progressText">0/0</span>
        </div>
        <div id="statsInfo" class="stats-info">
            <span>成功请求: <span id="successCount">0</span></span>
            <span>失败请求: <span id="failureCount">0</span></span>
            <span>任务总耗时: <span id="totalTaskTime">0</span> ms</span>

        </div>
        <div class="stats-charts-container">
            <button type="button" class="collapsible">图表统计</button>
            <div class="content stats-charts">
                <div class="chart-container">
                    <canvas id="responseTimeChart"></canvas>
                </div>
                <div class="chart-container">
                    <canvas id="responseTimeHistogram"></canvas>
                </div>
                <div class="stats-summary">
                    <span>平均响应时间: <span id="avgResponseTime">0</span> ms</span>
                    <span>最小响应时间: <span id="minResponseTime">0</span> ms</span>
                    <span>最大响应时间: <span id="maxResponseTime">0</span> ms</span>
                </div>
            </div>
        </div>
    </div>

    <div class="params-container">
        <button type="button" class="collapsible">参数列表 <span id="paramCount">(0个参数)</span></button>
        <div class="content">
            <table id="paramsTable" class="params-table">
                <thead>
                <tr>
                    <th>参数名</th>
                    <th>参数值</th>
                    <th>自增</th>
                    <th>步长</th>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>

    <h3>响应结果:</h3>
    <div class="response-container">
        <button type="button" class="collapsible">响应结果</button>
        <div class="content">
            <textarea id="response" readonly></textarea>
        </div>
    </div>
</div>
<script>
    let currentParams = new Map();
    let autoIncrFields = new Map();
    let isSending = false;
    let completedRequests = 0;
    let totalRequests = 0;
    let activePromises = [];
    let successCount = 0;
    let failureCount = 0;
    let responseTimes = [];
    let responseTimeChart = null;
    let responseHistogram = null;
    let isChartsVisible = false; // 初始状态设为false
    let isResponseVisible = false; // 初始状态设为false
    let pendingChartData = [];
    let pendingResponseData = [];
    let responseHistory = [];
    const MAX_HISTORY = 100;
    let taskStartTime=0;

    document.addEventListener('DOMContentLoaded', function () {
        console.log("DOM已加载完成");

        const coll = document.getElementsByClassName("collapsible");
        console.log("找到的折叠面板数量:", coll.length);

        for (let i = 0; i < coll.length; i++) {
            coll[i].addEventListener("click", function () {
                this.classList.toggle("active");
                const content = this.nextElementSibling;

                if (content.style.maxHeight) {
                    content.style.maxHeight = null;
                    content.style.display = 'none'; // 完全隐藏内容

                    // 如果是图表面板，标记为隐藏
                    if (content.classList.contains('stats-charts')) {
                        isChartsVisible = false;
                    }
                    // 如果是响应面板，标记为隐藏
                    if (content.querySelector('#response')) {
                        isResponseVisible = false;
                    }
                } else {
                    content.style.display = ''; // 恢复显示
                    content.style.maxHeight = content.scrollHeight + "px";

                    // 如果是图表面板，显示并更新待更新数据
                    if (content.classList.contains('stats-charts')) {
                        isChartsVisible = true;
                        if (pendingChartData.length > 0) {
                            console.log("更新待处理的图表数据:", pendingChartData.length);
                            batchUpdateCharts();
                        }
                    }

                    // 如果是响应面板，显示并更新待更新数据
                    if (content.querySelector('#response')) {
                        isResponseVisible = true;
                        if (pendingResponseData.length > 0) {
                            console.log("更新待处理的响应数据:", pendingResponseData.length);
                            batchUpdateResponse();
                        }
                    }
                }
            });
        }

        initCharts();

        // 初始化时折叠图表和响应区域
        const statsCharts = document.querySelector('.stats-charts');
        const responseContent = document.querySelector('.response-container .content');
        if (statsCharts) {
            statsCharts.style.maxHeight = null;
            statsCharts.style.display = 'none';
            isChartsVisible = false;
        }
        if (responseContent) {
            responseContent.style.maxHeight = null;
            responseContent.style.display = 'none';
            isResponseVisible = false;
        }
    });

    function initCharts() {
        const timeChartCtx = document.getElementById('responseTimeChart').getContext('2d');
        const histogramCtx = document.getElementById('responseTimeHistogram').getContext('2d');

        responseTimeChart = new Chart(timeChartCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '响应时间 (ms)',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '响应时间 (ms)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '请求序号'
                        }
                    }
                }
            }
        });

        responseHistogram = new Chart(histogramCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: '响应时间分布',
                    data: [],
                    backgroundColor: 'rgba(75, 192, 192, 0.5)',
                    borderColor: 'rgb(75, 192, 192)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '请求数量'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '响应时间范围 (ms)'
                        }
                    }
                }
            }
        });
    }

    function batchUpdateCharts() {
        if (!isChartsVisible || pendingChartData.length === 0) return;

        // 暂停图表动画
        if (responseTimeChart) responseTimeChart.options.animation = false;
        if (responseHistogram) responseHistogram.options.animation = false;

        // 批量更新折线图数据
        const newLabels = [];
        const newData = [];
        pendingChartData.forEach((responseTime, index) => {
            responseTimes.push(responseTime);
            newLabels.push(responseTimes.length);
            newData.push(responseTime);
        });

        responseTimeChart.data.labels = responseTimeChart.data.labels.concat(newLabels);
        responseTimeChart.data.datasets[0].data = responseTimeChart.data.datasets[0].data.concat(newData);

        // 更新直方图
        const binSize = 100;
        const bins = {};
        responseTimes.forEach(time => {
            const binIndex = Math.floor(time / binSize);
            bins[binIndex] = (bins[binIndex] || 0) + 1;
        });

        const sortedBins = Object.entries(bins).sort((a, b) => Number(a[0]) - Number(b[0]));
        responseHistogram.data.labels = sortedBins.map(([bin]) =>
            `${bin * binSize}-${(Number(bin) + 1) * binSize}`
        );
        responseHistogram.data.datasets[0].data = sortedBins.map(([, count]) => count);

        // 一次性更新图表
        responseTimeChart.update('none');
        responseHistogram.update('none');

        // 恢复图表动画
        if (responseTimeChart) responseTimeChart.options.animation = true;
        if (responseHistogram) responseHistogram.options.animation = true;

        // 更新统计信息
        updateResponseTimeStats();

        // 清空待更新数据
        pendingChartData = [];
    }

    function batchUpdateResponse() {
        if (!isResponseVisible || pendingResponseData.length === 0) return;

        const responseArea = document.getElementById('response');

        // 添加新的响应数据到历史记录
        responseHistory = [...pendingResponseData, ...responseHistory].slice(0, MAX_HISTORY);

        // 一次性更新显示
        responseArea.value = responseHistory.join('');
        responseArea.scrollTop = 0;

        // 清空待更新数据
        pendingResponseData = [];
    }

    // 图表更新
    function updateCharts(responseTime) {
        if (isUpdatingCharts) return;

        if (!isChartsVisible) {
            pendingChartData.push(responseTime);
            return;
        }

        isUpdatingCharts = true;
        requestAnimationFrame(() => {
            updateChartsImmediate(responseTime);
            isUpdatingCharts = false;
        });
    }

    function updateChartsImmediate(responseTime) {
        responseTimes.push(responseTime);

        // 更新折线图
        responseTimeChart.data.labels.push(responseTimes.length);
        responseTimeChart.data.datasets[0].data.push(responseTime);
        responseTimeChart.update();

        // 更新直方图
        const binSize = 100;
        const bins = {};
        responseTimes.forEach(time => {
            const binIndex = Math.floor(time / binSize);
            bins[binIndex] = (bins[binIndex] || 0) + 1;
        });

        const sortedBins = Object.entries(bins).sort((a, b) => Number(a[0]) - Number(b[0]));
        responseHistogram.data.labels = sortedBins.map(([bin]) =>
            `${bin * binSize}-${(Number(bin) + 1) * binSize}`
        );
        responseHistogram.data.datasets[0].data = sortedBins.map(([, count]) => count);
        responseHistogram.update();

        updateResponseTimeStats();
    }

    function resetCharts() {
        responseTimes = [];
        pendingChartData = [];
        if (responseTimeChart) {
            responseTimeChart.data.labels = [];
            responseTimeChart.data.datasets[0].data = [];
            responseTimeChart.update();
        }
        if (responseHistogram) {
            responseHistogram.data.labels = [];
            responseHistogram.data.datasets[0].data = [];
            responseHistogram.update();
        }
        updateResponseTimeStats();
    }

    function updateResponseTimeStats() {
        if (responseTimes.length === 0) {
            document.getElementById('avgResponseTime').textContent = '0';
            document.getElementById('minResponseTime').textContent = '0';
            document.getElementById('maxResponseTime').textContent = '0';
            return;
        }

        const avg = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
        const min = Math.min(...responseTimes);
        const max = Math.max(...responseTimes);

        document.getElementById('avgResponseTime').textContent = avg.toFixed(2);
        document.getElementById('minResponseTime').textContent = min.toFixed(2);
        document.getElementById('maxResponseTime').textContent = max.toFixed(2);


    }

    async function userBehavior(userId, totalRequests, delayBetweenRequests, retryCount) {
        for (let i = 0; i < totalRequests && isSending; i++) {
            try {
                await sendSingleRequestWithRetry(retryCount);
                console.log(`User ${userId} completed request #${i + 1}`);
            } catch (error) {
                console.error(`User ${userId} request #${i + 1} failed:`, error);
            }
            if (delayBetweenRequests > 0) {
                await new Promise(resolve => setTimeout(resolve, delayBetweenRequests));
            }
        }
    }

    async function sendSingleRequestWithRetry(retryCount) {
        const timeout = parseInt(document.getElementById('timeout').value) || 5000;
        let finalUrl;

        for (let retry = 0; retry <= retryCount; retry++) {
            try {
                const startTime = performance.now();
                const baseUrl = new URL(document.getElementById('url').value);
                const newParams = new URLSearchParams();

                for (const [key, value] of currentParams) {
                    if (autoIncrFields.has(key)) {
                        const autoIncrInfo = autoIncrFields.get(key);
                        newParams.set(key, autoIncrInfo.value.toString());
                        autoIncrInfo.value = autoIncrInfo.value + autoIncrInfo.step;
                        currentParams.set(key, autoIncrInfo.value.toString());
                    } else {
                        newParams.set(key, value);
                    }
                }

                updateParamsTable();

                finalUrl = `${baseUrl.origin}${baseUrl.pathname}?${newParams.toString()}`;

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);

                const response = await fetch(finalUrl, {
                    signal: controller.signal
                });

                const endTime = performance.now();
                const responseTime = endTime - startTime;

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                completedRequests++;
                successCount++;
                updateProgress();
                updateStats();
                updateCharts(responseTime);
                appendToResponse(`请求 #${completedRequests}\nURL: ${finalUrl}\n响应时间: ${responseTime.toFixed(2)}ms\n响应:\n${formatJSON(text)}\n\n`);
                return true;
            } catch (error) {
                if (retry === retryCount) {
                    completedRequests++;
                    failureCount++;
                    updateProgress();
                    updateStats();
                    appendToResponse(`请求失败 (重试${retry}次): ${error.message}\n请求URL: ${finalUrl}\n\n`);
                    throw error;
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    }

    async function startSimulation() {
        if (isSending) return;
        taskStartTime = performance.now();  // 记录开始时间
        const userCount = parseInt(document.getElementById('concurrency').value);
        totalRequests = parseInt(document.getElementById('totalRequests').value); // 修改为requestCount
        const delayBetweenRequests = parseInt(document.getElementById('interval').value);
        const retryCount = parseInt(document.getElementById('retryCount').value);

        if (userCount < 1 || totalRequests < 1 || delayBetweenRequests < 0) {
            alert('请输入有效的参数！');
            return;
        }

        // 计算每个用户应该发送的请求数
        const requestsPerUser = Math.floor(totalRequests / userCount);
        // 计算剩余的请求数
        const remainingRequests = totalRequests % userCount;

        resetStats();
        resetCharts();
        completedRequests = 0;
        isSending = true;

        document.getElementById('startContinuous').disabled = true;
        document.getElementById('stopContinuous').disabled = false;
        updateProgress();

        const userPromises = [];

        // 分配请求给用户
        for (let userId = 1; userId <= userCount; userId++) {
            // 最后一个用户处理剩余的请求
            const thisUserRequests = userId === userCount ?
                requestsPerUser + remainingRequests : requestsPerUser;

            userPromises.push(
                userBehavior(userId, thisUserRequests, delayBetweenRequests, retryCount)
            );
        }

        try {
            await Promise.all(userPromises);
            const totalTime = performance.now() - taskStartTime;
            document.getElementById('totalTaskTime').textContent = totalTime.toFixed(2);
        } catch (error) {
            console.error('模拟过程中出错:', error);
        } finally {
            isSending = false;
            resetSendingStatus();
        }
    }

    function stopSimulation() {
        isSending = false;
        resetSendingStatus();
        responseHistory = [];
        pendingResponseData = [];
        pendingChartData = [];
        responseTimes = [];

        // 清理DOM
        document.getElementById('response').value = '';

        // 重置进度
        document.getElementById('progressFill').style.width = '0%';
        document.getElementById('progressText').textContent = '0/0 (0%)';

        // 重置统计
        resetStats();
        resetCharts();
    }

    function parseUrl() {
        console.log("开始解析URL");
        const urlInput = document.getElementById('url').value;
        console.log("输入的URL:", urlInput);

        try {
            const url = new URL(urlInput);
            const params = url.searchParams;
            currentParams.clear();
            autoIncrFields.clear();

            params.forEach((value, key) => {
                console.log(`解析参数: ${key} = ${value}`);
                currentParams.set(key, value);
            });

            updateParamsTable();

            // 获取参数面板元素
            const paramsContainer = document.querySelector('.params-container');
            const collapsibleButton = paramsContainer.querySelector('.collapsible');
            const content = paramsContainer.querySelector('.content');

            // 添加active类并展开内容
            collapsibleButton.classList.add('active');
            content.style.display = '';

            // 使用requestAnimationFrame确保DOM更新后再设置高度
            requestAnimationFrame(() => {
                content.style.maxHeight = content.scrollHeight + "px";
                console.log("设置面板高度:", content.scrollHeight);
            });

        } catch (e) {
            console.error("解析URL出错:", e);
            document.getElementById('response').value = '解析URL失败: ' + e.message;
        }
    }

    function updateParamCount() {
        const count = currentParams.size;
        document.getElementById('paramCount').textContent = `(${count}个参数)`;
    }

    function updateParam(key, value) {
        currentParams.set(key, value);
        if (autoIncrFields.has(key)) {
            let numValue;
            try {
                numValue = BigInt(value.replace(/[^\d-]/g, ''));
            } catch {
                numValue = BigInt(0);
            }
            autoIncrFields.get(key).value = numValue;
        }
    }

    function toggleAutoIncr(key, checked) {
        const stepInput = document.querySelector(`input[data-step-for="${key}"]`);
        if (checked) {
            let currentValue;
            try {
                currentValue = BigInt(currentParams.get(key).replace(/[^\d-]/g, ''));
            } catch {
                currentValue = BigInt(0);
            }
            const step = BigInt(stepInput.value) || BigInt(1);
            autoIncrFields.set(key, {
                value: currentValue,
                step: step
            });
            stepInput.disabled = false;
        } else {
            autoIncrFields.delete(key);
            stepInput.disabled = true;
        }
    }

    function updateStep(key, step) {
        if (autoIncrFields.has(key)) {
            try {
                autoIncrFields.get(key).step = BigInt(step) || BigInt(1);
            } catch {
                autoIncrFields.get(key).step = BigInt(1);
            }
        }
    }

    // 参数表更新
    function updateParamsTable() {
        if (isUpdatingParams) return;

        isUpdatingParams = true;
        requestAnimationFrame(() => {
            const tbody = document.querySelector('#paramsTable tbody');
            if (!tbody) {
                isUpdatingParams = false;
                return;
            }

            const fragment = document.createDocumentFragment();
            currentParams.forEach((value, key) => {
                const isAutoIncr = autoIncrFields.has(key);
                const autoIncrInfo = autoIncrFields.get(key) || {
                    step: document.getElementById('defaultStep').value || 1
                };

                const row = document.createElement('tr');
                row.innerHTML = `
                <td>${key}</td>
                <td><input type="text" value="${value}" onchange="updateParam('${key}', this.value)" style="width: 100%; padding: 5px;"></td>
                <td><input type="checkbox" ${isAutoIncr ? 'checked' : ''} onchange="toggleAutoIncr('${key}', this.checked)"></td>
                <td><input type="number" class="step-input" data-step-for="${key}" value="${autoIncrInfo.step}"
                    onchange="updateStep('${key}', this.value)" ${!isAutoIncr ? 'disabled' : ''}></td>
            `;
                fragment.appendChild(row);
            });

            tbody.innerHTML = '';
            tbody.appendChild(fragment);
            updateParamCount();
            isUpdatingParams = false;
        });
    }

    async function sendRequest() {
        try {
            const startTime = performance.now();
            const baseUrl = new URL(document.getElementById('url').value);
            const newParams = new URLSearchParams();

            for (const [key, value] of currentParams) {
                if (autoIncrFields.has(key)) {
                    const autoIncrInfo = autoIncrFields.get(key);
                    newParams.set(key, autoIncrInfo.value.toString());
                    autoIncrInfo.value = autoIncrInfo.value + autoIncrInfo.step;
                    currentParams.set(key, autoIncrInfo.value.toString());
                } else {
                    newParams.set(key, value);
                }
            }

            updateParamsTable();

            const finalUrl = `${baseUrl.origin}${baseUrl.pathname}?${newParams.toString()}`;

            document.getElementById('response').value = '发送请求中...\n' + finalUrl;

            const response = await fetch(finalUrl);
            const endTime = performance.now();
            const responseTime = endTime - startTime;

            const text = await response.text();
            successCount++;
            updateStats();
            updateCharts(responseTime);
            const formattedResponse = formatJSON(text);
            appendToResponse(`请求URL: ${finalUrl}\n响应时间: ${responseTime.toFixed(2)}ms\n\n响应:\n${formattedResponse}\n\n`);
        } catch (e) {
            failureCount++;
            updateStats();
            appendToResponse('请求失败: ' + e.message + '\n\n');
        }
    }

    function resetSendingStatus() {
        document.getElementById('startContinuous').disabled = false;
        document.getElementById('stopContinuous').disabled = true;
    }

    function clearResponse() {
        document.getElementById('response').value = '';
        responseHistory = [];
        pendingResponseData = [];
    }

    function resetStats() {
        successCount = 0;
        failureCount = 0;
        updateStats();
    }

    function copyResponse() {
        const responseText = document.getElementById('response');
        responseText.select();
        document.execCommand('copy');
        alert('已复制到剪贴板');
    }

    function formatJSON(str) {
        try {
            const obj = JSON.parse(str);
            return JSON.stringify(obj, null, 2);
        } catch (e) {
            return str;
        }
    }

    // 响应内容更新
    let isUpdatingResponse = false;
    function appendToResponse(text) {
        if (!isResponseVisible) {
            pendingResponseData.push(text);
            return;
        }

        if (isUpdatingResponse) return;

        isUpdatingResponse = true;
        requestAnimationFrame(() => {
            responseHistory.unshift(text);
            if (responseHistory.length > MAX_HISTORY) {
                responseHistory.pop();
            }
            const responseArea = document.getElementById('response');
            responseArea.value = responseHistory.join('');
            responseArea.scrollTop = 0;
            isUpdatingResponse = false;
        });
    }
    // 添加状态标志
    let isUpdatingProgress = false;
    let isUpdatingStats = false;
    let isUpdatingCharts = false;
    let isUpdatingParams = false;

    // 进度更新
    function updateProgress() {
        if (isUpdatingProgress) return;

        isUpdatingProgress = true;
        requestAnimationFrame(() => {
            const percentage = (completedRequests / totalRequests) * 100;
            document.getElementById('progressFill').style.width = `${percentage}%`;
            document.getElementById('progressText').textContent =
                `${completedRequests}/${totalRequests} (${percentage.toFixed(1)}%)`;
            isUpdatingProgress = false;
        });
    }

    // 统计信息更新
    function updateStats() {
        if (isUpdatingStats) return;
        isUpdatingStats = true;
        requestAnimationFrame(() => {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('failureCount').textContent = failureCount;
            const totalTime = performance.now() - taskStartTime;
            document.getElementById('totalTaskTime').textContent = totalTime.toFixed(2);
            isUpdatingStats = false;
        });
    }

    function throttle(func, limit) {
        let inThrottle;
        return function (...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    let updatePending = false;

    function scheduleUpdate() {
        if (!updatePending) {
            updatePending = true;
            requestAnimationFrame(() => {
                updateStats();
                updateProgress();
                updateCharts();
                updatePending = false;
            });
        }
    }

    function monitorPerformance() {
        console.log('性能监控:', {
            pendingChartData: pendingChartData.length,
            pendingResponseData: pendingResponseData.length,
            isChartsVisible,
            isResponseVisible,
            memoryUsage: performance.memory ? {
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                usedJSHeapSize: performance.memory.usedJSHeapSize
            } : 'Not available'
        });
    }

    setInterval(monitorPerformance, 5000);

</script>