package com.lc.billion.icefire.core.common;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.simfun.sgf.enumtype.IntEnum;
import com.simfun.sgf.utils.JavaUtils;
import org.reflections.Reflections;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 *
 * <AUTHOR>
 */
public class JsonUtils {

	public static final ObjectMapper objectMapper;

	static {
		objectMapper = new ObjectMapper();
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES); // 跳过不存在的属性
		objectMapper.enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL); // 不存在的枚举设置为null
	}

	public static void intEnumForJson(String packageName) {
		Reflections reflections = new Reflections(packageName);
		Set<Class<? extends IntEnum>> intEnumClasses = reflections.getSubTypesOf(IntEnum.class);
		if (intEnumClasses.isEmpty()) {
			return;
		}
		// intEnumForFastJson(intEnumClasses);
		intEnumForJackson(intEnumClasses);
	}

	@SuppressWarnings("unused")
	private static void intEnumForFastJson(Set<Class<? extends IntEnum>> intEnumClasses) {
		SerializeConfig serializeConfig = SerializeConfig.getGlobalInstance();
		ParserConfig parserConfig = ParserConfig.getGlobalInstance();
		for (Class<? extends IntEnum> c : intEnumClasses) {
			intEnumForFastJson(c, serializeConfig);
			intEnumForFastJson(c, parserConfig);
		}
	}

	private static void intEnumForJackson(Set<Class<? extends IntEnum>> intEnumClasses) {
		ObjectMapper om = JsonUtils.objectMapper;
		SimpleModule mod = new SimpleModule();
		for (Class<? extends IntEnum> c : intEnumClasses) {
			intEnumForJackson(c, mod);
		}
		om.registerModule(mod);
	}

	public static void intEnumForFastJson(Class<? extends IntEnum> c, SerializeConfig serializeConfig) {
		serializeConfig.put(c, com.simfun.sgf.common.fastjson.IntEnumSerializer.instance);
	}

	public static void intEnumForFastJson(Class<? extends IntEnum> c, ParserConfig parserConfig) {
		parserConfig.putDeserializer(c, new com.simfun.sgf.common.fastjson.IntEnumDeserializer(c));
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public static void intEnumForJackson(Class<? extends IntEnum> c, SimpleModule mod) {
		mod.addSerializer(c, new com.simfun.sgf.common.jackson.IntEnumSerializer(c));
		mod.addKeySerializer(c, new com.simfun.sgf.common.jackson.IntEnumKeySerializer(c));
		mod.addDeserializer(c, new com.simfun.sgf.common.jackson.IntEnumDeserializer(c));
	}

	public static JsonNode readTree(String content) {
		try {
			return objectMapper.readTree(content);
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	public static <T> T readValue(String content, Class<T> clazz) {
		try {
			return objectMapper.readValue(content, clazz);
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	public static String toJson(Object object) {
		try {
			return objectMapper.writeValueAsString(object);
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	public static <T> JSONObject parseMap(Map<String, T> map) {
		JSONObject object = new JSONObject();
		map.forEach((k, v) -> object.put(k,v));
		return object;
	}

	public static <T> List<T> readValueList(String json, TypeReference<List<T>> t) {

		try {
			return objectMapper.readValue(json, t);
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

}
