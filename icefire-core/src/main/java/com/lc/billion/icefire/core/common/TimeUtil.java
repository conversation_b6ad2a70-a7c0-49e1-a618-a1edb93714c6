package com.lc.billion.icefire.core.common;

import com.simfun.sgf.utils.TimeUtils;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.Calendar;
import java.util.Date;

/**
 *
 */
public class TimeUtil extends TimeUtils {

    /**
     * 获取当前时间的半天数
     *
     * @return
     */
    public static int getHalfDayNum() {
        Calendar calendar = Calendar.getInstance();
        int day = calendar.get(Calendar.DAY_OF_YEAR);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (hour < 12) {
            return day * 2;
        } else {
            return day * 2 + 1;
        }

    }

    /**
     * 获取当前时间是一年中的多少天
     *
     * @return
     */
    public static int getDayOfYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * 根据一个时间戳给出具体是周几 返回的是1-7 分别代表了周一到周日
     *
     * @param now
     * @return
     */
    public static int getDayOfWeek(long now) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(now);
        // 获取周几
        return translateWeekDay(calendar.get(Calendar.DAY_OF_WEEK));
    }

    /**
     * 获取周结束的时间点
     *
     * @param now
     * @return
     */
    public static long getEndOfWeek(long now) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(now);
        // 获取周几
        int weekDay = translateWeekDay(calendar.get(Calendar.DAY_OF_WEEK));
        now += (7 - weekDay) * DAY_MILLIS;
        return getEndOfDay(now);
    }

    /**
     * Calendar的weekday是从1~7分别代表了周日~周六，转换为我们常用的1~7表示周一到周日
     *
     * @param weekDayOfCalendar
     * @return
     */
    private static int translateWeekDay(int weekDayOfCalendar) {
        int weekDay = weekDayOfCalendar - 1;
        if (weekDay == 0) {
            weekDay = 7;
        }
        return weekDay;

    }


    /**
     * 将我们用的1-7表示周一~周日的方式，转回到默认的 1-7表示周日~周六
     *
     * @param outWeekDay
     * @return
     */
    private static int transBackWeekDay(int outWeekDay) {
        int weekDay = outWeekDay + 1;
        if (weekDay == 8) {
            weekDay = 1;
        }

        return weekDay;
    }

    /**
     * 判断是不是新的一周
     */
    public static boolean isNewWeek(long nowTime, long lastTime) {
        if (getEndOfWeek(nowTime) != getEndOfWeek(lastTime))
            return true;

        return false;

    }

    /**
     * 获取当日指定时间 点
     *
     * @param now
     * @param hour
     * @param minute
     * @param second
     * @return
     */
    public static long getSameDayTs(long now, int hour, int minute, int second) {
        Calendar instance = Calendar.getInstance();
        instance.setTimeInMillis(now);
        instance.set(Calendar.HOUR_OF_DAY, hour);
        instance.set(Calendar.MINUTE, minute);
        instance.set(Calendar.SECOND, second);
        instance.set(Calendar.MILLISECOND, 0);
        return instance.getTimeInMillis();
    }

    // public static void main(String[] args) {
    // // Calendar instance = Calendar.getInstance(TimeZone.getTimeZone("GMT+0"));
    // // instance.set(2018, 6, 15, 0, 0, 0);
    // // System.out.println(instance.getTimeInMillis());
    // //
    // // Date time = instance.getTime();
    // // System.out.println(time.toLocaleString());
    //
    // long currentTimeMillis = TimeUtil.getNow();
    // // for (int i = 0; i < 20; i++) {
    // // int week = getWeek(currentTimeMillis);
    // // System.out.println(week);
    // // currentTimeMillis+=24*60*60*1000;
    // // }
    // System.out.println(getNextWeekDay(currentTimeMillis, 4));
    // }

    public static int getHour(long now) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(now);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    public static int getMinute(long now) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(now);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 得到下一个周几的0:00的毫秒值 week为 1-7(周一~周日)
     *
     * @param referTime 时间戳
     * @param week      想要周几
     * @return
     */
    public static long getNextWeekDay(long referTime, int week) {
        return getCurWeekDay(referTime, week) + 7 * DAY_MILLIS;
    }

    /**
     * 得到上一个周几的0:00的毫秒值 week为 1-7(周一~周日)
     *
     * @param referTime
     * @param week
     * @return
     */
    public static long getPrevWeekDay(long referTime, int week) {
        return getCurWeekDay(referTime, week) - 7 * DAY_MILLIS;
    }

    /**
     * 得到当前周周几的0:00的毫秒值 week为 1-7(周一~周日)
     */
    public static long getCurWeekDay(long referTime, int week) {
        // 说明 jdk1.8后的LocalDate 1-7代表周一到周日  见：DayOfWeek枚举
        ZoneId defaultZoneId = ZoneId.systemDefault();
        LocalDate referDate = Instant.ofEpochMilli(referTime).atZone(defaultZoneId).toLocalDate();
        LocalDate targetDate = referDate.with(ChronoField.DAY_OF_WEEK, week);
        return targetDate.atStartOfDay(defaultZoneId).toInstant().toEpochMilli();
    }

    /**
     * 两个时间是否同一个月
     *
     * @param time1
     * @param time2
     * @return
     */
    public static boolean isSameMonth(long time1, long time2) {
        Calendar cal = Calendar.getInstance();

        cal.setTimeInMillis(time1);
        int year1 = cal.get(Calendar.YEAR);
        int month1 = cal.get(Calendar.MONTH);

        cal.setTimeInMillis(time2);
        int year2 = cal.get(Calendar.YEAR);
        int month2 = cal.get(Calendar.MONTH);

        return (year1 == year2) && (month1 == month2);
    }

    /**
     * 计算指定的两个时间的间隔天数
     *
     * @param time1
     * @param time2
     * @return
     */
    public static int betweenDays(long time1, long time2) {
        long beginOfDay1 = getBeginOfDay(time1);
        long beginOfDay2 = getBeginOfDay(time2);

        long d = Math.abs(beginOfDay1 - beginOfDay2);

        return (int) (d / DAY_MILLIS);
    }

    /**
     * 计算指定的两个时间的间隔自然周数。
     * 如：本周一到本周二之间间隔0周，本周四到下周二间隔1周。
     *
     * @return
     */
    public static int betweenWeeks(long time1, long time2) {
        if (time1 > time2) {
            long t = time2;
            time2 = time1;
            time1 = t;
        }

        int days = betweenDays(time1, time2);
        int weekDay1 = getDayOfWeek(time1);
        int weekDay2 = getDayOfWeek(time2);

        return days / 7 + (weekDay2 < weekDay1 ? 1 : 0);
    }

    public static long getNow() {
        return Clock.getInstance().now();
    }

    /**
     * 转换时间戳为UTC时间字符串
     *
     * @param time
     * @return
     */
    public static String parseTime2UtcStr(long time) {
        Instant ins = Instant.ofEpochMilli(time);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(ins, ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm yyyy/MM/dd");
        return formatter.format(localDateTime);
    }

    public static Date getThisWeekMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        int day = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    public static Date getNextWeekMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getThisWeekMonday(date));
        cal.add(Calendar.DATE, 7);
        return cal.getTime();
    }

    public static long parseStr2MillTime(String timeStr, String formatStr) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatStr);
            LocalDateTime localDateTime = LocalDateTime.parse(timeStr, formatter);

            // 假设时区为系统默认时区
            ZoneId zoneId = ZoneId.systemDefault();

            // 将LocalDateTime对象转换为时间戳（毫秒）
            long timestamp = localDateTime.atZone(zoneId).toInstant().toEpochMilli();
            return timestamp;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static long getCustomWeekday(long timestamp, int day, int hour, int minute, int second) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timestamp);
        cal.set(Calendar.DAY_OF_WEEK, day);
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.SECOND, second);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    public static long getNextTime(long now, int hour, int min, int second) {
        Instant instant = Instant.ofEpochMilli(now);
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime nowTime = LocalDateTime.ofInstant(instant, zoneId);
        LocalDateTime t = nowTime.withHour(hour).withMinute(min).withSecond(second).withNano(0);
        if (nowTime.isAfter(t)) {
            t = t.plusDays(1);
        }
        // 获取对应的时间戳
        ZonedDateTime zonedDateTime = t.atZone(ZoneId.systemDefault());
        return zonedDateTime.toInstant().toEpochMilli();
    }

    public static long getPastWeekday(long timestamp, int day, int hour, int minute, int second) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timestamp);
        cal.set(Calendar.DAY_OF_WEEK, day);
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.SECOND, second);
        cal.set(Calendar.MILLISECOND, 0);
        if (cal.getTimeInMillis() > timestamp) {
            cal.add(Calendar.DATE, -7);
        }
        return cal.getTimeInMillis();
    }

    public static String formatTime(long timestamp) {
        return formatTime(timestamp, "yyyy-MM-dd HH:mm:ss.SSS");
    }

    public static String formatTime(long timestamp, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        // 将时间戳转换为Date对象
        Date date = new Date(timestamp);
        // 使用SimpleDateFormat格式化Date对象为字符串
        return dateFormat.format(date);
    }

    /**
     * @return 返回 YearMonthDay e.g. 20240123
     * Calender 的month 为 0-11 以后不用了
     */
    @Deprecated
    public static int getYMD() {
        Calendar cal = Calendar.getInstance();
        return cal.get(Calendar.YEAR) * 10000
                + cal.get(Calendar.MONTH) * 100
                + cal.get(Calendar.DATE);
    }

    public static long getYMDTime(int ymd) {
        try {
            LocalDateTime localDateTime = LocalDateTime.of(ymd / 10000, ymd % 10000 / 100, ymd % 100, 0, 0, 0);

            // 假设时区为系统默认时区
            ZoneId zoneId = ZoneId.systemDefault();

            // 将LocalDateTime对象转换为时间戳（毫秒）
            return localDateTime.atZone(zoneId).toInstant().toEpochMilli();
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static long parseStr2MillTime(String timeStr, String formatStr, ZoneId zoneId) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatStr);
            LocalDateTime localDateTime = LocalDateTime.parse(timeStr, formatter);
            // 将LocalDateTime对象转换为时间戳（毫秒）
            long timestamp = localDateTime.atZone(zoneId).toInstant().toEpochMilli();
            return timestamp;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

}
