package com.lc.billion.icefire.core.monitoring;

import org.apache.http.impl.client.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.BiFunction;

/**
 * HttpClient 监控集成类
 * <p>
 * 提供与监控模块的解耦集成，避免直接依赖 Micrometer
 * </p>
 */
public class HttpClientMonitoring {

    private static final Logger log = LoggerFactory.getLogger(HttpClientMonitoring.class);

    // 监控装饰器函数，由 sgf-monitoring 模块注入
    private static BiFunction<HttpClientBuilder, String, HttpClientBuilder> monitoringDecorator;

    /**
     * 设置监控装饰器
     * <p>
     * 此方法由 sgf-monitoring 模块调用，注入监控能力
     * </p>
     *
     * @param decorator 监控装饰器函数
     */
    public static void setMonitoringDecorator(BiFunction<HttpClientBuilder, String, HttpClientBuilder> decorator) {
        monitoringDecorator = decorator;
        log.info("HttpClient 监控装饰器已设置");
    }

    /**
     * 为 HttpClientBuilder 添加监控
     *
     * @param builder    HttpClientBuilder
     * @param clientName 客户端名称
     * @return 添加了监控的 HttpClientBuilder
     */
    public static HttpClientBuilder addMonitoring(HttpClientBuilder builder, String clientName) {
        if (monitoringDecorator != null) {
            try {
                return monitoringDecorator.apply(builder, clientName);
            } catch (Exception e) {
                log.warn("应用监控装饰器失败，客户端: {}，使用原始 builder", clientName, e);
            }
        } else {
            log.debug("监控装饰器未设置，客户端: {}，使用原始 builder", clientName);
        }
        return builder;
    }

    /**
     * 检查监控是否可用
     *
     * @return true 如果监控可用
     */
    public static boolean isMonitoringAvailable() {
        return monitoringDecorator != null;
    }
}
