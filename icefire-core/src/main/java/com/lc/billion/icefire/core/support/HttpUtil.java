package com.lc.billion.icefire.core.support;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.StringJoiner;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;

import org.apache.http.HeaderElement;
import org.apache.http.HeaderElementIterator;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.monitoring.HttpClientMonitoring;

public class HttpUtil {

	public static final Logger LOG = LoggerFactory.getLogger(HttpUtil.class);

	private static int connectTimeout = 10000; // 5s
	private static int socketTimeout = 5000; // 5s
	private static int maxTotalConnections = 200; // 最大连接数，可调整

	private static CloseableHttpClient httpClient;

	public static String post(String url, Map<String, String> params) throws Exception {
		List<NameValuePair> nvps = new ArrayList<>();
		for (Map.Entry<String, String> entry : params.entrySet()) {
			nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}

		HttpEntity entity = new UrlEncodedFormEntity(nvps, "UTF-8");
		return post(url, entity);
	}

	public static String postJSON(String url, String json) throws Exception {
		HttpEntity entity = new StringEntity(json, "UTF-8");
		return post(url, entity);
	}

	public static String postJSON(String url, String json, Map<String, String> headers) throws Exception {
		HttpEntity entity = new StringEntity(json, "UTF-8");
		return post(url, entity, headers);
	}

	public static String post(final String url, final HttpEntity entity) throws Exception {
		return post(url, entity, null);
	}

	public static String post(final String url, final HttpEntity entity, Map<String, String> headers) throws Exception {
		if (httpClient == null) {
			init();
		}

		HttpPost httpPost = new HttpPost(url);
		httpPost.setEntity(entity);

		if (headers == null || headers.size() == 0) {
			// 设置User-Agent
			httpPost.addHeader("User-Agent", "monster center server");
			httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded");
		} else {
			headers.forEach((k, v) -> httpPost.addHeader(k, v));
		}

		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			@Override
			public String handleResponse(final HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status == HttpStatus.SC_OK) {
					HttpEntity entity = response.getEntity();

					return entity != null ? EntityUtils.toString(entity, "UTF-8") : null;
				} else {
					LOG.error("request url: {}, response status: {}", url, status);

					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};

		try {
			String body = httpClient.execute(httpPost, responseHandler);
			return body;
		} catch (IOException e) {
			LOG.error("CatchedException", e);
			throw e;
		} finally {
			httpPost.releaseConnection();
		}
	}

	public static String get(final String url) throws Exception {
		if (httpClient == null) {
			init();
		}

		HttpGet httpPost = new HttpGet(url);

		// 设置User-Agent
		httpPost.addHeader("User-Agent", "monster center server");

		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			@Override
			public String handleResponse(final HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status == HttpStatus.SC_OK) {
					HttpEntity entity = response.getEntity();

					return entity != null ? EntityUtils.toString(entity, "UTF-8") : null;
				} else {
					LOG.error("request url: {}, response status: {}", url, status);

					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};

		try {
			String body = httpClient.execute(httpPost, responseHandler);
			return body;
		} catch (IOException e) {
			LOG.error("CatchedException", e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}

	/**
	 * 格式化参数，将参数转化成a=b&c=d&e=f的形式
	 * 
	 * @param parameters
	 * @return
	 */
	public static String formatParameters(Map<String, String> parameters) {
		StringBuilder sb = new StringBuilder();
		Iterator<Entry<String, String>> iterator = parameters.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, String> entry = iterator.next();
			sb.append(String.format("%s=%s&", entry.getKey(), entry.getValue()));
		}
		if (parameters.size() > 0) {
			return sb.substring(0, sb.length() - 1);
		}
		return sb.toString();
	}

	/**
	 * 格式化参数，将参数转化成a=b&c=d&e=f的形式
	 * 
	 * @param parameters
	 * @return
	 */
	public static String formatInovationParameters(Map<String, String[]> parameters) {
		StringBuilder sb = new StringBuilder();
		Iterator<Entry<String, String[]>> iterator = parameters.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, String[]> entry = iterator.next();
			sb.append(String.format("%s=%s&", entry.getKey(), join(entry.getValue())));
		}
		if (parameters.size() > 0) {
			return sb.substring(0, sb.length() - 1);
		}
		return sb.toString();
	}

	public static String join(String[] value) {
		if (value == null) {
			return "";
		}
		StringBuilder _sb = new StringBuilder();
		for (String a : value) {
			_sb.append(a);
		}
		return _sb.toString();
	}

	// 初始化， 创建httpclient实例
	@SuppressWarnings("deprecation")
	private static void init() {
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(connectTimeout).setSocketTimeout(socketTimeout).setExpectContinueEnabled(true)
				.setAuthenticationEnabled(true).build();

		HttpRequestRetryHandler retryHandler = new HttpRequestRetryHandler() {
			@Override
			public boolean retryRequest(IOException e, int retryNum, HttpContext httpContext) {
				if (retryNum >= 3) {
					return false;
				}

				if (e instanceof org.apache.http.NoHttpResponseException || e instanceof org.apache.http.client.ClientProtocolException || e instanceof java.net.SocketException) {
					return true;
				}
				return false;
			}
		};

		ConnectionKeepAliveStrategy myStrategy = new ConnectionKeepAliveStrategy() {
			@Override
			public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
				try {

					HeaderElementIterator it = new BasicHeaderElementIterator(response.headerIterator(HTTP.CONN_KEEP_ALIVE));
					while (it.hasNext()) {
						HeaderElement he = it.nextElement();
						String param = he.getName();
						String value = he.getValue();
						if (value != null && param.equalsIgnoreCase("timeout")) {
							return Long.parseLong(value) * 1000;
						}
					}
					return 10 * 1000;

				} catch (Exception e) {
					LOG.error("CatchedException", e);
				}

				return 0;
			}
		};

		try {
			SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
				// 信任所有
				@Override
				public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
					return true;
				}
			}).build();

			HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
			SSLConnectionSocketFactory sslFactory = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);

			Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
					.register("http", PlainConnectionSocketFactory.getSocketFactory()).register("https", sslFactory).build();

			PoolingHttpClientConnectionManager connMgr = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
			connMgr.setMaxTotal(maxTotalConnections);
			connMgr.setDefaultMaxPerRoute((connMgr.getMaxTotal()));

			HttpClientBuilder builder = HttpClients.custom().setKeepAliveStrategy(myStrategy).setDefaultRequestConfig(requestConfig).setSslcontext(sslContext)
					.setConnectionManager(connMgr).setRetryHandler(retryHandler);

			httpClient = builder.build();

		} catch (Exception e) {
			LOG.error("CatchedException", e);
		}
	}

	public static HttpClient getHttpClient() {
		return httpClient;
	}

	// 销毁
	public static void destroy() {
		if (httpClient != null) {
			try {
				httpClient.close();
				httpClient = null;
			} catch (Exception e) {
				LOG.error("CatchedException", e);
			}
		}
	}

	public static String getLocalHost() {
		// 根据网卡取本机配置的IP
		InetAddress inet = null;
		try {
			inet = InetAddress.getLocalHost();
			return inet.getHostAddress();
		} catch (UnknownHostException e) {
			LOG.error("CatchedException", e);
		}
		return "127.0.0.1";
	}

	public static String post(String url, String params) throws Exception {
		List<NameValuePair> nameValuePairs = new ArrayList<>();
		nameValuePairs.add(new BasicNameValuePair("paramsList", params));
		HttpEntity entity = new UrlEncodedFormEntity(nameValuePairs, "UTF-8");
		return post(url, entity);
	}

	public static void main(String... strings) {
		List<Integer> serverIds = new ArrayList<Integer>();
		serverIds.add(85000);
		JSONObject ret = new JSONObject();
		StringJoiner joiner = new StringJoiner(",");
		serverIds.forEach(serverId -> joiner.add(String.valueOf(serverId)));
		Map<String, String> params = new HashMap<>();
		params.put("project_name", "LS-TESTFLIGHT3");
		params.put("sid", joiner.toString());
		params.put("db_index", "1");
		try {
			String post = HttpUtil.post("http://127.0.0.1:8080/api/gvg/create", params);
			ret = JSONObject.parseObject(post);
			System.out.println(ret);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void postDingDing(String url,String content){
		Map<String, String> headers = new HashMap<>();
		headers.put("accept", "application/json");
		headers.put("Content-Type", "application/json");
		JSONObject text = new JSONObject();
		text.put("content", content);
		JSONObject obj = new JSONObject();
		obj.put("msgtype", "text");
		obj.put("text", text);
		try {
			postJSON(url, obj.toJSONString(), headers);
		}  catch (Exception e) {
			LOG.error("dingding error",e);
		}
	}

}
