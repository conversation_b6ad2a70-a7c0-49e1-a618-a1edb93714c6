package com.lc.billion.icefire.core.support;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;

/**
 * 用于对Stream中的某类对象进行计数统计，用于随机时统计资源数量等
 * @param <K>
 */
public class MapCounter<K> {

    /** 总数量 */
    private int total;
    /** 各分类数量 */
    private Map<K,Integer> map;

    public MapCounter(){
        map = new HashMap<>();
        total = 0;
    }

    /**
     * 查询某个分类的数量
     * @param key 分类Key
     * @return
     */
    public int get(K key){
        return map.getOrDefault(key,0);
    }

    /**
     * 增加某个分类的数量
     * @param key 分类Key
     * @param num 数量
     */
    public void add(K key,int num){
        map.compute(key,(k, v) -> v == null ? num : v + num);
        total += num;
    }

    /**
     * 增加单个
     * @param key
     */
    public void addOne(K key){
        add(key,1);
    }

    /***
     * 封装好的工厂方法
     * 先执行filter 后使用 keyFunc 进行数量统计
     * @param stream 原始对象Stream
     * @param keyFunc 获得分类key的方法
     * @param filter 判断属于此大类的 filter
     * @return
     * @param <T>
     * @param <K>
     */
    public static <T,K> MapCounter<K> Count(Stream<T> stream, Function<T,K> keyFunc, Predicate<T> filter){
        var counter = new MapCounter<K>();
        stream.forEach(t->{
            if(filter.test(t)) {
                var key = keyFunc.apply(t);
                if (key != null) {
                    counter.addOne(key);
                }
            }
        });
        return counter;
    }

    /**
     * 获得总数量
     * @return
     */
    public int getTotal() {
        return total;
    }

    @Override
    public String toString() {
        return "MapCounter{" +
                "total=" + total +
                ", map=" + map +
                '}';
    }
}
