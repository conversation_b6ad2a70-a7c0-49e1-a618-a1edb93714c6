package com.lc.billion.icefire.core.support;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.BitSet;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.NoSuchElementException;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collector;
import java.util.zip.Deflater;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.Inflater;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.reflect.ConstructorUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.lc.billion.icefire.core.Constants;
import com.lc.billion.icefire.core.monitoring.HttpClientMonitoring;
import com.google.common.base.Strings;
import com.simfun.sgf.utils.JavaUtils;

public class Utils {
	private static final Logger logger = LoggerFactory.getLogger(Utils.class);
	private static Map<String, int[]> versionCache = new HashMap<>();
	public static boolean useCache = true;

	/**
	 * 字符串是否相等
	 */
	public static boolean isStringEquals(String a, String b) {
		if (a == null || b == null) {
			return false;
		}
		return a.equals(b);
	}

	/**
	 * 判断两个对象是否相等
	 * 
	 * @param objA
	 * @param objB
	 * @return
	 */
	public static boolean isEquals(Object objA, Object objB) {
		return new EqualsBuilder().append(objA, objB).isEquals();
	}

	/**
	 * 参数1是否为参数2的子类或接口实现
	 * 
	 * @param parentCls
	 * @return
	 */
	public static boolean isInstanceof(Class<?> cls, Class<?> parentCls) {
		return parentCls.isAssignableFrom(cls);
	}

	/**
	 * 格式化时间戳
	 * 
	 * @param value
	 * @param pattern
	 * @return
	 */
	public static String formatTime(long timestamp, String pattern) {
		SimpleDateFormat format = new SimpleDateFormat(pattern);
		return format.format(new Date(timestamp));
	}

	/**
	 * 根据时分秒配置 获取今天配置时间点
	 * 
	 * @param suffix
	 * @return
	 */
	public static long formatDateStr(String dateStr, String pattern) {
		try {
			SimpleDateFormat bartDateFormat = new SimpleDateFormat(pattern);
			return bartDateFormat.parse(dateStr).getTime();
		} catch (Exception ex) {
			throw JavaUtils.sneakyThrow(ex);
		}
	}

	/**
	 * 是否是同一天
	 * 
	 * @param src
	 * @param target
	 * @return
	 */
	public static boolean isSameDay(long src, long target) {
		return formatYMDTime(src).equals(formatYMDTime(target));
	}

	/**
	 * 返回 <b>年份-月份-日期</b> 格式的时间. 例如: 2012-12-24
	 * 
	 * @param time
	 * @return
	 */
	public static String formatYMDTime(long time) {
		DateFormat ymdFormat = new SimpleDateFormat("yyyy-MM-dd");
		return ymdFormat.format(time);
	}

	/**
	 * 两个日期相差的天数
	 * 
	 * @param d1
	 * @param d2
	 * @return
	 */
	public static int getDaysBetween(Calendar d1, Calendar d2) {
		if (d1.after(d2)) {
			Calendar swap = d1;
			d1 = d2;
			d2 = swap;
		}

		int days = d2.get(Calendar.DAY_OF_YEAR) - d1.get(Calendar.DAY_OF_YEAR);
		int y2 = d2.get(Calendar.YEAR);
		if (d1.get(Calendar.YEAR) != y2) {
			d1 = (Calendar) d1.clone();
			do {
				days += d1.getActualMaximum(Calendar.DAY_OF_YEAR);
				d1.add(Calendar.YEAR, 1);
			} while (d1.get(Calendar.YEAR) != y2);
		}
		return days;
	}

	/**
	 * 判断两个时间戳之间相差的天数(tb - ta)
	 * 
	 * @param ta
	 * @param tb
	 * @return
	 */
	public static long getDaysBetween(long ta, long tb) {
		if (ta > 0L && tb > 0L) {
			return (tb / Time.DAY) - (ta / Time.DAY);
		} else {
			return 0L;
		}
	}

	/**
	 * 获取给定时间当前凌晨的时间对象
	 * 
	 * @param time
	 *            取当天凌晨的话传入 TimeUtil.getNow() 即可
	 * @return
	 */
	public static long getTimeBeginOfToday(long time) {
		Calendar ca = Calendar.getInstance();
		ca.setTimeInMillis(time);
		ca.set(Calendar.HOUR_OF_DAY, 0);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);

		return ca.getTimeInMillis();
	}

	/**
	 * 获取给定时间下一天的凌晨时间
	 * 
	 * @param time
	 * @return
	 */
	public static long getNextDayZeroClockTime(long time) {
		Calendar ca = Calendar.getInstance();
		ca.setTimeInMillis(time);
		ca.set(Calendar.HOUR_OF_DAY, 24);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);

		return ca.getTimeInMillis();
	}

	/**
	 * 获取给定的时间与给定时间当天凌晨相隔的时间(毫秒数)
	 * 
	 * @param time
	 * @return
	 */
	public static long getTimeFromBeginningOfDay(long time) {
		Calendar ca = Calendar.getInstance();
		ca.setTimeInMillis(time);
		ca.set(Calendar.HOUR_OF_DAY, 0);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);
		return time - ca.getTimeInMillis();
	}

	/**
	 * 获取给定时间当前凌晨的时间对象，如果给定的时间不存在则取今天凌晨
	 * 
	 * @param time
	 *            取当天凌晨的话传入-1即可
	 * @return
	 */
	public static Calendar getBeginningOfToday(long time) {
		Calendar ca = Calendar.getInstance();
		if (time > 0L) {
			ca.setTimeInMillis(time);
		}
		ca.set(Calendar.HOUR_OF_DAY, 0);
		ca.set(Calendar.MINUTE, 4);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);
		return ca;
	}

	/**
	 * 获取给定时间本周一的时间对象
	 * 
	 * @param time
	 *            取当天凌晨的话传入 TimeUtil.getNow() 即可
	 * @return
	 */
	public static long getTimeBeginOfWeek(long time) {
		Calendar ca = Calendar.getInstance();
		ca.setTimeInMillis(time);

		// 当今天是星期天的时候，需要特殊处理，因为星期天是按照这个星期第一天算的，而我们不是这么需要的
		long timeCheck = 0;
		if (ca.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
			timeCheck = Time.DAY * 7;
		}

		ca.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		ca.set(Calendar.HOUR_OF_DAY, 0);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);

		return ca.getTimeInMillis() - timeCheck;
	}

	/**
	 * referTime 之后第一个周日
	 * 
	 * @param referTime
	 * @return
	 */
	public static long getNextSundayTime(long referTime) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(referTime);

		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);

		// next 为推迟的周数，1本周，-1向前推迟一周，2下周，依次类推
		int next = 1;
		calendar.add(Calendar.DATE, next * 7);
		calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);

		return calendar.getTimeInMillis();
	}

	/**
	 * 得到周几
	 * 
	 * @param time
	 * @return
	 */
	public static int getDayOfWeek(long time) {
		Calendar _calendar = Calendar.getInstance();
		_calendar.setTimeInMillis(time);
		int week = _calendar.get(Calendar.DAY_OF_WEEK);
		// 这里需要从1-7开始，但是DAY_OF_WEEK返回从sunday开始到satuday，这里转换一下
		week--;
		if (week == 0) {
			// sunday
			week = 7;
		}
		return week;
	}

	/**
	 * 根据当前时间，获取小时数 24小时格式
	 * 
	 * @param time
	 *            当天的时间
	 * @return
	 */
	public static int getHourOfTime(long time) {
		Calendar ca = Calendar.getInstance();
		ca.setTimeInMillis(time);
		return ca.get(Calendar.HOUR_OF_DAY);
	}

	/**
	 * 构造List对象
	 * 
	 * 如果传入的是参数仅仅为一个对象数组(Object[])或原生数组(int[], long[]等)
	 * 那么表现结果表现是不同的，Object[]为[obj[0], obj[1], obj[2]] 而原生数组则为[[int[0],
	 * int[1]，int[2]]] 多了一层嵌套，需要对原生数组进行特殊处理。
	 * 
	 * @param <T>
	 * @param ts
	 * @return
	 */
	@SafeVarargs
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> List<T> ofList(T... ts) {
		List result = new ArrayList();

		// 对Null进行特殊处理
		if (ts == null) {
			result.add(null);
			return result;
		}

		// 对单独的原始数组类型进行特殊处理
		if (ts.length == 1 && ts[0] != null && OFLIST_ARRAY_CLASS.contains(ts[0].getClass())) {
			if (ts[0] instanceof int[]) {
				int[] val = (int[]) ts[0];
				for (int v : val) {
					result.add(v);
				}
			} else if (ts[0] instanceof long[]) {
				long[] val = (long[]) ts[0];
				for (long v : val) {
					result.add(v);
				}
			} else if (ts[0] instanceof boolean[]) {
				boolean[] val = (boolean[]) ts[0];
				for (boolean v : val) {
					result.add(v);
				}
			} else if (ts[0] instanceof byte[]) {
				byte[] val = (byte[]) ts[0];
				for (byte v : val) {
					result.add(v);
				}
			} else if (ts[0] instanceof double[]) {
				double[] val = (double[]) ts[0];
				for (double v : val) {
					result.add(v);
				}
			}
		} else { // 对象数组
			for (T t : ts) {
				result.add(t);
			}
		}

		return result;
	}

	// 专供ofList类使用 对于数组类型进行特殊处理
	private static final List<?> OFLIST_ARRAY_CLASS = Utils.ofList(int[].class, long[].class, boolean[].class, byte[].class, double[].class);

	/**
	 * 构造Map对象
	 * 
	 * @param <T>
	 * @param ts
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <K, V> Map<K, V> ofMap(Object... params) {
		LinkedHashMap<K, V> result = new LinkedHashMap<K, V>();

		// 无参 返回空即可
		if (params == null || params.length == 0) {
			return result;
		}

		// 处理成对参数
		int len = params.length;
		for (int i = 0; i < len; i += 2) {
			K key = (K) params[i];
			V val = (V) params[i + 1];

			result.put(key, val);
		}

		return result;
	}

	/**
	 * 基于参数创建字符串 #0开始
	 * 
	 * @param str
	 * @param params
	 * @return
	 */
	public static String createStr(String str, Object... params) {
		return String.format(str, params);
	}

	public static StringBuilder concat(StringBuilder builder, String split, Object... objs) {
		boolean first = true;
		for (Object obj : objs) {
			if (!first && split != null)
				builder.append(split);

			first = false;
			builder.append(obj);
		}
		return builder;
	}

	public static StringBuilder wrap(String str) {
		return concat(" ", str);
	}

	public static StringBuilder concat(String split, Object... objs) {
		return concat(new StringBuilder(), split, objs);
	}

	public static StringBuilder concat(StringBuilder builder, String split, Iterable<Object> coll) {
		boolean first = true;
		for (Object obj : coll) {
			if (!first && split != null)
				builder.append(split);

			first = false;
			builder.append(obj);
		}
		return builder;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static StringBuilder concat(String split, Iterable coll) {
		return concat(new StringBuilder(), split, coll);
	}

	public static void intToBytes(byte[] b, int offset, int v) {
		for (int i = 0; i < 4; ++i) {
			b[offset + i] = (byte) (v >>> (24 - i * 8));
		}
	}

	public static int bytesToInt(byte[] b, int offset) {
		int num = 0;
		for (int i = offset; i < offset + 4; ++i) {
			num <<= 8;
			num |= (b[i] & 0xff);
		}
		return num;
	}

	public static int bytesToInt(Object[] b, int offset) {
		int num = 0;
		for (int i = offset; i < offset + 4; ++i) {
			num <<= 8;
			num |= (((byte) b[i]) & 0xff);
		}
		return num;
	}

	public static int bytesToLittleEndian32(byte[] b, int offset) {
		return (((int) b[offset] & 0xff)) | (((int) b[offset + 1] & 0xff) << 8) | (((int) b[offset + 2] & 0xff) << 16) | (((int) b[offset + 3] & 0xff) << 24);
	}

	public static void LittleEndian32ToBytes(byte[] b, int offset, int value) {
		b[offset + 0] = (byte) ((value) & 0xFF);
		b[offset + 1] = (byte) ((value >> 8) & 0xFF);
		b[offset + 2] = (byte) ((value >> 16) & 0xFF);
		b[offset + 3] = (byte) ((value >> 24) & 0xFF);
	}

	public static String toHexString(byte[] byteBuffer, int length) {
		StringBuffer outputBuf = new StringBuffer(length * 4);

		for (int i = 0; i < length; i++) {
			String hexVal = Integer.toHexString(byteBuffer[i] & 0xff);

			if (hexVal.length() == 1) {
				hexVal = "0" + hexVal; //$NON-NLS-1$
			}

			outputBuf.append(hexVal); // $NON-NLS-1$
		}
		return outputBuf.toString();
	}

	/**
	 * 将整数值对应为布尔值 0:false,1:true
	 * 
	 * @param number
	 * @return
	 * @exception IllegalArgumentException
	 *                如果number!=0 或者number!=1
	 */
	public static boolean bool(int number) {
		if (number != Constants.FALSE_NUM && number != Constants.TRUE_NUM) {
			throw new IllegalArgumentException("must be 0 or 1.");
		}
		return number == Constants.TRUE_NUM;
	}

	public static String getClassPath() {
		return Class.class.getResource("/").getPath();
	}

	/**
	 * String转为int型 如果出错 则为0
	 * 
	 * @param value
	 * @return
	 */
	public static int intValue(String value) {
		if (StringUtils.isNotEmpty(value) && NumberUtils.isNumber(value))
			return Double.valueOf(value).intValue();
		else
			return 0;
	}

	public static int intValue(Integer value) {
		if (null == value)
			return 0;
		else
			return value;
	}

	public static int[] intArrayValue(String value) {
		String[] elems = value.split(",");
		if (elems.length > 0) {
			int[] temp = new int[elems.length];
			for (int i = 0; i < elems.length; i++) {
				temp[i] = Utils.intValue(elems[i]);
			}
			return temp;
		}
		return null;
	}

	public static String[] stringArrayValue(String value) {
		String[] elems = value.split(",");
		if (elems.length > 0) {
			String[] temp = new String[elems.length];
			for (int i = 0; i < elems.length; i++) {
				temp[i] = elems[i];
			}
			return temp;
		}
		return null;
	}

	/**
	 * String转为long型 如果出错 则为0
	 * 
	 * @param value
	 * @return
	 */
	public static long longValue(String value) {
		if (StringUtils.isNotEmpty(value) && NumberUtils.isNumber(value))
			return Long.valueOf(value);
		else
			return 0;
	}

	public static long longValue(Long value) {
		if (null == value)
			return 0;
		else
			return value;
	}

	public static long[] longArrayValue(String value) {
		String[] elems = value.split(",");
		if (elems.length > 0) {
			long[] temp = new long[elems.length];
			for (int i = 0; i < elems.length; i++) {
				temp[i] = longValue(elems[i]);
			}
			return temp;
		}
		return null;
	}

	/**
	 * String转为double型 如果出错 则为0.0
	 * 
	 * @param value
	 * @return
	 */
	public static double doubleValue(String value) {
		if (StringUtils.isNotEmpty(value) && NumberUtils.isNumber(value))
			return Double.valueOf(value);
		else
			return 0.0D;
	}

	public static double doubleValue(Double value) {
		if (null == value)
			return 0.0D;
		else
			return value;
	}

	public static double[] doubleArrayValue(String value) {
		String[] elems = value.split(",");
		if (elems.length > 0) {
			double[] temp = new double[elems.length];
			for (int i = 0; i < elems.length; i++) {
				temp[i] = doubleValue(elems[i]);
			}
			return temp;
		}
		return null;
	}

	/**
	 * String转为float型 如果出错 则为0.0
	 * 
	 * @param value
	 * @return
	 */
	public static float floatValue(String value) {
		if (StringUtils.isNotEmpty(value) && NumberUtils.isNumber(value))
			return Float.valueOf(value);
		else
			return 0.0f;
	}

	public static float floatValue(Float value) {
		if (null == value)
			return 0.0f;
		else
			return value;
	}

	public static float[] floatArrayValue(String value) {
		String[] elems = value.split(",");
		if (elems.length > 0) {
			float[] temp = new float[elems.length];
			for (int i = 0; i < elems.length; i++) {
				temp[i] = floatValue(elems[i]);
			}
			return temp;
		}
		return null;
	}

	/**
	 * String转为boolean型 如果出错 则为false
	 * 
	 * @param value
	 * @return
	 */
	public static boolean booleanValue(String value) {
		if ("true".equalsIgnoreCase(value) && value != null)
			return true;
		else
			return false;
	}

	public static boolean[] booleanArrayValue(String value) {
		String[] elems = value.split(",");
		if (elems.length > 0) {
			boolean[] temp = new boolean[elems.length];
			for (int i = 0; i < elems.length; i++) {
				temp[i] = booleanValue(elems[i]);
			}
			return temp;
		}
		return null;
	}

	public static JSONArray toJSONArray(long[] input) {
		JSONArray array = new JSONArray();
		for (long value : input) {
			array.add(value);
		}
		return array;
	}

	/**
	 * 从字符串转为JSONArray，主要目的是包装一下空值处理
	 * 
	 * @param str
	 * @return 正常返回对象，否则返回长度为0的JSONArray
	 */
	public static JSONArray toJSONArray(String str) {
		if (StringUtils.isEmpty(str)) {
			str = "[]";
		}

		return JSON.parseArray(str);
	}

	/**
	 * 从字符串转为JSONObject，主要目的是包装一下空值处理
	 * 
	 * @param str
	 * @return 正常返回对象，否则返回空的JSONObject
	 */
	public static JSONObject toJSONObject(String str) {
		if (StringUtils.isEmpty(str)) {
			str = "{}";
		}

		return JSON.parseObject(str);
	}

	/**
	 * 将对象转化为JSON字符串
	 * 
	 * @param obj
	 * @return
	 */
	public static String toJSONString(Object obj) {
		return JSON.toJSONString(obj, SerializerFeature.DisableCircularReferenceDetect);
	}

	/**
	 * 把两个数组组成一个匹配的Json 前面是属性，后面是数值
	 * 
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static String toJSONString(String[] str1, String[] str2) {
		Map<String, String> tempMap = new HashMap<String, String>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return toJSONString(tempMap);
	}

	/**
	 * 把两个数组组成一个匹配的Json 前面是属性，后面是数值
	 * 
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static String toJOSNString(String[] str1, int[] str2) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return toJSONString(tempMap);
	}

	public static Map<String, Integer> arrToMap(String[] str1, int[] str2) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return tempMap;
	}

	public static Map<String, Float> toMapWeight(String[] str1, float[] str2, float weight) {
		Map<String, Float> tempMap = new HashMap<String, Float>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], (str2[i] + weight));
			}
		}
		return tempMap;
	}

	public static String toJOSNStringWeight(String[] str1, int[] str2, double weight) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], (int) (str2[i] + weight));
			}
		}
		return toJSONString(tempMap);
	}

	public static String toJOSNStringNag(String[] str1, int[] str2) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], -str2[i]);
			}
		}
		return toJSONString(tempMap);
	}

	/**
	 * 把两个数组组成一个匹配的Json 前面是属性，后面是数值
	 * 
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static String toJSONString(String[] str1, float[] str2) {
		Map<String, Float> tempMap = new HashMap<String, Float>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return toJSONString(tempMap);
	}

	/**
	 * 获取文件的后缀名
	 * 
	 * @param fileName
	 *            文件名称
	 * @return
	 */
	public static String getSuffix(String fileName) {
		return org.apache.commons.lang3.StringUtils.substringAfterLast(fileName, ".");
	}

	/**
	 * 读取配置文件
	 * 
	 * @return
	 */
	public static Properties readProperties(String name) {
		String filePath = Utils.class.getClassLoader().getResource(name).getPath();
		try (FileInputStream in = new FileInputStream(filePath)) {
			Properties p = new Properties();
			p.load(in);

			if (Boolean.getBoolean("useSystemProps")) {
				for (Entry<Object, Object> entry : System.getProperties().entrySet()) {
					Object key = entry.getKey();
					Object value = entry.getValue();
					Object oriValue = p.get(key);
					if (oriValue != null) {
						String noticeMsg = String.format("replace properties with system's. key{%s:[%s->%s]}", key, oriValue, value);
						System.err.println(noticeMsg);
					}
					// put any way.
					p.put(key, value);
				}
			}

			return p;
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 获取对象的属性 会先尝试利用getter方法获取 然后再直接访问字段属性 如果给定的属性不存在 会返回null
	 * 
	 * @param obj
	 * @param fieldName
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T fieldRead(Object obj, String fieldName) {
		try {
			// 返回值
			Object result = null;

			Class<? extends Object> clazz = obj.getClass();

			// 先通过自省来获取字段的值(getter方法)
			boolean hasGetter = false;
			BeanInfo bi = Introspector.getBeanInfo(clazz);
			PropertyDescriptor[] pds = bi.getPropertyDescriptors();
			for (PropertyDescriptor p : pds) {
				if (!p.getName().equals(fieldName))
					continue;

				result = p.getReadMethod().invoke(obj);
				hasGetter = true;
			}

			// 如果通过getter方法没找到 那么就尝试直接读取字段
			if (!hasGetter) {
				for (Field f : clazz.getFields()) {
					if (!f.getName().equals(fieldName))
						continue;

					result = f.get(obj);
				}
			}

			return (T) result;
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 获取对象的静态属性
	 * 
	 * @param clazz
	 * @param fieldName
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T fieldRead(Class<?> clazz, String fieldName) {
		try {
			Field field = FieldUtils.getDeclaredField(clazz, fieldName);
			return (T) field.get(clazz);
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 设置对象的属性 会先尝试利用setter方法修改 然后再直接修改字段属性 如果给定的属性不存在 会抛出异常
	 * 
	 * @param obj
	 * @param fieldName
	 * @return
	 */
	public static void fieldWrite(Object obj, String fieldName, Object valueNew) {
		try {
			Class<? extends Object> clazz = obj.getClass();

			// 先通过自省来设置字段的值(setter方法)
			boolean hasSetter = false;
			BeanInfo bi = Introspector.getBeanInfo(clazz);
			PropertyDescriptor[] pds = bi.getPropertyDescriptors();
			for (PropertyDescriptor p : pds) {
				if (!p.getName().equals(fieldName))
					continue;

				// 到这里的话 证明属性能找到（至少有对应的getter）但是没有找到setter
				// 可能是setter方法不符合规范 比如非void有返回值等
				// 这种情况使用反射再次尝试
				Method wm = p.getWriteMethod();
				if (wm == null) {
					String wmStr = "set" + StringUtils.capitalize(fieldName);
					for (Method m : clazz.getMethods()) {
						if (!m.getName().equals(wmStr))
							continue;
						m.invoke(obj, valueNew);
					}
				} else {
					wm.invoke(obj, valueNew);
				}

				hasSetter = true;
			}

			// 如果通过setter方法没找到 那么就尝试直接操作字段
			if (!hasSetter) {
				Field f = clazz.getField(fieldName);
				f.set(obj, valueNew);
			}
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 通过反射执行函数
	 * 
	 * @param obj
	 * @param method
	 * @param param
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeMethod(Object obj, String method, Object... params) {
		try {
			return (T) MethodUtils.invokeMethod(obj, method, params);
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 通过反射执行函数
	 * 
	 * @param obj
	 * @param method
	 * @param param
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeStaticMethod(Class<?> cls, String method, Object... params) {
		try {
			return (T) MethodUtils.invokeStaticMethod(cls, method, params);
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 通过反射执行构造函数
	 * 
	 * @param cls
	 * @param params
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeConstructor(Class<?> cls, Object... params) {
		try {
			return (T) ConstructorUtils.invokeConstructor(cls, params);
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 获得类泛型第一个参数的Class
	 * 
	 * @param clazz
	 * @return
	 */
	public static <T> Class<T> getSuperClassGenricType(Class<?> clazz) {
		return getSuperClassGenricType(clazz, 0);
	}

	/**
	 * 获得类泛型参数的Class
	 * 
	 * @param clazz
	 * @param index
	 *            第几个参数, 从0开始
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> Class<T> getSuperClassGenricType(Class<?> clazz, int index) {
		Type genType = clazz.getGenericSuperclass();
		// 如果不是ParameterizedType接口的子类 就不可能含有泛型参数
		if (!(genType instanceof ParameterizedType))
			return null;
		// 泛型信息
		Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
		// 索引错误
		if (index >= params.length || index < 0)
			return null;
		// 不是class类型
		if (!(params[index] instanceof Class))
			return null;
		Class<T> result = (Class<T>) params[index];
		return result;
	}

	/**
	 * 发送GET请求,得到返回值
	 * 
	 * @param url
	 * @return
	 * @throws IOException
	 */
	public static synchronized String httpGet(String url) throws IOException {
		HttpGet httpGet = new HttpGet(url);
		// 创建带监控的HttpClient
		CloseableHttpClient httpClient = HttpClientMonitoring.addMonitoring(HttpClients.custom(), "Utils-httpGet").build();
		try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
			StringBuilder sb = new StringBuilder();
			if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				try {
					InputStream is = response.getEntity().getContent();
					BufferedReader reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));

					String line = null;
					while ((line = reader.readLine()) != null)
						sb.append(line + "\n");
				} finally {
					response.close();
				}
			}
			return sb.toString();
		} finally {
			// 关闭HttpClient
			if (httpClient != null) {
				httpClient.close();
			}
		}
	}

	/**
	 * 进行Get请求操作
	 * 
	 * @return
	 */
	public static String httpGet(String url, Map<String, String> params, boolean encode) {
		try {
			// 拼接地址
			StringBuilder urlSB = new StringBuilder(url);
			// 最终地址
			String urlStrFinal = urlSB.append(getUrlParam(params, encode)).toString();
			// 请求地址
			HttpGet get = new HttpGet(urlStrFinal);
			// 准备环境 - 创建带监控的HttpClient
			try (CloseableHttpClient http = HttpClientMonitoring.addMonitoring(HttpClients.custom(), "Utils-httpGet-params").build();
				 CloseableHttpResponse response = http.execute(get);) {
				// 返回内容
				HttpEntity entity = response.getEntity();
				// 主体数据
				InputStream in = entity.getContent();
				BufferedReader reader = new BufferedReader(new InputStreamReader(in));
				// 读取
				StringBuilder sb = new StringBuilder();
				String line = null;
				while ((line = reader.readLine()) != null) {
					sb.append(line);
				}
				return sb.toString();
			}
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 通过HTTPS请求获取json格式的返回
	 * 
	 * @param urlStr
	 * @param data
	 * @return
	 */
	public static InputStream httpsGet(String url, Map<String, String> params, boolean encode) {
		try {
			// 拼接地址
			StringBuilder urlSB = new StringBuilder(url);

			// 最终地址
			String urlStrFinal = urlSB.append(getUrlParam(params, encode)).toString();

			SSLConnectionSocketFactory socketFactory = enableSSL();
			RequestConfig defaultRequestConfig = RequestConfig.custom().setConnectTimeout(5000).setSocketTimeout(5000).build();
			Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create().register("http", PlainConnectionSocketFactory.INSTANCE)
					.register("https", socketFactory).build();

			PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
			connManager.setDefaultConnectionConfig(ConnectionConfig.custom().setCharset(StandardCharsets.UTF_8).build());

			// 创建带监控的HttpClient
			CloseableHttpClient httpclient = HttpClientMonitoring.addMonitoring(
					HttpClients.custom().setConnectionManager(connManager).setDefaultRequestConfig(defaultRequestConfig),
					"Utils-httpsGet").build();

			HttpGet httpGet = new HttpGet(urlStrFinal);
			HttpResponse response = httpclient.execute(httpGet);
			HttpEntity httpEntity = response.getEntity();

			return httpEntity.getContent();
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 进行Post请求操作
	 * 
	 * @return
	 */
	public static String httpPost(String url, Map<String, String> params) {
		try {
			// 参数
			List<NameValuePair> nvps = new ArrayList<NameValuePair>();
			for (Entry<String, String> entry : params.entrySet()) {
				Object key = entry.getKey();
				Object val = entry.getValue();
				String valStr = (val == null) ? "" : val.toString();

				nvps.add(new BasicNameValuePair(key.toString(), valStr));
			}
			// 请求地址
			HttpPost post = new HttpPost(url);
			// 设置参数
			post.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));
			// 准备环境
			try (CloseableHttpClient http = HttpClients.createDefault(); CloseableHttpResponse response = http.execute(post);) {
				// 返回内容
				HttpEntity entity = response.getEntity();
				StringBuffer stringBuffer = new StringBuffer();
				// 主体数据
				try (BufferedReader reader = new BufferedReader(new InputStreamReader(entity.getContent(), StandardCharsets.UTF_8))) {
					String str = "";
					while ((str = reader.readLine()) != null) {
						stringBuffer.append(str);
					}
				}
				return stringBuffer.toString();
			}
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		}
	}

	/**
	 * 将MAP参数格式变为URL参数格式 并根据KEY的字典顺序排列
	 * 
	 * @param map
	 * @param encode
	 * @return
	 */
	public static String getUrlParam(Map<String, String> params, boolean encode) {
		// 将Key按字段顺序排序
		List<String> keys = new ArrayList<String>(params.keySet());

		// 返回URL参数
		StringBuilder result = new StringBuilder();

		// 1.1 拼接参数
		try {
			for (String k : keys) {
				// 增加间隔符
				if (result.length() != 0) {
					result.append("&");
				}
				// 设置键值对
				String value = params.get(k);
				if (encode) {
					value = encodeUrl(value);
				}
				result.append(k).append("=").append(value);
			}
		} catch (Exception ex) {
			throw JavaUtils.sneakyThrow(ex);
		}

		// 1.2有需要拼接的参数
		if (!params.isEmpty()) {
			result.insert(0, "?");
		}

		return result.toString();
	}

	/**
	 * URL编码 (符合FRC1738规范)
	 *
	 * @param input
	 *            待编码的字符串
	 * @return 编码后的字符串
	 * @throws Exception
	 *             不支持指定编码时抛出异常。
	 */
	public static String encodeUrl(String input) throws Exception {
		return URLEncoder.encode(input, StandardCharsets.UTF_8.name()).replace("+", "%20").replace("*", "%2A");
	}

	public static List<Integer> strToIntList(String str) {
		if (str == null || str.isEmpty())
			return new ArrayList<Integer>();

		List<Integer> l = new ArrayList<Integer>();
		String[] o = str.split(",");
		for (String s : o) {
			if (s.isEmpty())
				continue;
			l.add(Integer.parseInt(s));
		}

		return l;
	}

	/**
	 * 将String，以“，”分割的字符串，转化为int[]
	 * 
	 * @param str
	 * @return
	 */
	public static int[] arrayStrToInt(String str) {
		if (StringUtils.isEmpty(str)) {
			return new int[0];
		}

		int[] skillLogicArr = null;
		String skillLogicArrTemp[] = str.split(","); // 逻辑库的数组

		skillLogicArr = new int[skillLogicArrTemp.length];
		for (int i = 0; i < skillLogicArrTemp.length; i++) {
			skillLogicArr[i] = Utils.intValue(skillLogicArrTemp[i]);
		}

		return skillLogicArr;
	}

	/**
	 * 将String[]，转化为String，以“，”分割
	 * 
	 * @param str
	 * @return
	 */
	public static String arrayStrToStr(String[] arr) {
		if (arr.length == 0) {
			return "";
		}

		String result = "";
		for (int i = 0; i < arr.length; i++) {
			result += (arr[i] + ",");
		}

		return result.substring(0, result.length() - 1);
	}

	/**
	 * 将int[]，转化为String，以“，”分割
	 * 
	 * @param str
	 * @return
	 */
	public static String arrayIntToStr(int[] arr) {
		if (arr.length == 0) {
			return "";
		}

		String result = "";
		for (int i = 0; i < arr.length; i++) {
			result += (arr[i] + ",");
		}

		return result.substring(0, result.length() - 1);
	}

	public static double saveDoubleBitNum(double x, int saveBitNum) {
		NumberFormat ddf1 = NumberFormat.getNumberInstance();
		ddf1.setMaximumFractionDigits(saveBitNum);
		// 去除千分位逗号
		ddf1.setGroupingUsed(false);
		String s = ddf1.format(x);
		return Utils.doubleValue(s);
	}

	public static float saveDoubleBitNum(float x, int saveBitNum) {
		NumberFormat ddf1 = NumberFormat.getNumberInstance();
		ddf1.setMaximumFractionDigits(saveBitNum);
		// 去除千分位逗号
		ddf1.setGroupingUsed(false);
		String s = ddf1.format(x);
		return Utils.floatValue(s);
	}

	/**
	 * 创建函数特征码 类全路径:函数名(参数类型)
	 * 
	 * @return
	 */
	public static String createMethodKey(Method method) {
		return createMethodKey(method.getDeclaringClass(), method);
	}

	/**
	 * 创建函数特征码 类全路径:函数名(参数类型)
	 * 
	 * @return
	 */
	public static String createMethodKey(Class<?> cls, Method method) {
		// 类全路径
		String clazzName = cls.getName();
		// 函数名
		String methodName = method.getName();
		// 参数类型字符串
		StringBuilder methodParam = new StringBuilder();
		methodParam.append("(");
		for (Class<?> clazz : method.getParameterTypes()) {
			if (methodParam.length() > 1)
				methodParam.append(", ");
			methodParam.append(clazz.getSimpleName());
		}
		methodParam.append(")");

		return clazzName + ":" + methodName + methodParam.toString();
	}

	/**
	 * 去除字符串中所有的空格
	 * 
	 * @param str
	 * @return
	 */
	public static String removeSpaces(String str) {
		return removeRegex(str, "[\\ ]");
	}

	/**
	 * 去除字符串中所有的非数字
	 * 
	 * @param str
	 * @return
	 */
	public static String removeNonNumeric(String str) {
		return removeRegex(str, "[\\D]");
	}

	/**
	 * 去除字符串中所有的数字
	 * 
	 * @param str
	 * @return
	 */
	public static String removeNumeric(String str) {
		return removeRegex(str, "[\\d]");
	}

	/**
	 * 去除字符串中所有正则表达式匹配的字符串
	 * 
	 * @param str
	 * @param regex
	 */
	private static String removeRegex(String str, String regex) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(str);
		return matcher.replaceAll("");
	}

	/**
	 * 替换sql中的关键字
	 *
	 * @param str
	 * @return
	 */
	public static String replaceSql(String str) {
		if (str != null) {
			return str.replaceAll("'", "’").replaceAll("<", "&lt;").replaceAll(">", "&gt;").replaceAll("\"", "&quot;");
		}
		return "";
	}

	/**
	 * 将地址中的ip替换成特定字符
	 * 
	 * @param address
	 * @param replace
	 * @return
	 */
	public static String replaceAdderssIp(String address, String replace) {
		Pattern pattern = Pattern.compile(".+://(.+):(.+)");
		Matcher matcher = pattern.matcher(address);
		boolean b = matcher.matches();
		if (b) {
			String ip = matcher.group(1);
			address = address.replace(ip, replace);
		}
		return address;
	}

	/**
	 * trim字符串
	 * 
	 * @param str
	 * @return
	 */
	public static String trim(String str) {
		if (str == null) {
			str = "";
		} else {
			str = str.trim();
		}
		if (str.length() == 0) {
			return str;
		}
		if (str.charAt(0) == '"') {
			str = str.substring(1);
		}
		if (str.charAt(str.length() - 1) == '"') {
			str = str.substring(0, str.length() - 1);
		}
		return str;
	}

	public static String[] getStringList(String str, String sep) {
		str = trim(str);
		return str.split(sep);
	}

	public static String[] getStringList(String str) {
		str = trim(str);
		if (str.endsWith(",")) {
			str = str.substring(0, str.length() - 1);
		}
		String sep = ",";
		if (str.indexOf(':') >= 0) {
			sep = ":";
		}
		return str.split(sep);
	}

	public static int[] getIntList(String str) {
		String[] prop = getStringList(str);
		List<Integer> tmp = new ArrayList<Integer>();
		for (int i = 0; i < prop.length; i++) {
			try {
				String sInt = prop[i].trim();
				if (sInt.length() < 20) {
					int r = Integer.parseInt(prop[i].trim());
					tmp.add(r);
				}
			} catch (Exception e) {
				throw JavaUtils.sneakyThrow(e);
			}
		}
		int[] ints = new int[tmp.size()];
		for (int i = 0; i < tmp.size(); i++) {
			ints[i] = tmp.get(i);
		}
		return ints;
	}

	public static int[] getIntArray(String str, String sep) {
		String[] prop = getStringList(str, sep);
		List<Integer> tmp = new ArrayList<Integer>();
		for (int i = 0; i < prop.length; i++) {
			try {
				int r = Integer.parseInt(prop[i]);
				tmp.add(r);
			} catch (Exception e) {
				throw JavaUtils.sneakyThrow(e);
			}
		}
		int[] ints = new int[tmp.size()];
		for (int i = 0; i < tmp.size(); i++) {
			ints[i] = tmp.get(i);
		}
		return ints;
	}

	public static int[] getIntArray(String... strArray) {
		int[] ret = {};
		if (strArray == null) {
			return ret;
		}
		ret = new int[strArray.length];
		for (int index = 0; index < strArray.length; index++) {
			ret[index] = Integer.parseInt(strArray[index]);
		}
		return ret;
	}

	public static double[] getDoubleArray(String str) {
		String[] prop = getStringList(str);
		double[] ds = new double[prop.length];
		for (int i = 0; i < ds.length; i++) {
			ds[i] = Double.parseDouble(prop[i]);
		}
		return ds;
	}

	public static List<Integer> getIntList(String str, String sep) {
		List<Integer> tmp = new ArrayList<Integer>();
		if (str == null || str.trim().equals("")) {
			return tmp;
		}
		String[] prop = getStringList(str, sep);
		for (int i = 0; i < prop.length; i++) {
			try {
				int r = Integer.parseInt(prop[i]);
				tmp.add(r);
			} catch (Exception e) {
				logger.error("CatchedException", e);
			}
		}
		return tmp;
	}

	public static List<String> getListBySplit(String str, String split) {
		List<String> list = new ArrayList<String>();
		if (str == null || str.trim().equalsIgnoreCase(""))
			return null;
		String[] strs = str.split(split);
		for (String temp : strs) {
			if (temp != null && !temp.trim().equalsIgnoreCase("")) {
				list.add(temp.trim());
			}
		}
		return list;
	}

	public static String join(String[] value) {
		if (value == null) {
			return "";
		}
		StringBuilder _sb = new StringBuilder();
		for (String a : value) {
			_sb.append(a);
		}
		return _sb.toString();
	}

	public static String join(String[] strs, String sep) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(strs[0]);
		for (int i = 1; i < strs.length; i++) {
			buffer.append(sep).append(strs[i]);
		}
		return buffer.toString();
	}

	public static String join(List<Integer> ints, String sep) {
		StringBuffer sb = new StringBuffer();
		sb.append(ints.get(0));
		for (int i = 1; i < ints.size(); i++) {
			sb.append(sep).append(ints.get(i));
		}
		return sb.toString();
	}

	/**
	 * 连接整形数组返回字符串
	 * 
	 * @param intArray
	 * @param sep
	 * @return
	 * 
	 */
	public static String joinIntArray(int[] intArray, String sep) {
		if (intArray == null || intArray.length <= 0) {
			return "";
		}

		StringBuffer sb = new StringBuffer();
		sb.append(intArray[0]);

		for (int i = 1; i < intArray.length; i++) {
			sb.append(sep).append(intArray[i]);
		}

		return sb.toString();
	}

	public static String toWrapString(Object obj, String content) {
		if (obj == null) {
			return "null";
		} else {
			return obj.getClass().getName() + "@" + obj.hashCode() + "[\r\n" + content + "\r\n]";
		}
	}

	/**
	 * 将1,2,3和{1,2,3}格式的字符串转化为JDK的bitset 考虑了两边是否有{}，数字两边是否有空格，是否合法数字
	 * 
	 * @param str
	 * @return
	 */
	public static BitSet bitSetFromString(String str) {
		if (str == null) {
			return new BitSet();
		}
		if (str.startsWith("{")) {
			str = str.substring(1);
		}
		if (str.endsWith("}")) {
			str = str.substring(0, str.length() - 1);
		}
		int[] ints = getIntList(str);
		BitSet bs = new BitSet();
		for (int i : ints) {
			bs.set(i);
		}
		return bs;
	}

	/**
	 * 是否包含保留字符
	 * 
	 * @param str
	 * @return
	 */
	public static boolean hasExcludeChar(String str) {
		if (str != null) {
			char[] chs = str.toCharArray();
			for (int i = 0; i < chs.length; i++) {
				if (Character.getType(chs[i]) == Character.PRIVATE_USE) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 判断字符串是否为空,有空格也算空
	 * 
	 * @param str
	 * @return true,字符串是空的;false,字符串不是空的
	 */
	public static boolean isEmpty(String str) {
		if (str == null || (str.trim().length() == 0)) {
			return true;
		}
		return false;
	}

	public static boolean isEmpty(Long str) {
		if (str == null || (str == 0)) {
			return true;
		}
		return false;
	}

	/**
	 * 判断字符串是否为空白
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isBlank(String str) {
		int strLen;
		if (str == null || (strLen = str.length()) == 0)
			return true;
		for (int i = 0; i < strLen; i++)
			if (!Character.isWhitespace(str.charAt(i)))
				return false;

		return true;
	}

	/**
	 * 使用解码的方式检查指定的字符串中是有效的Mysql支持的UTF-8编码
	 * 
	 * @param str
	 * @return true,有效的;false,无效的
	 * @throws UnsupportedEncodingException
	 */
	public static boolean isValidMySqlUTF8(String str) {
		byte[] strBytes;
		try {
			strBytes = str.getBytes("UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw JavaUtils.sneakyThrow(e);
		}
		final int length = strBytes.length;
		for (int i = 0; i < length;) {
			final int c = 0xFF & strBytes[i];
			do {
				if (c < 0x80) {
					i++;
					break;
				}
				if (c < 0xc2) {
					return false;
				}
				if (c < 0xe0) {
					final int c1Index = i + 1;
					if (c1Index >= length) {
						return false;
					}
					final int c1 = 0xFF & strBytes[c1Index];
					if (!((c1 ^ 0x80) < 0x40)) {
						return false;
					}
					i += 2;
					break;
				}
				if (c < 0xf0) {
					final int c1Index = i + 1;
					final int c2Index = c1Index + 1;
					if (c2Index >= length) {
						return false;
					}
					final int c1 = 0xFF & strBytes[c1Index];
					final int c2 = 0xFF & strBytes[c2Index];
					if (!((c1 ^ 0x80) < 0x40 && (c2 ^ 0x80) < 0x40 && (c >= 0xe1 || c1 >= 0xa0))) {
						return false;
					}
					i += 3;
					break;
				}
				return false;
			} while (false);
		}
		return true;
	}

	/**
	 * 检查指定的字符串中是有效的Mysql支持的UTF-8编码,该检测直接比较每个code point是否在0X0000~0XFFFF中(Mysql
	 * utf8能够有效支持的UNICODE编码范围0X000~0XFFFF)
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isValidMySqlUTF8Fast(String str) {
		final int length = str.length();
		for (int i = 0; i < length; i++) {
			int c = str.codePointAt(i);
			if (c < 0X0000 || c > 0Xffff) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 是否是email地址
	 * 
	 * @param email
	 * @return
	 */
	public static boolean isEmail(String email) {
		Pattern emailPattern = Pattern.compile("\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*");
		Matcher matcher = emailPattern.matcher(email);
		if (matcher.find()) {
			return true;
		}
		return false;
	}

	/**
	 * 是否是表情符号
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isEmo(String str) {
		String reg = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]";
		Pattern pattern = Pattern.compile(reg);
		Matcher matcher = pattern.matcher(str);
		while (matcher.find()) {
			return true;
		}
		return false;
	}

	/**
	 * 判断字符串是否是数字
	 * 
	 * @param text
	 * @return
	 */
	public static boolean isDigit(String text) {
		String reg = "[-]*[\\d]+[\\.\\d+]*";
		Pattern pat = Pattern.compile(reg);
		Matcher mat = pat.matcher(text);
		return mat.matches();
	}

	/**
	 * 判断一句话是否是汉语
	 * 
	 * @param text
	 * @return
	 */
	public static boolean isChiness(String text) {
		String reg = "[\\w]*[\\u4e00-\\u9fa5]+[\\w]*";
		Pattern pat = Pattern.compile(reg);
		Matcher mat = pat.matcher(text);
		boolean result = mat.matches();
		return result;
	}

	private static Pattern pat = Pattern.compile("[\\u4e00-\\u9fa5]+");

	/**
	 * 是否包含中文
	 * 
	 * @param text
	 * @return
	 */
	public static boolean containChinese(String text) {
		return pat.matcher(text).find();
	}

	/**
	 * 判断单个字符是否是汉语
	 * 
	 * @param cha
	 * @return
	 */
	public static boolean isChineseChar(char cha) {
		String reg = "[\\u4e00-\\u9fa5]";
		Pattern pat = Pattern.compile(reg);
		String text = Character.toString(cha);
		Matcher mat = pat.matcher(text);
		boolean result = mat.matches();
		return result;
	}

	/**
	 * 判断字符是否是字母(包括大小写)或者数字
	 * 
	 * @param cha
	 * @return
	 */
	public static boolean isLetterAndDigit(String cha) {
		String reg = "[\\w]+";
		Pattern pat = Pattern.compile(reg);
		Matcher mat = pat.matcher(cha);
		boolean result = mat.matches();
		return result;
	}

	/**
	 * 返回字符串中汉字的数量
	 * 
	 * @param test
	 * @return
	 */
	public static int getChineseCount(String test) {
		int count = 0;
		boolean tempResult = false;
		for (int i = 0; i < test.length(); i++) {
			char cha = test.charAt(i);
			tempResult = isChineseChar(cha);
			if (tempResult) {
				count++;
			}
		}
		return count;
	}

	/**
	 * 返回字符串中字母和数字的个数，其中字母包括大小写
	 * 
	 * @param text
	 * @return
	 */
	public static int getLetterAndDigitCount(String text) {
		int count = 0;
		boolean tempResult = false;
		for (int i = 0; i < text.length(); i++) {
			tempResult = isLetterAndDigit(text);
			if (tempResult) {
				count++;
			}
		}
		return count;
	}

	/**
	 * 将字符串首字母大写
	 * 
	 * @param s
	 * @return
	 */
	public static String upperCaseFirstCharOnly(String s) {
		if (s == null || s.length() < 1) {
			return s;
		}
		return s.substring(0, 1).toUpperCase() + s.substring(1).toLowerCase();
	}

	public static String lowerCaseFirstChar(String s) {
		return Strings.isNullOrEmpty(s) ? s : s.substring(0, 1).toLowerCase() + s.substring(1);
	}

	public static String upperCaseFirstChar(String s) {
		return Strings.isNullOrEmpty(s) ? s : s.substring(0, 1).toUpperCase() + s.substring(1);
	}

	/**
	 * 將字符串压缩为 gzip 流
	 * 
	 * @param content
	 * @return
	 */
	public static byte[] gzip(String content) {
		ByteArrayOutputStream baos = null;
		GZIPOutputStream out = null;
		byte[] ret = null;
		try {
			baos = new ByteArrayOutputStream();
			out = new GZIPOutputStream(baos);
			out.write(content.getBytes());
			out.close();
			baos.close();
			ret = baos.toByteArray();
		} catch (FileNotFoundException e) {
			logger.error("CatchedException", e);
		} catch (IOException e) {
			logger.error("CatchedException", e);
		} finally {
			if (out != null) {
				try {
					baos.close();
				} catch (IOException e) {
					logger.error("CatchedException", e);
				}
			}
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
					logger.error("CatchedException", e);
				}
			}
		}
		return ret;
	}

	public static byte[] ungzip(byte[] data) {
		byte[] result = new byte[0];
		ByteArrayInputStream input = new ByteArrayInputStream(data);
		try {
			GZIPInputStream in = new GZIPInputStream(input);
			result = readBytes(in);
		} catch (Exception e) {
			logger.error("CatchedException", e);
		} finally {
			if (input != null) {
				try {
					input.close();
				} catch (IOException e) {
					logger.error("CatchedException", e);
				}
			}
		}
		return result;
	}

	public static String ungzipToString(byte[] data) {
		String result = null;
		ByteArrayInputStream input = null;
		GZIPInputStream in = null;
		try {
			input = new ByteArrayInputStream(data);
			in = new GZIPInputStream(input);
			result = readBytesToString(in);
		} catch (FileNotFoundException e) {
			logger.error("CatchedException", e);
		} catch (IOException e) {
			logger.error("CatchedException", e);
		}finally {
			if (input != null) {
				try {
					input.close();
				} catch (IOException e) {
					logger.error("CatchedException", e);
				}
			}

			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
					logger.error("CatchedException", e);
				}
			}
		}

		return result;
	}

	private static byte[] readBytes(InputStream is) throws IOException {
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		byte[] data = new byte[1204];
		int count;
		while ((count = is.read(data)) != -1)
			os.write(data, 0, count);

		return os.toByteArray();
	}

	private static String readBytesToString(InputStream is) throws IOException {
		ByteArrayOutputStream os = null;
		String result = null;
		try {
			os = new ByteArrayOutputStream();
			byte[] data = new byte[1204];
			int count;
			while ((count = is.read(data)) != -1)
				os.write(data, 0, count);
			result = os.toString();
		}catch (FileNotFoundException e) {
			logger.error("CatchedException", e);
		} catch (IOException e) {
			logger.error("CatchedException", e);
		}finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					logger.error("CatchedException", e);
				}
			}
		}

		return result;
	}

	public static byte[] zip(byte input[]) {
		return zip(input, 0, input.length);
	}

	public static byte[] unzip(byte[] input) {
		return unzip(input, 0, input.length);
	}

	/**
	 * 压缩数据
	 * 
	 * @param input
	 * @return
	 */
	public static byte[] zip(byte input[], int off, int len) {
		Deflater compresser = new Deflater();
		compresser.reset();
		compresser.setInput(input, off, len);
		compresser.finish();
		byte output[] = new byte[0];
		try (ByteArrayOutputStream o = new ByteArrayOutputStream(len)) {
			byte[] buf = new byte[len];
			int got;
			while (!compresser.finished()) {
				got = compresser.deflate(buf);
				o.write(buf, 0, got);
			}
			output = o.toByteArray();
		} catch (Exception e) {
			logger.error("CatchedException", e);
		}
		compresser.end();
		return output;
	}

	/**
	 * 解压缩数据
	 * 
	 * @param input
	 * @return
	 */
	public static byte[] unzip(byte[] input, int off, int len) {
		byte output[] = new byte[0];
		Inflater decompresser = new Inflater();
		decompresser.reset();
		decompresser.setInput(input, off, len);
		try (ByteArrayOutputStream o = new ByteArrayOutputStream(input.length)) {
			byte[] buf = new byte[input.length];
			int got;
			while (!decompresser.finished()) {
				got = decompresser.inflate(buf);
				o.write(buf, 0, got);
			}
			output = o.toByteArray();
		} catch (Exception e) {
			logger.error("CatchedException", e);
		}
		decompresser.end();
		return output;
	}

	/**
	 * LinkedList Collector.
	 * 
	 * @see Collectors.toList()
	 */
	public static <T> Collector<T, List<T>, List<T>> toLinkedList() {
		return Collector.of(LinkedList::new, List::add, (left, right) -> {
			left.addAll(right);
			return left;
		});
	}

	/**
	 * Returns the element at the specified position in an iterable.
	 *
	 * @param position
	 *            position of the element to return
	 * @return the element at the specified position in {@code iterable}
	 * @throws IndexOutOfBoundsException
	 *             if {@code position} is negative or greater than or equal to the
	 *             size of {@code iterable}
	 */
	public static <T> T get(Iterable<T> iterable, int position) {
		if (iterable == null) {
			return null;
		}

		if (iterable instanceof List) {
			return ((List<T>) iterable).get(position);
		}

		return getIterator(iterable.iterator(), position);
	}

	/**
	 * Advances {@code iterator} {@code position + 1} times, returning the element
	 * at the {@code position}th position.
	 *
	 * @param position
	 *            position of the element to return
	 * @return the element at the specified position in {@code iterator}
	 * @throws IndexOutOfBoundsException
	 *             if {@code position} is negative or greater than or equal to the
	 *             number of elements remaining in {@code iterator}
	 */
	public static <T> T getIterator(Iterator<T> iterator, int position) {
		if (position < 0) {
			throw new IndexOutOfBoundsException("position (" + position + ") must not be negative");
		}

		int skipped = 0;
		while (iterator.hasNext()) {
			T t = iterator.next();
			if (skipped++ == position) {
				return t;
			}
		}

		throw new IndexOutOfBoundsException("position (" + position + ") must be less than the number of elements that remained (" + skipped + ")");
	}

	/**
	 * Advances {@code iterator} to the end, returning the last element.
	 *
	 * @return the last element of {@code iterator}
	 * @throws NoSuchElementException
	 *             if the iterator is empty
	 */
	public static <T> T getIteratorLast(Iterator<T> iterator) {
		while (true) {
			T current = iterator.next();
			if (!iterator.hasNext()) {
				return current;
			}
		}
	}

	/**
	 * 对字符串进行编码加密
	 * 
	 * @param src
	 * @param seed
	 * @return
	 */
	public static String encode(String src, String seed) {
		// Key为空null
		if (seed == null) {
			return null;
		}
		// 判断Key是否为16位
		if (seed.length() != 16) {
			return null;
		}
		byte[] raw = seed.getBytes();
		SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
		try {
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");// "算法/模式/补码方式"
			IvParameterSpec iv = new IvParameterSpec("1861172569762014".getBytes());// 使用CBC模式，需要一个向量iv，可增加加密算法的强度
			cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
			byte[] encrypted = cipher.doFinal(src.getBytes());
			return new String(Base64.getEncoder().encode(encrypted), StandardCharsets.UTF_8);// 此处使用BASE64做转码功能，同时能起到2次加密的作用。
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 对字符串进行解密
	 * 
	 * @param src
	 * @param seed
	 * @return
	 */
	public static String decode(String src, String seed) {
		try {
			// 判断Key是否正确
			if (seed == null) {
				return null;
			}
			// 判断Key是否为16位
			if (seed.length() != 16) {
				return null;
			}
			byte[] raw = seed.getBytes("ASCII");
			SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
			IvParameterSpec iv = new IvParameterSpec("IRONTOBRA2016".getBytes());
			cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
			byte[] encrypted1 = Base64.getDecoder().decode(src.getBytes(StandardCharsets.UTF_8));// 先用base64解密
			try {
				byte[] original = cipher.doFinal(encrypted1);
				String originalString = new String(original);
				return originalString;
			} catch (Exception e) {
				return null;
			}
		} catch (Exception ex) {
			return null;
		}
	}

	private static SSLConnectionSocketFactory enableSSL() {
		try {
			SSLContext context = SSLContext.getInstance("TLS");
			context.init(null, new TrustManager[] { new EasyX509TrustManager() }, new java.security.SecureRandom());
			return new SSLConnectionSocketFactory(context, NoopHostnameVerifier.INSTANCE);
		} catch (NoSuchAlgorithmException e) {
			logger.error("CatchedException", e);
		} catch (KeyManagementException e) {
			logger.error("CatchedException", e);
		}
		return null;
	}

	private static class EasyX509TrustManager implements X509TrustManager {

		@Override
		public X509Certificate[] getAcceptedIssuers() {
			return new X509Certificate[] {};
		}

		@Override
		public void checkClientTrusted(X509Certificate[] certs, String authType) {
			// Trust always
		}

		@Override
		public void checkServerTrusted(X509Certificate[] certs, String authType) {
			// Trust always
		}

	}

	public static int[][] copyArray(int[][] array) {
		int[][] copyArray = new int[array.length][];
		for (int i = 0; i < array.length; i++) {
			copyArray[i] = new int[array[i].length];
			System.arraycopy(array[i], 0, copyArray[i], 0, copyArray[i].length);
		}
		return copyArray;
	}

	/**
	 * 生成一个仅包括数字和字母的随机字符串
	 * 
	 * @param count
	 * @return
	 */
	public static String randomString(int count) {
		if (count < 0)
			count = 0;
		return RandomStringUtils.random(count, "abcdefghijklmnopqrstuvwxyz1234567890");
	}

	/**
	 * 验证MD5密码是否正确
	 * 
	 * @param pass
	 * @param inputString
	 * @return
	 */
	public static boolean authMD5String(String md5, String inputString) {
		// System.out.println(encodeByMD5(inputString));
		// System.out.println(md5);
		if (md5.equalsIgnoreCase(encodeByMD5(inputString))) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 将输入的字符串进行MD5加密（编码）
	 * 
	 * @param inputString
	 * @return
	 */
	public static String createMD5String(String inputString) {
		return encodeByMD5(inputString);
	}

	/**
	 * 对字符串进行MD5编码
	 * 
	 * @param originStr
	 * @return
	 */
	public static String encodeByMD5(String originStr) {
		if (originStr != null) {
			try {
				// 创建具有指定算法名称的信息摘要
				MessageDigest md = MessageDigest.getInstance("MD5");
				// 使用指定的字节数组对摘要进行最后的更新，然后完成摘要计算
				char[] _charStr = originStr.toCharArray();
				byte[] _byteStr = new byte[_charStr.length];
				for (int i = 0; i < _charStr.length; i++) {
					_byteStr[i] = (byte) _charStr[i];
				}
				byte[] _results = md.digest(_byteStr);
				StringBuffer _hexValue = new StringBuffer();
				for (int i = 0; i < _results.length; i++) {
					int _val = (_results[i]) & 0xff;
					if (_val < 16) {
						_hexValue.append("0");
					}
					_hexValue.append(Integer.toHexString(_val));
				}
				return _hexValue.toString();
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 密码编码
	 * 
	 * @param pwd
	 * @return
	 */
	public static String encodePwd(String pwd) {
		String res = null;
		try {
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			return new String(Base64.getEncoder().encode(md5.digest(pwd.getBytes(StandardCharsets.UTF_8))), StandardCharsets.UTF_8);
		} catch (Exception e) {
			logger.error("CatchedException", e);
		}
		return res;
	}

	/**
	 * 
	 * 合并数组
	 * 
	 * jay 2017/04/19
	 * 
	 * @param first
	 * @param others
	 * @return
	 */
	@SafeVarargs
	public static <T> T[] concatArrays(T[] first, T[]... others) {
		int totalLength = first.length;
		for (T[] array : others) {
			totalLength += array.length;
		}
		T[] result = Arrays.copyOf(first, totalLength);
		int offset = first.length;
		for (T[] array : others) {
			System.arraycopy(array, 0, result, offset, array.length);
			offset += array.length;
		}
		return result;
	}

	/**
	 * 比较版本号
	 *
	 * @return
	 * 0：version1大于version2；
	 * 1：相等；
	 * 2：version1小于version2。
	 */
	public static int compareVersion(String version1, String version2) {
		int[] versionNum1 = getVersionCache(version1);
		int[] versionNum2 = getVersionCache(version2);
		int comPare;
		if (versionNum1[0] > versionNum2[0]) {
			comPare = 0;
		} else if (versionNum1[0] < versionNum2[0]) {
			comPare = 2;
		} else if (versionNum1[1] > versionNum2[1]) {
			comPare = 0;
		} else if (versionNum1[1] < versionNum2[1]) {
			comPare = 2;
		} else if (versionNum1[2] > versionNum2[2]) {
			comPare = 0;
		} else if (versionNum1[2] < versionNum2[2]) {
			comPare = 2;
		} else {
			comPare = 1;
		}
		return comPare;
	}

	private static int[] getVersionCache(String version) {
		int[] versionNum1 = null;
		if (useCache) {
			versionNum1 = versionCache.get(version);
			if (versionNum1 == null) {
				versionNum1 = getIntArray(StringUtils.split(version, '.'));
				versionCache.putIfAbsent(version, versionNum1);
			}
		} else {
			versionNum1 = getIntArray(StringUtils.split(version, '.'));
		}
		return versionNum1;
	}
	/**
	 * 服务器是否满足条件
	 * @return
	 */
	public static boolean isOpen(String serverStr) {
		return "on".equals(serverStr);
	}

//	public static void main(String[] args) {
//		System.out.println(formatYMDTime(getNextSundayTime(TimeUtil.getNow())));
//		System.err.println(isChiness("aaa你sdfa好asdasdddasd"));
//		// 开新服时间设置
//		System.out.println(formatDateStr("2017-06-29 11:00:00", Constants.DEFAULT_DATETIME_FORMAT));
//		System.out.println(getNextDayZeroClockTime(TimeUtil.getNow()));
//	}
}
