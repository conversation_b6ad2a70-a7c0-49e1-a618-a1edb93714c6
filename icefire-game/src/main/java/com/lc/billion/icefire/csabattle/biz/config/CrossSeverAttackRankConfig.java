package com.lc.billion.icefire.csabattle.biz.config;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lc.billion.icefire.core.config.MetaUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;

/**
 * 跨服抢城排行奖励配置
 * 
 * <AUTHOR>
 */
@Config(name = "CrossSeverAttackRank", metaClass = CrossSeverAttackRankConfig.CrossSeverAttackRankMeta.class)
public class CrossSeverAttackRankConfig {

	private static final Logger logger = LoggerFactory.getLogger(CrossSeverAttackRankConfig.class);

	@MetaMap
	private Map<String, CrossSeverAttackRankMeta> dataById;

	public void init(List<CrossSeverAttackRankMeta> list) {
		dataById = new HashMap<>();
		for (CrossSeverAttackRankMeta meta : list) {
			dataById.put(meta.getId(), meta);
		}

		logger.debug("CrossSeverAttackRankConfig data = {}", dataById);
	}

	public CrossSeverAttackRankMeta getMeta(String metaId) {
		return dataById.get(metaId);
	}

	public Collection<CrossSeverAttackRankMeta> getAllMeta() {
		return dataById.values();
	}

	public static class CrossSeverAttackRankMeta extends AbstractMeta {

		/**
		 * 排名组
		 */
		protected int[] ranks;
		/**
		 * 排名奖励
		 */
		private String reward;
		/**
		 * 排名奖励邮件
		 */
		private String mail;

		@Override
		public void init(JsonNode jsonNode) {
			ranks = MetaUtils.parseInts(jsonNode.get("rank").asText(), META_SEPARATOR_2);
		}

		public int[] getRanks() {
			return ranks;
		}

		public void setRanks(int[] ranks) {
			this.ranks = ranks;
		}

		public String getReward() {
			return reward;
		}

		public void setReward(String reward) {
			this.reward = reward;
		}

		public String getMail() {
			return mail;
		}

		public void setMail(String mail) {
			this.mail = mail;
		}
	}
}
