package com.lc.billion.icefire.game.biz.async.gvg;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.gvg.RZEGameService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.rpc.GameRPCToGVGControlProxyService;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.protocol.structure.PsRoomInviteInfo;
import com.lc.billion.icefire.rpc.service.gvg.IGameRemoteGVGControlService;
import com.lc.billion.icefire.rpc.vo.gvg.RZERoomInfoVo;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class RZEInviteJoinRoomOperation extends AsyncRPCThreadOperation {
    private Role role;
    private Long allianceId;
    private int serverId;
    private List<PsRoomInviteInfo> inviteInfos;
    private GameRPCToGVGControlProxyService gameRPCToGVGControlProxyService;
    private RZEGameService rzeGameService;
    private List<RZERoomInfoVo> rzeRoomInfoVos;

    // 移除的：被邀请的房间Id列表 + 申请的房间Id列表 + 创建的房间列表，应战成功的不存在删除房间的概念
    private Set<Long> removeRoomIds = new HashSet<>();

    public RZEInviteJoinRoomOperation(Role role, List<PsRoomInviteInfo> inviteInfos,
            RZEGameService rzeGameService, GameRPCToGVGControlProxyService gameRPCToGVGControlProxyService) {
        this.role = role;
        this.inviteInfos = inviteInfos;
        this.allianceId = role.getAllianceId();
        this.serverId = role.getoServerId();
        this.rzeGameService = rzeGameService;
        this.gameRPCToGVGControlProxyService = gameRPCToGVGControlProxyService;
    }

    @Override
    public boolean init() {
        return true;
    }

    @Override
    public boolean run() {
        IGameRemoteGVGControlService gameRemoteGVGControlService = gameRPCToGVGControlProxyService.getGameRemoteGVGControlService();
        if (gameRemoteGVGControlService == null) {
            ErrorLogUtil.errorLog("中控服rpc连接失败");
            return false;
        }

        try {
            rzeRoomInfoVos = gameRemoteGVGControlService.updateInviteJoinRoom(role.getId(), allianceId, serverId, inviteInfos, removeRoomIds);
        }catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("更新GVG约战:邀请加入房间异常", e,"roleId",role.getId(), "allianceId",role.getAllianceId());
        }
        return true;
    }

    @Override
    public void finish() {
        rzeGameService.responseControlInviteJoinRoom(role, rzeRoomInfoVos, inviteInfos, removeRoomIds);
    }
}
