package com.lc.billion.icefire.game.biz.async.log;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.msg.AsyncMessageSender;
import com.lc.billion.icefire.game.msg.GameMessageConfigManager;
import org.apache.thrift.TBase;

/**
 * 异步记录GC日志
 */
public class GcLogOperation implements AsyncOperation {
	/**
	 * Logger for this class
	 */
	private final GameMessageConfigManager messageConfigManager = Application.getBean(GameMessageConfigManager.class);

	private final AsyncMessageSender.SendMessageTask task;

	public GcLogOperation(AsyncMessageSender.SendMessageTask task) {
		this.task = task;
	}

	@Override
	public boolean run() {
		TBase<?, ?> body = task.getMsg().getMsg();
		int msgType = messageConfigManager.getMessageType(body.getClass());
		AsyncMessageSender.GcLog(task, msgType, null);

		return false;
	}
}
