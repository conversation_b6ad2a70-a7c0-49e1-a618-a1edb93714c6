package com.lc.billion.icefire.game.biz.battle;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.battle.calculator.BattleDamage;
import com.lc.billion.icefire.game.biz.battle.calculator.FightUnitPropCalculate;
import com.lc.billion.icefire.game.biz.battle.result.FightItem;
import com.lc.billion.icefire.game.biz.battle.result.FightUnitAction;
import com.lc.billion.icefire.game.biz.battle.skill.BattleSkill;
import com.lc.billion.icefire.game.biz.battle.skill.FightEffect.BuffEffect;
import com.lc.billion.icefire.game.biz.battle.skill.FightEffect.buff.BattleBuff;
import com.lc.billion.icefire.game.biz.battle.skill.FightEffect.buff.BattleBuffType;
import com.lc.billion.icefire.game.biz.battle.skill.FightEffect.buff.Dot;
import com.lc.billion.icefire.game.biz.config.SettingConfig;
import com.lc.billion.icefire.game.biz.model.prop.NumberPropsContainer;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.protocol.constant.PsFightAction;
import com.lc.billion.icefire.protocol.constant.PsSoldierType;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

// 战斗的单位，一个战斗小队 士兵
public class FightUnit extends FightObject {
    private static final Logger logger = LoggerFactory.getLogger(FightUnit.class);

    private final String unitId; // 怪物Id/角色Id
    @Setter
    @Getter
    private FightUnitSoldier soldier; // 不能为空
    @Setter
    private boolean out; // 是否出局
    @Setter
    @Getter
    private boolean action; // 是否出手
    @Getter
    private final List<BattleBuff> buffs = new ArrayList<>(); // 挂在身上的buff

    // 这个小队全程记录 soldierId killCount
    private final Map<String, Integer> soldierTotalKillCount = new HashMap<>(); // 全程士兵击杀数
    private int soldierKillCount = 0; // 当前轮次士兵击杀数

    // 引用army中的属性
    private final FightProp props;

    @Setter
    @Getter
    private double hpValue;

    // 攻击后,不足一个士兵,剩余伤害保存在这里,待下次继续累加
    @Setter
    @Getter
    private double damageRemain = 0.0;

    private final ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);

    // 初始化
    public FightUnit(boolean attack, FightProp props, String unitId, String metaId, int count) {
        super(attack);
        out = false;
        this.props = props;
        this.unitId = unitId;
        soldier = new FightUnitSoldier(unitId, metaId, count);
    }

    @Override
    public boolean isOut() {
        return out;
    }

    public boolean isDead() {
        return soldier.getCount() <= 0;
    }

    @Override
    public Map<String, Integer> receivedDamage(FightUnitAction logAction, BattleDamage damage, FightUnit currentUnit, FightContext context) {
        Map<String, Integer> result = new HashMap<>();
        if (isOut()) {
            // 伤害浪费，直接返回
            // 不会出局了,再大回合结算出局
            return result;
        }

        // 被攻击方生命力计算
        double hpValue = getHp();
        logAction.setDefHpValue(hpValue);
        // currentUnit.setHpValue(hpValue);

        double damangePre = 1.0;
        if (null != currentUnit) {
            damangePre = currentUnit.getDamage();
        }

        // 伤害修正(减伤等buff)
        var targetDamage = new BattleDamage(damage.getDamage());
        damageModify(targetDamage, true, null);
        int count = soldier.getCount();
        // 击杀数量
        double delta = targetDamage.getDamage() * damangePre / hpValue;
        // 死亡
        int unitDead = (int) delta;

        var receiveItems = logAction.getTargetItems();
        // 结算伤亡
        int dead = 0;
        // 整数部分直接处理
        if (unitDead >= count) {
            dead = count;
            // 被击败
            // setLastOut(isOut());
            // setOut(true);
        } else {
            dead = unitDead;
        }
        result.put(soldier.getMetaId(), dead);
        soldier.setLastCount(count);
        soldier.setCount(count - dead);
        soldier.setTotalHp(soldier.getCount() * getHp());

        FightItem item = new FightItem(soldier.getUnitId(), soldier.getMetaId());
        item.setCount(soldier.getCount());
        item.setDead(dead);
        item.setHpVal(getHp());
        receiveItems.add(item);

        // 计算死兵
        int _delta = soldier.getRoundTotalCount() - soldier.getCount();
        soldier.setDead(_delta);
        soldier.setRoundTotalCount(soldier.getRoundTotalCount() - _delta);
        soldier.setTotalDead(soldier.getTotalDead() + _delta);

        // 主攻方实际参加战斗的数量
        if (null != currentUnit) {
            int realFightCount = currentUnit.getSoldier().getRealFightCount();
            logAction.setFightCount(realFightCount);
        }

        return result;
    }

    public double getAtk() {
        PsSoldierType soldierType = soldier.getMeta().getType();
        Double atkDel = props.getDelSoldierProp().get(soldierType, BattlePropType.ATK);
        if (null == atkDel) atkDel = 0.0;
        Double atkSoldier = props.getSoldierProp().get(soldierType, BattlePropType.ATK);
        if (null == atkSoldier) atkSoldier = 0.0;
        double atkBuff = 0.0;
        for (BattleBuff battleBuff : buffs) {
            atkBuff += battleBuff.atkEffect(soldier.getMeta());
        }
        double atk = soldier.getMeta().getAtk() * (1 + atkSoldier + atkBuff) / (1 + atkDel);
        return Math.max(atk, 0d);
    }

    public double getDef(FightUnit attackUnit) {
        PsSoldierType soldierType = soldier.getMeta().getType();
        Double defDel = props.getDelSoldierProp().get(soldierType, BattlePropType.DEF);
        if (null == defDel) defDel = 0.0;
        Double defSoldier = props.getSoldierProp().get(soldierType, BattlePropType.DEF);
        if (null == defSoldier) defSoldier = 0.0;
        double defBuff = 0.0;
        for (BattleBuff battleBuff : buffs) {
            defBuff += battleBuff.defEffect(attackUnit, this.soldier.getMeta());
        }
        double def = soldier.getMeta().getDef() * (1 + defSoldier + defBuff) / (1 + defDel);
        return Math.max(def, 1d);
    }

    public double getHp() {
        PsSoldierType soldierType = soldier.getMeta().getType();
        Double hpDel = props.getDelSoldierProp().get(soldierType, BattlePropType.HP);
        if (null == hpDel) hpDel = 0.0;
        Double hpSoldier = props.getSoldierProp().get(soldierType, BattlePropType.HP);
        if (null == hpSoldier) hpSoldier = 0.0;
        double hp = soldier.getMeta().getHp() * (1 + hpSoldier) / (1 + hpDel);
//		var meta = soldier.getMeta();
//		logger.info(">>>>>>>>>>>>>hp>>> id: {}, hp: {}, srcHp: {}", meta.getId(), hp, meta.getHp());
        return Math.max(hp, 1d);
    }

    // 兵种杀伤力
    public double getDamage() {
        PsSoldierType soldierType = soldier.getMeta().getType();
        Double damageDel = props.getDelSoldierProp().get(soldierType, BattlePropType.DAMAGE);
        if (null == damageDel) damageDel = 0.0;
        Double damageSoldier = props.getSoldierProp().get(soldierType, BattlePropType.DAMAGE);
        if (null == damageSoldier) damageSoldier = 0.0;
        double damage = soldier.getMeta().getDamage() * (1 + damageSoldier) / (1 + damageDel);
        return Math.max(damage, 0.0);
    }

    // 伤害修正
    public BattleDamage damageModify(BattleDamage damage, boolean isDamageTarget, FightObject targetUnit) {
        if (!isDamageTarget) {
            double damageModifyAdd = 0, damageModifySub = 0;
            for (BattleBuff battleBuff : buffs) {
                if (battleBuff.getBuffType() == BattleBuffType.DAMAGE_MODIFY_ADD) {
                    damageModifyAdd += battleBuff.getEffectRatio();
                } else if (battleBuff.getBuffType() == BattleBuffType.DAMAGE_MODIFY_REDUCE) {
                    damageModifySub += battleBuff.getEffectRatio();
                }
            }

            // 先修正累加
            damage.modify(damageModifyAdd);
            // 修正累减
            damage.setCurrentDamage(damage.getDamage() / (1 + damageModifySub));
            // 后面的继续累乘
            damage.setBase(damage.getDamage());
        }

        // 抽出部分属性,与buff效果,一起做值域修正
        damageModifyByBuff(damage, isDamageTarget, targetUnit);

        // 技能攻击、防御属性对伤害的修正
        skillDamageModify(damage, isDamageTarget, targetUnit);

        return damage;
    }

    /**
     * 技能攻击、防御属性对伤害的修正
     * 这个修正 是在最终伤害基础上 *（1 + 攻击方技能攻击加成） *（1 + 防守方技能防御加成），
     * 所以触发点在防守方伤害修正后
     */
    private void skillDamageModify(BattleDamage damage, boolean isDamageTarget, FightObject targetUnit) {
        if (!isDamageTarget) {
            // 如果不是 目标的承受者，不需要处理这个修正。
            return;
        }

        NumberPropsContainer attackerProps = props.getRoleProps();
        // 攻击方技能攻击加成
        double attackerSkillAtkAddPara = attackerProps.getDouble(props.setEffect(Prop.TROOP_SKILL_ATK_ADD_RATIO_12531, true));

        double defenderSkillDefAddPara = 1.0;
        if (null != targetUnit) {
            var defProps = targetUnit.getProps();
            NumberPropsContainer defenderProps = defProps.getRoleProps();
            defenderSkillDefAddPara = defenderProps.getDouble(props.setEffect(Prop.TROOP_SKILL_DEF_ADD_RATIO_12532, true));
        }

        if (attackerSkillAtkAddPara == 0 || defenderSkillDefAddPara == 0) {
            // logger.error("战斗系统，技能攻击、防御属性修正伤害异常。计算出来的乘数为0", new RuntimeException());
            return;
        }

        damage.setCurrentDamage(damage.getDamage() * attackerSkillAtkAddPara / defenderSkillDefAddPara);
    }

    // 抽出部分属性,与buff效果,一起做值域修正 10671,10670,51006
    // // isDamageTarget 为 true 表示 self是伤害承受者
    private void damageModifyByBuff(BattleDamage damage, boolean isDamageTarget, FightObject targetUnit) {
        double rallyRatio = 0, restraintRatio = 0;
        NumberPropsContainer curRoleProps = props.getRoleProps();
        // 伤害目标/检测自己是和否有削弱伤害的prop
        if (isDamageTarget) {
            restraintRatio += FightUnitPropCalculate.addProp(props, Prop.TROOP_RECEIVED_DAMAGE_DEL_RATIO_10670);
        } else {
            // 该属性是通过参与集结的英雄计算得到,该属性优先计算
            rallyRatio += FightUnitPropCalculate.addProp(props, Prop.RALLY_FINAL_DAMAGE_PERCENT_10700);
            rallyRatio += FightUnitPropCalculate.addProp(props, Prop.ALL_RALLY_ATK_FINAL_DAMAGE_ADD_150025);
            rallyRatio += FightUnitPropCalculate.addProp(props, Prop.ALL_RALLY_DEF_FINAL_DAMAGE_ADD_150026);
            //20241106 增加集结减伤
            rallyRatio -= FightUnitPropCalculate.addProp(targetUnit.getProps(), Prop.ALL_RALLY_DAMAGE_REDUCE_RATIO_150027);

            restraintRatio += FightUnitPropCalculate.addProp(props, Prop.TROOP_DAMAGE_ADD_RATIO_10671);
            restraintRatio += FightUnitPropCalculate.addProp(props, Prop.GVG_WATCH_TOWER_TO_ARM_FACTORY_DAMAGE_ADD_RATIO_SERVER_51006);

            // 该属性是通过羁绊计算得到
            restraintRatio += FightUnitPropCalculate.addProp(props, Prop.HERO_BOND_DAMAGE_ADD_RATIO_100000);

            // 该属性是通过羁绊克制计算得到
            restraintRatio += FightUnitPropCalculate.addProp(props, Prop.HERO_BOND_RESTRAIN_DAMAGE_ADD_RATIO_100010);
        }

        // base伤害 *（1+技能加成）*（1+集结伤害加成）*（1+阵营加成+阵营克制+全部伤害加成）

        if (rallyRatio > 0) {
            //只有大于0才修正，小于0的不处理
            damage.modify(rallyRatio);
            damage.setBase(damage.getDamage());
        }

        if (restraintRatio != 0) {
            damage.modify(restraintRatio);
        }

        // 做值域修正
        SettingConfig settingConfig = configService.getConfig(SettingConfig.class);
        double minDamage = damage.getBase() * (settingConfig.getBuffDamageMin());
        double maxDamage = damage.getBase() * (settingConfig.getBuffDamageMax());
        if (damage.getDamage() < minDamage) {
            damage.setCurrentDamage(minDamage);
        }
        if (damage.getDamage() > maxDamage) {
            damage.setCurrentDamage(maxDamage);
        }
    }

    public void update() {
        double originalTotalHp = soldier.getCount() * getHp();
        soldier.setOriginalTotalHp(originalTotalHp);
        soldier.setTotalHp(soldier.getOriginalTotalHp());
    }

    @Override
    public int getLastCount() {
        return soldier.getLastCount();
    }

    @Override
    public int getCount() {
        return soldier.getCount();
    }

    @Override
    public String getId() {
        return "unit:" + unitId;
    }

    @Override
    public List<FightUnit> getUnits() {
        var arr = new ArrayList<FightUnit>();
        arr.add(this);
        return arr;
    }

    @Override
    public String toString() {
        StringBuilder str = new StringBuilder("[Unit:");
        str.append(getId()).append("]");
        str.append(", isout:").append(isOut());
        str.append(", soldierInfo:").append(soldier.toString());
        str.append(", prop:").append(props.getSoldierProp().toString());
        str.append(", 士兵攻击击杀:").append(this.soldierKillCount);
        str.append("\r\n");
        return str.toString();

    }

    // 是否当前回合无法行动
    public boolean isSkipRound() {
        boolean ret = false;
        for (BattleBuff battleBuff : buffs) {
            if (battleBuff.isSkipRound()) {
                return true;
            }
        }
        return ret;
    }

    // 是否士兵无法行动
    private boolean isSoldierPass() {
        boolean ret = false;
        for (BattleBuff battleBuff : buffs) {
            if (battleBuff.isSoldierPass()) {
                return true;
            }
        }
        return ret;
    }

    // 是否 沉默（英雄不能放技能）
    public boolean isSilent() {
        boolean ret = false;
        for (BattleBuff battleBuff : buffs) {
            if (battleBuff.isSilent()) {
                return true;
            }
        }
        return ret;
    }

    public void addbuff(BattleBuff buff) {
        buffs.add(buff);
    }

    public Map<String, Integer> getSoldierKillCount() {
        return soldierTotalKillCount;
    }

    public int getSoldierTotalKillCount() {
        return soldierTotalKillCount.values().stream().mapToInt(Integer::intValue).sum();
    }

    public void addSoldierKillCount(Map<String, Integer> killCount) {
        this.soldierKillCount += killCount.values().stream().mapToInt(Integer::intValue).sum();
        for (var entry : killCount.entrySet()) {
            soldierTotalKillCount.merge(entry.getKey(), entry.getValue(), Integer::sum);
        }
    }

    // 检测过期的buff
    public void checkExpireBuff(FightContext context, int roundType) {
        if (isOut()) {
            return;
        }
        Iterator<BattleBuff> iterator = buffs.iterator();
        while (iterator.hasNext()) {
            BattleBuff buff = iterator.next();
            if (buff.getMeta().getSustainType() != roundType) {
                // 类型不同
                continue;
            }

            buff.setRoundRemain(buff.getRoundRemain() - 1);
            if (buff.getRoundRemain() <= 0) {
                context.getCurrentFightRound().addAction(new FightUnitAction(PsFightAction.BUFF_REMOVE, this, buff.getSourceSkill(), buff.getMeta().getId()));
                iterator.remove();
            }
        }
    }

    // 是否免疫buff效果
    public boolean isIgnoreBuffEffect(FightContext context, FightUnit unit, BuffEffect buffEffect, BattleSkill battleSkill) {
        boolean ret = false;
        for (BattleBuff battleBuff : buffs) {
            if (battleBuff.isIgnoreBuffEffect(buffEffect.getEffectMeta().getParam1())) {
                BattleBuff attackBuff = buffEffect.createBattleBuff(context, buffEffect.getEffectMeta(), battleSkill, unit, this);
                context.getCurrentFightRound().addAction(new FightUnitAction(PsFightAction.IGNORE_BUFF, unit, battleSkill, attackBuff.getMeta().getId()));
                return true;
            }
        }
        return ret;
    }

    // 士兵是否可以行动
    public boolean isEnable() {
        // 没出局 && 没被眩晕 && 没被限制普攻
        return !isOut() && !isSkipRound() && !isSoldierPass();
    }

    @Override
    public FightProp getProps() {
        return props;
    }

    // dot伤害结算
    public void dotCalculate(FightContext context) {
        for (BattleBuff battleBuff : buffs) {
            if (battleBuff.getBuffType() != BattleBuffType.DOT) {
                continue;
            }

            Dot dot = (Dot) battleBuff;
            BattleDamage damage = new BattleDamage(dot.getDotDamage());
            FightUnitAction logAction = new FightUnitAction(PsFightAction.DOT_HURT, dot.getSourceSkill(), damage.getDamage(), battleBuff.getMeta().getId());
            context.getCurrentFightRound().addAction(logAction);

            var killCount = receivedDamage(logAction, damage, null, context);
            addSoldierKillCount(killCount);
            int delta = killCount.values().stream().mapToInt(Integer::intValue).sum();
            dot.getSourceSkill().addSkillKillCount(delta);
            logAction.setKill(delta);
        }
    }
}
