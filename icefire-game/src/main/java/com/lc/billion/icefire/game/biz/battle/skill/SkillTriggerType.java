package com.lc.billion.icefire.game.biz.battle.skill;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

// 技能释放 时机
public enum SkillTriggerType implements IntEnum {
	BEFORE_BATTLE(1, "战斗前技能， 在回合开始前"),

	ROUND_BIG(2, "大回合技能"),

	ROUND_SMALL(3, "小回合技能"),

	DOUBLE_HIT(4, "士兵攻击后，检测追击"),

	;

	private static final SkillTriggerType[] INDEXES = EnumUtils.toArray(values());

	private final int id;
	private String desc;

	private SkillTriggerType(int id, String desc) {
		this.id = id;
		this.desc = desc;
	}

	@Override
	public int getId() {
		return id;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public static SkillTriggerType findById(int value) {
		if (value < 0 || value >= INDEXES.length) {
			return null;
		}
		return INDEXES[value];
	}
}
