package com.lc.billion.icefire.game.biz.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Config(name = "Emoji", metaClass = EmojiConfig.EmojiMeta.class)
public class EmojiConfig {
    @MetaMap
    private final Map<String, EmojiMeta> metaMap = new HashMap<>();

    public void init(List<EmojiMeta> list) {

    }

    public Map<String, EmojiConfig.EmojiMeta> getMetaMap() {
        return metaMap;
    }

    public static class EmojiMeta extends AbstractMeta {
        private String name;

        private int unlock;

        public void init(JsonNode metaJson) {
        }

        public String getName() {
            return name;
        }

        public int getUnlock() {
            return unlock;
        }
    }
}
