package com.lc.billion.icefire.game.biz.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Config(name = "RegionCapitalFeast", metaClass = RegionCapitalFeastConfig.RegionCapitalFeastMeta.class)
public class RegionCapitalFeastConfig {

    @MetaMap
    private Map<String, RegionCapitalFeastMeta> dataById;

    public Map<String, RegionCapitalFeastConfig.RegionCapitalFeastMeta> getMetaAllGroup() {
        return dataById;
    }

    public RegionCapitalFeastMeta getMeta(String metaId) {
        return dataById.get(metaId);
    }

    public RegionCapitalFeastMeta getBySeason(int season) {
        for (RegionCapitalFeastMeta meta : dataById.values()) {
            for (int seasonId : meta.getSeasons()) {
                if (seasonId == season) {
                    return meta;
                }
            }
        }
        return null;
    }


    @EqualsAndHashCode(callSuper = true)
    @Setter
    @Getter
    @Data
    public static class RegionCapitalFeastMeta extends AbstractMeta {
        private int metaId;

        /**
         * 赛季ids
         */
        private int[] seasons;
        /**
         * 活动时长s
         */
        private int duration;
        /**
         * "刷新间隔s|波次开启时刷新第一波"
         */
        private int[] refreshIntervals;
        /**
         * "宴席池设定ID|数量;ID|数量"
         */
        private List<int[]> feastList;
        /**
         * 关联礼包组
         */
        private List<String> productGroupList;
        /**
         * 加宴宴席组（待确定）
         */
        private long[] productFeasts;
        /**
         * 开启所需功勋
         */
        private int requireHonor;

        @Override
        public void init(JsonNode json) {
            this.metaId = json.get("id").asInt();
            this.seasons = MetaUtils.parseInts(json.path("season").asText(), AbstractMeta.META_SEPARATOR_2);
            this.duration = json.path("duration").asInt();
            this.refreshIntervals = MetaUtils.parseInts(json.path("interval").asText(), AbstractMeta.META_SEPARATOR_2);
            this.feastList = MetaUtils.parseIntegerArrList(json.path("feast").asText(), AbstractMeta.META_SEPARATOR_3);
            this.productGroupList = MetaUtils.parseStringList(json.path("productGroup").asText(), AbstractMeta.META_SEPARATOR_2);
            this.productFeasts = MetaUtils.parseLongs(json.path("productFeast").asText(), AbstractMeta.META_SEPARATOR_2);
            this.requireHonor = json.path("requireHonor").asInt();
        }
    }
}
