package com.lc.billion.icefire.game.biz.config;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ProjectName: lsserver
 * @Package: com.lc.billion.icefire.game.biz.config
 * <AUTHOR>
 * @Date 2024-07-12 11:09:48.637
 * @ClassName ReserveCampConfig
 * @Description 后备营 军饷速度系数
 */
@Config(name = "ReserveCamp", metaClass = ReserveCampConfig.ReserveCampMeta.class)
public class ReserveCampConfig {

    private static final Logger logger = LoggerFactory.getLogger(ReserveCampConfig.class);

    @MetaMap
    private Map<String, ReserveCampMeta> metaMap;

    private Map<Integer, Double> numToParam = new HashMap<>();

    public void init(List<ReserveCampMeta> metaList) {
        metaList.forEach(reserveCampMeta -> {
            numToParam.put(Integer.parseInt(reserveCampMeta.num), Double.parseDouble(reserveCampMeta.param));
        });

        /* 测试用
        for (var i = 0; i < 100; i++) {
            logger.error("count:{} param:{}", 10000 * i, getParam(10000 * i));
        }
         */
    }

    public Double getParam(int count) {
        var max = -1;
        var result = 0D;
        for (var entry : numToParam.entrySet()) {
            if (entry.getKey() >= max && count >= entry.getKey()) {
                max = entry.getKey();
                result = entry.getValue();
            }
        }
        return result;
    }

    public static class ReserveCampMeta extends AbstractMeta {
        private String num;
        private String param;

        public String getNum() {
            return num;
        }

        public void setNum(String num) {
            this.num = num;
        }

        public String getParam() {
            return param;
        }

        public void setParam(String param) {
            this.param = param;
        }
    }
}
