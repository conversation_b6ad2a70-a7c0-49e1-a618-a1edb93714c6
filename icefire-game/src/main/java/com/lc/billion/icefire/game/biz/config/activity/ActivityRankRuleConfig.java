package com.lc.billion.icefire.game.biz.config.activity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.model.AbstractConfig;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.core.support.StringInternDeserializer;
import com.lc.billion.icefire.game.biz.config.IGameMeta;
import com.lc.billion.icefire.game.biz.config.RankingConfig;
import lombok.Getter;

//@Config(name = "ActivityRankRule", metaClass = ActivityRankRuleConfig.ActivityRankRuleMeta.class)
public class ActivityRankRuleConfig extends AbstractConfig<ActivityRankRuleConfig.ActivityRankRuleMeta> {


    public static class ActivityRankRuleMeta extends AbstractMeta implements IActivityRankRuleMeta, IGameMeta {

        @Getter
        private long scoreLimit;

        @Getter
        private int rankLimit;

        @Getter
        private int expireDay;

        @JsonDeserialize(using = StringInternDeserializer.class)
        private String rankReward;

        @Override
        public RankingConfig.RankingMeta getRankMeta() {
            return getConfig(RankingConfig.class).getMetaById(rankReward);
        }

        @Override
        public String getRankEmailContent() {
            return null;
        }
    }
}
