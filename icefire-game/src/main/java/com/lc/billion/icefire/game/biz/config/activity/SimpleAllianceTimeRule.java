package com.lc.billion.icefire.game.biz.config.activity;

import com.lc.billion.icefire.game.biz.model.alliance.affair.AllianceAffairType;
import com.lc.billion.icefire.game.biz.model.alliance.rank.Auth;
import com.lc.billion.icefire.game.biz.model.email.SystemEmailType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SimpleAllianceTimeRule implements IActivityAllianceTimeRuleMeta{

    private Auth auth;
    private boolean schedule;
    private AllianceAffairType affairType;
    private long lastMilliseconds;
    private SystemEmailType mailType;
    private String startMailNotice;
    private String firstScheduleNotice;
    private String changeScheduleNotice;
    private String cancelScheduleNotice;

}
