
package com.lc.billion.icefire.game.biz.dao;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.BindSaveThreadOperation;
import com.lc.billion.icefire.game.biz.dao.jongo.multi.MongoWriteOperation;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.buff.Buff;
import com.lc.billion.icefire.game.biz.model.libao.RoleLibaoRecord;
import com.lc.billion.icefire.game.biz.model.scene.node.NewResNode;
import com.lc.billion.icefire.game.biz.model.scene.node.NpcCenterNode;
import com.lc.billion.icefire.game.biz.model.scene.node.NpcNode;
import com.lc.billion.icefire.game.biz.model.scene.node.WorldBossNode;
import com.lc.billion.icefire.game.biz.model.soldier.SoldierInfo;
import com.lc.billion.icefire.game.biz.model.vip.RoleVip;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.game.common.CommonLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.metrics.MemoryDaoMetrics;
import com.lc.billion.icefire.game.metrics.MemoryDaoMetricsFactory;
import com.simfun.sgf.utils.JavaUtils;
import lombok.SneakyThrows;
import org.jongo.MongoCursor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 
 * 主要是在AbstractDbDao的基础上，实现在内存里缓存所有实体，实体异步落库的功能。 <code>
 * 20200708 biwei：为了实现删除老实体，保存新实体（新老实体业务主键相同，pk不同）
 *           modify集合 按 persistkey 保存save和delete操作，save和delete，按pk可以互相覆盖，以最后一个为准。
 *           clear时要及时落地，避免persistAll时，实体找不到（已经被清理了）
 * </code>
 * 
 * <AUTHOR>
 *
 */
public abstract class AbstractMemoryDao<ENTITY_TYPE extends AbstractEntity> extends AbstractDbDao<ENTITY_TYPE> {

	protected static final Logger log = LoggerFactory.getLogger(AbstractMemoryDao.class);

	@Autowired(required = false)
	protected AsyncOperationServiceImpl asyncOperationService;

	@Autowired(required = false)
	private CommonLogUtil commonLogUtil;
	// @Autowired
	// private ConfigServiceImpl configService;
    @Autowired(required = false)
    protected FunctionSwitchService functionSwitchService;

	protected ConcurrentMap<Long, BeModified> modifiedEntities = new MyConcurrentMap<>(); // 需要被保存或删除的实体Id
    protected Set<Long> savingEntityIds = ConcurrentHashMap.newKeySet(); // 正在保存的实体Id

	protected ConcurrentMap<Long, ENTITY_TYPE> entities = new MyConcurrentMap<>(); // 所有内存里的实体

	protected ConcurrentMap<ENTITY_TYPE, ENTITY_TYPE> bizKeyMap = new MyConcurrentMap<>(); // 用来进行快速业务主键检测（equals/hashcode）

	protected boolean loadAll;

	@Autowired
	private MemoryDaoMetricsFactory memoryDaoMetricsFactory;

	private MemoryDaoMetrics memoryDaoMetrics;


	protected AbstractMemoryDao(Class<ENTITY_TYPE> clazz, boolean needIdGenerator) {
		super(clazz, needIdGenerator);
	}

	@PostConstruct
	public void init() {
		// 首先调用父类的初始化逻辑
		super.init();
		// 然后初始化内存DAO的指标
		memoryDaoMetrics = memoryDaoMetricsFactory.create(this.getEntityClass(), this::getEntitySize);
	}

	public Collection<Long> findAllKeys() {
		return Collections.unmodifiableCollection(entities.keySet());
	}

	public Collection<ENTITY_TYPE> findAll() {
		return Collections.unmodifiableCollection(entities.values());
	}

	// public Set<Long> findActiveIds() {
	// return Collections.unmodifiableMap(this.entities).keySet();
	// }

	public int getEntitySize() {
		return entities.size();
	}

	public int getUnsavedEntitySize() {
		return this.modifiedEntities.size();
	}

	/**
	 * 把实体放入JVM内存
	 * 
	 * @param entity
	 */
	protected void putMemory(ENTITY_TYPE entity, boolean throwException) { // 注意：private 而不是
		// protected，是怕子类滥用，暂时不放开。
		// 所有Manager都有一个按EntityID（即PersistKey）保存的Map。先把Entity放入这个Map
		if (bizKeyMap.containsKey(entity)) {
			// 检测业务主键是否重复
			String msg = String.format("%s:%s 对应的实体（value）已经存在！（业务主键重复）%s", entity.getClass().getSimpleName(), entity, this.bizKeyMap.get(entity));
			boolean throwsExceptionIfDuplicatePrimaryKey = ServerConfigManager.getInstance().getGameConfig().isThrowsExceptionIfDuplicatePrimaryKey();
			
			if (throwsExceptionIfDuplicatePrimaryKey || throwException) {
				// if
				// (configService.getConfig(SettingConfig.class).entityDuplicateBusinessKeyCheckException())
				// {
				throw new AlertException("实体value已存在,业务主键重复","entityName",entity.getClass().getSimpleName(),
						"entity",entity, "bizKey",this.bizKeyMap.get(entity));
			} else {
				ErrorLogUtil.errorLog("实体value已存在,业务主键重复","entityName",entity.getClass().getSimpleName(),
						"entity",entity, "bizKey",this.bizKeyMap.get(entity));
			}
		}
		ENTITY_TYPE oldEntity = this.entities.putIfAbsent(entity.getPersistKey(), entity);
		if (null != oldEntity) {
			// 危险：为什么要重复放入？
			throw new AlertException("实体key已存在,数据库主键重复", "entityName",entity.getClass().getSimpleName(),
					"entity",entity, "oldEntity",oldEntity);
		}
		bizKeyMap.put(entity, entity);
		// 再放入其他方便查询的数据结构中去（比如：按Name索引的Map）
		putMemoryIndexes(entity);
		putMemoryIndexesForServerId(entity);
		//计数
		memoryDaoMetrics.recordCreate();
	}

	/**
	 * 子类应覆盖此方法。在子类所需要的额外的方便查询的数据结构中保持住Entity
	 * 
	 * @param entity
	 */
	protected abstract void putMemoryIndexes(ENTITY_TYPE entity);

	protected void putMemoryIndexesForServerId(ENTITY_TYPE entity) {

	}

	//
	// find
	//

	/**
	 * 根据ID从内存里查找实体
	 * 
	 * @param entityId
	 * @return
	 */
	@Override
	public ENTITY_TYPE findById(Long entityId) {
		if (null == entityId) {
			return null;
		}
		return this.entities.get(entityId);
	}

	//
	// create
	//
	// /**
	// * 新建一个Entity（已经有EntityID，并且放入了JVM，做了Save标记）
	// */
	// @Deprecated
	// protected ENTITY_TYPE create(int db) {
	// ENTITY_TYPE entity = super.create();
	// entity.setDB(db);
	// putMemory(entity, true);
	// save(entity);
	// return entity;
	// }

	@Override
	@Deprecated
	public ENTITY_TYPE create() {
		// 不允许直接调AbstractDbDao的create方法。
		throw new AlertException("不允许直接调用此方法");
	}

	/**
	 * 生成带自定义主键的实体
	 */
	protected ENTITY_TYPE createById(int db, int oServerId, int currentServerId, Long entityId) {
		if (null == entityId) {
			throw new NullPointerException("entityId不能为null");
		}
		ENTITY_TYPE entity = super.createById(entityId);
		entity.setDB(db);
		entity.setCurrentServerId(currentServerId);
		entity.setoServerId(oServerId);
		putMemory(entity, true);
		save(entity);
		return entity;
	}

	@Override
	@Deprecated
	public ENTITY_TYPE createById(Long entityId) {
		// 不允许直接调AbstractDbDao的create方法。
		throw new AlertException("不允许直接调用此方法");
	}

	/**
	 * 索引结构key需要预先给值再内存管理 没有id补齐一个
	 */
	protected ENTITY_TYPE createEntity(int db, int oServerId, int currentServerId, ENTITY_TYPE entity) {
		if (null == entity) {
			throw new NullPointerException("实体不能为null");
		}
		if (entity.getPersistKey() == null) { // 所有实体ID都应该是Object形式
			entity.setPersistKey(nextEntityId());
		}
		entity.setDB(db);
		entity.setCurrentServerId(currentServerId);
		entity.setoServerId(oServerId);
		entity.setCreateTime(TimeUtil.getNow());
		putMemory(entity, true);
		save(entity);
		return entity;
	}

	//
	// save
	//

	/**
	 * 保存实体，给业务逻辑用（不允许Delete后再Save）
	 * 
	 * @param entity
	 */
	@Override
	public void save(ENTITY_TYPE entity) {
		if (null == entity) {
			throw new NullPointerException("实体不能为null");
		}
		if (!this.entities.containsKey(entity.getPersistKey())) {
			throw new AlertException("实体不在entities里,很可能是new了一个实体但是还没create就save,这是不允许的","entityName",entity.getClass().getSimpleName(),"entity",entity);
		}
		if (entity.getDB() <= 0) {
			throw new AlertException("实体持久化之前必须设置DB属性","entity",entity);
		}
		// 只是做标记，以便后续SaveLoop中执行保存。
		BeModified old = this.modifiedEntities.put(entity.getPersistKey(), new BeModified(Modify.SAVE, entity)); // 此时如果之前modifyedEntitys中存在该entityId：若原来是DELETE，继续SAVE（注意已经冲掉了原来的DELETE）没问题。若原来是SAVE，继续SAVE也没问题。
		if (old != null && old.getOperator() == Modify.DELETE && old.getBeModified().getDB() != entity.getDB()) {
			/**
			 * 我们发现线上起服出现了主键冲突，现象大概是这样：一个玩家退出了A服的联盟然后迁入同一个JVM的B服。然后一分钟之内立刻加入了联盟。
			 * A服的allianceMember要执行delete操作，
			 * B服的ALLianceMember执行save操作把A服的delete的操作覆盖掉了，导致A服的mongodb里存在一个错误的allianceMember副本
			 * 然后起服的时候allianceMember主键冲突了原因是加载到了A服的脏数据
			 */
			log.info("[主键] delete被save覆盖 entityId:{} oldDb:{}, newDb:{}", entity.getPersistKey(), old.getBeModified().getDB(), entity.getDB());
			asyncOperationService.execute(new BindSaveThreadOperation(old.getBeModified().getGroupingId()) {
				@Override
				protected void save() {
					dbDelete(old.getBeModified());
				}
			});
		}
		memoryDaoMetrics.recordUpdate();
		// this.modifiedEntities.compute(entity.getPersistKey(), (key, value) -> {
		// if (value == null) {
		// return Modify.SAVE;
		// } else if (value == Modify.SAVE) {
		// return Modify.SAVE;
		// } else {
		// log.warn(String.format("save:实体%s为什么会先Delete后再Save:%s",
		// entity.getClass().getName(), entity.toString()));
		// return Modify.SAVE;
		// }
		// });
	}

	/**
	 * 实现立即保存（相对于上面的定时保存的save()来说）
	 * 
	 * @param entity
	 */
	public void saveImmediately(ENTITY_TYPE entity) {
		if (null == entity) {
			throw new NullPointerException("实体不能为null");
		}
		if (!this.entities.containsKey(entity.getPersistKey())) {
			throw new AlertException("实体不在entities里,很可能是new了一个实体但是还没create就save,这是不允许的","entityName",entity.getClass().getSimpleName(),"entity",entity);
		}
		if (entity.getDB() <= 0) {
			throw new AlertException("实体持久化之前必须设置DB属性","entityName",entity.getClass().getSimpleName(),"entity",entity);
		}
		// 清理掉定期保存标志。
		this.modifiedEntities.remove(entity.getPersistKey()); // 此时如果之前modifyedEntitys中存在该entityId：若原来是DELETE，继续SAVE（注意已经冲掉了原来的DELETE）没问题。若原来是SAVE，继续SAVE也没问题。
		// this.modifiedEntities.compute(entity.getPersistKey(), (key, value) -> {
		// if (value == null) {
		// return null;
		// } else if (value == Modify.SAVE) {
		// return null;
		// } else {
		// throw new
		// RuntimeException(String.format("saveImmediately:实体%s不允许Delete后再Save:%s",
		// entity.getClass().getName(), entity.toString()));
		// }
		// }); //
		// 此时如果之前modifyedEntitys中存在该entity：若原来是SAVE，继续SAVE没问题。若原来是DELETE，不允许继续SAVE。
		// this.modifiedEntities.remove(entity); //
		// 此时如果之前modifyedEntitys中存在该entity：若原来是SAVE，清理掉没问题。若原来是DELETE，清理掉应该也没问题。

		// 让SaveLoop立即执行。
		asyncOperationService.execute(new BindSaveThreadOperation(entity.getGroupingId()) {
			@Override
			protected void save() {
				dbSave(entity);
			}
		});
	}

	/**
	 * 把隐藏掉的父类方法（dbSave）放到这里。
	 * 
	 * 改成protected，因为RoleServerInfo需要直接操作库
	 * 
	 * @param
	 * @return
	 */
	protected void dbSave(ENTITY_TYPE entity) { // 注意：private而不是protected，是怕子类滥用，暂时不放开。
		if (null == entity) {
			throw new NullPointerException("实体不能为null");
		}
		super.save(entity);
	}

	//
	// delete
	//

	@Override
	public void delete(ENTITY_TYPE entity) {
		if (entity == null) {
			ErrorLogUtil.errorLog("删除实体对象为空","实体对象", getCollectionName());
			return;
		}
		this.delete(entity.getPersistKey());
	}

	/**
	 * 删除实体
	 * 
	 * @param entityId
	 */
	public void delete(Long entityId) {
		if (null == entityId) {
			log.warn("{} entityId为null，不予处理。", this.getClass().getSimpleName());
			return;
		}
		ENTITY_TYPE entity = this.entities.get(entityId);
		if (null == entity) {
			log.warn("{} {} 实体不在entities里，不予处理。", this.getClass().getSimpleName(), entityId);
			return;
		}
		if (entity.getDB() <= 0) {
			throw new AlertException("实体删除之前必须设置DB属性","entityName",entity.getClass().getSimpleName(),"entity",entity);
		}
		// 先从JVM中删除。
		removeMemory(entityId);
		// 然后做标记，以便后续SaveLoop中执行删除。
		this.modifiedEntities.put(entityId, new BeModified(Modify.DELETE, entity)); // 此时如果之前modifyedEntitys中存在该entityId：若原来是SAVE，继续DELETE（注意已经冲掉了原来的SAVE）没问题。若原来是DELETE，继续DELETE也没问题。
		if (!(entity instanceof NpcNode)
				// && !(entity instanceof ResNode)
				&& !(entity instanceof WorldBossNode) && !(entity instanceof NpcCenterNode) && !(entity instanceof RoleLibaoRecord) && !(entity instanceof Buff)
				// && !(entity instanceof ArmyInfo)
				&& !(entity instanceof NewResNode)) {
			log.warn("{}对应实体删除{} ", this.getClass().getSimpleName(), entity.getPersistKey());
		}
		if (entity instanceof SoldierInfo || entity instanceof RoleVip) {
			ErrorLogUtil.errorLog("玩家居然要删除,密切关注","key",entity.getPersistKey(),"name",entity.getClass().getSimpleName());
			// throw new NullPointerException("玩家" + entityId + "居然要删除" +
			// entity.getClass().getSimpleName() + "，大问题");
		}
		//计数
		memoryDaoMetrics.recordDelete();
	}

	/**
	 * 实现立即删除（相对于上面的定时保存的delete()来说）
	 * 
	 * @param
	 */
	public void deleteImmediately(Long entityId) {
		if (null == entityId) {
			log.warn("{} entityId为null，不予处理。", this.getClass().getSimpleName());
			return;
		}
		ENTITY_TYPE entity = this.entities.get(entityId);
		if (null == entity) {
			log.warn("{} {} 实体不在entities里，不予处理。", this.getClass().getSimpleName(), entityId);
			return;
		}
		if (entity.getDB() <= 0) {
			throw new AlertException("实体删除之前必须设置DB属性","entityName",entity.getClass().getSimpleName(),"entity",entity);
		}
		// 先从JVM中删除。
		removeMemory(entityId);
		// 清理掉定期删除标志。
		this.modifiedEntities.remove(entityId); // 此时如果之前modifyedEntitys中存在该entityId：若原来是SAVE，继续DELETE（注意已经冲掉了原来的SAVE）没问题。若原来是DELETE，继续DELETE也没问题。
		// 让SaveLoop立即执行。
		asyncOperationService.execute(new BindSaveThreadOperation(entity.getGroupingId()) {
			@Override
			protected void save() {
				dbDelete(entity);
			}
		});
	}

	/**
	 * 从JVM内存中删除实体
	 * 
	 * @param entityId
	 * @return
	 */
	private ENTITY_TYPE removeMemory(Long entityId) { // 注意：private 而不是
														// protected，是怕子类滥用，暂时不放开。
														// 先从Map<EntityId, Entity>中，删除。
		ENTITY_TYPE entity = this.entities.remove(entityId);
		bizKeyMap.remove(entity);
		// 然后从其他方便查询的数据结构中，删除。
		this.removeMemoryIndexes(entity);
		removeMemoryIndexesForServerId(entity);
		return entity;
	}

	/**
	 * 子类应覆盖此方法。在这里执行从其他方便查询的数据结构中删除Entity的操作。
	 * 
	 * @param
	 */
	protected abstract void removeMemoryIndexes(ENTITY_TYPE entity);

	protected void removeMemoryIndexesForServerId(ENTITY_TYPE entity) {

	}

	/**
	 * 把隐藏掉的父类方法（dbSave）放到这里。
	 * 
	 * @param entity
	 */
	private void dbDelete(ENTITY_TYPE entity) { // 注意：private而不是protected，是怕子类滥用，暂时不放开。
		super.delete(entity);
	}

	/**
	 * 采用批量落盘，持久化所有有变化的和删除了的Entity
	 *
	 * @param asynchronousSave 是否异步回存
	 */
	public void persistAllBatch(boolean asynchronousSave) {
        // 异步保存优化开关
        boolean isAsyncSaveOptimize = asynchronousSave && functionSwitchService.isOpen(FunctionType.MONGO_ASYNC_SAVE_OPTIMIZE);

		Set<Long> allEntityIds = this.modifiedEntities.keySet();
		// 获取DB线程数量
		int dbThreadCount = asyncOperationService.getUsingSaveThredSize();
		// 将需要落盘的实体先按db分组，再按id和线程数取模分组
		Map<String, Map<Integer, Set<Long>>> dbEntityIds = new HashMap<>();
		for (Long entityId : allEntityIds) {

            // 如果已经在保存中，则跳过，避免重复提交落盘异步任务，造成任务堆积
            if (isAsyncSaveOptimize && this.savingEntityIds.contains(entityId)) {
                continue;
            }

			BeModified beModified = this.modifiedEntities.get(entityId);
			ENTITY_TYPE entity = beModified.getBeModified();
			String db = String.valueOf(entity.getDB());
			Map<Integer, Set<Long>> dbIds = dbEntityIds.computeIfAbsent(db, k -> new HashMap<>());
			// 这里必须要按id取模再分一次组，确保批量落盘和瞬时落盘的同一个实体能落到一个db线程上，避免数据被覆盖
			int threadIndex = (int) (entityId % dbThreadCount);
			Set<Long> entityIds = dbIds.computeIfAbsent(threadIndex, k -> new HashSet<>());
			entityIds.add(entityId);
		}
		// 将DB的分组
		for (Map.Entry<String, Map<Integer, Set<Long>>> entry : dbEntityIds.entrySet()) {
			Map<Integer, Set<Long>> dbIds = entry.getValue();
			// 按id和线程数取模的分组
			for (Map.Entry<Integer, Set<Long>> dbId : dbIds.entrySet()) {
				Set<Long> entityIds = dbId.getValue();
				// 这里50是在仿真测试的经验值，批量落盘的数量过大，可能会导致：
				// 1. Mongo单次请求过大；
				// 2. 文档越多，并发修改概率越大，并发修改导致的序列化异常，会导致大量文档一起落盘失败
				// 3. 数据库瞬间压力过大
				List<List<Long>> entityIdGroups = JavaUtils.parseCollection(entityIds, 50);
				for (int i = 0; i < entityIdGroups.size(); i++) {
					List<Long> entityIdGroup = entityIdGroups.get(i);
					// 捕捉异常，保证后续其他批次的实体能正常落盘
					try {
						if (asynchronousSave) {
                            // 记录正在保存的实体id
                            if (isAsyncSaveOptimize) {
                                this.savingEntityIds.addAll(entityIdGroup);
                            }
							asyncOperationService.execute(new BindSaveThreadOperation((long) i) {
								@Override
								protected void save() {
                                    if (isAsyncSaveOptimize) {
                                        entityIdGroup.forEach(savingEntityIds::remove);
                                    }
									flushBatchImmediately(entry.getKey(), entityIdGroup);
								}
							});
						} else {
							flushBatchImmediately(entry.getKey(), entityIdGroup);
						}
					} catch (Throwable t) {
						ErrorLogUtil.exceptionLog("persistAllBatch过程中报错", t);
					}
				}
			}
		}
		// 记录落盘数量
		if (!allEntityIds.isEmpty()) {
			commonLogUtil.modifiedEntityCount(this.getCollectionName(), allEntityIds.size());
		}
	}

	/**
	 * 持久化所有有变化的和删除了的Entity。
	 * 
	 * @param asynchronousSave
	 *            是否异步回存
	 */
    public void persistAll(boolean asynchronousSave) {
		Set<Long> entityIds = this.modifiedEntities.keySet();
        persistSome(asynchronousSave, entityIds);
    }

	public void persistSome(boolean asynchronousSave, Set<Long> entityIds) {
		List<BeModified> failed = new ArrayList<>();
		for (Long entityId : entityIds) {
			BeModified beModified = this.modifiedEntities.get(entityId);
			ENTITY_TYPE entity = beModified.getBeModified();
			try {
				if (asynchronousSave) {
					asyncOperationService.execute(new BindSaveThreadOperation(entity.getGroupingId()) {
						@Override
						protected void save() {
							BeModified beModified = modifiedEntities.get(entityId);
							try {
								flushImmediately(entityId);
							} catch (Exception e) {
								if (beModified != null) {
									if (!(e instanceof ExpectedException)) {
										ErrorLogUtil.exceptionLog("async persist过程中报错", e, "id", entityId, "name", getCollectionName());
									}
									// 异步存储 如果抛出异常~重新save~增加重试~下一个tick继续处理
									if (beModified.getOperator() == Modify.SAVE) {
										AbstractMemoryDao.this.save(beModified.getBeModified());
									} else if (beModified.getOperator() == Modify.DELETE) {
										if (!(e instanceof ExpectedException)) {
											ErrorLogUtil.errorLog("异步重置删除操作报错", "key", beModified.getBeModified().getPersistKey(), "name", beModified.getBeModified().getCollectionName());
										}
									}
								}
								if (e instanceof IllegalArgumentException) {
									// mongodb写入序列化异常特殊处理防止聚合类型过多,asBsonDocument()方法会抛出异常
									// 重新new是为了不连接e.getMessage()但是要堆栈
									throw new AlertException("数据库save对象序列化异常,多数为并发修改导致", e, "entityName", entity.getClass().getSimpleName());
								}
								throw e;
							}
						}
					});
				} else {
					flushImmediately(entityId);
				}
			} catch (Throwable t) {
				ErrorLogUtil.exceptionLog("persist过程中报错",t, "id",entityId, "name",getCollectionName()); // 这个异常必须吃掉，保证后续的其他Entity的sava／delete都能正常进行。
				// 存储出错后重新save一遍~下次存储再处理
				failed.add(beModified);
			}
		}
		// 同步存储 如果出现异常了~重新save~增加重试~下一个tick继续处理
		failed.forEach(modified -> {
			if (modified.getOperator() == Modify.SAVE) {
				save(modified.getBeModified());
			} else if (modified.getOperator() == Modify.DELETE) {
				ErrorLogUtil.errorLog("同步重置删除操作报错", "实体持久化key",modified.getBeModified().getPersistKey(),"实体名字", modified.getBeModified().getCollectionName());
			}
		});
		// 记录大小>0的entity落盘日志
		if (!entityIds.isEmpty()) {
			commonLogUtil.modifiedEntityCount(this.getCollectionName(), entityIds.size());
		}
	}

	@SuppressWarnings("unchecked")
	protected void flushBatchImmediately(String db, List<Long> entityIds) {
		List<MongoWriteOperation<ENTITY_TYPE>> operations = new ArrayList<>();
		for (Long entityId : entityIds) {
			BeModified beModified = modifiedEntities.remove(entityId);
			if (beModified == null) {
				// 可能已经被 deleteImmediately、saveImmediately 处理过了
			} else if (beModified.getOperator() == Modify.SAVE || beModified.getOperator() == Modify.DELETE) {
				MongoWriteOperation<ENTITY_TYPE> operation = new MongoWriteOperation<>(beModified.getOperator(), entityId, beModified.getBeModified());
				operations.add(operation);
			} else {
				ErrorLogUtil.errorLog("flushBatchImmediately过程中报错", new UnsupportedOperationException("未知的modify操作"), "modified", beModified);
			}
		}
		if (operations.isEmpty()) {
			return;
		}
		// 如果出现异常了，重新save，增加重试，下一个tick继续处理
		try {
			super.bulkWrite(db, operations.toArray(new MongoWriteOperation[0]));
		} catch (Exception e) {
			// 数据库save对象序列化异常,多数为并发修改导致,故直接重试
			if (e instanceof IllegalArgumentException) {
                try {
                    ErrorLogUtil.exceptionLog("数据库save对象序列化异常,立即重试", e);
                    super.bulkWrite(db, operations.toArray(new MongoWriteOperation[0]));
                } catch (Exception ex) {
                    handleBulkWriteException(ex, operations);
                }
                // 这一段处理先注掉。因为经过研究发现，这个异常报出是因为单个文档超过限制，所以加了这个也没用。后面如果有合批超限的情况再说。
//            } else if (e instanceof BsonMaximumSizeExceededException) {
//                // 文档过大异常特殊处理
//                try {
//                    ErrorLogUtil.exceptionLog("合批写数据库时文档过大异常, 尝试单个写入", e);
//                    Set<Long> entityIdSet = new HashSet<>(entityIds);
//                    persistSome(true, entityIdSet);
//                } catch (Exception ex) {
//                    handleBulkWriteException(ex, operations);
//                }
			} else {
				handleBulkWriteException(e, operations);
			}
		}
	}

	@SneakyThrows
	private void handleBulkWriteException(Exception e, List<MongoWriteOperation<ENTITY_TYPE>> operations) {
		if (!(e instanceof ExpectedException)) {
			ErrorLogUtil.exceptionLog("flushBatchImmediately过程中报错", e);
		}
		for (MongoWriteOperation<ENTITY_TYPE> operation : operations) {
			BeModified beModified = new BeModified(operation.getType(), operation.getPojo());
			modifiedEntities.put(operation.getId(), beModified);
		}
		if (e instanceof IllegalArgumentException) {
			// mongodb写入序列化异常特殊处理防止聚合类型过多,asBsonDocument()方法会抛出异常
			// 重新new是为了不连接e.getMessage()但是要堆栈
			throw new AlertException("数据库save对象序列化异常,多数为并发修改导致", e);
		}
		throw e;
	}

	protected void flushImmediately(Long entityId) {
		BeModified beModified = modifiedEntities.remove(entityId);
		if (beModified == null) {
			// 可能已经被 deleteImmediately、saveImmediately 处理过了
		} else if (beModified.getOperator() == Modify.SAVE) {
			dbSave(beModified.getBeModified());
		} else if (beModified.getOperator() == Modify.DELETE) {
			dbDelete(beModified.getBeModified());
		} else {
			ErrorLogUtil.errorLog("flushImmediately过程中报错",new UnsupportedOperationException("未知的modify操作"),"modified",beModified);
		}
	}

	//
	// 工具方法。
	//

	/**
	 * 调用警告
	 * 
	 * @param info
	 */
	protected void invokingWarning() {
		ErrorLogUtil.errorLog("Dao中这个方法不该被调用",new RuntimeException());
	}

	/**
	 * 调用警告
	 * 
	 * @param info
	 */
	protected void invokingWarning(String info) {
		String errorInfo = "Dao中这个方法不该被调用(不会中断，仅提示，打印堆栈)";
		if (info != null) {
			errorInfo = info;
		}
		ErrorLogUtil.errorLog("Dao中这个方法不该被调用",new RuntimeException());
	}

	public enum Modify {
		SAVE, DELETE
	}

	public class BeModified {
		private Modify operator;
		private ENTITY_TYPE beModified;

		public Modify getOperator() {
			return operator;
		}

		public ENTITY_TYPE getBeModified() {
			return beModified;
		}

		public BeModified(Modify operator, ENTITY_TYPE beModified) {
			super();
			this.operator = operator;
			this.beModified = beModified;
		}

		@Override
		public String toString() {
			return "BeModified [operator=" + operator + ", beModified=" + beModified + "]";
		}
	}

	@SuppressWarnings("unchecked")
	public void delete(Collection<ENTITY_TYPE> entities) {
		if (JavaUtils.bool(entities)) {
			for (Object entity : entities.toArray()) {
				ENTITY_TYPE entity_TYPE = (ENTITY_TYPE) entity;
				delete(entity_TYPE);
			}
		}
	}

	public boolean isLoadAll() {
		return loadAll;
	}

	protected void ensureIndex(String keys) {
		super.ensureIndex(configCenter.getCurrentGameServerConfig().getGameServerId(), keys);
	}

	protected void ensureIndex(String keys, String options) {
		super.ensureIndex(configCenter.getCurrentGameServerConfig().getGameServerId(), keys, options);
	}

	//
	// helper method for subclass
	//

	/**
	 * 根据主键id获得数据
	 * 
	 * @param groupName
	 *            条件列
	 * @param id
	 * @return
	 */
	protected MongoCursor<ENTITY_TYPE> dbFindByPrimaryId(int db, Long id) {
		return dbFind(db, "_id", id);
	}

	protected MongoCursor<ENTITY_TYPE> dbFindByPrimaryIds(int db, List<Long> ids) {
		return dbFind(db, "_id", ids);
	}

	protected MongoCursor<ENTITY_TYPE> dbFindByRoleId(int db, Long roleId) {
		return dbFind(db, "roleId", roleId);
	}

	protected MongoCursor<ENTITY_TYPE> dbFindByRoleIds(int db, List<Long> roleIds) {
		return dbFind(db, "roleId", roleIds);
	}

	protected MongoCursor<ENTITY_TYPE> dbFindByPlayerId(int db, Long playerId) {
		return dbFind(db, "playerId", playerId);
	}

	protected MongoCursor<ENTITY_TYPE> dbFindByPlayerIds(int db, List<Long> playerIds) {
		return dbFind(db, "playerId", playerIds);
	}

	protected MongoCursor<ENTITY_TYPE> dbFindByAllianceId(int db, Long allianceId) {
		return dbFind(db, "allianceId", allianceId);
	}

	protected MongoCursor<ENTITY_TYPE> dbFindByAllianceIds(int db, List<Long> allianceIds) {
		return dbFind(db, "allianceId", allianceIds);
	}

	public long count() {
		return super.count(configCenter.getCurrentGameServerConfig().getGameServerId());
	}

	public void setLoadAll(boolean loadAll) {
		this.loadAll = loadAll;
	}

	public ENTITY_TYPE createForTest() {
		return newEntityInstance();
	}



}
