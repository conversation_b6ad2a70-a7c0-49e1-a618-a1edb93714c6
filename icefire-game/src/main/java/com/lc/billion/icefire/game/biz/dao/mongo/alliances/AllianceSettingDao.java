package com.lc.billion.icefire.game.biz.dao.mongo.alliances;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.AllianceConfig;
import com.lc.billion.icefire.game.biz.dao.AlliancesEntityDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceSetting;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class AllianceSettingDao extends AlliancesEntityDao<AllianceSetting> {
//    @Autowired
//    private ConfigServiceImpl configService;
//    @Autowired
//    private ServerInfoServiceImpl serverInfoService;
//    private ServiceDependency srvDpd;

    public AllianceSettingDao() {
        super(AllianceSetting.class, false);
    }

    @Override
    protected MongoCursor<AllianceSetting> doFindAll(int db, List<Long> roleIds) {
        return dbFindByPrimaryIds(db, roleIds);
    }

    @Override
    protected void putMemoryIndexes(AllianceSetting entity) {

    }

    @Override
    protected void removeMemoryIndexes(AllianceSetting entity) {

    }

    public AllianceSetting create(Alliance alliance, int requestLimitFightPower) {
        AllianceSetting allianceSetting = newEntityInstance();
        allianceSetting.setAllianceId(alliance.getPersistKey());
        allianceSetting.setRequestLimitFightPower(requestLimitFightPower);
        initInactiveManagement(allianceSetting); // 清理不活跃的默认值
        initRankRequestSetting(allianceSetting); // 官职申请的默认值
        return createEntity(alliance, allianceSetting);
    }

    private void initRankRequestSetting(AllianceSetting setting) {
        ServiceDependency srvDpd = Application.getBean(ServiceDependency.class);
        AllianceConfig allianceCfg = srvDpd.getConfigService().getConfig(AllianceConfig.class);
        if (allianceCfg == null) {
            return;
        }
        setting.setMinFightPowerRankRequest(allianceCfg.getApplyPowerCondition());
        setting.setMinCityLevelRankRequest(allianceCfg.getApplyLevelCondition());
    }

    public boolean useBeforeConfig() {
        ServiceDependency srvDpd = Application.getBean(ServiceDependency.class);
        AllianceConfig allianceCfg = srvDpd.getConfigService().getConfig(AllianceConfig.class);
        if (allianceCfg == null) {
            return false;
        }
        long serverOpenTime = srvDpd.getServerInfoService().getServerOpenTime();
        int deltaDays = TimeUtil.betweenDays(TimeUtil.getNow(), serverOpenTime) + 1;
        return deltaDays <= allianceCfg.getLoginKickCondition(); // 开服时间小于配置指定的天数
    }

    private void initInactiveManagement(AllianceSetting setting) {
        ServiceDependency srvDpd = Application.getBean(ServiceDependency.class);
        AllianceConfig allianceCfg = srvDpd.getConfigService().getConfig(AllianceConfig.class);
        if (allianceCfg == null) {
            return;
        }
        boolean useBefore = useBeforeConfig();
        setting.setInactiveAutoKick(true);
        setting.setMinFightPower(allianceCfg.getKickPowerCondition(useBefore));
        setting.setMinCityLevel(allianceCfg.getKickLevelCondition(useBefore));
        setting.setMinOfflineDuration(allianceCfg.getMinOfflineIndex(useBefore));
        setting.setMinRank(allianceCfg.getKickRankCondition(useBefore));
    }

    public void loadAllianceFromOtherDB(int db, Long allianceId) {
        AllianceSetting allianceSetting = findById(allianceId);
        if (allianceSetting != null) {
            log.info("allianceSetting 联盟已加载{}", allianceId);
            return;
        }
        long start = TimeUtil.getNow();
        // 联盟的db不是全用本服的，GVG战斗服需要拉取别的服的联盟，db设置为原服的，并且不回存，联盟下挂的信息包括member、tech等同样需要处理
        MongoCursor<AllianceSetting> mongoCursor = dbFindByPrimaryId(db, allianceId);
        Collection<AllianceSetting> dbFindAll = transform(mongoCursor);
        for (AllianceSetting allianceSetting1 : dbFindAll) {
            allianceSetting1.setDB(db); // 只是 @JsonIgnore
            allianceSetting1.setoServerId(db);
            allianceSetting1.setCurrentServerId(Application.getServerId());
            putMemory(allianceSetting1, false); // 启动服务器时不抛异常
        }
        log.info("{} 从数据库 {} 共加载活跃的实体 {} 个，消耗 {} 毫秒", this.getClass().getSimpleName(), db, dbFindAll.size(), TimeUtil.getNow() - start);
    }
}
