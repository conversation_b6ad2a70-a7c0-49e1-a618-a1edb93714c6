package com.lc.billion.icefire.game.biz.dao.mongo.roles;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.biz.config.ItemConfig;
import com.lc.billion.icefire.game.biz.config.ItemConfig.ItemMeta;
import com.lc.billion.icefire.game.biz.dao.RolesEntityDao;
import com.lc.billion.icefire.game.biz.model.item.*;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import org.jongo.MongoCursor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR>
 *
 */
@Repository
public class ItemDao extends RolesEntityDao<Item> {

	@Autowired
	private ConfigServiceImpl configSrv;

	private ConcurrentMap<Long, Map<BagType, AbstractBag>> bagMap = new MyConcurrentMap<>();

	private ConcurrentMap<Long, List<Item>> dataByRoleId = new MyConcurrentMap<>();

	protected ItemDao() {
		super(Item.class, true);
	}

	@Override
	protected MongoCursor<Item> doFindByPlayerId(int db, Long playerId) {
		return dbFindByPlayerId(db, playerId);
	}

	@Override
	protected MongoCursor<Item> doFindAll(int db, List<Long> playerIds) {
		return dbFindByPlayerIds(db, playerIds);
	}

	@Override
	protected void putMemoryIndexes(Item entity) {
		dataByRoleId.compute(entity.getRoleId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
		Map<BagType, AbstractBag> roleBagMap = bagMap.get(entity.getRoleId());
		if (roleBagMap == null) {
			roleBagMap = new EnumMap<>(BagType.class);
			bagMap.put(entity.getRoleId(), roleBagMap);
		}
		ItemMeta itemMeta = configSrv.getConfig(ItemConfig.class).get(entity.getMetaId());
		if (itemMeta == null) {
			ErrorLogUtil.errorLog("ItemMeta not exists", "itemId",entity.getPersistKey());
			return;
		}
		BagType bagType = itemMeta.getType().getBagType();
		AbstractBag bag = roleBagMap.get(bagType);
		if (bag == null) {
			bag = new DynamicBag(bagType, ItemUtils.DEFUALT_DYNAMIC_BAG_CAPACITY);
			roleBagMap.put(bagType, bag);
		}
		int index = bag.getEmptySlotForAdd(itemMeta);
		if (index == ItemUtils.INVALID_INDEX) {
			ErrorLogUtil.errorLog("Player Bag Full", "uid",entity.getRoleId());
			return;
		}
		bag.addItem(entity, index);

	}

	@Override
	protected void removeMemoryIndexes(Item entity) {
		if (entity == null) {
			return;
		}
		dataByRoleId.compute(entity.getRoleId(), (k, v) -> v == null ? new ArrayList<>() : v).remove(entity);
		try {
			var roleBagData = bagMap.get(entity.getRoleId());
			if (roleBagData != null) {
				var bag = roleBagData.get(entity.getBagType());
				if (bag != null) {
					bag.removeItem(entity.getBagIndex());
				}
			}
//			AbstractBag bag = bagMap.get(entity.getRoleId()).get(entity.getBagType());
//			bag.removeItem(entity.getBagIndex());
		} catch (ExpectedException ignored) {

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("player bag not exists", e,"uid",entity.getRoleId(), "itemId",entity.getPersistKey());
		}
	}

	@Override
	protected Collection<Long> clearMemoryIndexes(Long playerId) {
		if (null == playerId) {
			throw new NullPointerException("playerId不能为null");
		}
		// 清空背包
		// 从内存中取数据
		HashSet<Long> ret = new LinkedHashSet<>();
		List<Item> remove = dataByRoleId.remove(playerId);
		if (remove != null) {
			Set<Long> items = remove.stream().map(Item::getPersistKey).collect(Collectors.toSet());
			ret.addAll(items);
		}
		Map<BagType, AbstractBag> removed = bagMap.remove(playerId);
		// map可能为空
		if (removed != null) {
			for (AbstractBag bag : removed.values()) {
				Set<Long> items = bag.getItems().stream().map(Item::getPersistKey).collect(Collectors.toSet());
				ret.addAll(items);
			}
		}
		return ret;
	}

	@Override
	public Collection<Item> findByRoleId(Long roleId) {
		List<Item> list = dataByRoleId.get(roleId);
		return list == null ? Collections.emptyList() : list;
	}

	/**
	 * 根据roleId获取玩家背包
	 * 
	 * @param playerId
	 * @return
	 */
	public Map<BagType, AbstractBag> findBagMapByPlayerId(long playerId) {
		return bagMap.get(playerId);
	}

	// /**
	// * 根据roleId获取玩家所拥有的道具
	// * @param playerId
	// * @return
	// */
	// public Collection<Item> findItemsByPlayerId(long playerId) {
	// Collection<Item> items = findAll();
	// Collection<Item> playerItems = new ArrayList<>();
	// for (Item item : items) {
	// if (item.getRoleId() == playerId) {
	// playerItems.add(item);
	// }
	// }
	// return playerItems;
	// }

	/**
	 * 创建item
	 * 
	 * @param role
	 * @param itemMeta
	 * @param count
	 * @return
	 */
	public Item createItem(Role role, ItemMeta itemMeta, int count) {
		if (itemMeta == null) {
			throw new NullPointerException("itemMeta is null");
		}
		if (count <= 0) {
			throw new AlertException("count <= 0","count",count);
		}

		Item item = newEntityInstance();
		item.setRoleId(role.getId());
		item.setMetaId(itemMeta.getId());
		item.setCount(count);

		// 2020-3-31 目前没有时限的道具暂时注掉
		// if (itemMeta.getTime() != 0) {
		// item.setExpireTime(TimeUtil.getNow() + itemMeta.getTime() * Time.MIN);
		// }
		return createEntity(role, item);
	}

	/**
	 * 获取背包
	 * 
	 * @return
	 */
	public ConcurrentMap<Long, Map<BagType, AbstractBag>> getBagMap() {
		return bagMap;
	}

	@Override
	public boolean copy(Role role, Long sourceRoleId, Long targetRoleId) {
		// 先获取所有的fromRole的数据，然后获取toRole的数据，删除toRoleId的数据，重新创建
		try {
			Collection<Item> fromItems = findByRoleId(sourceRoleId);
			clearMemoryIndexes(targetRoleId);
			for (Item item : fromItems) {
				Item newItem = createItem(
						role,
						configSrv.getConfig(ItemConfig.class).get(item.getMetaId()),
						item.getCount());
				copySimpleProperties(item, newItem);
			}
		} catch (Exception e) {
			if (!(e instanceof ExpectedException)) {
				ErrorLogUtil.exceptionLog(e);
			}
			e.printStackTrace();
			return false;
		}
		return true;
	}
}
