package com.lc.billion.icefire.game.biz.dao.mongo.roles;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.ExploreEventConfig;
import com.lc.billion.icefire.game.biz.config.NewResRefreshConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KvkNewResRefreshConfig;
import com.lc.billion.icefire.game.biz.dao.RolesEntityDao;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.worldExploreEvent.EventGroupType;
import com.lc.billion.icefire.game.biz.model.worldExploreEvent.EventStatus;
import com.lc.billion.icefire.game.biz.model.worldExploreEvent.EventType;
import com.lc.billion.icefire.game.biz.model.worldExploreEvent.PlayerWorldExploreEvent;
import com.longtech.ls.config.ServerType;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.ConcurrentMap;

/**
 * @author: huyafei
 * @Date: 2021/8/5 4:26 下午
 * @Description: 世界探索事件dao
 **/
@Repository
public class PlayerWorldExploreEventDao extends RolesEntityDao<PlayerWorldExploreEvent> {
    /**
     * 所有事件的管理 key 代表玩家
     */
    protected ConcurrentHashMap<Long, ConcurrentHashMap<EventGroupType, ConcurrentHashMap<PlayerWorldExploreEvent, Boolean>>> playerWorldExploreEventGroupMap = new ConcurrentHashMap<>();
    /**
     * 所有事件点的管理 TODO 策划需求，事件点，不再是唯一的啦，之前做的是唯一的，key是当前服务Id value是所有事件坐标点 TODO 需要修改为
     * 事件点挂在玩家身上，那么此处key 修改为 玩家roleId 即可 value是，玩家自己所有事件坐标点
     */
    private final ConcurrentHashMap<Long, ConcurrentHashMap<Point, PlayerWorldExploreEvent>> eventResourceZoneManagerMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, ConcurrentMap<Point, ConcurrentLinkedDeque<PlayerWorldExploreEvent>>> pointToPlayerWorldExploreEvent = new ConcurrentHashMap<>();

    protected PlayerWorldExploreEventDao() {
        super(PlayerWorldExploreEvent.class, true);
    }

    @Override
    protected MongoCursor<PlayerWorldExploreEvent> doFindByPlayerId(int db, Long playerId) {
        return dbFindByRoleId(db, playerId);
    }

    @Override
    protected MongoCursor<PlayerWorldExploreEvent> doFindAll(int db, List<Long> playerIds) {
        return dbFindByRoleIds(db, playerIds);
    }

    @Override
    protected Collection<Long> clearMemoryIndexes(Long playerId) {
        /**
         * 玩家被清理出内存，这个变量需要置位true 因为这个玩家下次重新 load 到内存，需要将相应的事件点存储
         */
        ConcurrentHashMap<EventGroupType, ConcurrentHashMap<PlayerWorldExploreEvent, Boolean>> _map = getPlayerWorldExploreEventGroup(playerId);
        /**
         * PlayerWorldExploreEvent 主键表的记录
         */
        List<Long> entitys = new ArrayList<>();
        if (_map != null && !_map.isEmpty()) {
            Collection<ConcurrentHashMap<PlayerWorldExploreEvent, Boolean>> values = _map.values();
            values.forEach(eventMap -> {
                if (eventMap != null && !eventMap.isEmpty()) {

                    // 删除点映射
                    eventMap.keySet().forEach(this::removeWorldExploreEvent);

                    eventMap.keySet().forEach(event -> entitys.add(event.getPersistKey()));
                }
            });
        }
        var remove = playerWorldExploreEventGroupMap.remove(playerId);
        if (remove != null && !remove.isEmpty()) {
            for (var entry : remove.entrySet()) {
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    entry.getValue().clear();
                }
            }
        }
        ConcurrentHashMap<Point, PlayerWorldExploreEvent> remove1 = eventResourceZoneManagerMap.remove(playerId);
        if (remove1 != null && !remove1.isEmpty()) {
            remove1.clear();
        }
        // log.info("clearMemoryIndexes roleId:{},entitys:{},entitys2:{}",playerId, entitys,entitys2);
        return entitys;
    }

    @Override
    public Collection<PlayerWorldExploreEvent> findByRoleId(Long roleId) {
        ConcurrentHashMap<EventGroupType, ConcurrentHashMap<PlayerWorldExploreEvent, Boolean>> _map = getPlayerWorldExploreEventGroup(roleId);
        if (_map != null && !_map.isEmpty()) {
            Collection<ConcurrentHashMap<PlayerWorldExploreEvent, Boolean>> values = _map.values();
            List<PlayerWorldExploreEvent> ret = new ArrayList<>();
            values.forEach(eventMap -> {
                if (eventMap != null && !eventMap.isEmpty()) {
                    ret.addAll(eventMap.keySet());
                }
            });
            return ret;
        }
        return Collections.emptyList();
    }

    /**
     * 1.服务器启动加载的时候到这 2.创建1个事件之后 save 之后到这 3.玩家从内存load的时候
     * <p>
     * 在创建的时候我判断啦，但是从内存加载也需要判断一下,这个addPlayerWorldExploreEventGroup 方法里面是有判断数量上限的
     * 因为每种类型的事件数量是有上限的，所以这块需要判断是否达到上限，达到上限应该要删除多余的，不能放到内存的
     */
    @Override
    protected void putMemoryIndexes(PlayerWorldExploreEvent entity) {
        this.getPlayerWorldExploreEventGroup(entity.getRoleId()).computeIfAbsent(EventGroupType.findById(entity.getEventGroupType()),
                (k) -> new ConcurrentHashMap<>()).put(entity, Boolean.TRUE);
        this.getEventResourceZoneManagerMap(entity.getCurrentServerId(), entity.getRoleId()).put(entity.getPoint(), entity);
        this.addWorldExploreEvent(entity);
    }

    @Override
    protected void removeMemoryIndexes(PlayerWorldExploreEvent entity) {
        this.getPlayerWorldExploreEventGroup(entity.getRoleId()).computeIfAbsent(EventGroupType.findById(entity.getEventGroupType()),
                (k) -> new ConcurrentHashMap<>()).remove(entity);
        this.getEventResourceZoneManagerMap(entity.getCurrentServerId(), entity.getRoleId()).remove(entity.getPoint());
        this.removeWorldExploreEvent(entity);
        /**
         * 删除之后增加打印， 验证一下 entities 的 key 连接起来 验证一下 自己map 存的 event
         */
        // String allEventIdJoin2 = getAllEventIdJoin2();
        // String allEventIdJoin = getAllEventIdJoin();
        // boolean equals = allEventIdJoin2.equals(allEventIdJoin);
        // log.info("PlayerWorldExploreEventDaoRemoveMemoryIndexes
        // roleId:{},equals:{},allEventIdJoin2:{},allEventIdJoin",entity.getRoleId(),equals,!equals?allEventIdJoin2:"",!equals?allEventIdJoin:"");
    }

    /**
     * 创建一个事件
     *
     * @param meta
     * @param role
     * @param point
     * @param order 这个可以用于排序 但是主键id其实是自增的好像也没啥必要啦
     * @return
     */
    public PlayerWorldExploreEvent createPlayerWorldExploreEvent(String origin, ExploreEventConfig.ExploreEventMeta meta, Role role,
                                                                 Point point, int order, EventGroupType eventGroupType,
                                                                 ArrayList<SimpleItem> dropList, long createTime, long assistRoleId) {
        PlayerWorldExploreEvent item = new PlayerWorldExploreEvent();
        item.setEventId(meta.getId());
        item.setStatus(EventStatus.INIT.getId());
        item.setAward(false);
        item.setLinkType(meta.getLinkType().getId());
        item.setLinkId(meta.getLinkId());
        item.setDropList(dropList);
        item.setPoint(point);
        item.setRoleId(role.getRoleId());
        item.setEventGroupType(eventGroupType.getId());
        item.setOrder(order);
        item.setEventGroupId(meta.getEventGroupId());
        item.setEventLv(meta.getEventLv());
        item.setCreateOriginDesc(origin);

        // 采集任务需设置采集信息
        if (meta.getLinkType() == EventType.EVENT_GATHER) {
            var resReserves = 0;
            var resMetaId = meta.getLinkId();
            var configService = Application.getBean(ConfigServiceImpl.class);
            KvkNewResRefreshConfig resRefreshConfig = configService.getConfig(KvkNewResRefreshConfig.class);
            if (Application.getServerType(role.getCurrentServerId()) == ServerType.KVK_SEASON) {
                var kvkNewResRefreshMeta = resRefreshConfig.get(resMetaId);
                resReserves = kvkNewResRefreshMeta.getResReserves();
            } else {
                NewResRefreshConfig.NewResRefreshMeta newResRefreshMeta = configService.getConfig(NewResRefreshConfig.class).get(resMetaId);
                resReserves = newResRefreshMeta.getResReserves();
            }
            item.setResTotal(resReserves);
            item.setResReversed(resReserves);
        }
        if (meta.getLinkType() == EventType.EVENT_ASSIST) {
            item.setAssistRoleId(assistRoleId);
        }

        // 设置过期时间判断结束时间 填0表示没有时间限制。
        long endTime = 0;
        long durationTime = meta.getDurationTime();
        if (durationTime > 0) {
            endTime = createTime + durationTime * TimeUtil.SECONDS_MILLIS;
        }
        item.setEndTime(endTime);

        createEntity(role, item);
        return item;
    }

    public Collection<PlayerWorldExploreEvent> getPlayerWorldExploreEvent(long roleId, EventGroupType type) {
        var events = new ArrayList<PlayerWorldExploreEvent>();
        var allEventsMap = getPlayerWorldExploreEventGroup(roleId);
        if (allEventsMap == null) {
            return events;
        }

        var eventsMap = allEventsMap.get(type);
        if (eventsMap == null) {
            return events;
        }

        eventsMap.forEach((key, value) -> {
            events.add(key);
        });

        return events;
    }

    public Collection<PlayerWorldExploreEvent> getPlayerWorldExploreEvent(long roleId) {
        var events = new ArrayList<PlayerWorldExploreEvent>();
        var allEventsMap = getPlayerWorldExploreEventGroup(roleId);
        if (allEventsMap == null) {
            return events;
        }

        allEventsMap.forEach((var type, var map) -> {
            map.forEach((key, value) -> {
                events.add(key);
            });
        });

        return events;
    }

    public ConcurrentHashMap<EventGroupType, ConcurrentHashMap<PlayerWorldExploreEvent, Boolean>> getPlayerWorldExploreEventGroup(long roleId) {
        return playerWorldExploreEventGroupMap.computeIfAbsent(roleId, (k) -> new ConcurrentHashMap<>());
    }

    /**
     * 之前这个方法是获取当前服务起Id的所有事件点集合 暂时修改为获取玩家自己的事件点集合， serverId 暂时先保留着
     *
     * @param serverId
     * @param roleId
     * @return
     */
    public ConcurrentHashMap<Point, PlayerWorldExploreEvent> getEventResourceZoneManagerMap(int serverId, long roleId) {
        return eventResourceZoneManagerMap.computeIfAbsent(roleId, (k) -> new ConcurrentHashMap<>());
    }

    /**
     * 查询所有事件数量 没有实际意义 主要用来自己验证一下
     *
     * @return
     */
    public long getTotalEventNum() {
        long _temp = 0;
        for (var entry : playerWorldExploreEventGroupMap.entrySet()) {
            if (entry.getValue() != null) {
                ConcurrentHashMap<EventGroupType, ConcurrentHashMap<PlayerWorldExploreEvent, Boolean>> value = entry.getValue();
                if (value != null && !value.isEmpty()) {
                    for (var entry2 : value.entrySet()) {
                        if (entry2.getValue() != null && !entry2.getValue().isEmpty()) {
                            _temp += entry2.getValue().size();
                        }
                    }

                }
            }
        }
        return _temp;
    }

    /**
     * 查询所有事件id 连接id 没有实际意义 主要用来自己验证一下
     *
     * @return
     */
    public String getAllEventIdJoin() {
        StringBuilder _str = new StringBuilder();
        List<Long> _list = new ArrayList<>();
        for (var entry : playerWorldExploreEventGroupMap.entrySet()) {
            if (entry.getValue() != null) {
                ConcurrentHashMap<EventGroupType, ConcurrentHashMap<PlayerWorldExploreEvent, Boolean>> value = entry.getValue();
                if (value != null && !value.isEmpty()) {
                    for (var entry2 : value.entrySet()) {
                        if (entry2.getValue() != null && !entry2.getValue().isEmpty()) {
                            entry2.getValue().keySet().forEach(e -> _list.add(e.getPersistKey()));
                        }
                    }
                }
            }
        }

        _list.sort(Long::compareTo);
        _list.forEach(e -> _str.append(e).append(","));
        return _str.toString();
    }

    /**
     * 查询所有事件id 连接id 没有实际意义 根据entities 主要用来自己验证一下
     *
     * @return
     */
    public String getAllEventIdJoin2() {
        StringBuilder _str = new StringBuilder();
        List<Long> _list = new ArrayList<>(this.entities.keySet());
        _list.sort(Long::compareTo);
        _list.forEach(e -> _str.append(e).append(","));
        return _str.toString();
    }

    /**
     * 查询所有事件id 连接id 没有实际意义 根据 entities 主要用来自己验证一下
     *
     * @return
     */
    public long getTotalEventNum2() {
        return this.entities.keySet().size();
    }

    /**
     * 更新坐标点 需相应更新索引 删掉原来的索引，增加新的索引
     */
    public void updataWorldExploreEventPoint(PlayerWorldExploreEvent event, Point point) {
        removeMemoryIndexes(event);
        event.setPoint(point);
        putMemoryIndexes(event);
        save(event);
    }

    /**
     * 获取某个点上的所有事件
     */
    public Collection<PlayerWorldExploreEvent> getWorldExploreEventsByPoint(int serverId, Point point) {
        var map = this.pointToPlayerWorldExploreEvent.get(serverId);
        if (point == null || map == null) {
            return new ArrayList<>();
        }
        var list = map.get(point);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        return new ArrayList<>(list);
    }

    /**
     * 添加新的事件
     */
    public void addWorldExploreEvent(PlayerWorldExploreEvent event) {
        var map = this.pointToPlayerWorldExploreEvent.computeIfAbsent(event.getCurrentServerId(), k -> new ConcurrentHashMap<>());
        var mmap = map.computeIfAbsent(event.getPoint(), k -> new ConcurrentLinkedDeque<>());
        mmap.add(event);
    }

    /**
     * 删除事件
     */
    public void removeWorldExploreEvent(PlayerWorldExploreEvent event) {
        var map = this.pointToPlayerWorldExploreEvent.get(event.getCurrentServerId());
        if (map == null) {
            return;
        }

        var mmap = map.get(event.getPoint());
        if (null == mmap) {
            return;
        }

        mmap.remove(event);
    }

    /**
     * 判断某个点位是否有事件
     */
    public boolean hasWorldExploreEvents(int serverId, Point point) {
        var list = getWorldExploreEventsByPoint(serverId, point);
        if (list == null || list.isEmpty()) {
            return false;
        }

        return true;
    }

    public int getEventCount(long roleId, EventGroupType type) {
        return getPlayerWorldExploreEvent(roleId, type).size();
    }

    public void delCacheEvent(PlayerWorldExploreEvent entity) {
        removeMemoryIndexes(entity);
    }
}
