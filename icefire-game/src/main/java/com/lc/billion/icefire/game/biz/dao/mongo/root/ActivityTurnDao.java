package com.lc.billion.icefire.game.biz.dao.mongo.root;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.activity.ActivityTurn;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.exception.AlertException;
import com.longtech.ls.config.ServerType;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * 活动轮次，仅供后端逻辑
 * 
 * <AUTHOR>
 *
 */
@Repository
public class ActivityTurnDao extends RootDao<ActivityTurn> {

	private Map<ActivityType, ActivityTurn> dataByActivityType = new HashMap<>();
    private Map<String, ActivityTurn> dataByActivityMetaId = new HashMap<>();

	public ActivityTurnDao() {
		super(ActivityTurn.class, true);
	}

	public ActivityTurn create(ActivityType activityType, String metaId) {
		ActivityTurn activityTurn = newEntityInstance();
		activityTurn.setActivityType(activityType);
		activityTurn.setMetaId(metaId);
		int db = Application.getServerId();
		// 对于 ActivityTurn 这个表，他永远存在当前服上。
		return createEntity(db, activityTurn);
	}

	public ActivityTurn findByActivityType(ActivityType activityType) {
		return dataByActivityType.get(activityType);
	}

    public ActivityTurn findByActivityMetaId(String activityMetaId) {
        return dataByActivityMetaId.get(activityMetaId);
    }
	@Override
	protected MongoCursor<ActivityTurn> doFindAll(int db) {
		if (Application.getServerType() == ServerType.KVK_SEASON && Application.getServerId() != db) {
			// K服不捞其他服的
			return null;
		}
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(ActivityTurn entity) {
		ActivityTurn activityTurn = dataByActivityType.putIfAbsent(entity.getActivityType(), entity);
		if (null != activityTurn) {
			// 活动重复提醒
			throw new AlertException("对应类型的活动实体已存在", "entityName",entity.getClass().getSimpleName(),"entity",entity, "activityTurn",activityTurn);
		}
        activityTurn = dataByActivityMetaId.put(entity.getMetaId(), entity);
        if (null != activityTurn) {
            // 活动重复提醒
            throw new AlertException("对应Id的活动实体已存在", "entityName",entity.getClass().getSimpleName(),"entity",entity, "entityMetaId",entity.getMetaId());
        }
	}

	@Override
	protected void removeMemoryIndexes(ActivityTurn entity) {
		dataByActivityType.remove(entity.getActivityType());
        dataByActivityMetaId.remove(entity.getMetaId());
	}

}
