package com.lc.billion.icefire.game.biz.dao.mongo.root;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.scene.node.AllianceBossNode;
import com.longtech.ls.config.ServerType;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class AllianceBossNodeDao extends RootDao<AllianceBossNode> {

    private final Map<Long, AllianceBossNode> data = new MyConcurrentMap<>();

    protected AllianceBossNodeDao() {
        super(AllianceBossNode.class, true);
    }

    @Override
    protected void putMemoryIndexes(AllianceBossNode entity) {
        data.put(entity.getAllianceId(), entity);
    }

    @Override
    protected void removeMemoryIndexes(AllianceBossNode entity) {
        data.remove(entity.getAllianceId());
    }

    @Override
    protected MongoCursor<AllianceBossNode> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    public AllianceBossNode create(long allianceId, int x, int y, int serverId) {
        AllianceBossNode bossNode = new AllianceBossNode(allianceId, x, y);
        return createEntity(serverId, bossNode);
    }

    public AllianceBossNode findByAllianceId(long allianceId) {
        return data.get(allianceId);
    }
}
