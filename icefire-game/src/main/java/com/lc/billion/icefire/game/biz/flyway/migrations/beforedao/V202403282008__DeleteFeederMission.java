package com.lc.billion.icefire.game.biz.flyway.migrations.beforedao;

import com.lc.billion.icefire.game.biz.flyway.AbstractMongoMigrationForBeforeDao;
import com.lc.billion.icefire.game.biz.flyway.model.FlywayMongoCollection;
import com.lc.billion.icefire.game.biz.model.mission.feederMission.PlayerFeederMission;
import com.mongodb.client.model.Updates;
import com.mongodb.client.result.UpdateResult;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class V202403282008__DeleteFeederMission extends AbstractMongoMigrationForBeforeDao {
    private final Logger logger = LoggerFactory.getLogger(V202403282008__DeleteFeederMission.class);

    @Override
    protected void rollback() throws Exception {

    }

    @Override
    protected void updateForBeforeDao() throws Exception {
        logger.info("清理支线任务数据>>>>>>>>>>>>开始>");
        FlywayMongoCollection feederMission = getMongoCollection(PlayerFeederMission.class);
        UpdateResult result = feederMission.updateMany(new Document(), Updates.unset("viewList"));
        logger.info("清理支线任务数据>>>>>>>>>>>>结束>" + result.getModifiedCount());
    }
}
