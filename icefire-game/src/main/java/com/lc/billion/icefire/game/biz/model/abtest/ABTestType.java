package com.lc.billion.icefire.game.biz.model.abtest;

import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.protocol.constant.PsActivityType;
import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

public enum ABTestType implements IntEnum {
    // 所有玩家
    ALL(0),
    // 仅新玩家
    ONLY_NEW(1),
    // 仅老玩家
    ONLY_OLD(2),
    ;

    private final int type;

    private ABTestType(int type) {
        this.type = type;
    }

    private static final ABTestType[] INDEXES = EnumUtils.toArray(values());

    @Override
    public int getId() {
        return type;
    }

    public static ABTestType findById(int id) {
        if (id < 0 || id >= INDEXES.length) {
            return null;
        }
        return INDEXES[id];
    }
}
