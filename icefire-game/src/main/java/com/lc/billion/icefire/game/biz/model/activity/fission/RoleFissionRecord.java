package com.lc.billion.icefire.game.biz.model.activity.fission;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.model.activity.ActivityRecord;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class RoleFissionRecord extends ActivityRecord {
    @Getter
    @Setter
    private List<RoleShareItem> shareItemList = new ArrayList<>();
    /**
     * 根据这个字段重置数据 如果openTime < 活动开启时间 则 数据重置且更新成活动开启时间
     */
    @Getter
    @Setter
    private long openTime;
    @Setter
    @Getter
    private int totalRewardStatus;
    /**
     * 首次奖励领取状态
     */
    @Getter
    @Setter
    private HashMap<String, Integer> fissionStatus = new HashMap<>();


    @Getter
    @Setter
    private int buyCount;

    /**
     *
     */
    @Getter
    @Setter
    private HashMap<String, Integer> drawRewardTimes = new HashMap<>();

    /**
     * @param clickerAccountId
     * @param fissionId
     * @return
     */
    public RoleShareItem findByClickerIdAndFissionId(long clickerAccountId, String fissionId) {
        var op = shareItemList.stream().filter(m -> m.getAccountId() == clickerAccountId && m.getFissionId().equals(fissionId)).findFirst();
        return op.orElse(null);
    }

    /**
     * 添加点击记录
     *
     * @param clickRoleId   点击分享的角色ID
     * @param fissionMetaId 点击分享的配置ID
     * @return true代表添加成功 false代表添加失败
     */
    public boolean addClickRecord(long clickRoleId, long accountId, String fissionMetaId, String collectItem) {
        var data = findByClickerIdAndFissionId(accountId, fissionMetaId);
        if (data != null) {
            return false;
        }
        RoleShareItem shareItem = new RoleShareItem(clickRoleId, accountId, TimeUtil.getNow(), fissionMetaId, collectItem);
        shareItemList.add(shareItem);
        return true;
    }

    /**
     * 分享的已经被点击次数
     *
     * @param fissionMetaId
     * @return
     */
    public int getClickCount(String fissionMetaId) {
        return (int) shareItemList.stream().filter(m -> m.getFissionId().equals(fissionMetaId)).count();
    }

    public void reset() {
        this.shareItemList.clear();
        this.totalRewardStatus = 0;
        this.fissionStatus.clear();
        this.setBuyCount(0);
        this.drawRewardTimes.clear();
    }

}
