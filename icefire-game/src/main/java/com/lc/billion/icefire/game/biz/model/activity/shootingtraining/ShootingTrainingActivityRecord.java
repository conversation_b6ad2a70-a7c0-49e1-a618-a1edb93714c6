package com.lc.billion.icefire.game.biz.model.activity.shootingtraining;

import com.lc.billion.icefire.game.biz.model.activity.ActivityRecord;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 射击训练活动
 * <AUTHOR>
 */
@Setter
@Getter
public class ShootingTrainingActivityRecord extends ActivityRecord {

	/**
	 * 活动开始时间
	 */
	private long startTime;

	/**
	 * 下次刷新时间
	 * 如果该时间小于当前时间，则玩家可以领取免费子弹；如果该时间大于当前时间，则说明玩家已经领完了这次的，展示的下次刷新的时间
	 */
	private long nextRefreshTime;

	/**
	 * 当天已经刷新的次数
	 */
	private int refreshCount;

	/**
	 * 当前选择的养成线
	 *  1-发展; 2-幸存者； 3-改装车; 4-装备
	 */
	private int devType;

	/**
	 * 当前养成线已经打了多少枪
	 */
	private int shootingCount;

	//首次射击
	private boolean firstShoot;
	/**
	 * 奖励
	 */
	private Map<Integer, List<ShootingTrainingActivityItem>> rewards;

	/**
	 * 每日刷新上限
	 * 如果该上限小于0，则说明策划没有配置上限，可以无限刷新；否则表示玩家剩余的刷新次数
	 */
	private int dailyRefreshLimit;
}
