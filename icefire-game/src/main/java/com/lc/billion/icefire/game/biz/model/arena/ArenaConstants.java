package com.lc.billion.icefire.game.biz.model.arena;

import com.lc.billion.icefire.core.common.TimeUtil;

/**
 * @ClassName ArenaConstants
 * @Description 竞技场需要用到的常量
 * <AUTHOR>
 * @Date 2023/12/20 14:28
 * @Version 1.0
 */
public class ArenaConstants {
    /**
     * 解锁功能所需建筑
     */
    public static final int ARENA_UNLOCK_BUILDING = 331901;
    /**
     * 初始化名次
     */
    public static final int ARENA_RANK_INIT = -1;
    /**
     * 排行榜
     */
    public static final String ARENA_RANK = "ARENA_RANK";
    /**
     * 成员信息表
     */
    public static final String ARENA_MEMBER = "ARENA_MEMBER";
    /**
     * 防守阵容表
     */
    public static final String ARENA_DEFENSE_LINEUP = "ARENA_DEFENSE_LINEUP";
    /**
     * 奖励发放记录
     */
    public static final String ARENA_DAILY_REWARD = "ARENA_DAILY_REWARD";
    public static final String ARENA_SEASON_REWARD = "ARENA_SEASON_REWARD";
    /**
     * 排行榜备份
     */
    public static final String ARENA_RANK_BAK = "ARENA_RANK_BAK";
    /**
     * 竞技场信息
     */
    public static final String ARENA_GROUP = "ARENA_GROUP";

    // 消息推送标记
    public static final String ARENA_DAILY_REWARD_PUSH = "ARENA_DAILY_REWARD_PUSH";
    public static final String ARENA_SEASON_REWARD_PUSH = "ARENA_SEASON_REWARD_PUSH";

    /**
     * 默认过期时间
     */
    public static final int DEFAULT_EXPIRE = (int) (TimeUtil.DAY_SECONDS * 30);
}
