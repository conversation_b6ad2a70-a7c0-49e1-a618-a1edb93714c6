package com.lc.billion.icefire.game.biz.model.email.alliance;

import com.lc.billion.icefire.game.biz.model.email.EmailSubType;
import com.lc.billion.icefire.protocol.structure.PsAllianceEmail;

/**
 * @author: 周会勤
 * @create: 2020-02-06 16:19
 **/
public class AllianceLeaderCleanNoticeEmail extends AbstractAllianceEmail {
    private int notLoginDay;
    private int leftDay;
    @Override
    public EmailSubType getSubType() {
        return EmailSubType.ALLIANCE_LEADER_CLEAN_NOTICE;
    }

    @Override
    public AllianceEmailType getAllianceEmailType() {
        return AllianceEmailType.ALLIANCE_LEADER_CLEAN_NOTICE;
    }

    @Override
    public Object toInfo() {
        PsAllianceEmail info = new PsAllianceEmail();
        info.setType(getAllianceEmailType().getType());
        info.setId(String.valueOf(getId()));
        info.setStatus(getStatus());
        info.setTime(getCreateTime());
        info.setLocked(isLock());
        info.setServerId(getServerId());
        if (this.getAlliance() != null) {
            info.setAllianceInfo(this.getAlliance().toInfo());
        }
        info.setNotLoginDay(notLoginDay);
        info.setLeftDay(leftDay);

        return info;

    }

    public int getNotLoginDay() {
        return notLoginDay;
    }

    public void setNotLoginDay(int notLoginDay) {
        this.notLoginDay = notLoginDay;
    }

    public int getLeftDay() {
        return leftDay;
    }

    public void setLeftDay(int leftDay) {
        this.leftDay = leftDay;
    }
}
