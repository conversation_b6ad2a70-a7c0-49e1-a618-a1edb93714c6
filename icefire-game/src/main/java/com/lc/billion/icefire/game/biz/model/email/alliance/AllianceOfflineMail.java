package com.lc.billion.icefire.game.biz.model.email.alliance;

import com.lc.billion.icefire.game.biz.model.email.EmailSubType;
import com.lc.billion.icefire.protocol.structure.PsAllianceEmail;

import java.util.ArrayList;
import java.util.List;


public class AllianceOfflineMail extends AbstractAllianceEmail {

    private static final long serialVersionUID = 1L;

	private List<Long> members;

	@Override
	public AllianceEmailType getAllianceEmailType() {
		return AllianceEmailType.ALLIANCE_OFFLINE_NOTIFY;
	}

	@Override
	public EmailSubType getSubType() {
		return EmailSubType.ALLIANCE_OFFLINE_NOTIFY;
	}

	@Override
	public Object toInfo() {
		PsAllianceEmail info = new PsAllianceEmail();
		info.setType(getAllianceEmailType().getType());
		info.setId(String.valueOf(getId()));
		info.setStatus(getStatus());
		info.setTime(getCreateTime());
		info.setLocked(isLock());
		if (this.getAlliance() != null) {
			info.setAllianceInfo(this.getAlliance().toInfo());
		}
		List<String> param = new ArrayList<>();
		for(Long memberId:members){
			param.add(memberId + "");
		}

		info.setParams(param);
		return info;
	}

	public List<Long> getMembers() {
		return members;
	}

	public void setMembers(List<Long> members) {
		this.members = members;
	}
}
