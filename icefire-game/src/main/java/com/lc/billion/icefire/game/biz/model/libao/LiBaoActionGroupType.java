package com.lc.billion.icefire.game.biz.model.libao;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

/**
 * 礼包动作分组
 * by wang<PERSON><PERSON>@tuyoogame.com 2024-03-18 21:10:04
 */
public enum LiBaoActionGroupType implements IntEnum {

    ADD(1),
    DEL(2),
    BUY(3),
    RESET(4),
    SOLD_OUT(5),
    CAN_BUY(6),
	;

	private final int id;
	private static final LiBaoActionGroupType[] INDEXES = EnumUtils.toArray(values());

	private LiBaoActionGroupType(int id) {
		this.id = id;
	}

	@Override
	public int getId() {
		return id;
	}

    public static LiBaoActionGroupType findById(int id) {
        if (id < 0 || id > INDEXES.length) {
            return null;
        }

        return INDEXES[id];
    }
}
