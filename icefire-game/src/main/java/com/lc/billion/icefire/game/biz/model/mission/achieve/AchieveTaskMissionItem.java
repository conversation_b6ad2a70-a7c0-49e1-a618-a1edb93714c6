package com.lc.billion.icefire.game.biz.model.mission.achieve;

import com.lc.billion.icefire.game.biz.config.CallbackTaskConfig;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.AbstractBaseMissionItem;
import com.lc.billion.icefire.game.config.ConfigHelper;
/**
 * @Author: huyafei
 * @Date: 2022/1/14 15:43
 * @Description:
 * 成就系统任务
 **/
public class AchieveTaskMissionItem extends AbstractBaseMissionItem {
    /**
     * 任务完成时间
     * 针对完成登陆去检查的任务，这个时间肯定是不准确的，已经之前没有记录这个和策划说过啦
     */
    private long completeTime;

    public long getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(long completeTime) {
        this.completeTime = completeTime;
    }

    @Override
    public CallbackTaskConfig.CallbackTaskMissionMeta getMeta() {
        CallbackTaskConfig config = ConfigHelper.getServiceInstance().getConfig(CallbackTaskConfig.class);
        return config.getCallbackTaskMissionMeta(this.missionId);
    }

    @Override
    public String toString() {
        return "AchieveTaskMissionItem{" +
                "completeTime=" + completeTime +
                ", missionId='" + missionId + '\'' +
                ", progress=" + progress +
                ", status=" + status +
                ", award=" + award +
                ", missionType=" + missionType +
                '}';
    }
}
