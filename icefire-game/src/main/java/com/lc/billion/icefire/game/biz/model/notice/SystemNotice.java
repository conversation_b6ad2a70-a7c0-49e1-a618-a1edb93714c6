package com.lc.billion.icefire.game.biz.model.notice;

import com.lc.billion.icefire.protocol.constant.PsNoticeType;

/**
 * 
 * <AUTHOR>
 * @sine 2016年1月25日 下午5:46:49
 * 
 */

public class SystemNotice {

	private long id;

	private String content;

	/**
	 * 2020-5-9  这个值现在不需要了
	 */
	@Deprecated
	private long interval;

	private long expireTime;

	private PsNoticeType type;
	/**播放次数*/
	private int playCount;

	public SystemNotice() {

	}

	public SystemNotice(long id, String content, int playCount, long expireTime, PsNoticeType type) {
		this.id = id;
		this.content = content;
//		this.interval = interval;
		this.expireTime = expireTime;
		this.type = type;
		this.playCount = playCount;
	}


	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Deprecated
	public long getInterval() {
		return interval;
	}

	@Deprecated
	public void setInterval(long interval) {
		this.interval = interval;
	}

	public long getExpireTime() {
		return expireTime;
	}

	public void setExpireTime(long expireTime) {
		this.expireTime = expireTime;
	}

	public PsNoticeType getType() {
		return type;
	}

	public void setType(PsNoticeType type) {
		this.type = type;
	}

	public int getPlayCount() {
		return playCount;
	}
}
