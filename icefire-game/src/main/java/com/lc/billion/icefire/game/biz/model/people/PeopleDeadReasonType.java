package com.lc.billion.icefire.game.biz.model.people;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;
import lombok.Getter;

/**
 * @ClassName PeopleDeadReasonType
 * @Description
 * <AUTHOR>
 * @Date 2024/8/29 21:43
 * @Version 1.0
 */
public enum PeopleDeadReasonType implements IntEnum {
    HUNGRY(1, "hungry", "饿死"),
    TIRED(2, "tired", "累死"),
    ILL(3, "ill", "病死"),
    ;

    private final int id;
    @Getter
    private final String reason;
    private final String desc;

    public static final PeopleDeadReasonType[] INDEXES = EnumUtils.toArray(values());

    private PeopleDeadReasonType(int id, String reason, String desc) {
        this.id = id;
        this.reason = reason;
        this.desc = desc;
    }

    @Override
    public int getId() {
        return id;
    }

    public static PeopleDeadReasonType getByReason(String reason) {
        for (PeopleDeadReasonType type : PeopleDeadReasonType.values()) {
            if (type.getReason().equals(reason)) {
                return type;
            }
        }
        return null;
    }

    public static PeopleDeadReasonType getById(int id) {
        if (id < 0 || id >= INDEXES.length) {
            return null;
        }
        return INDEXES[id];
    }
}