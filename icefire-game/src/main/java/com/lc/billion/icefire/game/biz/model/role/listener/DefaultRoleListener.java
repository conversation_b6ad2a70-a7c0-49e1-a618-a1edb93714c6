/**
 *
 */
package com.lc.billion.icefire.game.biz.model.role.listener;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.expedition.ExpeditionNumberGateService;
import com.lc.billion.icefire.game.biz.service.impl.tick.TickService;
import com.lc.billion.icefire.game.biz.service.impl.tradepost.TradePostServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kingdombuff.KingdomBuffServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.KvkSeasonServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.SeasonTaskService;

/**
 * <AUTHOR>
 */
public class DefaultRoleListener {

    private Role role;
    private ServiceDependency srvDpd;

    public DefaultRoleListener(Role role, ServiceDependency srvDpd) {
        this.role = role;
        this.srvDpd = srvDpd;
    }

    public void onEnterWorld() {
        // 客户端本地读取开关总表，因此注释掉下面返回消息
        // srvDpd.getFunctionSwitchService().onEnterWorld(role);
        srvDpd.getZeroScheduleService().onEnterWorld(role);
        srvDpd.getStoryService().onEnterWorld(role);
        // 先推送城市，后推送队列，客户端需要先拿城市消息，因为队列里需要去城市的东西
        srvDpd.getRoleCityService().onEnterWorld(role);
        srvDpd.getWorkService().onEnterWorld(role);
        srvDpd.getSevenCaptureService().onEnterWorld(role);

        srvDpd.getHeroService().onEnterWorld(role);
        srvDpd.getSoldierService().onEnterWorld(role);
        srvDpd.getUnlockService().onEnterWorld(role);

        srvDpd.getItemService().onEnterWorld(role);
        srvDpd.getAllianceService().onEnterWorld(role);
        srvDpd.getAllianceBossService().onEnterWorld(role);
        srvDpd.getAllianceWarService().onEnterWorld(role);
        srvDpd.getAllianceInactiveManagementService().onEnterWorld(role);
        srvDpd.getAllianceRankRequestService().onEnterWorld(role);
        srvDpd.getNoticeService().onEnterWorld(role);
        srvDpd.getPlayerService().onEnterWorld(role);
        srvDpd.getLotteryService().onEnterWorld(role);
        // srvDpd.getShopStationService().onEnterWorld(role);
        srvDpd.getBuffService().onEnterWorld(role);
        srvDpd.getArmyServiceImpl().onEnterWorld(role);
        srvDpd.getMissionService().onEnterWorld(role);
        srvDpd.getMissionFeederService().onEnterWorld(role);
        srvDpd.getAllianceHelpService().onEnterWorld(role);
        // 主线跟支线enterworld后,发送任务消息
        srvDpd.getMissionService().getMainMissions(role);
        srvDpd.getMissionDailyService().onEnterWorld(role);
        srvDpd.getMissionChapterService().onEnterWorld(role);

        srvDpd.getTechService().onEnterWorld(role);
        // srvDpd.getSecondQueueServiceImpl().onEnterWorld(role);
        srvDpd.getDailyGiftService().onEnterWorld(role);
        srvDpd.getRechargeService().onEnterWorld(role);
        srvDpd.getRookieGiftService().onEnterWorld(role);
        // 礼包这里最好不要随意更改，可能会影响已有系统礼包下发时发送List和update顺序导致前端显示错误
        srvDpd.getLiBaoService().onEnterWorld(role);
        // srvDpd.getTowerService().onEnterWorld(role);
        srvDpd.getVipService().onEnterWorld(role);
        srvDpd.getOnlineRewardServiceImpl().onEnterWorld(role);
        srvDpd.getMonthCardService().onEnterWorld(role);
        srvDpd.getFreeBuyService().onEnterWorld(role);
        srvDpd.getActivityService().onEnterWorld(role);
        srvDpd.getMissionNewPlayerService().onEnterWorld(role);
        srvDpd.getWorldExploreEventService().onEnterWorld(role);
        srvDpd.getSettingService().onEnterWorld(role);
        srvDpd.getAllianceTechService().onEnterWorld(role);
        srvDpd.getPopFaceService().onEnterWorld(role);
        srvDpd.getAdService().onEnterWorld(role);
        srvDpd.getBattlePassService().onEnterWorld(role);
        srvDpd.getEvaluationService().onEnterWorld(role);
        srvDpd.getContinuousRechargeService().onEnterWorld(role);
        srvDpd.getSkinManager().onEnterWorld(role);
        srvDpd.getAllianceMarkService().onEnterWorld(role);
        srvDpd.getMilestoneService().onEnterWorld(role);
        srvDpd.getRegionCapitalService().onEnterWorld(role);
        srvDpd.getDailyRechargeService().onEnterWorld(role);
        srvDpd.getExcellentMarkService().onEnterWorld(role);
        srvDpd.getGvgGameService().onEnterWorld(role);
        srvDpd.getGVGStrongHoldService().onEnterWorld(role);
        srvDpd.getQuestionnaireService().onEnterWorld(role);
        srvDpd.getTitleService().onEnterWorld(role);
        srvDpd.getAllianceAffairService().onEnterWorld(role);

        srvDpd.getOfficialsService().onEnterWorld(role);

        srvDpd.getKvkGameService().onEnterWorld(role);
        srvDpd.getMailSender().onEnterWorld(role);
        Application.getBean(KvkSeasonServiceImpl.class).onEnterWorld(role);
        srvDpd.getLoyaltyHallService().onEnterWorld(role);

        srvDpd.getShootingTrainingService().onEnterWorld(role);

        Application.getBean(SeasonTaskService.class).onEnterWorld(role);

        Application.getBean(KingdomBuffServiceImpl.class).onEnterWorld(role);

        srvDpd.getSkinService().onEnterWorld(role);
        srvDpd.getBingoActivityService().onEnterWorld(role);
        srvDpd.getTvtGameService().onEnterWorld(role);

        Application.getBean(LegionService.class).onEnterWorld(role);

        srvDpd.getAchievementService().onEnterWorld(role);
        srvDpd.getStoreService().onEnterWorld(role);
        srvDpd.getCaravanService().onEnterWorld(role);

        srvDpd.getExpeditionService().onEnterWorld(role);
        srvDpd.getExpeditionMulService().onEnterWorld(role);
        srvDpd.getBlizzardService().onEnterWorld(role);
        srvDpd.getBlizzardResistService().onEnterWorld(role);
        srvDpd.getRobberService().onEnterWorld(role);
        srvDpd.getArenaService().onEnterWorld(role);
        srvDpd.getLoginRewardService().onEnterWorld(role);
        srvDpd.getFirebasePushService().onEnterWorld(role);
        srvDpd.getWechatPushService().onEnterWorld(role);
        srvDpd.getPopularWillService().onEnterWord(role);
        srvDpd.getPopularSkillService().onEnterWorld(role);
        srvDpd.getMissionPopularWillService().onEnterWorld(role);
        srvDpd.getFreeTreasureBoxService().onEnterWorld(role);
        srvDpd.getActivityNoticeService().onEnterWorld(role);

        srvDpd.getBrotherHoodService().onEnterWorld(role);
        srvDpd.getIceWheelService().onEnterWorld(role);
        srvDpd.getWorldBossDongZhuoService().onEnterWorld(role);

        srvDpd.getItemRecordService().onEnterWorld(role);
        srvDpd.getPeopleServiceImpl().onEnterWorld(role);
        srvDpd.getCommonMissionActivityManager().onEnterWorld(role);
        srvDpd.getEnlistmentService().onEnterWorld(role);
        srvDpd.getAllianceRecruitService().onEnterWorld(role);
        srvDpd.getLordTreasureService().onEnterWorld(role);
        srvDpd.getAllianceLeaderMissionService().onEnterWorld(role);
        srvDpd.getAllianceMissionService().onEnterWorld(role);

        srvDpd.getShareService().onEnterWorld(role);
        srvDpd.getMissionShareService().onEnterWorld(role);
        srvDpd.getRegionCapitalRecordService().onEnterWorld(role);
        srvDpd.getFightPowerServiceImpl().onEnterWorld(role);
        srvDpd.getHorseService().onEnterWorld(role);
        srvDpd.getTrusteeshipService().onEnterWorld(role);

        srvDpd.getSiegeEnginesService().onEnterWorld(role);
        srvDpd.getGvgBattleService().onEnterWorld(role);

        srvDpd.getSeasonWarmUpActivityService().onEnterWorld(role);

        Application.getBean(TradePostServiceImpl.class).onEnterWorld(role);

        srvDpd.getRedPackActivityManager().onEnterWorld(role);

        Application.getBean(ExpeditionNumberGateService.class).onEnterWorld(role);
    }

    public void onLeaveWorld() {
        srvDpd.getAllianceService().onLeaveWord(role);
    }

    public void onWaitReconnect() {
        srvDpd.getAllianceService().onWaitReconnect(role);
    }

    public void onReconnect() {
        srvDpd.getSceneService().onReconnect(role);
        srvDpd.getAllianceService().onReconnect(role);
        Application.getBean(TickService.class).onReconnect(role);
        srvDpd.getAllianceTechService().onReconnect(role);
    }

    public void onResetDayData(long now, boolean isEnterWorld) {
        srvDpd.getRoleService().onResetDayData(role, !isEnterWorld);
        srvDpd.getSoldierService().onResetDayData(role, !isEnterWorld);
        // roleExtra需要在Libao之前
        srvDpd.getRoleExtraManager().onResetDayData(role, isEnterWorld);
        srvDpd.getLiBaoService().onResetDayData(role, isEnterWorld);
        srvDpd.getLotteryService().onResetDayData(role, isEnterWorld);
        srvDpd.getMissionDailyService().onResetDayData(role, !isEnterWorld);
        srvDpd.getMissionNewPlayerService().onResetDayData(role);
        srvDpd.getOnlineRewardServiceImpl().onResetDayData(role);
        srvDpd.getRechargeService().onResetDayData(role);
        srvDpd.getDailyRechargeService().onResetDayData(role, !isEnterWorld);
        srvDpd.getCaravanService().onResetDayData(role);
        srvDpd.getHeroService().onResetDayData(role, !isEnterWorld);
        srvDpd.getFreeBuyService().onResetDayData(role, !isEnterWorld);
        srvDpd.getShootingTrainingService().onResetDayData(role, !isEnterWorld);
        srvDpd.getBingoActivityService().onResetDayData(role, isEnterWorld);
        srvDpd.getDiamondCountConsumerService().onResetDayData(role, !isEnterWorld);
//        srvDpd.getArenaService().onResetDayData(role, !isEnterWorld);
        srvDpd.getLoginRewardService().onResetDayData(role);
        srvDpd.getPushHelper().onResetDayData(role);
        srvDpd.getSevenCaptureService().onResetDayData(role);
        srvDpd.getIceWheelService().onResetDayData(role, !isEnterWorld);
        srvDpd.getBattlePassService().onResetDayData(role, isEnterWorld);
        srvDpd.getCommonMissionActivityManager().onResetDayData(role);
        srvDpd.getPeopleServiceImpl().onResetDayData(role);
        srvDpd.getShareService().onResetDayData(role);
        srvDpd.getWhispererActivityService().onResetDayData(role);
        srvDpd.getActivityCommonService().onResetDayData(role,isEnterWorld);
        srvDpd.getArmyServiceImpl().onResetDayData(role, isEnterWorld);
        srvDpd.getHorseService().onResetDayData(role);
        srvDpd.getAlipayReportService().onResetDayData(role);
        srvDpd.getTrusteeshipService().onResetDayData(role, !isEnterWorld);
        srvDpd.getRedPackActivityManager().onResetDayData(role,isEnterWorld);
    }
}
