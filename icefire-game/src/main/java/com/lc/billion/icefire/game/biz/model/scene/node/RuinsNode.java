package com.lc.billion.icefire.game.biz.model.scene.node;

import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.scene.MapNodeUpdater;
import com.lc.billion.icefire.protocol.GcMapNodeUpdate;
import com.lc.billion.icefire.protocol.structure.PsMapNode;
import com.lc.billion.icefire.protocol.structure.PsMapRuinsInfo;
import org.apache.thrift.TBase;

import java.util.Date;

/**
 * 废墟
 * 
 * <AUTHOR>
 *
 */
public class RuinsNode extends SceneNode {

	private static final long serialVersionUID = -3467859374682125572L;

	/** 普通废墟 */
	public static final int TYPE_COMMON = 0;
	/** 迁城废墟 */
	public static final int TYPE_MIGRATE_GVG = 1;

	private Long ownerId;

	private long disappearTime;

	private int type;

	public Long getRoleId() {
		return ownerId;
	}

	public void setRoleId(Long roleId) {
		ownerId = roleId;
	}

	@Override
	public String toString() {
		return "RuinsNode [id=" + getPersistKey() + "][" + position + "]" + "ownerId:" + ownerId + ",disappearTime=" + disappearTime + "," + new Date(disappearTime);
	}

	@Override
	public SceneNodeType getNodeType() {
		return SceneNodeType.RUINS;
	}

	@Override
	public GcMapNodeUpdate toMapNodeUpdate(MapNodeUpdater updater) {
		GcMapNodeUpdate msg = new GcMapNodeUpdate();
		PsMapNode psMapNode = toPsMapNode();
		msg.addToNodes(psMapNode);
		updater.accept(psMapNode);
		return msg;
	}


	@Override
	public String getBIConfigId() {
		return null;
	}

	public PsMapRuinsInfo toPsMapRuinsInfo() {
		PsMapRuinsInfo mapRuinsInfo = new PsMapRuinsInfo();
		mapRuinsInfo.setDisappearTime(disappearTime);
		mapRuinsInfo.setOwnerId(ownerId);
		return mapRuinsInfo;
	}

	public Long getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Long ownerId) {
		this.ownerId = ownerId;
	}

	public long getDisappearTime() {
		return disappearTime;
	}

	public void setDisappearTime(long disappearTime) {
		this.disappearTime = disappearTime;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}


	@Override
	public PsMapNode._Fields getField() {
		return PsMapNode._Fields.RUINS_INFO;
	}

	@Override
	public TBase<?, ?> getFieldValue() {
		return toPsMapRuinsInfo();
	}
}
