package com.lc.billion.icefire.game.biz.model.scene.node;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.api.client.util.Lists;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.battle.FightArmy;
import com.lc.billion.icefire.game.biz.battle.FightArmyCreator;
import com.lc.billion.icefire.game.biz.battle.FightProp;
import com.lc.billion.icefire.game.biz.battle.calculator.FightUnitPropCalculate;
import com.lc.billion.icefire.game.biz.config.AllianceBuildingPositionConfig;
import com.lc.billion.icefire.game.biz.config.NpcConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.activity.ArmyCampData;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconArmyInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconRoleInfo;
import com.lc.billion.icefire.game.biz.model.hero.Hero;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.info.DefaultRoleInfo;
import com.lc.billion.icefire.game.biz.model.scene.*;
import com.lc.billion.icefire.game.biz.model.scene.node.armyTarget.IReconTargetNode;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroOutput;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.regioncapital.RegionCapitalService;
import com.lc.billion.icefire.game.biz.service.impl.scene.MapNodeUpdater;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.siegeengines.SiegeEnginesServiceImpl;
import com.lc.billion.icefire.protocol.GcMapNodeUpdate;
import com.lc.billion.icefire.protocol.constant.PsAllianceSiegeEngineStateType;
import com.lc.billion.icefire.protocol.constant.PsAllianceSiegeEngineType;
import com.lc.billion.icefire.protocol.structure.PsMapNode;
import com.lc.billion.icefire.protocol.structure.PsMapRegionSiegeEngines;
import com.lc.billion.icefire.protocol.structure.PsSiegeEnginesSimple;
import lombok.Data;
import org.apache.thrift.TBase;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName SiegeEnginesNode
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 14:18
 * @Version 1.0
 */
@Data
public class SiegeEnginesNode extends SceneNode implements IReconTargetNode, ISceneWithWatcherNode {
    /**
     * 对应的州府id
     */
    private Long capitalNodeId;
    /**
     * 联盟id
     */
    private Long allianceId;
    /**
     * 开始时间
     */
    private long startTime;
    /**
     * 建造完成时间
     */
    private long buildingCompleteTime;
    /**
     * 结束时间
     */
    private long endTime;
    /**
     * 上一次攻击时间
     */
    private long lastAttackTime = 0;
    /**
     * 状态 0: 建造中 1: 建造完成
     */
    private SiegeEnginesBuildState status = SiegeEnginesBuildState.BUILDING;
    /**
     * 自动任命队长
     */
    private boolean isAutoAssignCaptain = true;
    /**
     * 死亡的普通怪
     */
    private int deadNormalMonsterCount = 0;
    /**
     * 类型
     */
    private SiegeEnginesType type;
    /**
     * 配置id
     */
    private String metaId;
    /**
     * 驻防队长信息
     */
    private Long leaderId;
    /**
     * 等级
     */
    private int level;
    /**
     * 时间减少比例
     */
    private double timeReduceRatio = 1.0;
    /**
     * 驻防的军队
     */
    private List<ArmyCampData> armyList = new ArrayList<>();

    /**
     * 加锁 pvp后继续进攻
     */
    @JsonIgnore
    private long continuePveLockId;
    /**
     * 击败的pve数量
     */
    @JsonIgnore
    private int continuePveCount;

    /**
     * 减少建造时间
     *
     * @param time: 减少时间
     */
    public void reduceBuildingTime(long time) {
        buildingCompleteTime -= time;
    }

    /**
     * 判断是否需要拆除
     *
     * @param now
     * @return
     */
    public boolean isNeedDemolish(long now) {
        return now >= endTime || isNpcDeadAll();
    }

    public boolean isNpcDeadAll() {
        AllianceBuildingPositionConfig.AllianceBuildingPosition meta = getMeta();
        return meta.getNpcNum() <= deadNormalMonsterCount;
    }

    private AllianceBuildingPositionConfig.AllianceBuildingPosition getMeta() {
        return Application.getBean(ConfigServiceImpl.class).getConfig(AllianceBuildingPositionConfig.class).get(metaId);
    }

    public boolean isNeedAttack(long now) {
        // 未建造完成
        if (buildingCompleteTime > now) {
            return false;
        }
        return getAttackCd() + lastAttackTime <= now;
    }

    public long getAttackCd() {
        AllianceBuildingPositionConfig.AllianceBuildingPosition meta = getMeta();
        return meta.getProperty2() * TimeUtil.SECONDS_MILLIS;
    }

    @Override
    public String getNpcMetaId() {
        if (!isNpcDeadAll()) {
            var meta = getMeta();
            return meta.getNpc();
        } else {
            return null;
        }
    }

    @Override
    public SceneNodeType getNodeType() {
        return SceneNodeType.ALLIANCE_SIEGE_ENGINES;
    }

    @Override
    public String getBIConfigId() {
        return metaId;
    }

    @Override
    public PsMapNode._Fields getField() {
        return PsMapNode._Fields.SIEGE_ENGINES_INFO;
    }

    @Override
    public TBase<?, ?> getFieldValue() {
        return toPsSiegeEnginesInfo();
    }

    public PsMapRegionSiegeEngines toPsSiegeEnginesInfo() {
        PsMapRegionSiegeEngines info = new PsMapRegionSiegeEngines();
        info.setId(getPersistKey());
        info.setMetaId(metaId);
        info.setBelongAllianceId(allianceId);
        info.setState(PsAllianceSiegeEngineStateType.findByValue(status.getId()));
        info.setEndTime(endTime);
        info.setAutoAppointLeader(isAutoAssignCaptain);

        SiegeEnginesServiceImpl siegeEnginesService = Application.getBean(SiegeEnginesServiceImpl.class);
        info.setStationArmys(siegeEnginesService.stationArmyList(this));

        info.setTroopMax(siegeEnginesService.getStationTroopMaxNum(this));
        info.setSoldierMax(siegeEnginesService.getStationSoldierMaxNum(this));
        info.setBossDeadCount(0);
        info.setMonsterDeadCount(deadNormalMonsterCount);

        return info;
    }

    @Override
    public boolean canRecon(Role role) {
        Alliance alliance = Application.getBean(AllianceDao.class).findById(role.getAllianceId());
        if (alliance == null || Objects.equals(alliance.getPersistKey(), allianceId)) {
            return false;
        }
        // 查看是否可以处于战争中
        return !Application.getBean(RegionCapitalService.class).isAllianceAtWar(alliance.getPersistKey(), capitalNodeId);
    }

    @Override
    public void reconBaseInfo(ReconInfo reconInfo) {
        ReconRoleInfo reconRoleInfo = new ReconRoleInfo();
        reconRoleInfo.setX(getX());
        reconRoleInfo.setY(getY());
        reconRoleInfo.setTargetType(getNodeType());
        reconRoleInfo.setMetaId(metaId);
        reconInfo.setReconRoleInfo(reconRoleInfo);

        if (isStationed()) {
            reconToRole(reconInfo);
        } else {
            if (!isNpcDeadAll()) {
                reconToNpc(reconInfo);
            }
        }
    }

    @Override
    public void addEnemy(ArmyInfo army) {
        var armyManager = Application.getBean(ArmyManager.class);
        SceneServiceImpl sceneService = Application.getBean(SceneServiceImpl.class);
        var arrives = sceneService.getNodeArriveArmies(this);
        for (var defendArmy : arrives) {
            Role defendRole = defendArmy.getOwner();
            if (defendRole != null) {
                armyManager.addEnemy(army, defendRole);
            }
        }
    }


    @Override
    public FightArmy createFightArmy() {
        var armyCreator = Application.getBean(FightArmyCreator.class);
        var arrives = getArrivedArmyList();
        FightArmy defender;
        if (!arrives.isEmpty()) {
            // 有人驻防
            defender = armyCreator.createFightArmy(arrives, getNodeType());
        } else {
            // 打boss
            var meta = getMeta();
            defender = armyCreator.createFightArmy(false, meta.getNpc(), SceneNodeType.ALLIANCE_SIEGE_ENGINES,
                    continuePveCount);
        }

        defender.setMapNodeMetaId(metaId);
        return defender;
    }

    private void reconToNpc(ReconInfo reconInfo) {
        ReconRoleInfo reconRoleInfo = reconInfo.getReconRoleInfo();
        ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
        AllianceBuildingPositionConfig.AllianceBuildingPosition meta = getMeta();
        NpcConfig.NpcMeta npcMeta = null;
        if (!isNpcDeadAll()) {
            npcMeta = configService.getConfig(NpcConfig.class).get(meta.getNpc());
        }
        // 没有找到npc
        if (null == npcMeta) {
            return;
        }
        reconRoleInfo.setRoleId(Long.parseLong(npcMeta.getId()));
        reconRoleInfo.setPlayerName("@" + npcMeta.getName() + "@");
        reconRoleInfo.setHeadIcon(npcMeta.getIcon());
        reconRoleInfo.setRoleInfo(DefaultRoleInfo.toRoleInfo());

        ReconArmyInfo reconArmyInfo = new ReconArmyInfo();
        for (var entry : npcMeta.getSoldierList()) {
            reconArmyInfo.getSoldierMap().put(entry.getId(), entry.getCount());
        }
        reconInfo.setReconArmyInfo(reconArmyInfo);

        var props = npcMeta.getSlgProps();
        if (null != props) {
            FightProp fightProp = new FightProp();
            for (var entry : props) {
                Prop prop = Prop.findById(entry.getId());
                if (null == prop) {
                    continue;
                }

                fightProp.addRoleProp(prop, entry.getCount() / 10000);
            }
            reconInfo.setEffectMap(FightUnitPropCalculate.calcReconProp(fightProp, getNodeType()));
        }
    }

    private void reconToRole(ReconInfo reconInfo) {
        ReconRoleInfo reconRoleInfo = reconInfo.getReconRoleInfo();
        Alliance alliance = Application.getBean(AllianceServiceImpl.class).getAllianceById(getAllianceId());
        if (alliance != null) {
            reconRoleInfo.setAllianceShortName(alliance.getAliasName());
            reconRoleInfo.setAllianceName(alliance.getName());
        }

        List<ArmyCampData> arrivedArmyList = getArrivedArmyList();
        if (arrivedArmyList.isEmpty()) {
            return;
        }

        ArmyCampData armyCamp = arrivedArmyList.getFirst();
        ArmyInfo army = Application.getBean(ArmyManager.class).findById(armyCamp.getArmyId());
        if (army == null) {
            return;
        }
        Role role = Application.getBean(RoleManager.class).getRole(armyCamp.getRoleId());
        reconRoleInfo.setRoleId(role.getPersistKey());
        reconRoleInfo.setPlayerName(role.getName());
        reconRoleInfo.setHeadIcon(role.getHead());
        reconRoleInfo.setRoleInfo(role.toRoleInfo());

        ReconArmyInfo reconArmyInfo = new ReconArmyInfo();
        for (var entry : army.getArmySoldiers().entrySet()) {
            reconArmyInfo.getSoldierMap().put(entry.getKey(), entry.getValue().getCount());
        }
        List<Hero> heroList = Lists.newArrayList();
        HeroServiceImpl heroService = Application.getBean(HeroServiceImpl.class);
        for (var entry : army.getHeros()) {
            var hero = heroService.getHero(role, entry);
            heroList.add(hero);
            reconArmyInfo.getHeroInfoList().add(HeroOutput.toInfo(hero));
        }
        FightProp fightProp = new FightProp();
        fightProp.setRoleProps(role.getNumberProps());
        fightProp.setHeroBuffValues(FightArmy.heroProps(heroList, fightProp));
        reconInfo.setEffectMap(FightUnitPropCalculate.calcReconProp(fightProp, getNodeType()));
        reconInfo.setReconArmyInfo(reconArmyInfo);
    }

    @Override
    public Role reconOwnerRole() {
        return null;
    }

    @Override
    public List<ArmyInfo> reconReinforceInfo() {
        List<ArmyInfo> retArmy = new ArrayList<>();
        List<ArmyCampData> arrivedArmyList = getArrivedArmyList();
        if (!arrivedArmyList.isEmpty()) {
            ArmyCampData firstArmyCamp = arrivedArmyList.getFirst();
            ArmyManager armyManager = Application.getBean(ArmyManager.class);
            for (ArmyCampData armyCamp : arrivedArmyList) {
                if (firstArmyCamp != null && firstArmyCamp.getArmyId().equals(armyCamp.getArmyId())) {
                    continue;
                }
                ArmyInfo army = armyManager.findById(armyCamp.getArmyId());
                if (army == null) {
                    continue;
                }
                retArmy.add(army);
            }
        }
        return retArmy;
    }

    public PsSiegeEnginesSimple toPsSiegeEnginesSimple() {
        PsSiegeEnginesSimple psSiegeEnginesSimple = new PsSiegeEnginesSimple();
        psSiegeEnginesSimple.setAllianceId(allianceId);
        psSiegeEnginesSimple.setId(nodeId);
        psSiegeEnginesSimple.setEndTime(endTime);
        psSiegeEnginesSimple.setBuildCompleteTime(buildingCompleteTime);
        psSiegeEnginesSimple.setStatus(PsAllianceSiegeEngineStateType.findByValue(status.getId()));
        psSiegeEnginesSimple.setSiegeEnginesType(PsAllianceSiegeEngineType.findByValue(type.getId()));
        psSiegeEnginesSimple.setLastAttackTime(lastAttackTime);
        psSiegeEnginesSimple.setDeadNpcCnt(deadNormalMonsterCount);
        psSiegeEnginesSimple.setAutoAppointLeader(isAutoAssignCaptain);
        psSiegeEnginesSimple.setCapitalNodeId(capitalNodeId);
        return psSiegeEnginesSimple;
    }

    public PsMapRegionSiegeEngines toPsMapRegionSiegeEngines() {
        PsMapRegionSiegeEngines psSiegeEngines = new PsMapRegionSiegeEngines();

        psSiegeEngines.setId(nodeId);
        psSiegeEngines.setMetaId(metaId);
        psSiegeEngines.setX(position.getX());
        psSiegeEngines.setY(position.getY());
        psSiegeEngines.setSiegeEnginesType(type.getPsAllianceSiegeEngineType());
        psSiegeEngines.setState(PsAllianceSiegeEngineStateType.findByValue(status.getId()));
        psSiegeEngines.setBelongAllianceId(allianceId);
        psSiegeEngines.setBuildStartTime(startTime);
        psSiegeEngines.setBuildCompleteTime(buildingCompleteTime);
        psSiegeEngines.setLastAttackTime(lastAttackTime);
        psSiegeEngines.setEndTime(endTime);
        psSiegeEngines.setCapitalNodeId(capitalNodeId);
        psSiegeEngines.setBossDeadCount(0);
        psSiegeEngines.setMonsterDeadCount(deadNormalMonsterCount);
        psSiegeEngines.setAutoAppointLeader(isAutoAssignCaptain);

        SiegeEnginesServiceImpl siegeEnginesService = Application.getBean(SiegeEnginesServiceImpl.class);
        psSiegeEngines.setTroopMax(siegeEnginesService.getStationTroopMaxNum(this));
        psSiegeEngines.setSoldierMax(siegeEnginesService.getStationSoldierMaxNum(this));
        psSiegeEngines.setStationArmys(siegeEnginesService.stationArmyList(this));

        return psSiegeEngines;
    }


    public List<ArmyCampData> getArrivedArmyList() {
        return armyList.stream().filter(ArmyCampData::isArrive).collect(Collectors.toList());
    }

    @Override
    public PsMapNode toPsMapNode() {
        PsMapNode psMapNode = super.toPsMapNode();
        psMapNode.setSiegeEnginesInfo(toPsMapRegionSiegeEngines());
        return psMapNode;
    }

    @Override
    public GcMapNodeUpdate toMapNodeUpdate(MapNodeUpdater updater) {
        GcMapNodeUpdate msg = new GcMapNodeUpdate();
        PsMapNode psNode = super.toPsMapNode();
        if (updater != null) {
            updater.accept(psNode);
        }
        msg.addToNodes(psNode);
        return msg;
    }

    @Override
    public List<ArmyCampData> getArmys() {
        return armyList;
    }

    /**
     * 是否有人驻防
     *
     * @return
     */
    public boolean isStationed() {
        return armyList.stream().anyMatch(ArmyCampData::isArrive);
    }

    @Override
    public void removeEnemy(ArmyInfo army) {
        var armyManager = Application.getBean(ArmyManager.class);
        for (ArmyCampData armyCamp : armyList.stream().toList()) {
            if (!armyCamp.isArrive()) {
                continue;
            }
            ArmyInfo defendArmy = armyManager.findById(armyCamp.getArmyId());
            if (defendArmy != null) {
                Role defendRole = defendArmy.getOwner();
                armyManager.removeEnemy(defendRole, army);
            }
        }
    }

    @Override
    public String getMetaName() {
        var meta = getMeta();
        if (meta == null) {
            return "";
        }
        return meta.getName();
    }

    public boolean isBuildFinish() {
        return status == SiegeEnginesBuildState.FINISHED;
    }
}
