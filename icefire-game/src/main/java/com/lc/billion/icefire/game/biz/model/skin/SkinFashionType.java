package com.lc.billion.icefire.game.biz.model.skin;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

public enum SkinFashionType implements IntEnum {
    //城堡皮肤
    CHENG_BAO(1),

    //铭牌
    MING_PAI(2),

    //聊天气泡
    QI_PAO(3),

    //头像框
    HEAD_FRAME(4),

    // 排行榜称号
    RANK_FASHION(6);

    private final int id;

    private static final SkinFashionType[] INDEXES = EnumUtils.toArray(values());

    private SkinFashionType(int id) {
        this.id = id;
    }

    @Override
    public int getId() {
        return id;
    }

    public static SkinFashionType findById(int id) {
        if (id < 0 || id > INDEXES.length) {
            return null;
        }

        return INDEXES[id];
    }

}
