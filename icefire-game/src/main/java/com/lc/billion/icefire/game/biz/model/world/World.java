package com.lc.billion.icefire.game.biz.model.world;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.perf.Perf4jUtils;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.BizConstants;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.game.biz.container.PlayerUnitManager;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleCacheDao;
import com.lc.billion.icefire.game.biz.internalmsg.login.ClosePlayerReason;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.AbstractRole;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.cache.RoleCache;
import com.lc.billion.icefire.game.biz.model.scene.MapData;
import com.lc.billion.icefire.game.biz.model.scene.MapRegion;
import com.lc.billion.icefire.game.biz.service.impl.DaoService;
import com.lc.billion.icefire.game.biz.service.impl.ManagerService;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.ServiceStarterImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.MsgMetaManager;
import com.lc.billion.icefire.game.biz.service.impl.log.MsgTypeEnum;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.slow.SlowLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.scene.PlayerAction;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneResourceZoneManager;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.metrics.GameTickerMetrics;
import com.lc.billion.icefire.game.metrics.GameTickerMetricsFactory;
import com.longtech.cod.common.counter.Counter;
import com.longtech.ls.config.ServerType;
import lombok.Getter;
import org.perf4j.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class World {
    private static final Logger logger = LoggerFactory.getLogger(World.class);

    private final Map<Integer, MapData> mapDatas = new HashMap<>();

    @Getter
    private final Map<Integer, SceneResourceZoneManager> resourceZoneManagers = new HashMap<>();

    private final Map<Integer, PlayerUnitManager> playerManagersForCurrentServerId = new HashMap<>();
    private final Map<Integer, PlayerUnitManager> playerManagersForOServerId = new HashMap<>();

    @Getter
    private final PlayerUnitManager playerManagerForAll = new PlayerUnitManager();

    private ServiceDependency srvDpd;
    private DaoService daoService;
    private ServiceStarterImpl serviceStarter;
    private ManagerService service;

    private final List<AbstractTicker<?>> tickers = new ArrayList<>();

    private long lastAfterTickerTime;

    private GameTickerMetrics gameTickerMetrics;

    public void init(ServiceDependency srvDpd) {
        this.srvDpd = srvDpd;
        daoService = Application.getBean(DaoService.class);
        serviceStarter = Application.getBean(ServiceStarterImpl.class);
        service = Application.getBean(ManagerService.class);
        // 初始化游戏 Ticker 指标记录器
        gameTickerMetrics = Application.getBean(GameTickerMetricsFactory.class).create();
        if (ServerType.KVK_CONTROL == Application.getServerType()) {
            return;
        }

        initSceneResourceZoneManager();
        @SuppressWarnings("rawtypes")
        Map<String, AbstractTicker> beansOfType = Application.getBeansOfType(AbstractTicker.class);
        for (AbstractTicker<?> u : beansOfType.values()) {
            tickers.add(u);
        }
    }

    private void initSceneResourceZoneManager() {
        List<Integer> allServerIds = Application.getAllServerIds();
        for (Integer gameServerId : allServerIds) {
            init(gameServerId);
        }
    }

    private void init(int gameServerId) {
        MapData mapData = srvDpd.getMapLoader().load(gameServerId);
        mapDatas.put(gameServerId, mapData);
        SceneResourceZoneManager sceneResourceZoneManager = new SceneResourceZoneManager(mapData, srvDpd, gameServerId);
        resourceZoneManagers.put(gameServerId, sceneResourceZoneManager);
        PlayerUnitManager playerManager = new PlayerUnitManager();
        playerManagersForCurrentServerId.put(gameServerId, playerManager);
        playerManager = new PlayerUnitManager();
        playerManagersForOServerId.put(gameServerId, playerManager);
    }

    public void tick(long now) {
        if (!daoService.isLoadEnd()) {
            logger.info("tick has interrupt by dao load");
            return;
        }
        if (!serviceStarter.isBeforeTickerEnd()) {
            logger.info("tick has interrupt by service start");
            return;
        }
        // tick名称
        String op;

        service.beforeTick();
        HashMap<String, Long> hashMap = WorldWorker.count.get();
        StopWatch tickerStopWatch;

        for (AbstractTicker<?> ticker : tickers) {
            long start = TimeUtil.getNow();
            String name = ticker.getClass().getSimpleName();
            op = "WorldTick." + name;
            tickerStopWatch = Perf4jUtils.stopWatch(op);
            MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
            long startNanoTime = System.nanoTime();
            try {
                ticker.tick(now);
            } finally {
                hashMap.put(name, TimeUtil.getNow() - start);
                gameTickerMetrics.recordTickerExecution(op, System.nanoTime() - startNanoTime);
                // 处理下ticker里面每一步的耗时统计
                Counter counter = ticker.getCounter();
                counter.calcAvg();

                Map<String, Long> totals = counter.getTotal();
                for (Map.Entry<String, Long> entry : totals.entrySet()) {
                    String key = entry.getKey();
                    long cost = entry.getValue();
                    if (cost > 0) {
                        long average = counter.getAvgValue().computeIfAbsent(key, k -> 0L);
                        long max = counter.getMaxValue().computeIfAbsent(key, k -> 0L);
                        long times = counter.getTimes().computeIfAbsent(key, k -> 0L);
                        SlowLogUtil.tickLogDetail(name + "." + key, cost, average, max, times);
                    }
                }

                tickerStopWatch.stop();
                MsgMetaManager.clearMsgMeta();
            }
        }

        op = "clearLeaderAllianceRes";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        long start = TimeUtil.getNow();
        // 联盟心跳，内部10分钟一处理
        srvDpd.getAllianceService().lowTick(now);
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "kickOutInactiveMember";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        // 联盟心跳，内部10分钟一处理
        srvDpd.getAllianceInactiveManagementService().lowTick(now);
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "Milestone";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        // 里程碑心跳，内部1分钟一处理
        srvDpd.getMilestoneService().lowTick(now);
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "Official";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getOfficialsService().lowTick(now);
        hashMap.put(op, TimeUtil.getNow() - start);

        // 数据更新服务
        // srvDpd.getDataUpdateService().tick();
        op = "dao";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        daoService.tick(now);
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "titleManager";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getTitleService().tick(now);
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "CaravanMatch";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getCaravanMatchService().lowTick();
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "ItemRecord";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getItemRecordService().lowTick();
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "caravanHistory";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getCaravanService().lowTick();
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "allianceRecruit";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getAllianceRecruitService().lowTick();
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "regionCapital";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getRegionCapitalService().lowTick();
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "questionnaire";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getQuestionnaireService().lowTick(now);
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "horseMate";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getHorseService().lowTick();
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "redPack";
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        srvDpd.getRedPackActivityManager().lowTick(start);
        hashMap.put(op, TimeUtil.getNow() - start);

        op = "afterTickers";
        tickerStopWatch = Perf4jUtils.stopWatch("WorldTick." + op);
        MsgMetaManager.createMsgMeta(MsgTypeEnum.TICK, op);
        start = TimeUtil.getNow();
        try {
            if (TimeUtil.getNow() - lastAfterTickerTime > BizConstants.AFTER_TICK_INTERVAL) {
                service.afterTick();
                lastAfterTickerTime = TimeUtil.getNow();
            }
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("world.aftertick error", e);
            }
            service.cleanTickCache();
        } finally {
            tickerStopWatch.stop();
            hashMap.put("afterTick", TimeUtil.getNow() - start);
            MsgMetaManager.clearMsgMeta();
        }
    }

    public MapData getMapData(int serverId) {
        return mapDatas.get(serverId);
    }

    public SceneResourceZoneManager getResourceZoneManager(int serverId) {
        return resourceZoneManagers.get(serverId);
    }

    private Player getPlayerByCurrentServerId(int serverId, long roleId) {
        PlayerUnitManager playerUnitManager = playerManagersForCurrentServerId.get(serverId);
        if (playerUnitManager == null) {
            ErrorLogUtil.errorLog("缺少PlayerUnitManager",new RuntimeException(),"serverId",serverId);
            return null;
        }
        return playerUnitManager.get(roleId);
    }

    public Player getPlayer(Role role) {
        return getPlayerByCurrentServerId(role.getCurrentServerId(), role.getId());
    }

    public Player getPlayer(Player player) {
        return getPlayerByCurrentServerId(player.getCurrentServerId(), player.getId());
    }

    public Role getRole(long id) {
        return srvDpd.getRoleDao().findById(id);
    }

    public AbstractRole getAbstractRole(long roleId) {
        Role role = getRole(roleId);
        if (role != null) {
            return role;
        }
        RoleCacheDao roleCacheDao = Application.getBean(RoleCacheDao.class);
        RoleCache roleCache = roleCacheDao.findById(roleId);
        if (roleCache != null) {
            return roleCache;
        }
        return null;
    }

    public void playerForeach(PlayerAction playerAction) {
        playerManagerForAll.forEach(bean -> playerAction.action(bean));
    }

    /**
     * 按oServerId广播，不仅要广播O服还要广播K服的部分玩家
     *
     * @param oServerId
     * @param playerAction
     */
    public void playerForeachForOServerId(int oServerId, PlayerAction playerAction) {
        PlayerUnitManager playerUnitManager = playerManagersForOServerId.get(oServerId);
        if (playerUnitManager == null) {
            ErrorLogUtil.errorLog("缺少PlayerUnitManager",new RuntimeException(),"oServerId",oServerId);
            return;
        }
        playerUnitManager.forEach(bean -> playerAction.action(bean));
    }

    /**
     * 按currentServerId广播，仅要广播当前服
     *
     * @param currentServerId
     * @param playerAction
     */
    public void playerForeachForCurrentServerId(int currentServerId, PlayerAction playerAction) {
        PlayerUnitManager playerUnitManager = playerManagersForCurrentServerId.get(currentServerId);
        if (playerUnitManager == null) {
            ErrorLogUtil.errorLog("缺少PlayerUnitManager",new RuntimeException(),"curServerId",currentServerId);
            return;
        }
        playerUnitManager.forEach(bean -> playerAction.action(bean));
    }

    public void addPlayer(Player player) {
        if (player.getCurrentServerId() != 0) {
            PlayerUnitManager playerUnitManager = playerManagersForCurrentServerId.get(player.getCurrentServerId());
            if (playerUnitManager == null) {
                ErrorLogUtil.errorLog("缺少PlayerUnitManager",new RuntimeException(),"curServerId", player.getCurrentServerId());
            } else {
                playerUnitManager.add(player);
            }
        }
        if (player.getoServerId() != 0) {
            PlayerUnitManager playerUnitManager = playerManagersForOServerId.get(player.getoServerId());
            if (playerUnitManager == null) {
                if (!Application.isGVGBattleServer()) {
                    ErrorLogUtil.errorLog("缺少PlayerUnitManager",new RuntimeException(),"oServerId",player.getoServerId());
                }
            } else {
                playerUnitManager.add(player);
            }
        }
        playerManagerForAll.add(player);
    }

    public void removePlayer(Player player) {
        if (player.getCurrentServerId() != 0) {
            PlayerUnitManager playerUnitManager = playerManagersForCurrentServerId.get(player.getCurrentServerId());
            if (playerUnitManager == null) {
                ErrorLogUtil.errorLog("缺少PlayerUnitManager",new RuntimeException(),"curServerId",player.getCurrentServerId());
            } else {
                playerUnitManager.remove(player.getId());
            }
        }
        if (player.getoServerId() != 0) {
            PlayerUnitManager playerUnitManager = playerManagersForOServerId.get(player.getoServerId());
            if (playerUnitManager == null) {
                ErrorLogUtil.errorLog("缺少PlayerUnitManager",new RuntimeException(),"oServerId", player.getoServerId());
            } else {
                playerUnitManager.remove(player.getId());
            }
        }
        playerManagerForAll.remove(player.getId());
    }

    public int getPlayerTotalSize() {
        return playerManagerForAll.size();
    }

    public int getPlayerSizeByCurrentServerId(int currentServerId) {
        PlayerUnitManager playerUnitManager = playerManagersForCurrentServerId.get(currentServerId);
        if (playerUnitManager == null) {
            ErrorLogUtil.errorLog("缺少PlayerUnitManager",new RuntimeException(),"curServerId",currentServerId);
            return 0;
        }
        return playerUnitManager.size();
    }

    public MapRegion getMapRegion(int serverId, byte regionId) {
        MapData data = getMapData(serverId);
        return data == null ? null : data.getMapRegion(regionId);
    }

    /**
     * 默认全部踢下不做服务器区分
     * @param serverId
     */
    public void kickAllOnline(int serverId) {
        for (Player player : playerManagerForAll.values()) {
            try {
                player.tryKickOutFromWorld(ClosePlayerReason.GMKick);
            } catch (ExpectedException ignored) {

            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("踢玩家下线 系统异常", e, "playerId", player.getId());
            }
        }
    }
}
