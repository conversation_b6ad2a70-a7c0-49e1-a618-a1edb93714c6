package com.lc.billion.icefire.game.biz.redis;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.metrics.RedisMetrics;
import com.lc.billion.icefire.game.metrics.RedisMetricsHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.SetParams;
import redis.clients.jedis.params.ZParams;
import redis.clients.jedis.resps.Tuple;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.lc.billion.icefire.game.biz.exec.MainWorker.isInMainWorker;

/**
 * 
 * redis原始抽象类
 * 
 * <AUTHOR>
 *
 */
public abstract class RedisClient {

	private static Logger LOG = LoggerFactory.getLogger(RedisClient.class);

	// codis属性
	// protected String zkAddr;
	//
	// protected String zkProxyDir;
	//
	// protected int zkSessionTimeoutMillis = 30000;

	// jedis属性
	protected String host;

	protected int port;

	protected int timeout = 30000;

	/**
	 * JedisPoolConfig
	 */
	protected String password;

	protected int dbindex;
	protected int maxTotal;
	protected int maxIdle;
	protected int mxWaitMillis;


	private static final long OPERATION_COST_LONG = 100L;

	@PostConstruct
	public abstract void init();


	/**
	 * 尽量不要直接获取jedis操作redis数据~调用封装好的方法操作redis~
	 * 调用该方法后记得再使用完jedis调用close关闭
	 * @return
	 */
	protected abstract Jedis getResource();

	public abstract void close(Jedis resource);

	public <T> T doOperation(IRedisOperation<T> op, RedisDataType redisDataType, String opName) {
		long begin = TimeUtil.getNow();
		Jedis jedis = getResource();
		boolean success = false;
		Exception operationException = null;
		try {
			T result = op.execute(jedis);
			success = true;
			return result;
		}catch (ExpectedException ignored) {
			// ExpectedException不记录为错误
			success = true;
		} catch (Exception e) {
			operationException = e;
			ErrorLogUtil.exceptionLog("redis operator failed", e);
		} finally {
			jedis.close();
			long costTime = TimeUtil.getNow() - begin;

			// 记录监控指标
			recordMetrics(redisDataType, opName, costTime, success, operationException);

			if(costTime > OPERATION_COST_LONG) {
				ErrorLogUtil.warnLog("[redis-slow] redis cost too long", "costTime",costTime);
			}
            try {
                if (isInMainWorker()) {
					LOG.info("redis_operator_in_main_worker, costTime: {} RedisDataType: {}", costTime, redisDataType);
                }
            } catch (Throwable ignored) {}
		}
		return null;
	}

	public long zcard(final String key, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zcard(key), redisDataType, "zcard");
	}

	public long incr(final String key, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.incr(key), redisDataType, "incr");
	}

	public String get(final String key, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.get(key), redisDataType, "get");
	}

	public byte[] getBytes(final String key, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.get(key.getBytes()), redisDataType, "get");
	}

	public List<String> mget(final List<String> keys, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.mget(keys.toArray(new String[0])), redisDataType, "mget");
	}

	public String set(final String key, final String value, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.set(key, value), redisDataType, "set");
	}

	public String set(final String key, final byte[] value, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.set(key.getBytes(), value), redisDataType, "set");
	}

	public String set(final String key, final byte[] value, final long expireTime, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.psetex(key.getBytes(), expireTime, value), redisDataType, "set");
	}

	public String set(final String key, final String value, final long expireTime, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.psetex(key, expireTime, value), redisDataType, "set");
	}

	public String setnx(final String key, final String value, final long seconds, RedisDataType redisDataType) {
		SetParams params = SetParams.setParams().nx().ex(seconds);
		return doOperation(jedis -> jedis.set(key, value, params), redisDataType, "setnx");
	}

	public long setnx(final String key, final String value, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.setnx(key, value), redisDataType, "setnx");
	}

	/**
	 * set value + expire seconds
	 * 
	 * 
	 * @param key
	 * @param seconds
	 * @param value
	 * @return
	 */
	public String setex(final String key, final long seconds, final String value, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.setex(key, seconds, value), redisDataType, "setex");
	}

	public long del(final String key, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.del(key), redisDataType, "del");
	}

	public long hdel(final String key, final String mapKey, final RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.hdel(key, mapKey), redisDataType, "hdel");
	}

	public long sadd(final String key, RedisDataType redisDataType, final String... members) {
		return doOperation(jedis -> jedis.sadd(key, members), redisDataType, "sadd");
	}

	public Set<String> smembers(final String key, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.smembers(key), redisDataType, "smembers");
	}

	public long srem(final String key, RedisDataType redisDataType, final String... members) {
		return doOperation(jedis -> jedis.srem(key, members), redisDataType, "srem");
	}

	public List<String> hmget(final String key, final List<String> fields, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.hmget(key, fields.toArray(new String[0])), redisDataType, "hmget");
	}

	public String hget(final String key, String mapKey, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.hget(key, mapKey), redisDataType, "hget");
	}


	public Map<String, String> hgetAll(final String key, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.hgetAll(key), redisDataType, "hgetall");
	}

	public String hmset(final String key, final Map<String, String> map, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.hmset(key, map), redisDataType, "hmset");
	}

	public Long hset(final String key, final String mapKey, final String value, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.hset(key, mapKey, value), redisDataType, "hset");
	}

    /**
     * 设置过期时间
     * @param key
     * @param ms
     * @param fields
     * @param redisDataType
     * @return
     */
    // 注释掉，redis 7.4.0 后才能用，本地调试环境、线上云redis都还没支持到
//    public List<Long> hpexpire(final String key, final long ms, RedisDataType redisDataType, final String... fields) {
//        return doOperation(jedis -> jedis.hpexpire(key, ms, fields), redisDataType);
//    }

	public long zadd(final String key, final double score, final String member, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zadd(key, score, member), redisDataType, "zadd");
	}

	public long zadd(final String key, Map<String, Double> scoreMembers, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zadd(key, scoreMembers), redisDataType, "zadd");
	}

	public long zdel(final String key, final String member, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zrem(key, member), redisDataType, "zrem");
	}

	public double zincrby(final String key, double score, String member, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zincrby(key, score, member), redisDataType, "zincrby");
	}

	public List<String> zrange(final String key, final long start, final long end, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zrange(key, start, end), redisDataType, "zrange");
	}

	public List<String> zrevrange(final String key, final long start, final long end, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zrevrange(key, start, end), redisDataType, "zrevrange");
	}

	public Long zrevrank(final String key, final String member, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zrevrank(key, member), redisDataType, "zrevrank");
	}

	public Double zscore(final String key, final String member, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zscore(key, member), redisDataType, "zscore");
	}

	public List<String> zrevrangeByScore(final String key, final double max, final double min, final int offset, final int count, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zrevrangeByScore(key, max, min, offset, count), redisDataType, "zrevrangebyscore");
	}

	public List<String> zrangeByScore(final String key, final double min, final double max, final int offset, final int count, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zrangeByScore(key, min, max, offset, count), redisDataType, "zrangebyscore");
	}

	public List<Tuple> zrevrangeWithScores(final String key, final long start, final long end, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zrevrangeWithScores(key, start, end), redisDataType, "zrevrangewithscores");
	}

	public List<Tuple> zrangeByScore(final String key, final long start, final long end, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zrangeByScoreWithScores(key, start, end), redisDataType, "zrangebyscore");
	}

	public Long zunionstore(final String dstkey, RedisDataType redisDataType, final String... sets) {
		return doOperation(jedis -> jedis.zunionstore(dstkey, sets), redisDataType, "zunionstore");
	}

	public Long zunionstore(final String dstkey, RedisDataType redisDataType, final ZParams params, final String... sets) {
		return doOperation(jedis -> jedis.zunionstore(dstkey, params, sets), redisDataType, "zunionstore");
	}

	public Long zcount(String dstkey, RedisDataType redisDataType, double min, double max) {
		return doOperation(jedis -> jedis.zcount(dstkey, min, max), redisDataType, "zcount");
	}

	public long zRemRangeByRank(final String key, final long start, final long end, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.zremrangeByRank(key, start, end), redisDataType, "zremrangebyrank");
	}

	public Boolean exists(final String key, RedisDataType redisDataType){
		return doOperation(jedis -> jedis.exists(key), redisDataType, "exists");
	}

	public Object eval(final String script, final List<String> keys, final List<String> values, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.eval(script, keys, values), redisDataType, "eval");
	}

	public Long expire(final String key, RedisDataType redisDataType, final int seconds) {
		return doOperation(jedis -> jedis.expire(key, seconds), redisDataType, "expire");
	}

	public String ltrim(final String key, RedisDataType redisDataType, final long start , final long stop) {
		return doOperation(jedis -> jedis.ltrim(key, start, stop), redisDataType, "ltrim");
	}

	public Long rpush(final String key, RedisDataType redisDataType, String... params) {
		return doOperation(jedis -> jedis.rpush(key, params), redisDataType, "rpush");
	}

	public List<String> lrange(final String key, RedisDataType redisDataType, final long start , final long stop) {
		return doOperation(jedis -> jedis.lrange(key, start, stop), redisDataType, "lrange");
	}

	public Long llen(final String key, RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.llen(key), redisDataType, "llen");
	}

	public String lset(final String key, RedisDataType redisDataType,long listIndex,String valueStr) {
		return doOperation(jedis -> jedis.lset(key, listIndex, valueStr), redisDataType, "lset");
	}

	public List<String> mget(RedisDataType redisDataType, final String... keys) {
		return doOperation(jedis -> jedis.mget(keys), redisDataType, "mget");
	}

	public long incrBy(final String key, long value,RedisDataType redisDataType) {
		return doOperation(jedis -> jedis.incrBy(key, value), redisDataType, "incrby");
	}

	public long zunionstore(final String dstkey, RedisDataType redisDataType, final String key) {
		return doOperation(jedis -> jedis.zunionstore(dstkey, key), redisDataType, "zunionstore");
	}

	public String rename(final String srcKey, final String dstKey, RedisDataType redisType){
		return doOperation(jedis -> jedis.rename(srcKey, dstKey), redisType, "rename");
	}

	/**
	 * redis 操作接口
	 * 
	 * @param <T>
	 */
	public interface IRedisOperation<T> {

		T execute(Jedis jedis);

	}


	// get set

	// public String getZkAddr() {
	// return zkAddr;
	// }
	//
	// public void setZkAddr(String zkAddr) {
	// this.zkAddr = zkAddr;
	// }
	//
	// public String getZkProxyDir() {
	// return zkProxyDir;
	// }
	//
	// public void setZkProxyDir(String zkProxyDir) {
	// this.zkProxyDir = zkProxyDir;
	// }
	//
	// public int getZkSessionTimeoutMillis() {
	// return zkSessionTimeoutMillis;
	// }
	//
	// public void setZkSessionTimeoutMillis(int zkSessionTimeoutMillis) {
	// this.zkSessionTimeoutMillis = zkSessionTimeoutMillis;
	// }

	public String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public int getTimeout() {
		return timeout;
	}

	public void setTimeout(int timeout) {
		this.timeout = timeout;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public int getMaxTotal() {
		return maxTotal;
	}

	public void setMaxTotal(int maxTotal) {
		this.maxTotal = maxTotal;
	}

	public int getMaxIdle() {
		return maxIdle;
	}

	public void setMaxIdle(int maxIdle) {
		this.maxIdle = maxIdle;
	}

	public int getMxWaitMillis() {
		return mxWaitMillis;
	}

	public void setMxWaitMillis(int mxWaitMillis) {
		this.mxWaitMillis = mxWaitMillis;
	}

	public int getDbindex() {
		return dbindex;
	}

	public void setDbindex(int dbindex) {
		this.dbindex = dbindex;
	}

	/**
	 * 记录Redis操作监控指标
	 *
	 * @param redisDataType Redis数据类型
	 * @param costTime      执行耗时（毫秒）
	 * @param success       是否成功
	 * @param exception     异常信息（如果有）
	 */
	private void recordMetrics(RedisDataType redisDataType, String opName, long costTime, boolean success, Exception exception) {
		try {
			RedisMetrics redisMetrics = RedisMetricsHolder.getRedisMetrics();
			if (redisMetrics != null) {
				String dataType = redisDataType.getName();
				redisMetrics.recordOperation(dataType, opName, costTime, success);

				// 如果有异常且不是ExpectedException，记录异常指标
				if (exception != null && !(exception instanceof ExpectedException)) {
					redisMetrics.recordException(dataType, opName, exception);
				}
			}
		} catch (Throwable e) {
			// 监控指标记录失败不应影响业务，静默处理
		}
	}


}
