package com.lc.billion.icefire.game.biz.service.impl;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.GameConfig;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.config.FilterWordConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Service
public class CheckStringServiceImpl {

	@Autowired
	private ConfigServiceImpl configSrv;

	private static char[] ILLEGAL_CHARS = {' ', '　', '<', '>', '_', '\\', '`', '~', '!', '@', '#', '$', '%', '^', '&', '*', '?', '\'', '"', ':', ';', '/', '+', '-', '=', '{',
			'}'};

	public CheckStringServiceImpl() {

	}

	public boolean checkName(String name, int maxLen) {
		return checkName(name, maxLen, true, null);
	}

	public boolean checkName(String name, int maxLen, boolean includeSpace) {
		return checkName(name, maxLen, includeSpace, null);
	}

	public boolean checkName(String name, int maxLen, boolean includeSpace, char[] includeChars) {
		// 先简单检查一下长度
		int len = name.length();
		if (len < 1 || len > maxLen) {
			return false;
		}
		for (int i = 0; i < len; i++) {
			char c = name.charAt(i);
			// 判断字符合法性
			if (includeChars != null && Arrays.binarySearch(includeChars, c) >= 0) { // 可以包含的字符，将不进行下面的字符过滤
				continue;
			}
			if (!checkChar(c, includeSpace)) {
				return false;
			}
		}

		return len <= maxLen;
	}

	public boolean checkName(String name, int minLen, int maxLen, boolean includeSpace, char[] includeChars) {
		// 先简单检查一下长度
		int len = name.length();
		if (len < minLen || len > maxLen) {
			return false;
		}
		for (int i = 0; i < len; i++) {
			char c = name.charAt(i);
			// 判断字符合法性
			if (includeChars != null && Arrays.binarySearch(includeChars, c) >= 0) { // 可以包含的字符，将不进行下面的字符过滤
				continue;
			}
			if (!checkChar(c, includeSpace)) {
				return false;
			}
		}

		return true;
	}

	private boolean checkChar(char c, boolean includeSpace) {
		if (c == ' ') {
			return includeSpace;
		}

		for (int i = 0; i < ILLEGAL_CHARS.length; i++) {
			if (c == ILLEGAL_CHARS[i]) {
				return false;
			}
		}

		// mysql特殊字符过滤
		if (c < 0X0000 || c > 0Xffff) {
			return false;
		}

		return true;
	}

	public boolean checkShortName(String name, int minLen, int maxLen, boolean includeSpace) {
		// 先简单检查一下长度
		int len = name.length();
		if (len < minLen || len > maxLen) {
			return false;
		}
//		int nameLen = 0;
//		for (int i = 0; i < len; i++) {
//			char c = name.charAt(i);
//			// 判断字符合法性
//			if (!checkShortChar(c, includeSpace)) {
//				return false;
//			}
//			if (c > 127) { // TODO （周会勤:不会执行到这里） 非ascii码算2个字符
//				nameLen += 2;
//			} else {
//				nameLen += 1;
//			}
//		}
//		// 检查长度
//		if (nameLen > maxLen) {
//			return false;
//		}


		return true;
	}

	private boolean checkShortChar(char c, boolean includeSpace) {
		if (c == ' ') {
			return includeSpace;
		}
		return c >= '0' && c <= '9' || c >= 'A' && c <= 'Z' || c >= 'a' && c <= 'z';
	}

	public boolean checkWord(String word) {
		GameConfig gameConfig = ServerConfigManager.getInstance().getGameConfig();
		if (!gameConfig.isCheckWord()) { // 关闭验证
			return true;
		}
		FilterWordConfig wordConfig = configSrv.getConfig(FilterWordConfig.class);
		if (!wordConfig.check(word)) {
			return false;
		}
		return true;
	}

	public String clean(String word) {
		FilterWordConfig fwCfg = configSrv.getConfig(FilterWordConfig.class);
		return fwCfg.clean(word);
	}
}
