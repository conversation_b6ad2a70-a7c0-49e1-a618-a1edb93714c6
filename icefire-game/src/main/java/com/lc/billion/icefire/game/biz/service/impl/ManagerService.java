package com.lc.billion.icefire.game.biz.service.impl;

import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.manager.RoleBuildSearchManager;
import com.lc.billion.icefire.game.biz.manager.activity.*;
import com.lc.billion.icefire.game.biz.manager.alliance.AllianceMessageBordManager;
import com.lc.billion.icefire.game.biz.manager.alliance.AllianceRequestManager;
import com.lc.billion.icefire.game.biz.manager.alliance.AllianceSettingManager;
import com.lc.billion.icefire.game.biz.manager.bingo.BingoManager;
import com.lc.billion.icefire.game.biz.manager.gvg.GVGGameDataVoManager;
import com.lc.billion.icefire.game.biz.manager.gvg.GVGRoleInfoManager;
import com.lc.billion.icefire.game.biz.manager.gvg.RZEGameDataVoManager;
import com.lc.billion.icefire.game.biz.manager.kvk.IKvkSeasonSwitchManager;
import com.lc.billion.icefire.game.biz.manager.kvk.IMigrateForKVKManager;
import com.lc.billion.icefire.game.biz.manager.migrate.MigrateForEverManager;
import com.lc.billion.icefire.game.biz.manager.migrateforgvg.RoleCacheManager;
import com.lc.billion.icefire.game.biz.manager.tvt.TvtGameDataManager;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.fight.FightPowerServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.gvg.model.MigrateForEverContext;
import com.lc.billion.icefire.game.biz.service.impl.gvg.model.MigrateForGVGContext;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.officials.OfficialsService;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.manager.alliance.GVGAllianceManager;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleFieldTimeLineManager;
import com.lc.billion.icefire.kvkseason.biz.manager.honor.KvkHonorManager;
import com.lc.billion.icefire.kvkseason.biz.manager.kvk.RoleSeasonInfoManager;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.*;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class ManagerService {
    private static final Logger logger = LoggerFactory.getLogger(ManagerService.class);

    @Autowired
    private ApplicationContext appCtx;
    @Autowired
    private ServiceDependency sdp;
    @Autowired
    private FightPowerServiceImpl fightPowerService;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private OfficialsService officialsService;

    private final List<AbstractRoleManager> roleManagers = new ArrayList<>();

    private final List<IRoleAllianceManager> roleAllianceManagers = new ArrayList<>();

    private final List<IMigrateForEverManager> migrateForEverManagers = new ArrayList<>();

    private final List<IAccountDeleteManager> accountDeleteManagers = new ArrayList<>();

    private final List<IMigrateForGVGManager> migrateForGVGManagers = new ArrayList<>();

    private final List<IMigrateForKVKManager> migrateForKVKManagers = new ArrayList<>();

    private final List<IKvkSeasonSwitchManager> kvkSeasonSwitchManagers = new ArrayList<>();

    private RoleManager roleManager;

    @PostConstruct
    public void init() {
        // 需要加载的manager
        List<String> managerNames = initManagerName();
        // spring里所有 IManager
        Map<String, IManager> iManagers = appCtx.getBeansOfType(IManager.class, false, true);
        for (String managerName : managerNames) {
            IManager iManager = iManagers.get(managerName);
            if (iManager == null) {
                throw new AlertException("加载manager报错,实例找不到","managerName",managerName);
            }
            if (iManager instanceof AbstractRoleManager) {
                roleManagers.add((AbstractRoleManager) iManager);
            }

            if (iManager instanceof IRoleAllianceManager) {
                roleAllianceManagers.add((IRoleAllianceManager) iManager);
            }

            if (iManager instanceof IMigrateForEverManager) {
                migrateForEverManagers.add((IMigrateForEverManager) iManager);
            }

            if (iManager instanceof IMigrateForGVGManager) {
                migrateForGVGManagers.add((IMigrateForGVGManager) iManager);
            }

            if (iManager instanceof RoleManager) {
                this.roleManager = (RoleManager) iManager;
            }

            if (iManager instanceof IAccountDeleteManager) {
                accountDeleteManagers.add((IAccountDeleteManager) iManager);
            }

            if (iManager instanceof IMigrateForKVKManager) {
                migrateForKVKManagers.add((IMigrateForKVKManager) iManager);
            }

            if (iManager instanceof IKvkSeasonSwitchManager) {
                kvkSeasonSwitchManagers.add((IKvkSeasonSwitchManager) iManager);
            }
        }

        Set<String> allNames = new HashSet<String>(iManagers.keySet());
        allNames.removeAll(managerNames);
        if (JavaUtils.bool(allNames)) {
            allNames.forEach(managerName -> ErrorLogUtil.errorLog("漏加载manager","managerName",  managerName));
            throw new AlertException("加载manager报错,数量不一致","缺少个数",allNames.size());
        }

    }

    public void startService() {
    }

    private List<String> initManagerName() {
        List<String> list = new ArrayList<>();
        // 最先
        addManager(list, RoleServerInfoManager.class);
        addManager(list, GVGAllianceManager.class);
        // 其次加载roleManager，其他逻辑可能依赖role
        addManager(list, RoleManager.class);
        addManager(list, RoleCityManager.class);
        addManager(list, RoleExtraManager.class);
        addManager(list, RoleRecordManager.class);
        addManager(list, RoleDeviceManager.class);
        addManager(list, CityBuildManager.class);
        addManager(list, ActivityManager.class);
        addManager(list, AllianceMessageBordManager.class);
        addManager(list, MissionManager.class);
        addManager(list, ResourceOutputManager.class);
        addManager(list, RoleAllianceRecordManager.class);
        addManager(list, RoleCurrencyManager.class);
        addManager(list, RoleHeadManager.class);
        addManager(list, RoleHeroManager.class);
//        addManager(list, RoleEquipManager.class);
        addManager(list, RoleItemManager.class);
        addManager(list, RoleLibaoManager.class);
        addManager(list, RoleLotteryManager.class);
        addManager(list, RoleSettingManager.class);
        addManager(list, RoleVipManager.class);
        addManager(list, RoleStoryManager.class);
        addManager(list, SkinManager.class);
        addManager(list, SoldierManager.class);
        addManager(list, WorkProgressManager.class);
        addManager(list, AllianceReplaceManager.class);
        addManager(list, AllianceGiftManager.class);
        addManager(list, RuinsNodeManager.class);
        addManager(list, AllianceTechManager.class);
        addManager(list, RoleActivityRecordManager.class);
        addManager(list, RoleDailyRechargeManager.class);
        addManager(list, AllianceRequestManager.class);
        addManager(list, AllianceSettingManager.class);
        addManager(list, GVGGameDataVoManager.class);
        addManager(list, GVGBattleDataVoManager.class);
        addManager(list, RoleStrongHoldManager.class);
        addManager(list, GVGBattleFieldTimeLineManager.class);
        addManager(list, RoleTitleManager.class);
        addManager(list, RoleCacheManager.class);
        addManager(list, GVGRoleInfoManager.class);
        addManager(list, WorldExploreEventManager.class);
        addManager(list, AllianceMemberManager.class);
        addManager(list, CSARoleAllianceManager.class);
        addManager(list, RoleSeasonInfoManager.class);
        addManager(list, KvkHonorManager.class);
        addManager(list, MigrateForEverManager.class);
        addManager(list, LegionRequestManager.class);
        addManager(list, RZEGameDataVoManager.class);
        addManager(list, LegionManager.class);
        addManager(list, LegionOfficialManager.class);
        addManager(list, BingoManager.class);
        addManager(list, TvtGameDataManager.class);
        addManager(list, LegionLogManager.class);
        addManager(list, LegionMarkManager.class);
        addManager(list, OnlineRewardDataManager.class);
        addManager(list, NewStrongestLordsActivityManager.class);
        addManager(list, RolePopularWillManager.class);
        addManager(list, CommonMissionActivityManager.class);
        addManager(list, RoleSiYuLinkNewUserRewardManager.class);
        addManager(list, RoleGameEventManager.class);
        addManager(list, AllianceNameManager.class);
        addManager(list, RoleUserInfoManager.class);
        addManager(list, ActivityCommonStrongestLordManager.class);
        addManager(list, BattleLoseCompensationManager.class);
        addManager(list, BarbarianActivityManager.class);
        addManager(list, RoyalActivityManager.class);
        addManager(list, RechargeScoreActivityManager.class);
        addManager(list, SnowActivityManager.class);
        addManager(list, SignActivityManager.class);
        addManager(list, LuckyBagActivityManager.class);
        addManager(list, RedPackActivityManager.class);

        addManager(list, CaravanManager.class);
        addManager(list, ArenaMigrateManager.class);
        addManager(list, RoleHorseManager.class);
        addManager(list, RoleBuildSearchManager.class);

        return list;
    }

    private void addManager(List<String> list, Class<? extends IManager> manager) {
        String managerName = manager.getName();
        if (list.contains(managerName)) {
            throw new AlertException("初始化加载manager重复","managerName",managerName);
        }
        list.add(managerName);
    }

    public Role createRole(int db, Long roleId, long accountId) {
        Role newCreateRole = this.roleManager.startCreateRole(db, roleId, accountId);
        return newCreateRole;
    }

    public void uncreateRole(Long roleId) {
        this.roleManager.delete(roleId);
    }

    public void onCreateRole(Role role, Player player) {
        roleManagers.forEach(manager -> manager.onCreateRole(role, player));
    }

    public void onUncreateRole(Role role) {
        roleManagers.forEach(manager -> manager.onUncreateRole(role));
    }

    public void afterCreateRole(Role role, Player player) {
        roleManagers.forEach(manager -> manager.afterCreateRole(role, player));
    }

    public void beforeLogin(Role role) {
        roleManagers.forEach(manager -> manager.beforeLogin(role));
    }

    public void afterLogin(Role role) {
        roleManagers.forEach(manager -> manager.afterLogin(role));
    }

    /**
     * 永久迁服本地动作
     */
    public void migrateForeverInSrcServer(MigrateForEverContext migrateForEverContext) {
        migrateForEverManagers.forEach(manager -> manager.migrateForeverInSrcServer(migrateForEverContext));
        officialsService.officialsAbdicate(migrateForEverContext.getRoleId(), migrateForEverContext.getSrcServerId());
    }

    /**
     * 永久迁服失败动作 失败了不做任何操作避免更麻烦的情况
     */
    public void migrateForeverInSrcServerFailed(MigrateForEverContext migrateForEverContext) {
        migrateForEverManagers.forEach(manager -> manager.migrateForeverInSrcServerFailed(migrateForEverContext));
    }

    /**
     * 永久迁服目标服动作
     */
    public void migrateForeverInTargetServer(Role role) {
        migrateForEverManagers.forEach(manager -> {
            try {
                manager.migrateForeverInTargetServer(role);
            } catch (ExpectedException ignored) {

            } catch (Exception e) {
                // 为了其他的操作继续执行
                ErrorLogUtil.exceptionLog("玩家迁服后在目标服登录时的处理报错", e);
            }
        });
    }

    /**
     * GVG迁服在本服的动作
     */
    public void migrateForGVGInSrcServer(Long roleId, MigrateForGVGContext migrateForGVGContext) {
        migrateForGVGManagers.forEach(manager -> manager.migrateForGVGInSrcServer(roleId, migrateForGVGContext));
    }

    /**
     * GVG迁服失败动作
     */
    public void migrateForGVGInSrcServerFailed(MigrateForGVGContext migrateForGVGContext) {
        migrateForGVGManagers.forEach(manager -> manager.migrateForGVGInSrcServerFailed(migrateForGVGContext));
    }

    /**
     * GVG迁服目标服动作
     */
    public void migrateForGVGInTargetServerForFirstLogin(Role role) {
        migrateForGVGManagers.forEach(manager -> {
            try {
                manager.migrateForGVGInTargetServerForFirstLogin(role);
            } catch (ExpectedException ignored) {

            } catch (Exception e) {
                // 为了其他的操作继续执行
                ErrorLogUtil.exceptionLog("玩家GVG迁服后在目标服登录时的处理报错", e);
            }
        });
    }

    public void migrateForGVGBackFirstLogin(Role role) {
        migrateForGVGManagers.forEach(manager -> {
            try {
                manager.migrateForGVGBackFirstLogin(role);
            } catch (ExpectedException ignored) {

            } catch (Exception e) {
                // 为了其他的操作继续执行
                ErrorLogUtil.exceptionLog("玩家GVG迁回首次登录时的处理报错", e);
            }
        });
    }

    public void migrateForKVKFirstLogin(Role role) {
        migrateForKVKManagers.forEach(manager -> {
            try {
                manager.migrateForKVKFirstLoginInKVKServer(role);
            } catch (ExpectedException ignored) {

            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("玩家KVK迁服首次登录时的处理报错", e);
            }
        });
    }

    public void onAllianceJoin(Long roleId, Long newAllianceId) {
        roleAllianceManagers.forEach(manager -> manager.onAllianceJoin(roleId, newAllianceId));
    }

    public void onAllianceLeave(Long roleId, Long oldAllianceId) {
        roleAllianceManagers.forEach(manager -> manager.onAllianceLeave(roleId, oldAllianceId));
    }

    public void onLegionJoin(Long allianceId, Long legionId) {
        roleAllianceManagers.forEach(manager -> manager.onLegionJoin(allianceId, legionId));
    }

    public void onLegionLeave(Long allianceId, Long legionId) {
        roleAllianceManagers.forEach(manager -> manager.onLegionLeave(allianceId, legionId));
    }

    public void onAllianceChangeName(Long allianceId) {
        roleAllianceManagers.forEach(manager -> manager.onAllianceChangeName(allianceId));
    }

    public void onAllianceChangeAliasName(Long allianceId) {
        roleAllianceManagers.forEach(manager -> manager.onAllianceChangeAliasName(allianceId));
    }
    public void accountDelete(Role role) {
        accountDeleteManagers.forEach(manager -> manager.accountDelete(role));
    }

    public void beforeTick() {
        roleManagers.forEach(manager -> manager.beforeTicker());
    }

    public void afterTick() {
        roleManagers.forEach(manager -> {
            manager.afterTicker();
            manager.cleanTickerCache();
        });
//        fightPowerService.afterTicker();
    }

    /**
     * 当出现异常需要手动清理tickCache时调用这个方法
     */
    public void cleanTickCache() {
        logger.info("tick cache clean begin after exception");
        roleManagers.forEach(manager -> {
            manager.cleanTickerCache();
        });
    }

    public void kvkSeasonSwitchFirstLogin(Role role) {
        migrateForKVKManagers.forEach(manager -> manager.kvkSeasonSwitchFirstLogin(role));
        activityManager.kvkSeasonSwitchFirstLogin(role);
    }

    public void kvkSeasonSwitch(int newSeason) {
        logger.warn("ManagerService kvkSeasonSwitch start, newSeason={}", newSeason);
        kvkSeasonSwitchManagers.forEach(manager -> manager.kvkSeasonSwitch(newSeason));
        // 赛季里程碑相关数据清理
        sdp.getMilestoneService().clearMilestoneDataWhenSeasonChange();
        sdp.getRankService().onKvKSeasonSwitch(newSeason);
        // 赛季任务，清理上个赛季数据
        sdp.getSeasonTaskService().seasonChange();
        logger.warn("ManagerService kvkSeasonSwitch end, newSeason={}", newSeason);
    }
    public void migrateForKVK(Role role, int fromServerId, int toServerId, boolean isEnterKvk) {
        migrateForKVKManagers.forEach(manager -> manager.migrateForKvk(role, fromServerId, toServerId, isEnterKvk));
    }
}
