package com.lc.billion.icefire.game.biz.service.impl.activity;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig.ActivityListMeta;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.manager.gvg.GVGGameDataVoManager;
import com.lc.billion.icefire.game.biz.manager.tvt.TvtGameDataManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.csattack.ICrossServerAttackService;
import com.lc.billion.icefire.game.biz.service.impl.gvg.GVGGameService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.protocol.GcAcitivityList;
import com.lc.billion.icefire.protocol.GcAcitivityUpdate;
import com.lc.billion.icefire.protocol.structure.PsActivityInfo;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.thrift.TBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;

@Service
public class PlayerActivityExport {
    private static final Logger logger = LoggerFactory.getLogger(PlayerActivityExport.class);

    @Autowired
    private ActivityServiceImpl activityService;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private GVGGameDataVoManager gvgDataVoManager;
    @Autowired
    private GVGGameService gvgGameService;
    @Autowired
    private ICrossServerAttackService crossServerAttackService;
    @Autowired
    private TvtGameDataManager tvtDataManager;

    /**
     * 给玩家推送所有活动信息
     *
     */
    public void pushPlayerActivitiesInfo(Role role) {
        GcAcitivityList msg = new GcAcitivityList();
        ActivityListConfig activityListConfig = configService.getConfig(ActivityListConfig.class);
        Collection<Activity> allActivities = activityDao.findAll();
        if (JavaUtils.bool(allActivities)) {
            allActivities.forEach(activity -> {
                AbstractActivityHandler<?> activityHandler = activityService.getActivityHandler(activity.getType());
                // 活动元数据
                ActivityListMeta activityListMeta = activityListConfig.getMetaById(activity.getMetaId());
                if (activityListMeta == null) {
                    ErrorLogUtil.errorLog("活动meta数据空","metaId",activity.getMetaId());
                    return;
//                    throw new RuntimeException(errorMsg);
                }
                if(!activityListMeta.isVisible(role.getClientId())){
                    return;
                }
                if (activityHandler == null || !activity.getType().isNeedHandler()) {
                    return;
                }
                PsActivityInfo roleActivityInfo = activityHandler.getRoleActivityInfo(role, activityListMeta);
                if (roleActivityInfo != null) {
                    msg.addToActivities(roleActivityInfo);
                    // 活动详细数据
                    TBase<?, ?> psInfo = activityHandler.getActivityInfo(role, activity.getMetaId());
                    if (psInfo != null) {
                        role.send(psInfo);
                    }
                }
            });
        }
        // GVG活动 2021/8/17包括GvG杯赛数据
        ActivityVo gvgActivityVo = gvgDataVoManager.findGvgActivityVo();
        // 如果是跨服玩法迁服玩家登陆，不发送gvg活动数据
        if (gvgActivityVo != null && !crossServerAttackService.isCSACrossPlayer(role) && TimeUtil.getNow() < gvgActivityVo.getEndTime() && isRoleCanJoin(gvgActivityVo, role)) {
            AbstractActivityHandler<?> activityHandler = activityService.getActivityHandler(gvgActivityVo.getType());
            // 活动元数据
            ActivityListMeta activityListMeta = activityListConfig.getMetaById(gvgActivityVo.getMetaId());
            if (activityListMeta == null) {
                ErrorLogUtil.errorLog("活动meta数据空","metaId",gvgActivityVo.getMetaId());
                throw new AlertException("活动meta数据空","metaId",gvgActivityVo.getMetaId());
            }
            PsActivityInfo roleActivityInfo = activityHandler.getRoleActivityInfo(role, activityListMeta);
            if (roleActivityInfo != null) {
                msg.addToActivities(roleActivityInfo);
                // 活动详细数据
                TBase<?, ?> psInfo = activityHandler.getActivityInfo(role, gvgActivityVo.getMetaId());
                if (psInfo != null) {
                    role.send(psInfo);
                }
            }
        }

        // TVT 活动
        ActivityVo tvtActivityVo = tvtDataManager.findTvtActivityVo();
        if (tvtActivityVo != null && !crossServerAttackService.isCSACrossPlayer(role)) {
            AbstractActivityHandler<?> activityHandler = activityService.getActivityHandler(tvtActivityVo.getType());
            // 活动元数据
            ActivityListMeta activityListMeta = activityListConfig.getMetaById(tvtActivityVo.getMetaId());
            if (activityListMeta == null) {
                ErrorLogUtil.errorLog("tvt活动meta数据空","metaId",tvtActivityVo.getMetaId());
                throw new AlertException("tvt活动meta数据空","metaId",tvtActivityVo.getMetaId());
            }
            PsActivityInfo roleActivityInfo = activityHandler.getRoleActivityInfo(role, activityListMeta);
            if (roleActivityInfo != null) {
                msg.addToActivities(roleActivityInfo);
                // 活动详细数据
                TBase<?, ?> psInfo = activityHandler.getActivityInfo(role, tvtActivityVo.getMetaId());
                if (psInfo != null) {
                    role.send(psInfo);
                }
            }
        }

        // 取GvG杯赛开启状态, csa跨服玩家不发送
        if (gvgGameService.isGvgCupStart() && !crossServerAttackService.isCSACrossPlayer(role)) {
            //取活动数据， 白名单检测在方法内部实现
            PsActivityInfo gvgCupActivityInfo = gvgGameService.getGvGCupActivityInfo(role);
            if (gvgCupActivityInfo != null) {
                msg.addToActivities(gvgCupActivityInfo);
            }
        } else {
            logger.info("GVG杯赛Activity resolve失败 或不是预热、开始状态。或者 是CSA玩家 不发ActivityInfo");
        }

        role.send(msg);
    }

    /**
     * 玩家是否在测试列表内
     * @param gvgActivityVo
     * @param role
     * @return
     */
    private boolean isRoleCanJoin(ActivityVo gvgActivityVo, Role role) {
        if (gvgActivityVo == null) {
            return false;
        }

        GvgSettingConfig config = configService.getConfig(GvgSettingConfig.class);
        Map<Long, Integer> testMatchAllianceIndexInfo = config.getTestMatchAllianceIndexInfo();
        if (!JavaUtils.bool(testMatchAllianceIndexInfo)) {
            return true;
        }

        if (role == null || !JavaUtils.bool(role.getAllianceId())) {
            return false;
        }

        return testMatchAllianceIndexInfo.containsKey(role.getAllianceId());
    }

    /**
     * 领取奖励
     *
     * @param activityTypeMetaId
     * @param goalMetaId
     */
    public void receiveActivityReward(Role role, String activityTypeMetaId, String goalMetaId) {
        ActivityListConfig activityTypeCfg = configService.getConfig(ActivityListConfig.class);
        ActivityListMeta meta = activityTypeCfg.getMetaById(activityTypeMetaId);
        ActivityHandler<?> handler = activityService.getActivityHandler(meta.getActivityType());
        if (handler != null) {
            handler.receiveReward(role, activityTypeMetaId, goalMetaId);
            GcAcitivityUpdate msg = new GcAcitivityUpdate();
            PsActivityInfo psInfo = handler.getRoleActivityInfo(role, meta);
            if (psInfo != null) {
                msg.setActivity(psInfo);
                role.send(msg);
            }
        }

        TBase<?, ?> info = getActivityInfo(role, activityTypeMetaId);
        if (info != null) {
            role.send(info);
        }
    }

    /**
     * 获取活动信息
     *
     * @param activityTypeMetaId
     * @return
     */
    public TBase<?, ?> getActivityInfo(Role role, String activityTypeMetaId) {
        ActivityListConfig activityTypeCfg = configService.getConfig(ActivityListConfig.class);
        ActivityListMeta meta = activityTypeCfg.getMetaById(activityTypeMetaId);
        ActivityHandler<?> handler = activityService.getActivityHandler(meta.getActivityType());
        return handler.getActivityInfo(role, activityTypeMetaId);
    }

    /**
     * 移除活动的特殊红点，如：
     * 订阅有礼活动首次解锁时的红点（打开活动界面后移除）
     */
	public void removeSpecRedPoint(Role role, String activityTypeMetaId) {
    	ActivityListConfig activityTypeCfg = configService.getConfig(ActivityListConfig.class);
        ActivityListMeta activityMeta = activityTypeCfg.getMetaById(activityTypeMetaId);
        if(activityMeta == null) {
        	ErrorLogUtil.errorLog("removeSpecRedPoint fail,illegal activityTypeMetaId", "metaId",activityTypeMetaId, "roleId",role.getRoleId());
        	return;
        }
        
        ActivityHandler<?> handler = activityService.getActivityHandler(activityMeta.getActivityType());
        handler.removeSpecRedPoint(role, activityMeta);
    }
}
