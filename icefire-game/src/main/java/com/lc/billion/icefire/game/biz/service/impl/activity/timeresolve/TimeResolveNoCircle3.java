package com.lc.billion.icefire.game.biz.service.impl.activity.timeresolve;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.model.activity.ActivityTimeResolveEntity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityTimeType;
import com.lc.billion.icefire.game.biz.service.impl.activity.AbstractTimeResolve;
import com.lc.billion.icefire.game.biz.service.impl.activity.ActivityTimeEntity;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.simfun.sgf.common.tuple.Tuple;
import com.simfun.sgf.common.tuple.TwoTuple;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 9-不循环，基于赛季服开服第n天，日｜时｜分｜秒｜持续时间
 * 
 * <AUTHOR>
 *
 */
@Service
public class TimeResolveNoCircle3 extends AbstractTimeResolve {

	@Autowired
	private ConfigCenter configCenter;

	public TimeResolveNoCircle3() {
		super(ActivityTimeType.NO_CIRCLE3);
	}

	@Override
	public ActivityTimeEntity resolve(ActivityTimeResolveEntity activityTimeResolveEntity) {
		// 检测赛季服的，非赛季服跳过
		ServerType currentGameServerType = configCenter.getCurrentGameServerType();
		if (currentGameServerType != ServerType.KVK_SEASON) {
			return null;
		}
		int[][] timeSects = activityTimeResolveEntity.getActivityListMeta().getTimeSects();
		if (timeSects != null && timeSects.length >= 0 && timeSects[0].length >= 5) {
			int jumpOverRound = activityTimeResolveEntity.getJumpOverRound();
			List<TwoTuple<Long, Long>> parseTime = parseTime(activityTimeResolveEntity, 0);
			List<ActivityTimeEntity> checkSectTime = checkSectTime(activityTimeResolveEntity, parseTime);
			if(checkSectTime == null){
				return null;
			}
			if (JavaUtils.bool(checkSectTime)) {
				for (ActivityTimeEntity activityTimeEntity : checkSectTime) {
					if (jumpOverRound > 0) {
						jumpOverRound--;
						continue;
					}
					return activityTimeEntity;
				}
			}
		}
		return null;
	}

	@Override
	protected List<TwoTuple<Long, Long>> parseTime(ActivityTimeResolveEntity activityTimeResolveEntity, int next) {
		int[][] timeSects = activityTimeResolveEntity.getActivityListMeta().getTimeSects();
		List<TwoTuple<Long, Long>> timeSect1s = new ArrayList<>();
		KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getCurrentKvkSeasonServerGroupConfig();
		if (kvkSeasonServerGroupConfig == null) {
			return timeSect1s;
		}
		long startTime = kvkSeasonServerGroupConfig.getStartTime();
		for (int[] timeSect : timeSects) {
			// 开服第几天
			int day = timeSect[0];
			Calendar calendar = Calendar.getInstance();
			calendar.setTimeInMillis(startTime);
			// 开服当天是第一天
			calendar.add(Calendar.DAY_OF_YEAR, day - 1);
			calendar.set(Calendar.HOUR_OF_DAY, timeSect[1]);
			calendar.set(Calendar.MINUTE, timeSect[2]);
			calendar.set(Calendar.SECOND, timeSect[3]);
			calendar.set(Calendar.MILLISECOND, 0);
			long continueTime = timeSect[4] * TimeUtil.SECONDS_MILLIS;
			timeSect1s.add(Tuple.tuple(calendar.getTimeInMillis(), calendar.getTimeInMillis() + continueTime));
		}
		return timeSect1s;
	}

	@Override
	public int calcRound(ActivityListConfig.ActivityListMeta meta, long serverStartTime, long startTime) {
		return 1;
	}
}
