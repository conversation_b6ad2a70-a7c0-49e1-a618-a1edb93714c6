package com.lc.billion.icefire.game.biz.service.impl.alipay.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class AlipayReportConfig {
    @JsonProperty(value = "sendAlipayLog")
    private boolean sendAlipayLog;
    @JsonProperty(value = "url")
    private String url;
    @JsonProperty(value = "url_check_add_homepage")
    private String url_check_add_homepage;
    @JsonProperty(value = "appId")
    private String appId;
    @JsonProperty(value = "alipayClientId", defaultValue = "38465")
    private int alipayClientId;
}
