package com.lc.billion.icefire.game.biz.service.impl.allianceBattle.matchrules;

import com.lc.billion.icefire.game.biz.config.AllianceBattleMatchConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.allianceBattle.MatchType;
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * @ClassName SoldierLevelRule
 * @Description
 * <AUTHOR>
 * @Date 2024/8/16 15:07
 * @Version 1.0
 */
@Service
public class SoldierLevelRule implements IAllianceBattleMatchScoreRule {
    @Autowired
    private SoldierServiceImpl soldierService;
    @Autowired
    private RoleDao roleDao;

    @Override
    public MatchType getType() {
        return MatchType.UNLOCK_SOLDIER_LEVEL;
    }

    @Override
    public int calcScore(long allianceId, long roleId) {
        int score = 0;
        Role role = roleDao.findById(roleId);
        if (role == null) {
            return score;
        }
        int roleSoldierMaxLevel = soldierService.getRoleMaxSoldierLevel(role);
        for (AllianceBattleMatchConfig.AllianceBattleMatchMeta meta : getMetas()) {
            if (Integer.parseInt(meta.getPara1()) == roleSoldierMaxLevel) {
                score += meta.getScore();
                break;
            }
        }
        return score;
    }
}
