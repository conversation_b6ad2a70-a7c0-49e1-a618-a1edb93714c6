package com.lc.billion.icefire.game.biz.service.impl.army.build;

import com.lc.billion.icefire.game.biz.BizTime;
import com.lc.billion.icefire.game.biz.config.AllianceWarConfig;
import com.lc.billion.icefire.game.biz.model.army.*;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/7/28
 */
@Service
public class DefaultRallyArmyBuilder extends ArmyBuilder {
	@Override
	public ArmyInfo build(Role role, ArmyType armyType, int stamina, SceneNode targetNode, ArmySetoutParam param) {
		ArmyInfo army = new ArmyInfo();
		army.setArmyType(armyType);
		army.setUseStamina(stamina);
		army.setTargetNodeId(targetNode.getPersistKey());
		army.setTargetNodeType(targetNode.getNodeType());
		RoleCity city = roleCityManager.getRoleCity(role.getPersistKey());
		army.setStart(city);
		army.setEnd(targetNode);
		army.setBodyType(ArmyBodyType.NORMAL);
		army.setArmyBodyContext(param.getArmyBodyContext());
		army.setOriginMetaId(targetNode.getBIConfigId());
		//

		army.setRoleId(role.getPersistKey());
		army.setWorkType(ArmyWorkType.RALLYING);
		army.setPos(city.getPosition().toPosition());
		army.setArmySoldiers(param.getArmySoldiers());
		army.setHeros(param.getHeros());
		armyDao.createEntity(role, army);
		//
		AllianceWarConfig.AllianceWarMeta meta = configService.getConfig(AllianceWarConfig.class).getById(param.getMassWaitingTimeMetaId());
		long time = meta == null ? TimeUnit.SECONDS.toMillis(30) : meta.getTime();
		int rallySoldierMax = armyManager.getRallySoldierMax(army);
		RallyContext rallyContext = new RallyContext(role.getAllianceId(), role.getPersistKey(), army.getPersistKey(), time,
				role.getNumberProps().getInt(Prop.RALLY_CAPACITY_14000), rallySoldierMax);
		army.setRallyContext(rallyContext);
		//
		long now = BizTime.now();
		army.setStartWorkTime(now);
		army.setTotalWorkTime(time);
		army.setOriginalTotalWorkTime(time);
		return army;
	}
}
