package com.lc.billion.icefire.game.biz.service.impl.army.oper;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.config.RegionCapitalConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceBuildingNodeDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.node.AllianceBuildingNode;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alliance.alliancebuilding.AllianceBuildingServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeConstants;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.regioncapital.RegionCapitalService;
import com.lc.billion.icefire.protocol.constant.PsAllianceBuildingStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 单人攻击联盟建筑
 * 
 * <AUTHOR>
 *
 */
@Service
public class AttackAllianceBuildingOperation extends AbstractArmyFightOperation {
	@Autowired
	private AllianceBuildingServiceImpl allianceBuildingService;
	@Autowired
	private AllianceBuildingNodeDao allianceBuildingNodeDao;
	@Autowired
	private RegionCapitalService regionCapitalService;
	@Autowired
	private NoticeServiceImpl noticeService;
	@Autowired
	private ServiceDependency srvDep;


	@Override
	public ArmyType getArmyType() {
		return ArmyType.ATTACK_ALLIANCE_BUILDING;
	}

	/**
	 * 是否可以发生战斗检测
	 */
	@Override
	protected boolean check(ArmyInfo army, SceneNode sceneNode) {
		boolean checked = super.check(army, sceneNode);
		if (!checked) {
			return false;
		}


		AllianceBuildingNode allianceBuildingNode = (AllianceBuildingNode) sceneNode;

		// 是否是争夺状态
		if (allianceBuildingNode.isInAllianceDomain()) {
			RegionCapitalNode capitalNode = regionCapitalService.getRegionCapital(sceneNode.getCurrentServerId(), sceneNode.getRegionId());
			if (capitalNode == null) {
				return false;
			}
			if (!capitalNode.isAttack()) {
				ErrorLogUtil.errorLog("allianceBuildingNode pvp status error", "buildingId", allianceBuildingNode.getBuildingId(), "allianceId",allianceBuildingNode.getAllianceId());
				return false;
			}
		}

		// 没有联盟
		Role role = army.getOwner();
		if (role.getAllianceId() == null) {
			return false;
		}

		if (allianceBuildingNode.isResourceCenter()) {
			return false;
		}

		return true;
	}

	@Override
	public void armyArrive(ArmyInfo army) {
		warService.warDel(army);
		SceneNode targetNode = armyManager.getArmyTargetNode(army);
		if (!check(army, targetNode)) {
			ErrorLogUtil.errorLog("check role army failed",  "roleId",army.getRoleId(), "armyId",army.getPersistKey());
			armyManager.returnArmy(army);
			return;
		}

		Role role = army.getOwner();
		AllianceBuildingNode allianceBuildingNode = (AllianceBuildingNode) targetNode;
		Alliance alliance = allianceDao.findById(role.getAllianceId());
		// 联盟不存在
		if (null == alliance) {
			ErrorLogUtil.errorLog("check role alliance isn't find", "roleId",role.getId(), "allianceId",role.getAllianceId());
			armyManager.returnArmy(army);
			return;
		}

		// 是同一个联盟，返回
		long belongId = allianceBuildingNode.getAllianceId();
		if (alliance.getPersistKey().equals(belongId)) {
			ErrorLogUtil.errorLog("check role belong alliance is same", "roleId",role.getId(), "allianceId",role.getAllianceId());
			armyManager.returnArmy(army);
			return;
		}

		// pve需要有穿透效果
		if (!allianceBuildingNode.isArriveArmy()) {
			var rcc = configService.getConfig(RegionCapitalConfig.class);
			// 保证部队打完后可以返回
			allianceBuildingNode.setContinuePveCount(rcc.getMaxSingleAttackNpc());
		}

		// 设置pve状态
		boolean isPvp = allianceBuildingNode.isArriveArmy();
        army.setIsPve(!isPvp);

		// 有人防守/发生战斗
		super.armyArrive(army);
	}

	@Override
	public void finishWork(ArmyInfo armyInfo) {
		super.finishWork(armyInfo);
		SceneNode armyTargetNode = armyManager.getArmyTargetNode(armyInfo);
		if (armyTargetNode == null) {
			armyManager.marchRetBILog(armyInfo);
			armyManager.returnArmy(armyInfo);
			return;
		}
		AllianceBuildingNode allianceBuildingNode = (AllianceBuildingNode) armyTargetNode;
		if (allianceBuildingNode.isDeadAll()) {
			allianceBuildingService.removeAllianceBuildingAndUpdateProp(allianceBuildingNode);
			armyManager.marchRetBILog(armyInfo);
			armyManager.returnArmy(armyInfo);
			return;
		}
		if (!armyInfo.getPersistKey().equals(allianceBuildingNode.getContinuePveLockId())) {
			// 没有继续战斗,则返回
			armyManager.marchRetBILog(armyInfo);
			armyManager.returnArmy(armyInfo);
		}
	}

	@Override
	public void returnArrived(ArmyInfo army) {
		armyManager.takeBackArmy(army);
	}

	@Override
	protected void endBattle(ArmyInfo armyInfo) {
		boolean isWin = armyInfo.getFightContext().isWin();
		SceneNode armyTargetNode = armyManager.getArmyTargetNode(armyInfo);
		AllianceBuildingNode allianceBuildingNode = (AllianceBuildingNode) armyTargetNode;
		boolean isPvp = allianceBuildingNode.isArriveArmy();
		if (!isPvp) {
			if (isWin) {
				allianceBuildingService.pveBattleWinLogic(armyInfo, allianceBuildingNode);
			}
		} else {
			if (isWin) {
				// 清理参战的失败守军
				removeFightArmyWhenDefendLose(armyInfo, allianceBuildingNode);
				allianceBuildingService.pvpBattleWinLogic(armyInfo, allianceBuildingNode);
			}
		}

		Role role = armyInfo.getOwner();
		logger.info("battleFinish Role:{} armyId:{} node:{} nodeMeta:{} armyType:{} isPvp:{} result:{} pveCount:{}",
				role.getId(), armyInfo.getPersistKey(), allianceBuildingNode.getPersistKey(), allianceBuildingNode.getMetaId(),
				armyInfo.getArmyType(), isPvp, isWin ? 1 : 0, allianceBuildingNode.getContinuePveCount());
		allianceBuildingNodeDao.save(allianceBuildingNode);

		allianceBuildingNode.setContinuePveLockId(0);
		// pvp胜利后,立刻与一个守军进行一场战斗,获胜则打掉一个守军单位
		if (isPvp && isWin) {
			// 放入队首,继续执行
			allianceBuildingNode.imidiateAddWaitting(armyInfo.getPersistKey());
			armyInfo.setWorkType(ArmyWorkType.SETOUT);
			allianceBuildingNode.setContinuePveLockId(armyInfo.getPersistKey());
		}
		srvDep.getBiLogUtil().allianceBuildingFight(role, allianceBuildingNode.getLevelMetaId(), allianceBuildingNode.getAllianceId());
	}
}
