package com.lc.billion.icefire.game.biz.service.impl.army.oper;

import com.lc.billion.icefire.core.common.NumberUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.NewResRefreshConfig;
import com.lc.billion.icefire.game.biz.config.NewResRefreshConfig.NewResRefreshMeta;
import com.lc.billion.icefire.game.biz.config.ResInfoConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KvkNewResRefreshConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.NewResNodeDao;
import com.lc.billion.icefire.game.biz.manager.RoleExtraManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.manager.RoleTechManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.army.GatherContext;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.scene.node.NewResNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyOperation;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BIMarchResult;
import com.lc.billion.icefire.game.biz.service.impl.email.MailService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeConstants;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 采集新的世界资源点
 * 
 * <AUTHOR>
 *
 */
@Service
public class GatherNewResOperation extends AbstractGatherOperation {
	private static final Logger logger = LoggerFactory.getLogger(GatherNewResOperation.class);

	@Autowired
	private RoleManager roleManager;
	@Autowired
	private NewResNodeDao newResNodeDao;
	@Autowired
	private RoleExtraManager roleExtraManager;
	@Autowired
	private RoleTechManager roleTechManager;
	@Autowired
	private MailService mailService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GATHER_NEW_RES;
	}

	@Override
	protected boolean check(ArmyInfo army, SceneNode target) {
		Long targetNodeId = army.getTargetNodeId();
		if (target == null) {
			return false;
		}
		if (!target.getPersistKey().equals(targetNodeId)) {
			return false;
		}
		if (target.getNodeType() != SceneNodeType.NEW_RES) {
			return false;
		}
		NewResNode resNode = (NewResNode) target;
		if (resNode.getAllianceId() != null && !resNode.getAllianceId().equals(army.getOwner().getAllianceId())) {
			return false;
		}

		int resLevel = 0;
		String resName = "";
		Currency resType = null;
		KvkNewResRefreshConfig resRefreshConfig = configService.getConfig(KvkNewResRefreshConfig.class);
		if (Application.getServerType(resNode.getCurrentServerId()) == ServerType.KVK_SEASON) {
			var kvkNewResRefreshMeta = resRefreshConfig.get(resNode.getMetaId());
			if (kvkNewResRefreshMeta == null) {
				return false;
			}
			resLevel = kvkNewResRefreshMeta.getLevel();
			resName = kvkNewResRefreshMeta.getName();
			resType = Currency.findById(kvkNewResRefreshMeta.getType());

		} else {
			NewResRefreshMeta newResRefreshMeta = configService.getConfig(NewResRefreshConfig.class).get(resNode.getMetaId());
			if (newResRefreshMeta == null) {
				return false;
			}
			resLevel = newResRefreshMeta.getLevel();
			resName = newResRefreshMeta.getName();
			resType = newResRefreshMeta.getType();
		}

		// if (resNode.inGathering() && resNode.getRoleId().equals(army.getRoleId())) {
		// // 我自己有部队，在目标资源点采集
		// return false;
		// }
		// if (resNode.getRoleId().equals(army.getRoleId()) && resNode.isStation()) {
		// logger.error("自己正在驻扎的资源田不能采集{},{}", resNode.getRoleId(), resNode);
		// return false;
		// }
		if (resNode.inGathering()) {
			// 有驻扎的队伍了
			// 自己或友军
			Long ownerRoleId = resNode.getRoleId();
			if (army.getRoleId().equals(ownerRoleId)) {
				// 我自己的
				ErrorLogUtil.errorLog("资源田自己已经驻扎或采集了", "resNode",resNode);
				return false;
			}
			Role armyRole = army.getOwner();
			Role ownerRole = roleManager.getRole(ownerRoleId);
			if (ownerRole != null && JavaUtils.bool(armyRole.getAllianceId()) && JavaUtils.bool(ownerRole.getAllianceId())
					&& armyRole.getAllianceId().equals(ownerRole.getAllianceId())) {
				ErrorLogUtil.errorLog("资源田友军已经驻扎或采集了","resNode", resNode);
				//
				// 邮件通知
				List<String> params = new ArrayList<>();
				params.add(resLevel + "");
				params.add(NoticeConstants.getKey(resName));
				params.add(resNode.getPosition().getX() + "");
				params.add(resNode.getPosition().getY() + "");
				params.add("" + resNode.getCurrentServerId());
				mailService.sendSystemEmail(armyRole, EmailConstants.NEW_RES_MAIL_2, null, params);
				return false;
			}
		}
		RoleExtra roleExtra = roleExtraManager.getRoleExtra(army.getRoleId());
//		if (roleExtra.getNpcCanAttackLevel() - 1 < resLevel) {
//			logger.error("玩家击杀野怪等级不足与采集{}，{}，{}", army.getRoleId(), roleExtra.getNpcCanAttackLevel(), resNode);
//			return false;
//		}
//		boolean unlockCurrencyGatherAndStation = roleTechManager.unlockCurrencyGatherAndStation(army.getRoleId(), resType);
//		if (!unlockCurrencyGatherAndStation) {
//			logger.error("玩家采集资源田但对应的科技未解锁{}，{}", army.getRoleId(), resNode);
//			return false;
//		}
		return true;
	}

	/**
	 * 资源点是否需要战斗检测
	 */
	@Override
	protected boolean checkFight(ArmyInfo army) {
		SceneNode target = armyManager.getArmyTargetNode(army);
		NewResNode resNode = (NewResNode) target;
		Role role = army.getOwner();
		// 目标点 被别人抢先占领
		// if (!resNode.hasOwner()) {
		// return true;
		// }
		if (resNode.hasOwner() && !role.getPersistKey().equals(resNode.getRoleId()) && (resNode.inGathering())) {
			return true;
		}
		//
		return false;
	}

	@Override
	public void armyArrive(ArmyInfo army) {
		SceneNode target = armyManager.getArmyTargetNode(army);
		if (!check(army, target)) {
			armyManager.marchRetBILog(army);
			armyManager.returnArmy(army);
			return;
		}
		if (checkFight(army)) {
			NewResNode resNode = (NewResNode) target;
			// if (!resNode.hasOwner()) {
			// army.setArmyType(ArmyType.NEW_RES_PVE);
			// ArmyOperation armyOperation = armyService.getArmyOperation(army);
			// armyOperation.armyArrive(army);
			// return;
			// }
			if (allianceService.isSameAlliance(army.getRoleId(), resNode.getRoleId())) {
				armyManager.marchRetBILog(army);
				armyManager.returnArmy(army);
				return;
			}
			if (resNode.inGathering()) {
				if (armyManager.findById(resNode.getArmyId()) == null) {
					ErrorLogUtil.errorLog("采集发现目标点有行军，但行军找不到了，脏数据" ,new RuntimeException(),"armyId", resNode.getArmyId());
					// 更新脏数据
					resNode.setArmyId(0L);
					newResNodeDao.save(resNode);
					sceneService.update(resNode, null);
					// 行军返回
					armyManager.marchRetBILog(army);
					armyManager.returnArmy(army);
					return;
				} else {
					army.setArmyType(ArmyType.NEW_RES_PVP);
				}
			} else if (resNode.isStation()) {
				army.setArmyType(ArmyType.NEW_RES_PVP);
			} else {
				ErrorLogUtil.errorLog("目标既不在采集中，也不再驻扎状态，但却检测需要战斗，异常打印", "x",resNode.getX(), "y",resNode.getY());
				armyManager.marchRetBILog(army);
				armyManager.returnArmy(army);
				return;
			}
			ArmyOperation armyOperation = armyService.getArmyOperation(army);
			armyOperation.armyArrive(army);
			return;
		}

		NewResNode resNode = (NewResNode) target;
		//
		//
		double speedgrow = 0.1d;
		Currency resType = null;
		KvkNewResRefreshConfig resRefreshConfig = configService.getConfig(KvkNewResRefreshConfig.class);
		if (Application.getServerType(resNode.getCurrentServerId()) == ServerType.KVK_SEASON) {
			var kvkNewResRefreshMeta = resRefreshConfig.get(resNode.getMetaId());
			speedgrow = kvkNewResRefreshMeta.getSpeedgrow();
			resType = Currency.findById(kvkNewResRefreshMeta.getType());
		} else {
			NewResRefreshMeta newResRefreshMeta = configService.getConfig(NewResRefreshConfig.class).get(resNode.getMetaId());
			speedgrow = newResRefreshMeta.getSpeedgrow();
			resType = newResRefreshMeta.getType();
		}

		resNode.setArmyId(army.getPersistKey());
		newResNodeDao.save(resNode);
		//

		army.setWorkType(ArmyWorkType.GATHERING);
		// 采集上限、携带量(上限/资源重量)
		long armyHaveCarryMax = armyManager.getMaxCarry(army, army.getOwner(), resType);
		ResInfoConfig resInfoConfig = configService.getConfig(ResInfoConfig.class);
		long armyHaveCarry = NumberUtils.toLong(armyHaveCarryMax / resInfoConfig.get(resType.getIdStr()).getResCarry());
		armyHaveCarry = Math.min(armyHaveCarry, resNode.getResReversed());
		// 采集速度
		double gatherSpeed = armyManager.getGatherSpeed(army.getRoleId(), resType, target.getPosition());

		// 新的资源点，采集速度需要乘以系数
		gatherSpeed *= speedgrow;

		GatherContext gatherContext = new GatherContext(armyHaveCarry, gatherSpeed, TimeUtil.getNow(), resNode.getMetaId(), resType);
		army.setGatherContext(gatherContext);

		newResNodeDao.changeIndex(resNode, army.getRoleId());

		// 更新信息给前端
		armyService.updateArmyProgress(army);
		// 更新资源点信息
		sceneService.update(resNode, updater -> updater.setArmyInfo(NewResNode.toSimpleInfo(army)));
		sceneService.remove(army);
		armyDao.save(army);
		logger.info("玩家" + army.getRoleId() + "采集开始,预计采集量" + gatherContext.getCarry() + ",预计采集时间" + (int) (gatherContext.getReaminGatherTime() / 60) + ":"
				+ (int) (gatherContext.getReaminGatherTime() % 60) + " | " + army.getPersistKey());
		armyManager.marchRetBILog(army, BIMarchResult.VICTORY);
	}
}
