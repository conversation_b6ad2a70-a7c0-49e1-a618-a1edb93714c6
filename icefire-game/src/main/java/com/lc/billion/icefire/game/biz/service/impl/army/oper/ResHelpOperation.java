package com.lc.billion.icefire.game.biz.service.impl.army.oper;

import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.RoleCurrencyManager;
import com.lc.billion.icefire.game.biz.manager.RoleExtraManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ResHelpContext;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.email.MailUtils;
import com.lc.billion.icefire.game.biz.model.email.ResHelpMail;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.record.type.RoleTotalRecordType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.model.role.info.DefaultRoleInfo;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyOperation;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.mission.MissionServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.record.RecordServiceImpl;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.GcDailyResHelpAmountUpdate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 联盟资源援助 到达处理
 * 
 * <AUTHOR>
 * @date 2020/9/21
 */
@Service
public class ResHelpOperation implements ArmyOperation {

	@Autowired
	private ArmyManager armyManager;
	@Autowired
	private RoleManager roleManager;
	@Autowired
	private RoleCurrencyManager roleCurrencyManager;
	@Autowired
	private RoleExtraManager roleExtraManager;
	@Autowired
	private AllianceServiceImpl allianceService;
	@Autowired
	private BiLogUtil biLogUtil;
	@Autowired
	private RecordServiceImpl recordService;
	@Autowired
	private MissionServiceImpl missionService;
	@Autowired
	private ServiceDependency srvDep;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.RES_HELP;
	}

	@Override
	public void armyArrive(ArmyInfo army) {
		SceneNode targetNode = armyManager.getArmyTargetNode(army);
		if (targetNode == null || targetNode.getNodeType() != SceneNodeType.CITY) {
			armyManager.returnArmy(army);
			return;
		}
		Role targetRole = roleManager.getRole(targetNode.getPersistKey());
		if (targetRole == null) {
			armyManager.returnArmy(army);
			return;
		}
		RoleExtra targetRoleExtraData = roleExtraManager.getRoleExtra(targetRole.getPersistKey());
		if (targetRole.getNumberProps().getInt(Prop.RES_HELP_DAILY_RECEIVED_LIMIT_14014) <= targetRoleExtraData.getResHelpDailyReceivedAmount()) {
			// 每日上限没有了，直接带资源返回
			army.getResHelpContext().setReturnAmount(army.getResHelpContext().getResHelpAmount());
			armyManager.returnArmy(army);
			return;
		}
		ResHelpContext resHelpContext = army.getResHelpContext();
		Map<Currency, Integer> resHelpAmount = resHelpContext.getResHelpAmount();
		Map<Currency, Integer> returnAmount = resHelpContext.getReturnAmount();
		int totalAddCount = 0;
		Map<Currency, Integer> realGet = new HashMap<>();
		//
		//
		for (Map.Entry<Currency, Integer> entry : resHelpAmount.entrySet()) {
			int addCount = 0;
			long canAddCount = getRoleCanAddNum(targetRole, entry.getKey());
			if (canAddCount < entry.getValue()) {
				if (returnAmount == null) {
					returnAmount = new HashMap<>();
					resHelpContext.setReturnAmount(returnAmount);
				}
				resHelpContext.getReturnAmount().put(entry.getKey(), Long.valueOf(entry.getValue() - canAddCount).intValue());
				addCount = (int) canAddCount;
			} else {
				addCount = entry.getValue();
			}
			totalAddCount += addCount;
			realGet.put(entry.getKey(), addCount);
			roleCurrencyManager.add(targetRole, entry.getKey(), addCount, LogReasons.MoneyLogReason.RES_HELP, "ADD");
		}
		armyManager.saveArmy(army);
		// 我的数据
		RoleExtra roleExtra = roleExtraManager.getRoleExtra(army.getRoleId());
		roleExtra.setResHelpAmountDayLimt(roleExtra.getResHelpAmountDayLimt() + totalAddCount);
		roleExtraManager.saveRoleExtra(roleExtra);
		// 更新我的历史记录
		recordService.addRoleTotalNum(army.getOwner(), RoleTotalRecordType.RES_HELP_AMOUNT, totalAddCount);
		recordService.addRoleTotalNum(army.getOwner(), RoleTotalRecordType.RESOURCE_ASSISTANCE_TIMES, 1);
		// 更新目标的历史记录
		recordService.addRoleTotalNum(targetRole, RoleTotalRecordType.RES_HELP_RECEIVED_AMOUNT, totalAddCount);
		// 目标玩家的数据
		targetRoleExtraData.setResHelpDailyReceivedAmount(targetRoleExtraData.getResHelpDailyReceivedAmount() + totalAddCount);
		roleExtraManager.saveRoleExtra(targetRoleExtraData);
		Role role = army.getOwner();
		GcDailyResHelpAmountUpdate notice = new GcDailyResHelpAmountUpdate();
		notice.setResHelpAmount(roleExtra.getResHelpAmountDayLimt());
		if (role != null) {
			role.send(notice);
		}
		// 邮件
		ResHelpMail mail = new ResHelpMail(role == null ? "" : role.getName(), role == null ? "" : role.getHead(), role == null ? DefaultRoleInfo.toRoleInfo() : role.toRoleInfo(),
				allianceService.getAllianceByRoleId(army.getRoleId()) == null ? "" : allianceService.getAllianceByRoleId(army.getRoleId()).getAliasName(), realGet);
		srvDep.getMailCreator().setAbstractEmailField(targetRole.getPersistKey(), targetRole.getCurrentServerId(), MailUtils.buildEmptyTitle(), mail);
		srvDep.getMailSender().sendOneMail(mail);

		// biLog
		Map<String, Object> extParam = new HashMap<>();
		extParam.put("mainBuildingLevel", String.valueOf(role.getLevel()));
		extParam.put("targetRoleId", targetRole.getPersistKey().toString());
		extParam.put("targetBuildingLevel", String.valueOf(targetRole.getLevel()));
		extParam.put("originalResHelp", resHelpAmount.toString());
		extParam.put(Currency.WOOD.toString(), realGet.get(Currency.WOOD) == null ? 0 : realGet.get(Currency.WOOD));
		extParam.put(Currency.FOOD.toString(), realGet.get(Currency.FOOD) == null ? 0 : realGet.get(Currency.FOOD));
		extParam.put(Currency.IRON.toString(), realGet.get(Currency.IRON) == null ? 0 : realGet.get(Currency.IRON));
		extParam.put(Currency.WATER.toString(), realGet.get(Currency.WATER) == null ? 0 : realGet.get(Currency.WATER));
		extParam.put("tax", resHelpContext.getTax().toString());
		biLogUtil.resHelpBiLog(role, extParam);
		//
		finishWork(army);
	}

	@Override
	public void returnArrived(ArmyInfo army) {
		armyManager.takeBackArmy(army);
	}

	@Override
	public void finishWork(ArmyInfo army) {
		armyManager.returnArmy(army);
	}

	private long getRoleCanAddNum(Role role, Currency type) {
		long canAddCount = roleCurrencyManager.getCanAddCount(role, type, false);
		RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getPersistKey());
		long min = Math.min(canAddCount, role.getNumberProps().getInt(Prop.RES_HELP_DAILY_RECEIVED_LIMIT_14014) - roleExtra.getResHelpDailyReceivedAmount());
		return min < 0 ? 0 : min;
	}

}
