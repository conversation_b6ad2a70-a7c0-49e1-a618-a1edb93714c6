package com.lc.billion.icefire.game.biz.service.impl.army.processor;

import com.lc.billion.icefire.game.biz.manager.WorldExploreEventManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.worldExploreEvent.EventType;
import com.lc.billion.icefire.game.biz.model.worldExploreEvent.PlayerWorldExploreEvent;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采集新资源点
 *
 * <AUTHOR>
 */

@Service
public class ExploreEventGatherProcessor extends AbstractGatherResProcessor {

    @Autowired
    public WorldExploreEventManager eventManager;

    @Override
    public ArmyType getArmyType() {
        return ArmyType.WORLD_EXPLORE_EVENT_GATHER;
    }

    @Override
    public SceneNode getTargetNode(int currentServerId, ArmySetoutParam param, Role role) {
        return eventManager.findByPointEvent(currentServerId, role.getRoleId(), param.getTargetPoint());
    }

    /**
     * 参考父类
     *
     * @param attacker
     * @param param
     * @return
     */
    @Override
    public ArmyInfo initArmy(Role attacker, ArmySetoutParam param) {
        SceneNode targetSceneNode = getTargetNode(attacker.getCurrentServerId(), param, attacker);
        int staminaCost = getStaminaCost(targetSceneNode, attacker.getRoleId(), param);
        ArmyInfo army = armyBuildDirector.creatArmy(attacker, getArmyType(), staminaCost, targetSceneNode, param);
        if (army == null) {
            return null;
        }
        army.setTargetNode(targetSceneNode);
        // 更新车状态
        return army;
    }

    @Override
    protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
        if (targetNode instanceof PlayerWorldExploreEvent) {
            PlayerWorldExploreEvent event = (PlayerWorldExploreEvent) targetNode;
            if (event.getStatus() == 1) {
                ErrorLogUtil.errorLog("event already be completed.", "roleId",role.getRoleId(), "eventId",event.getEventId());
                return false;
            }
            return event.getLinkType() == EventType.EVENT_GATHER.getId();
        }
        return false;
    }

    @Override
    protected void removeEnemyToResNode(ArmyInfo myArmy) {
    }

}
