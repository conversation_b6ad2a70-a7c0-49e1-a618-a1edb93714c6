package com.lc.billion.icefire.game.biz.service.impl.continuousrecharge;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.BizTime;
import com.lc.billion.icefire.game.biz.config.ReusableContinuousRechargeConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.manager.RoleContinuousRechargeManager;
import com.lc.billion.icefire.game.biz.model.continuousrecharge.RoleContinuousRecharge;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.GcContinuousRechargeData;
import com.lc.billion.icefire.protocol.GcContinuousRechargeReward;
import com.lc.billion.icefire.protocol.GcContinuousRechargeRewardInfo;
import com.lc.billion.icefire.protocol.structure.PsContinuousRechargeRewardInfo;
import com.lc.billion.icefire.protocol.structure.PsSimpleItem;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.lc.billion.icefire.game.biz.config.ReusableContinuousRechargeConfig.GROUP_NEW_PLAYER;

/**
 * @ProjectName: lsserver
 * @Package: com.lc.billion.icefire.game.biz.service.impl.continuousrecharge
 * <AUTHOR>
 * @Date 2024-02-19 17:17:08.587
 * @ClassName ContinuousRechargeServiceImpl
 * @Description 累计充值
 */
@Service
public class ContinuousRechargeServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(ContinuousRechargeServiceImpl.class);

    @Autowired
    private ConfigServiceImpl configService;

    @Autowired
    private RoleContinuousRechargeManager conRechargeManager;
    @Autowired
    private DropServiceImpl dropService;
    @Autowired
    private ItemServiceImpl itemService;

    @Autowired
    protected ActivityDao activityDao;

    @Autowired
    protected BiLogUtil biLogUtil;

    private static final Byte STATUS_CAN_NOT_BE_RECEIVED = 0;    // 不可领取
    private static final Byte STATUS_CAN_BE_RECEIVED = 1;        // 可领取
    private static final Byte STATUS_ALREADY_BE_RECEIVED = 2;    // 已经领取

    public void onEnterWorld(Role role) {
        sendConRechargeInfo(role);
    }

    private void sendConRechargeInfo(Role role) {
        var conRecharge = conRechargeManager.getRoleContinuousRecharge(role.getId());
        if (conRecharge == null) {
            conRecharge = conRechargeManager.createRoleContinuousRecharge(role);
        }

        // 奖励未领完，继续下发
        GcContinuousRechargeRewardInfo msg = wrapperInfo(conRecharge);
        if (hasUnreceivedReward(msg)) {
            role.send(msg);

            // send GcContinuousRechargeData
            GcContinuousRechargeData dataMsg = new GcContinuousRechargeData();
            dataMsg.setScore(conRecharge.getScore());
            role.send(dataMsg);
        }
    }

    /**
     * 增加累计数
     */
    public void onRecharge(Role role, int diamond) {
        try {
            RoleContinuousRecharge conRecharge = conRechargeManager.getRoleContinuousRecharge(role.getId());
            if (conRecharge == null) {
                conRecharge = conRechargeManager.createRoleContinuousRecharge(role);
            }

            conRecharge.setScore(conRecharge.getScore() + diamond);
            conRechargeManager.updateroleContinuousRecharge(conRecharge);

            // send GcContinuousRechargeData
            GcContinuousRechargeData msg = new GcContinuousRechargeData();
            msg.setScore(conRecharge.getScore());
            role.send(msg);
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("ContinuousRechargeServiceImpl onRecharge error",e);
        }
    }

    /**
     * 领取奖励
     */
    public void receiveReward(Role role, String metaId) {
        RoleContinuousRecharge conRecharge = conRechargeManager.getRoleContinuousRecharge(role.getId());
        if (conRecharge == null) {
            conRecharge = conRechargeManager.createRoleContinuousRecharge(role);
        }

        // check rewrad
        Map<String, Long> metaRewardMap = conRecharge.getMetaRewardMap();
        if (metaRewardMap.containsKey(metaId)) {
            logger.info("receiveReward the reward has already been received! metaId:{}", metaId);
            return;
        }

        var meta = configService.getConfig(ReusableContinuousRechargeConfig.class).getMetaByGroupAndId(GROUP_NEW_PLAYER, metaId);
        if (meta == null) {
            logger.info("receiveReward the reward config not found! metaId:{}", metaId);
            return;
        }

        // update
        metaRewardMap.put(metaId, BizTime.now());
        conRechargeManager.updateroleContinuousRecharge(conRecharge);

        // 累充打点
        int index = 0;
        var rewardList = new ArrayList<Integer>();
        for (var k : metaRewardMap.keySet()) {
            rewardList.add(index);
            index++;
        }
        biLogUtil.rechargeClaimReward(role, rewardList);

        // 发送奖励
        List<SimpleItem> drop = dropService.drop(meta.getRewardNew());
        if (!JavaUtils.bool(drop)) {
            ErrorLogUtil.errorLog("新手累计充值配置的奖励解析后为空","类名", getClass().getSimpleName(),"dropMetaId", metaId);
        } else {
            // 发奖励
            itemService.give(role, drop, LogReasons.ItemLogReason.CONTINUOUS_RECHARGE);
        }

        // send reward msg
        GcContinuousRechargeReward rewardMsg = new GcContinuousRechargeReward();
        List<PsSimpleItem> list = new ArrayList<>();
        for (SimpleItem item : drop) {
            PsSimpleItem psItem = new PsSimpleItem();
            psItem.setMetaId(item.getMetaId());
            psItem.setCount(item.getCount());
            list.add(psItem);
        }
        rewardMsg.setRewards(list);
        role.send(rewardMsg);

        // send conInfo msg
        GcContinuousRechargeRewardInfo infoMsg = wrapperInfo(conRecharge);
        role.send(infoMsg);
    }

    public GcContinuousRechargeRewardInfo wrapperInfo(RoleContinuousRecharge conRecharge) {
        Map<String, Long> metaRewardMap = conRecharge.getMetaRewardMap();
        GcContinuousRechargeRewardInfo msg = new GcContinuousRechargeRewardInfo();

        var metaList = configService.getConfig(ReusableContinuousRechargeConfig.class).getMetaListByGroup(GROUP_NEW_PLAYER);
        for (var meta : metaList) {
            var rewardInfo = new PsContinuousRechargeRewardInfo();
            rewardInfo.metaId = meta.getId();
            if (metaRewardMap.containsKey(meta.getId())) {
                rewardInfo.setStatus(STATUS_ALREADY_BE_RECEIVED);
            } else if (meta.getNeedValue() < conRecharge.getScore()) {
                rewardInfo.setStatus(STATUS_CAN_BE_RECEIVED);
            } else {
                rewardInfo.setStatus(STATUS_CAN_NOT_BE_RECEIVED);
            }
            msg.addToRewardInfo(rewardInfo);
        }

        return msg;
    }

    public boolean hasUnreceivedReward(GcContinuousRechargeRewardInfo info) {
        for (var item : info.getRewardInfo()) {
            if (item.getStatus() != STATUS_ALREADY_BE_RECEIVED) {
                return true;
            }
        }

        return false;
    }
}
