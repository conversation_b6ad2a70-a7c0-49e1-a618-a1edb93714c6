package com.lc.billion.icefire.game.biz.service.impl.fight;

import com.google.common.collect.ArrayListMultimap;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.BindThreadOperation;
import com.lc.billion.icefire.game.biz.async.arena.ArenaUpdateDefencePowerOperation;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.manager.RoleCalcPropManager;
import com.lc.billion.icefire.game.biz.manager.RoleExtraManager;
import com.lc.billion.icefire.game.biz.manager.RoleRecordManager;
import com.lc.billion.icefire.game.biz.manager.prop.base.ICalcProp;
import com.lc.billion.icefire.game.biz.model.alipay.AlipayReportType;
import com.lc.billion.icefire.game.biz.model.event.GameEventType;
import com.lc.billion.icefire.game.biz.model.fightpower.FightPowerType;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.record.type.RoleTotalRecordType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.ScheduleService;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alipay.AlipayReportServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.fight.calc.IFightPower;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service
public class FightPowerServiceImpl {
    public static final Logger logger = LoggerFactory.getLogger(FightPowerServiceImpl.class);

    @Autowired
    private ApplicationContext appCtx;
    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private RoleCalcPropManager roleCalcPropManager;
    @Autowired
    private RoleRecordManager roleRecordManager;
    @Autowired
    private RoleExtraManager roleExtraManager;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private AlipayReportServiceImpl alipayReportService;

    private Map<FightPowerType, IFightPower> capacityMap;

    private ArrayListMultimap<Long, FightPowerType> roleChangePower = ArrayListMultimap.create();

    private final ArrayListMultimap<Long, FightPowerType> roleLimitBuildingChangePower = ArrayListMultimap.create();

    @PostConstruct
    public void init() {
        try {
            this.capacityMap = new EnumMap<>(FightPowerType.class);

            Map<String, IFightPower> temp = appCtx.getBeansOfType(IFightPower.class, false, true);
            for (IFightPower fc : temp.values()) {
                this.capacityMap.put(fc.getType(), fc);
            }
            this.capacityMap = Collections.unmodifiableMap(capacityMap);
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("CaughtException",e);
        }

    }

    public void startService() {
        // 战斗力初始化
        for (Role role : roleDao.findAll()) {
            updateCapacity(role);
        }
        scheduleService.asyncScheduleAtFixedRate(new BindThreadOperation() {
            @Override
            public long getBindId() {
                return 0;
            }

            @Override
            public boolean run() {
                afterTicker();
                return false;
            }
        }, TimeUtil.SECONDS_MILLIS * 3, 50);

    }

    public void updateCapacity(Role role) {
        for (FightPowerType type : capacityMap.keySet()) {
            IFightPower fc = capacityMap.get(type);
            fc.calc(role);
        }
        roleExtraManager.updateFightPower(role);
    }

    public void onEnterWorld(Role role) {
        long currHighTroopPower = roleRecordManager.getRoleTotalRecord(role.getPersistKey(), RoleTotalRecordType.HIGHEST_TROOP_POWER_HISTORY);
        if (currHighTroopPower == 0) {
            long currTroopPower = role.getNumberProps().getLong(Prop.TROOPS_POWER);
            roleRecordManager.putRoleTotalRecord(role.getPersistKey(), RoleTotalRecordType.HIGHEST_TROOP_POWER_HISTORY, currTroopPower);
        }
    }

    /**
     * 异步线程改变战力马上计算~否则容易出现concurrentException
     *
     * @param role
     * @param type
     */
    public void changeImmediate(Role role, FightPowerType type) {
        this.doChange(role, type);
    }

    /**
     * 重新计算玩家战斗力~主线程计算 这个方法不要在IFightPower的calc方法里面调用！！！会导致循环调用，calc方法只负责计算添加战力
     * 这个方法不要在IFightPower的calc方法里面调用！！！会导致循环调用，calc方法只负责计算添加战力
     * 这个方法不要在IFightPower的calc方法里面调用！！！会导致循环调用，calc方法只负责计算添加战力
     *
     * @param role
     * @param type
     */
    public void change(Role role, FightPowerType type) {
        if (ServerConfigManager.getInstance().getGameConfig().isTickCalcOpen() && !role.isCreating()) {
            roleChangePowerAsync(role.getRoleId(), type);
        } else {
            this.doChange(role, type);
        }
    }

    private void doChange(Role role, FightPowerType type) {
        doChangeAsync(role, type, false);
    }

    /**
     * 限定装饰建筑战力变更调用
     * 由于change方法调用太多，所以单独添加一个方法出来
     *
     * @param role
     * @param type
     */
    public void changeByBuilding(Role role, FightPowerType type, boolean limitBuilding) {
        if (ServerConfigManager.getInstance().getGameConfig().isTickCalcOpen() && !role.isCreating()) {
            //限定装饰性建筑
            if (limitBuilding) {
                roleLimitBuildingChangePower.put(role.getId(), type);
            } else {
                roleChangePowerAsync(role.getRoleId(), type);
            }
        } else {
            doChangeAsync(role, type, limitBuilding);
        }
    }

    private void doChangeAsync(Role role, FightPowerType type, boolean limitBuilding) {
        IFightPower opt = capacityMap.get(type);
        if (opt == null) {
            ErrorLogUtil.errorLog("FightPower not impl", "type",type);
            return;
        }

        long oldPower = role.getNumberProps().getLong(Prop.FIGHT_CAPACITY);
        Map<FightPowerType, Long> typeOldPower = new HashMap<>(role.getFightPowerManager().getCapacities());
        opt.calc(role);
        Map<FightPowerType, Long> typeNewPower = new HashMap<>(role.getFightPowerManager().getCapacities());
        StringBuffer detail = new StringBuffer();
        for (var f : FightPowerType.values()) {
            var beforePower = typeOldPower.getOrDefault(f, 0L);
            var nowPower = typeNewPower.getOrDefault(f, 0L);
            if (!Objects.equals(beforePower, nowPower)) {
                long sub = nowPower - beforePower;
                if (sub > 0) {
                    detail.append(" type:" + f.name() + "增了, change=" + (nowPower - beforePower) + " now=" + nowPower );
                } else {
                    detail.append(" type:" + f.name() + "降了, change=" + (nowPower - beforePower) + " now=" + nowPower );
                }
            }
        }
        long oldPowerBefore = oldPower;
        roleCalcPropManager.updateProps(role, ICalcProp.RoleType.FIGHT_POWER);
        oldPower = role.getNumberProps().getLong(Prop.FIGHT_CAPACITY);
        logger.info("Role {} oldPower is {} -> {} type={}, detail={}", role.getPersistKey(), oldPowerBefore, oldPower, type, detail);
        srvDpd.getMissionService().onMissionFinish(role, MissionType.FIGHTINGCAPACITY, role);
        long currHightPower = roleRecordManager.getRoleTotalRecord(role.getPersistKey(), RoleTotalRecordType.HIGHEST_POWER_HISTORY);
        long currPower = role.getNumberProps().getLong(Prop.FIGHT_CAPACITY);
        if (currPower > currHightPower) {
            roleRecordManager.putRoleTotalRecord(role.getPersistKey(), RoleTotalRecordType.HIGHEST_POWER_HISTORY, currPower);
            //支付宝最高战力改变时触发
            alipayReportService.onAlipayReportProgress(role, AlipayReportType.FIGHTING_CAPACITY_UPDATE, currPower);
        }

        // 记录最高部队战力
        if (type == FightPowerType.SOLDIER) {
            long currHighTroopPower = roleRecordManager.getRoleTotalRecord(role.getPersistKey(), RoleTotalRecordType.HIGHEST_TROOP_POWER_HISTORY);
            long currTroopPower = role.getNumberProps().getLong(Prop.TROOPS_POWER);
            if (currTroopPower > currHighTroopPower) {
                roleRecordManager.putRoleTotalRecord(role.getPersistKey(), RoleTotalRecordType.HIGHEST_TROOP_POWER_HISTORY, currTroopPower);
            }
        }

        long increasePower = currPower - oldPowerBefore;
        if (increasePower != 0) {
            // 战斗力发生了变化
            roleExtraManager.updateFightPower(role);
        }

        long typeIncreasePower = role.getFightPowerManager().get(type) - typeOldPower.getOrDefault(type, 0L);
        if (type == FightPowerType.BUILDING) {
            //2022年5月10日 sean 这里加入装饰建筑判断，如果是限定装饰建筑则不进行加分处理
            srvDpd.getRoleGameEventManager().fire(role, GameEventType.BUILDING_POWER, typeIncreasePower);
        } else if (type == FightPowerType.TECHNOLOGY) {
            // 活动
            srvDpd.getRoleGameEventManager().fire(role, GameEventType.TECH_POWER, typeIncreasePower);
        } else if (type == FightPowerType.HERO) {
            // 竞技场战斗力更新 2024年1月12日
            srvDpd.getAsyncOperService().execute(new ArenaUpdateDefencePowerOperation(srvDpd, role));
        } else if (type == FightPowerType.SOLDIER) {
            // 部队总实力
        }
        else if (type == FightPowerType.LORD_EQUIP) {
            // 领主装备战力
        } else if (type == FightPowerType.LORD_GEM) {
            // 领主宝石战力
        }
        if (increasePower > 0) {
            if (role.isOnline()) {
                // 玩家不在线不用处理，重新登录的时候自然会处理
                srvDpd.getUnlockService().onPowerChanged(role, oldPowerBefore, currPower);
            }
            srvDpd.getBiLogUtil().powerUp(role, increasePower, type.name(), oldPowerBefore, currPower);
        } else if (increasePower < 0) {
            srvDpd.getBiLogUtil().powerDown(role, oldPowerBefore, currPower, type.name(), null);
        }
    }

    public void afterTicker() {
        if (ServerConfigManager.getInstance().getGameConfig().isTickCalcOpen()) {
            try {
                if (this.roleChangePower.isEmpty()) {
                    return;
                }
                var changePowerSet = this.roleChangePower;
                this.roleChangePower = ArrayListMultimap.create();

                //把限定装饰性建筑也加入进去一起处理
//				roleIds.addAll(roleLimitBuildingChangePower.keySet());
                for (long roleId : changePowerSet.keySet()) {
                    try {
                        Role role = srvDpd.getWorldService().getWorld().getRole(roleId);
                        if (role == null) {
                            ErrorLogUtil.errorLog("role is null when fight power after ticker", "roleId",roleId);
                            continue;
                        }
                        // 正在清理中,不执行了
                        if (role.isClearing()) {
                            logger.info("afterTicker role:{} is clearing", roleId);
                            continue;
                        }
                        var list = changePowerSet.get(roleId);
                        if (list == null) {
                            continue;
                        }
                        var powerTypes = new HashSet<>(list);
                        for (FightPowerType type : powerTypes) {
                            doChange(role, type);
                        }
                        //2022年5月12日 sean 对限定装饰性建筑进行特殊处理（开服冲榜活动不加战力）
//						List<FightPowerType> limitBuildPowerTypes = roleLimitBuildingChangePower.get(roleId);
//						if(CollectionUtils.isNotEmpty(limitBuildPowerTypes)){
//							List<FightPowerType> copyTypes = new CopyOnWriteArrayList<>(limitBuildPowerTypes);
//							for (FightPowerType type : copyTypes) {
//								doChangeAsync(role, type, true);
//								limitHandled.add(type);
//							}
//						}
                    } catch (ExpectedException ignored) {

                    } catch (Exception e) {
                        ErrorLogUtil.exceptionLog("role's fight power after ticker error",e,"roleId",roleId);
                    } finally {
                    }
                }
            } catch (ExpectedException ignored) {

            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("fight power after ticker error",e);
            }
        }
    }
    private void roleChangePowerAsync(Long roleId, FightPowerType type) {
        if (!JavaUtils.bool(roleId) || type == null) {
            return;
        }
        srvDpd.getAsyncOperService().execute(new BindThreadOperation() {
            @Override
            public long getBindId() {
                return 0;
            }

            @Override
            public boolean run() {
                roleChangePower.put(roleId, type);
                return false;
            }
        });
    }
    public void afterTickerForCleanPlayer(Long roleId) {
    }
}
