package com.lc.billion.icefire.game.biz.service.impl.horse;

import com.lc.billion.icefire.core.collection.Tuples;
import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.horse.HorseRenameOperation;
import com.lc.billion.icefire.game.biz.config.*;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceMemberDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.HorseMateDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleHorseDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.manager.prop.base.ICalcProp;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.event.GameEventType;
import com.lc.billion.icefire.game.biz.model.fightpower.FightPowerType;
import com.lc.billion.icefire.game.biz.model.horse.*;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunctionType;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.service.impl.CheckStringServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceChatExport;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.fight.FightPowerServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.*;
import com.lc.billion.icefire.protocol.structure.*;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Service
public class HorseServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(HorseServiceImpl.class);
    @Autowired
    private ServiceDependency srvDep;
    @Autowired
    private RoleHorseDao horseDao;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private RoleItemManager roleItemManager;
    @Autowired
    private RoleCurrencyManager roleCurrencyManager;
    @Autowired
    private RoleGameEventManager roleGameEventManager;
    @Autowired
    private FightPowerServiceImpl fightPowerService;
    @Autowired
    private RoleCalcPropManager roleCalcPropManager;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private AllianceManager allianceManager;
    @Autowired
    private HorseMateDao horseMateDao;
    @Autowired
    private AllianceMemberDao allianceMemberDao;
    @Autowired
    private AllianceChatExport allianceChatExport;
    @Autowired
    private BiLogUtil biLogUtil;
    @Autowired
    private CheckStringServiceImpl checkStringService;

    @Autowired
    private DropServiceImpl dropServiceImpl;


    //region 主体逻辑协议处理 (合成、升级、升星、装备升级)

    /**
     * 使用碎片兑换坐骑
     * @param role
     * @param horseId
     */
    public void horseExchange(Role role,int horseId){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_SYSTEM)){
            return;
        }
        //错误码 1 配置错误 2 已经合成过了 3 碎片不足 99 服务器异常
        var res = new GcHorseExchange();
        res.setHorseId(horseId);
        try{
            var meta = getHorseMeta(horseId);
            if(meta == null){
                res.setErrorCode(1);
                return;
            }
            var roleHorse = getOrCreate(role);
            if(roleHorse == null){
                res.setErrorCode(1);
                return;
            }
            var exist = roleHorse.getHorseMap().get(horseId);
            if(exist != null){
                res.setErrorCode(2);
                return;
            }
            //检查合成消耗
            var exchangeStar = configService.getConfig(HorseStarConfig.class).getExchangeMeta(horseId);
            if(exchangeStar == null){
                res.setErrorCode(1);
                return;
            }
            var cost = exchangeStar.getCostItems();
            if(!checkAndCostItems(role,cost,LogReasons.ItemLogReason.HORSE_EXCHANGE,GameEventType.HORSE_STAR_UP)){
                res.setErrorCode(3);
                return;
            }

            //标记成功
            res.setErrorCode(0);

            //创建马匹
            var newHorse = createHorse(roleHorse,horseId);
            onHorseChanged(role,newHorse);

            //任务事件抛出 & 打点
            onHorseGet(role,horseId);
            biLogUtil.horseUnlock(role, horseId, HorseUnlockWay.FRAGMENT_UNLOCK.getId());

            logger.info("坐骑兑换成功,role:{},horse:{}",role.getId(),horseId);
        }catch (Exception e){
            res.setErrorCode(99);
            ErrorLogUtil.exceptionLog("坐骑兑换错误",e,"roleId",role.getId(),"horseId",horseId);
        } finally{
            role.send(res);
        }
    }

    /**
     * 马匹升级
     * @param role
     * @param horseId
     */
    public void horseLevelUp(Role role,int horseId){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_SYSTEM)){
            return;
        }
        //错误码 1 马匹不存在 2 达到最大等级 3 消耗不足 99 服务器异常
        var res = new GcHorseLevelUp();
        res.setHorseId(horseId);
        try{
            var horse = getHorse(role,horseId);
            if(horse == null){
                res.setErrorCode(1);
                return;
            }
            var nextLevel = horse.getLevel() + 1;
            var nextTemp = getHorseLevelMeta(horseId,nextLevel);
            if(nextTemp == null){
                res.setErrorCode(2);
                return;
            }
            var currentTemp = getHorseLevelMeta(horseId,nextLevel - 1);
            //检查升级消耗
            var cost = currentTemp.getExp();
            if(!roleCurrencyManager.checkAndCost(role, Currency.HORSE_EXP,cost, LogReasons.MoneyLogReason.HORSE_LEVEL_UP)){
                res.setErrorCode(3);
                return;
            }

            //标记成功
            res.setErrorCode(0);

            //升级
            horse.setLevel(nextLevel);

            onHorseChanged(role,horse);

            //任务事件抛出 & 打点
            onHorseLevelUp(role,horseId);
            biLogUtil.horseUpgrade(role, horseId, nextLevel);

            logger.info("坐骑升级成功,role:{},horse:{},level:{}",role.getId(),horseId,nextLevel);
        }catch (Exception e){
            res.setErrorCode(99);
            ErrorLogUtil.exceptionLog("坐骑升级错误",e,"roleId",role.getId(),"horseId",horseId);
        } finally{
            role.send(res);
        }
    }

    /**
     * 坐骑升星
     * @param role
     * @param horseId
     */
    public void horseStarUp(Role role,int horseId){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_SYSTEM)){
            return;
        }
        //错误码 1 马匹不存在 2 达到最大星级 3 消耗不足 4 装备等级不够 99 服务器异常
        var res = new GcHorseStarUp();
        res.setHorseId(horseId);
        try{
            var horse = getHorse(role,horseId);
            if(horse == null){
                res.setErrorCode(1);
                return;
            }
            var nextStar = horse.getStar() + 1;
            var nextTemp = getHorseStarMeta(horseId,nextStar);
            if(nextTemp == null){
                res.setErrorCode(2);
                return;
            }
            var currentTemp = getHorseStarMeta(horseId,nextStar - 1);
            //检查装备等级是否达标
            var requireEquipLevel = currentTemp.getRequireEquipTotal();
            var notRequired = horse.getEquipMap().values().stream().anyMatch(e->e.getLevel() < requireEquipLevel);
            if(notRequired){
                res.setErrorCode(4);
                return;
            }
            //检查升级消耗
            var cost = currentTemp.getCostItems();
            if(!checkAndCostItems(role,cost,LogReasons.ItemLogReason.HORSE_STAR_UP,GameEventType.HORSE_STAR_UP)){
                res.setErrorCode(3);
                return;
            }

            //标记成功
            res.setErrorCode(0);

            //升级
            horse.setStar(nextStar);

            onHorseChanged(role,horse);

            //TODO 任务事件抛出 & 打点
            srvDep.getMissionService().onMissionFinish(role, MissionType.HORSE_STAR_BY_ID, role, horseId);
            biLogUtil.horseStarUp(role, horseId, nextStar);

            logger.info("坐骑升星成功,role:{},horse:{},star:{}",role.getId(),horseId,nextStar);
        }catch (Exception e){
            res.setErrorCode(99);
            ErrorLogUtil.exceptionLog("坐骑升星错误",e,"roleId",role.getId(),"horseId",horseId);
        } finally{
            role.send(res);
        }
    }

    public void horseEquipLevelUp(Role role,int horseId,int part){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_SYSTEM)){
            return;
        }
        //错误码 1 马匹不存在 2 装备不存在 3 消耗不足 4 达到等级限制的最大级别 99 服务器异常
        var res = new GcHorseEquipLevelUp();
        res.setHorseId(horseId);
        res.setPart(part);
        try{
            var horse = getHorse(role,horseId);
            if(horse == null){
                res.setErrorCode(1);
                return;
            }
            var equip = horse.getEquipMap().get(part);
            if(equip == null){
                res.setErrorCode(2);
                return;
            }
            var nextLevel = equip.getLevel() + 1;
            var nextTemp = getHorseEquipLevelMeta(horseId,part,nextLevel);
            if(nextTemp == null){
                res.setErrorCode(4);
                return;
            }
            var currentTemp = getHorseEquipLevelMeta(horseId,part,nextLevel - 1);
            if(horse.getStar() < currentTemp.getRequireStarLevel()){
                res.setErrorCode(1);
                return;
            }
            //检查升级消耗
            var cost = currentTemp.getCostItems();
            if(!checkAndCostItems(role,cost,LogReasons.ItemLogReason.HORSE_EQUIP_LEVEL_UP,GameEventType.HORSE_EQUIP_LEVEL_UP)){
                res.setErrorCode(3);
                return;
            }

            //标记成功
            res.setErrorCode(0);

            //升级
            if(equip == null){
                equip = new HorseEquip(part, 1);
                horse.getEquipMap().put(part,equip);
            }

            equip.setLevel(nextLevel);

            onHorseChanged(role,horse);

            //任务事件抛出 & 打点
            onHorseEquipLevelUp(role,horseId);
            biLogUtil.horseEquipUpgrade(role, horseId, part, nextLevel);

            logger.info("坐骑装备升级成功,role:{},horse:{},part:{},level:{}",role.getId(),horseId,part,nextLevel);
        }catch (Exception e){
            res.setErrorCode(99);
            ErrorLogUtil.exceptionLog("坐骑装备升级错误",e,"roleId",role.getId(),"horseId",horseId,"part",part);
        } finally{
            role.send(res);
        }
    }

    /**
     * 坐骑改名
     * @param role
     * @param horseId
     * @param name
     */
    public void horseRename(Role role,int horseId,String name){
        // 开关检查
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_SYSTEM)){
            return;
        }
        name = name.trim();
        // 改名是否符合命名规则
        if (StringUtils.isEmpty(name)) {
            sendHorseRenameError(role, PsErrorCode.CHECK_WORD_ERROR);
            return;
        }
        // 首字符不能是数字
        char firstChar = name.charAt(0);
        if (firstChar >= '0' && firstChar <= '9') {
            sendHorseRenameError(role, PsErrorCode.CHECK_WORD_ERROR);
            return;
        }
        // 判断是否含有表情
        if (Utils.isEmo(name)) {
            sendHorseRenameError(role, PsErrorCode.CHECK_WORD_ERROR);
            return;
        }
        var settingConfig = srvDep.getConfigService().getConfig(SettingConfig.class);
        if(settingConfig == null){
            return;
        }
        int[] len = settingConfig.getHorseChangeNameRule();
        if(len == null || len.length != 2){
            return;
        }
        var min = len[0];
        var max = len[1];
        // 长度及特殊字符检查
        if(!checkStringService.checkName(name,min,max,false,null)){
            sendHorseRenameError(role, PsErrorCode.CHECK_WORD_ERROR);
            return;
        }
        // 坐骑检查
        var horse = getHorse(role,horseId);
        if(horse == null){
            // 没有对应坐骑
            return;
        }
        if(horse.getStar() < settingConfig.getHorseNameStarLimit()){
            // 星级限制
            return;
        }
        var free = false;
        // 冷却时间检查
        var now = TimeUtil.getNow();
        var lastRenameTime = horse.getLastRenameTime();
        if(lastRenameTime == 0){
            free = true;
        }
        if(lastRenameTime != 0 && now - lastRenameTime < settingConfig.getHorseNameCoolDown()){
            // 已经改过的并且时间未到，未改过的不用return
            return;
        }
        int cost = settingConfig.getHorseNameCost();
        // 检查敏感词和元宝数并改名
        srvDep.getAsyncOperService().execute(new HorseRenameOperation(role, this,horseId, name, free, cost));
    }

    public void doHorseRename(Role role,int horseId,String name, boolean free, int cost){
        // 元宝是否足够
        if(!free){
            if(!roleCurrencyManager.checkAndCost(role,Currency.DIAMOND,cost, LogReasons.MoneyLogReason.HORSE_RENAME)){
                GcHorseRename res = new GcHorseRename();
                res.setCode(PsErrorCode.RES_NOT_ENOUGH_DIAMOND);
                return;
            }
        }
        // 存库
        RoleHorse roleHorse = getOrCreate(role);
        var horse = roleHorse.getHorseMap().get(horseId);
        horse.setHorseName(name);
        var now = TimeUtil.getNow();
        horse.setLastRenameTime(now);
        horseDao.save(roleHorse);
        // 打点
        biLogUtil.horseChangeName(role,horseId);
        // 回消息
        sendHorseRename(role,PsErrorCode.SUCCESS,horseId,name,now);
        // 同步
        sendHorseList(role);
    }

    //endregion


    //region 通用的方法

    protected void onHorseGet(Role role,int horseId){

        srvDep.getMissionService().onMissionFinish(role, MissionType.HORSE_LEVEL_BY_ID, role, horseId);
        srvDep.getMissionService().onMissionFinish(role, MissionType.HORSE_STAR_BY_ID, role, horseId);
        srvDep.getMissionService().onMissionFinish(role, MissionType.HORSE_EQUIP_TOTAL_LEVEL_BY_ID, role, horseId);

        srvDep.getLiBaoService().liBaoUpdateOnCheck(role, "坐骑获得", LiBaoFunctionType.HORSE_GET, horseId);
    }

    protected void onHorseLevelUp(Role role,int horseId){
        srvDep.getMissionService().onMissionFinish(role, MissionType.HORSE_LEVEL_BY_ID,role, horseId);
    }

    protected void onHorseEquipLevelUp(Role role,int horseId){
        srvDep.getMissionService().onMissionFinish(role, MissionType.HORSE_EQUIP_TOTAL_LEVEL_BY_ID,role, horseId);
        srvDep.getMissionService().onMissionFinish(role, MissionType.HORSE_EQUIP_LEVEL_UP_NUM);
    }

    public Horse getHorse(Role role,int horseId){
        var meta = getHorseMeta(horseId);
        if(meta == null){
            return null;
        }
        var roleHorse = getOrCreate(role);
        if(roleHorse == null){
            return null;
        }
        return roleHorse.getHorseMap().get(horseId);
    }

    private boolean checkAndCostItems(Role role, List<SimpleItem> cost, LogReasons.ItemLogReason costReason,GameEventType eventType){
        if(!roleItemManager.hasItem(role,cost,false)){
            return false;
        }

        //扣除物品
        roleItemManager.removeItems(role,cost, costReason);
        //抛事件
        if(eventType != null){
            roleGameEventManager.fire(role, eventType, cost);
        }
        return true;
    }

    private void onHorseChanged(Role role,Horse horse){
        //存库
        save(role);

        //重新计算战力
        fightPowerService.change(role, FightPowerType.HORSE_TOTAL);

        // 更新属性
        roleCalcPropManager.addChangeType(role, ICalcProp.RoleType.HORSE);

        //发送马匹消息
        sendHorseUpdate(role,horse);
    }

    public long getHorsePower(Role role, int horseId){
        Horse horse = getHorse(role, horseId);
        if(horse == null){
            return 0;
        }
        return getHorsePower(horse);
    }

    public long getHorsePower(Horse horse){
        var tuple = getHorsePowerDivided(horse);
        return tuple.value1() + tuple.value2();
    }

    public Tuples.Tuple2<Long,Long> getHorsePowerDivided(Horse horse){
        var horsePower = 0L;
        var equipPower = 0L;
        if(horse != null){
            var levelConfig = configService.getConfig(HorseLevelConfig.class);
            var starConfig = configService.getConfig(HorseStarConfig.class);
            var equipConfig = configService.getConfig(HorseEquipConfig.class);

            var levelTemp = levelConfig.getLevelMeta(horse.getMetaId(), horse.getLevel());
            if(levelTemp != null){
                horsePower += levelTemp.getPower();
            }

            var starTemp = starConfig.getStarMeta(horse.getMetaId(),horse.getStar());
            if(starTemp != null){
                horsePower += starTemp.getPower();
            }

            for (var equip : horse.getEquipMap().entrySet()){
                var meta = equipConfig.getPartLevelMeta(horse.getMetaId(),equip.getKey(),equip.getValue().getLevel());
                if(meta != null){
                    equipPower += meta.getPower();
                }
            }
        }
        return Tuples.create(horsePower,equipPower);
    }

    //endregion


    //region 通用协议相关(列表推送 & 更新 & 协议对象转换)

    /**
     * 登录时发送全量
     * @param role
     */
    public void onEnterWorld(Role role){
        //登录时发送
        sendHorseList(role);
        getHorseMateList(role);
        calAndPushPersonalPageStatus(role);
    }

    private void sendHorseList(Role role){
        var msg = new GcHorseList();
        msg.setHorses(getPsHorseList(role));
        role.send(msg);
    }

    public List<PsHorseInfo> getPsHorseList(Role role){
        var list = new ArrayList<PsHorseInfo>();
        var horse = getOrCreate(role);
        if(horse == null){
            return list;
        }
        horse.getHorseMap().values().forEach(h->list.add(toPsInfo(h)));
        return list;
    }

    /**
     * 单个更新消息
     * @param role
     * @param horse
     */
    private void sendHorseUpdate(Role role,Horse horse){
        var msg = new GcHorseUpdate();
        msg.setHorse(toPsInfo(horse));
        role.send(msg);
    }

    private void sendHorseRename(Role role,PsErrorCode code, int horseId, String name,long lastRenameTime){
        var msg = new GcHorseRename();
        msg.setCode(code);
        if(code == PsErrorCode.SUCCESS){
            msg.setHorseId(horseId);
            msg.setName(name);
            msg.setLastRenameTime(lastRenameTime);
        }
        role.send(msg);
    }

    private void sendHorseRenameError(Role role, PsErrorCode code){
        GcHorseRename gcHorseRename = new GcHorseRename();
        gcHorseRename.setCode(code);
        role.send(gcHorseRename);
    }

    private PsHorseInfo toPsInfo(Horse horse){
        var res = new PsHorseInfo();
        res.setHorseId(horse.getMetaId());
        res.setLevel(horse.getLevel());
        res.setStar(horse.getStar());
        res.setEquips(new HashMap<>());
        res.setHorseName(horse.getHorseName());
        res.setLastRenameTime(horse.getLastRenameTime());
        for (var entry : horse.getEquipMap().entrySet()){
            res.getEquips().put(entry.getKey(),toPsInfo(entry.getValue()));
        }
        return res;
    }

    private PsHorseEquipInfo toPsInfo(HorseEquip equip){
        var res = new PsHorseEquipInfo();
        res.setLevel(equip.getLevel());
        return res;
    }

    private PsHorseMateObject toPsMateObject(Horse horse, String dropMetaId, long roleId, String name, long power){
        var ret = new PsHorseMateObject();
        ret.setHorse(toPsInfo(horse));
        ret.setDropMetaId(dropMetaId);
        var role = roleDao.findById(roleId);
        if(role != null){
            ret.setRoleInfo(role.toOfficialPsRoleInfo());
        }
        ret.setName(name);
        ret.setPower(power);
        return ret;
    }

    private PsHorseInfo toPsInfo(BestHorseInfo bestHorseInfo){
        var ret = new PsHorseInfo();
        if(bestHorseInfo != null){
            ret.setHorseId(bestHorseInfo.getHorseId());
            ret.setStar(bestHorseInfo.getStar());
            ret.setLevel(bestHorseInfo.getLevel());
            ret.setEquips(new HashMap<>());
            ret.setHorseName(bestHorseInfo.getHorseName());
            ret.setLastRenameTime(bestHorseInfo.getLastRenameTime());
            if(bestHorseInfo.getEquipMap() != null){
                for (var entry : bestHorseInfo.getEquipMap().entrySet()){
                    ret.getEquips().put(entry.getKey(),toPsInfo(entry.getValue()));
                }
            }
        }
        return ret;
    }

    private PsHorseMateRecord toPsHorseMateRecord(HorseMatingItem item){
        var ret = new PsHorseMateRecord();
        var role = roleDao.findById(item.getPartnerId());
        if(role != null){
            ret.setInviteRoleInfo(role.toOfficialPsRoleInfo());
        }
        var horseInfo = item.getSelfHorseInfo();
        if(horseInfo != null){
            ret.setHorse(toPsInfo(horseInfo));
            ret.setPower(horseInfo.getPower());
        }
        var partnerHorseInfo = item.getPartnerHorseInfo();
        if(partnerHorseInfo != null){
            ret.setInviteHorse(toPsInfo(partnerHorseInfo));
            ret.setInvitePower(partnerHorseInfo.getPower());
        }

        return ret;
    }

    private PsHorseMateRecord toPsHorseMatingRecord(HorseMatingRecord record) {
        var ret = new PsHorseMateRecord();
        if(record == null){
            return ret;
        }
        var horse = record.getHorse();
        if(horse != null){
            ret.setHorse(toPsInfo(horse));
            ret.setPower(horse.getPower());
        }
        var name = record.getName();
        if(name != null){
            ret.setName(name);
        }
        ret.setTime(record.getTime());
        var role = roleDao.findById(record.getRivalId());
        if(role != null){
            ret.setInviteRoleInfo(role.toOfficialPsRoleInfo());
        }
        var rivalHorse = record.getRivalHorse();
        if(rivalHorse != null){
            ret.setInviteHorse(toPsInfo(rivalHorse));
            ret.setInvitePower(rivalHorse.getPower());
        }
        ret.setRecordId(record.getRecordId());
        ret.setIsInviter(record.isInviter());

        return ret;
    }

    private void sendHorseMatingList(Role role, HorseMateErrorCode errorCode, List<Long> mates){
        var msg = new GcHorseMateList();
        msg.setCode(errorCode);

        RoleServerInfoDao roleServerInfoDao = Application.getBean(RoleServerInfoDao.class);
        if(JavaUtils.bool(mates)){
            RoleServerInfo rsi = roleServerInfoDao.findById(role.getRoleId());
            if (rsi == null || rsi.isRoleLoading()) {
                // 玩家还在加载中，临时处理，不让他再去创建，以免主键冲突
                return;
            }

            List<PsHorseMateObject> infos = new ArrayList<>();
            var myHorse = getHighestPowerHorse(role);
            if(myHorse == null){
                return;
            }
            var config = getHorseMatingConfig();
            var myDrop = config.getDropReward(myHorse.value2(), Application.getSeason());
            if(myDrop == null){
                return;
            }
            for(Long roleId : mates){
                Role inviter = roleDao.findById(roleId);
                if(inviter == null){
                    continue;
                }

                var inviterHorse = getHighestPowerHorse(inviter);
                if(inviterHorse == null){
                    continue;
                }
                String dropMetaId = inviterHorse.value2() > myHorse.value2() ? config.getDropReward(inviterHorse.value2(), Application.getSeason()) : myDrop;
                PsHorseMateObject info = toPsMateObject(inviterHorse.value1(), dropMetaId, roleId, inviter.getName(), inviterHorse.value2());
                infos.add(info);
            }
            msg.setInfos(infos);
        }
        role.send(msg);
    }

    private void sendHorseMateReward(Role role, HorseMateErrorCode errorCode, List<SimpleItem> rewardList, PsHorseMateSuccessType successType){
        var msg = new GcHorseMateGetReward();
        msg.setCode(errorCode);
        if(rewardList != null){
            List<PsSimpleItem> rewardItemList = new ArrayList<>();
            for(SimpleItem item : rewardList){
                PsSimpleItem psItem = item.toPsObject();
                rewardItemList.add(psItem);
            }
            msg.setRewardItems(rewardItemList);
            msg.setSuccessType(successType);
        }
        role.send(msg);
    }

    private void sendHorseMateRecord(Role role, HorseMateErrorCode code, List<HorseMatingRecord> records){
        var ret = new GcHorseMateRecord();
        ret.setCode(code);
        if(JavaUtils.bool(records)){
            List<PsHorseMateRecord> psRecords = new ArrayList<>();
            for(HorseMatingRecord record : records){
                psRecords.add(toPsHorseMatingRecord(record));
            }
            ret.setRecords(psRecords);
        }
        role.send(ret);
    }

    private void sendHorseMate(Role role, HorseMateErrorCode code){
        var msg = new GcHorseMate();
        msg.setCode(code);
        role.send(msg);
    }

    private void sendHorseMateJoin(Role role, HorseMateErrorCode code){
        var msg = new GcHorseMateJoin();
        msg.setCode(code);
        role.send(msg);
    }

    private void sendHorseMateCancel(Role role, HorseMateErrorCode code){
        var msg = new GcHorseMateCancel();
        msg.setCode(code);
        role.send(msg);
    }

    private void sendHorseMateSelf(Role role, HorseMateErrorCode code, HorseMating horseMating){
        var ret = new GcHorseMateSelf();
        ret.setCode(code);
        if(horseMating != null){
            ret.setMateCount(horseMating.getTimes());
            ret.setRecord(toPsHorseMateRecord(horseMating.getItem()));
            ret.setCanGetReward(horseMating.getStatus()==HorseMateStatus.FINISHED);
            ret.setEndTime(horseMating.getItem().getEndTime());
            ret.setStatus(horseMating.getClientStatus());
        }
        role.send(ret);
    }

    private void sendHorseMateShare(Role role, HorseMateErrorCode code){
        var ret = new GcHorseMateShare();
        ret.setCode(code);
        role.send(ret);
    }

//    public void sendItemToHorse(Role role,SimpleItem item){
//        var msg = new GcItemToHorse();
//        msg.setItems(item.toPsObject());
//        role.send(msg);
//    }
    //endregion


    //region 生命周期相关

    public RoleHorse getOrCreate(Role role){
        var roleHorses = horseDao.findById(role.getRoleId());
        if(roleHorses == null){
            roleHorses = horseDao.create(role);
            if(roleHorses != null){
                horseDao.save(roleHorses);
            }
        }
        return roleHorses;
    }

    private Horse createHorse(RoleHorse roleHorse,int horseId){
        var newHorse = new Horse();
        newHorse.setMetaId(horseId);
        newHorse.setLevel(1);
        newHorse.setStar(0);
        //初始化一级装备
        for (var part : configService.getConfig(HorseEquipConfig.class).getHorseParts(horseId)) {
            var equip = new HorseEquip(part, 1);
            newHorse.getEquipMap().put(part,equip);
        }
        roleHorse.getHorseMap().put(newHorse.getMetaId(),newHorse);
        return newHorse;
    }

    private void save(Role role){
        var roleHorses = horseDao.findById(role.getRoleId());
        if(roleHorses == null){
            roleHorses = horseDao.create(role);
        }
        horseDao.save(roleHorses);
    }

    private HorseMating getOrCreateHorseMating(Role role) {
        HorseMating horseMating = horseMateDao.findById(role.getPersistKey());
        if (horseMating == null) {
            horseMating = horseMateDao.create(role);
            horseMating.setStatus(HorseMateStatus.IDLE);
            horseMating.setClientStatus(PsHorseMateStatus.IDLE);
            horseMating.setItem(new HorseMatingItem());
            horseMateDao.save(horseMating);
        }

        return horseMating;
    }

    public SimpleItem horseGet(Role role, int horseId, LogReasons.ItemLogReason reason) {
        var roleHorses = getOrCreate(role);
        if(roleHorses.getHorseMap().get(horseId) != null){
            var horseMeta = getHorseMeta(horseId);
            // 碎片转换
            srvDep.getItemService().give(role,horseMeta.getFragmentId(),horseMeta.getFragmentExchange(),reason);
            return new SimpleItem(horseMeta.getFragmentId(),horseMeta.getFragmentExchange());
        }else{
            // 创建马
            Horse horse = createHorse(roleHorses,horseId);
            onHorseChanged(role, horse);
            onHorseGet(role,horseId);

            sendHorseList(role);
            biLogUtil.horseUnlock(role,horseId,HorseUnlockWay.WHOLE_UNLOCK.getId());
            return new SimpleItem(String.valueOf(horseId),1);
        }
    }

    //endregion

    //region 配置文件读取
    private HorseConfig.HorseMeta getHorseMeta(int horseId) {
        return configService.getConfig(HorseConfig.class).getMetaById(String.valueOf(horseId));
    }

    private HorseStarConfig.HorseStarMeta getHorseStarMeta(int horseId,int star){
        return configService.getConfig(HorseStarConfig.class).getStarMeta(horseId,star);
    }

    private HorseLevelConfig.HorseLevelMeta getHorseLevelMeta(int horseId,int level){
        return configService.getConfig(HorseLevelConfig.class).getLevelMeta(horseId,level);
    }

    private HorseEquipConfig.HorseEquipMeta getHorseEquipLevelMeta(int horseId,int part,int level){
        return configService.getConfig(HorseEquipConfig.class).getPartLevelMeta(horseId,part,level);
    }

    private HorseQualityConfig.HorseQualityMeta getHorseQualityMeta(int quality){
        return configService.getConfig(HorseQualityConfig.class).getQualityMeta(quality);
    }

    private SettingConfig getSettingConfig() {
        return configService.getConfig(SettingConfig.class);
    }

    private HorseMatingConfig getHorseMatingConfig() {
        return configService.getConfig(HorseMatingConfig.class);
    }


    //endregion

    //region 坐骑交配

    /**
     * 获取盟友邀约列表
     */
    public void getHorseMateList(Role role){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_MATE)){
            return;
        }
        var allianceId = role.getAllianceId();
        if(!JavaUtils.bool(allianceId)){
            return;
        }
        List<Long> allianceMateList = new ArrayList<>();
        var memberList = allianceMemberDao.findByAllianceId(allianceId);
        if(!JavaUtils.bool(memberList)){
            ErrorLogUtil.errorLog("玩家联盟人员列表为空","roleId",role.getId(),"allianceId",role.getAllianceId());
            sendHorseMatingList(role, HorseMateErrorCode.ALLIANCE_ERROR, null);
            return;
        }
        for(AllianceMember member : memberList){
            var id = member.getPersistKey();
            // 排除自己
            if(Objects.equals(id,role.getPersistKey())){
                continue;
            }
            var matingMap = horseMateDao.getMatingMap();
            if(matingMap.containsKey(id)){
                allianceMateList.add(id);
            }
        }
        logger.info("玩家 {} 获取到盟友邀约列表成功:{}", role.getId(),allianceMateList);
        sendHorseMatingList(role, HorseMateErrorCode.SUCCESS, allianceMateList);
    }

    /**
     * 发起坐骑交配邀约
     */
    public void horseMate(Role role){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_MATE)){
            return;
        }
        // 检查自己坐骑系统是否解锁
        if(!(srvDep.getUnlockService().isUnlock(role, UnlockModule.HORSE_BUILDING))){
            logger.info("玩家坐骑系统未解锁 roleId:{}",role.getId());
            sendHorseMate(role, HorseMateErrorCode.MODULE_LOCK);
            return;
        }
        // 检查玩家坐骑情况
        RoleHorse roleHorses = getOrCreate(role);
        if(!JavaUtils.bool(roleHorses.getHorseMap())){
            ErrorLogUtil.errorLog("玩家坐骑列表为空","roleId",role.getId());
            sendHorseMate(role, HorseMateErrorCode.NO_HORSE);
            return;
        }
        // 检查玩家联盟情况
        if(!allianceManager.roleAllianceValid(role)){
            sendHorseMate(role, HorseMateErrorCode.ALLIANCE_ERROR);
            return;
        }
        // 检查次数
        var config = getSettingConfig();
        var horseMating = getOrCreateHorseMating(role);
        if(config.getHorseMatingCount() <= horseMating.getTimes()){
            ErrorLogUtil.errorLog("坐骑邀约次数当日到达上限","roleId",role.getId());
            sendHorseMate(role, HorseMateErrorCode.MATE_TIMES_LIMIT);
            return;
        }
        // 检查当前状态，只有IDLE和CANCELED才能发起邀约
        if(!(horseMating.getStatus() == HorseMateStatus.IDLE || horseMating.getStatus() == HorseMateStatus.CANCELED)){
            ErrorLogUtil.errorLog("当前邀约未完成，不能发起新邀约","roleId",role.getId(),"status",horseMating.getStatus());
            sendHorseMate(role, HorseMateErrorCode.MATE_NOT_FINISH);
            return;
        }

        var time = TimeUtil.getNow();
        var horseMateItem = horseMating.getItem();
        // 发起邀约更新状态
        horseMateItem.setStartTime(time);
        horseMateItem.setEndTime(time+config.getHorseMatingRuntime()*TimeUtil.MINUTE_MILLIS);
        horseMateItem.setInviter(true);
        horseMating.setStatus(HorseMateStatus.INVITING);
        horseMating.setClientStatus(PsHorseMateStatus.INVITING);
        // 存库
        horseMateDao.save(horseMating);
        // 加入邀请中的列表
        var matingMap = horseMateDao.getMatingMap();
        matingMap.put(role.getRoleId(), horseMating);
        logger.info("玩家 {} 发起坐骑交配邀约成功", role.getId());
        sendHorseMate(role, HorseMateErrorCode.SUCCESS);
        calAndPushPersonalPageStatus(role);
        broadcastHorseMateToAlliance(role);
    }

    /**
     * 加入坐骑交配邀约
     * @param inviterId 邀约人id
     */
    public void horseMateJoin(Role role, long inviterId){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_MATE)){
            return;
        }
        // 加入的玩家需解锁坐骑系统
        if(!(srvDep.getUnlockService().isUnlock(role, UnlockModule.HORSE_BUILDING))){
            logger.info("玩家坐骑系统未解锁 roleId:{}",role.getId());
            sendHorseMateJoin(role, HorseMateErrorCode.MODULE_LOCK);
            return;
        }
        // 加入玩家需有坐骑
        RoleHorse horses = getOrCreate(role);
        if(!JavaUtils.bool(horses.getHorseMap())){
            ErrorLogUtil.errorLog("玩家坐骑列表为空","roleId",role.getId());
            sendHorseMateJoin(role, HorseMateErrorCode.NO_HORSE);
            return;
        }
        Role inviter = roleDao.findById(inviterId);
        // 检查邀约人联盟情况
        if(!allianceManager.roleAllianceValid(inviter) || !Objects.equals(role.getAllianceId(), inviter.getAllianceId())){
            ErrorLogUtil.errorLog("邀请人联盟检查未通过", "roleId",role.getId(), "roleAllianceId", role.getAllianceId(),"inviterId",inviterId, "inviterAllianceId", inviter.getAllianceId());
            // 在联盟中发起邀约后退出联盟，重新推送
            getHorseMateList(role);
            sendHorseMateJoin(role, HorseMateErrorCode.ALLIANCE_ERROR);
            return;
        }
        // 检查邀约状态
        var originator = getOrCreateHorseMating(inviter);
        if(originator.getStatus() != HorseMateStatus.INVITING){
            ErrorLogUtil.errorLog("邀约已结束", "roleId",role.getId(), "inviterId",inviterId);
            sendHorseMateJoin(role, HorseMateErrorCode.INVITATION_EXPIRED);
            getHorseMateList(role);
            return;
        }
        // 检查邀约是否过期
        if(originator.getItem().getEndTime()<TimeUtil.getNow()){
            ErrorLogUtil.errorLog("邀约已过期", "roleId",role.getId(), "inviterId",inviterId,"endTime",originator.getItem().getEndTime());
            sendHorseMateJoin(role, HorseMateErrorCode.INVITATION_EXPIRED);
            getHorseMateList(role);
            return;
        }
        // 当日完成次数检查
        var config = getSettingConfig();
        var participant = getOrCreateHorseMating(role);
        if(participant.getTimes() >= config.getHorseMatingCount()){
            ErrorLogUtil.errorLog("玩家当日坐骑交配次数已达上限","roleId",role.getId());
            sendHorseMateJoin(role, HorseMateErrorCode.MATE_TIMES_LIMIT);
            return;
        }
        // 如果当前宝箱未领取，则飘字提醒领完再加入
        if(participant.getStatus() == HorseMateStatus.FINISHED){
            sendHorseMateJoin(role, HorseMateErrorCode.BOX_NOT_CLAIM);
            return;
        }
        var partHorse = getHighestPowerHorse(role);
        var oriHorse = getHighestPowerHorse(inviter);

        if(partHorse == null || oriHorse == null){
            return;
        }
        // 当前玩家正在发起邀约又加入其他邀约
        if(participant.getStatus() == HorseMateStatus.INVITING){
            // 取消当前的邀约
            cancelHorseMate(role);
        }
        // 加入邀约双方消耗次数
        participant.setTimes(participant.getTimes()+1);
        originator.setTimes(originator.getTimes()+1);
        // 双方状态改变
        originator.setStatus(HorseMateStatus.FINISHED);
        originator.setClientStatus(PsHorseMateStatus.SHOW);
        participant.setStatus(HorseMateStatus.FINISHED);
        participant.setClientStatus(PsHorseMateStatus.SHOW);

        // 给发起方更新参与者的状态
        var oriItem = originator.getItem();
        oriItem.setPartnerId(role.getRoleId());
        oriItem.setPartnerName(role.getName());
        oriItem.setPartnerHorseInfo(new BestHorseInfo(partHorse.value1(),partHorse.value2()));
        oriItem.setSelfHorseInfo(new BestHorseInfo(oriHorse.value1(),oriHorse.value2()));
        // 更新自己的信息
        var partItem = participant.getItem();
        partItem.setPartnerId(inviterId);
        partItem.setPartnerName(inviter.getName());
        partItem.setPartnerHorseInfo(new BestHorseInfo(oriHorse.value1(),oriHorse.value2()));
        partItem.setSelfHorseInfo(new BestHorseInfo(partHorse.value1(),partHorse.value2()));
        partItem.setInviter(false);

        // 移出邀请中的列表
        var matingMap = horseMateDao.getMatingMap();
        matingMap.remove(inviterId);


        horseMateDao.tiedPartner(participant.getRoleId(), originator.getRoleId());
        horseMateDao.tiedPartner(originator.getRoleId(), participant.getRoleId());
        horseMateDao.save(participant);
        horseMateDao.save(originator);
        logger.info("玩家 {} 加入玩家 {}发起的坐骑交配邀约成功。己方坐骑 {} ，对方坐骑 {} ， 己方战力 {}， 对方战力 {}", role.getId(), inviterId, partHorse.value1().getMetaId(), oriHorse.value1().getMetaId(), partHorse.value2(), oriHorse.value2());
        sendHorseMateJoin(role, HorseMateErrorCode.SUCCESS);
        pushPersonalPageStatus(role);
        pushPersonalPageStatus(inviter);
        getHorseMateList(role);
        broadcastHorseMateToAlliance(role);
    }


    /**
     * 迁服断开双方的关联
     * @param roleId
     */
    public void handleWhenMigrate(Long roleId) {
        untiedAllRelations(roleId, EmailConstants.MAILID_GVG_SELF, EmailConstants.MAILID_GVG_PARTNER);
    }

    /**
     * 玩家被ban
     * @param roleId
     */
    public void handleByBan(Long roleId) {
        untiedAllRelations(roleId, EmailConstants.MAILID_BAN_SELF, EmailConstants.MAILID_BAN_PARTNER);
    }

    private void untiedAllRelations(Long roleId, String mailSelf, String mailPartner) {
        if (roleId == null) {
            return;
        }
        HorseMating horseMating = horseMateDao.findById(roleId);
        if(horseMating == null){
            return;
        }
        Role role = roleDao.findById(roleId);
        var roleHorse = getHighestPowerHorse(role);
        if (roleHorse == null) {
            return;
        }

        // 对方的
        List<Long> allRelationRole = horseMateDao.getAllRelationRole(roleId);

        if (JavaUtils.bool(allRelationRole)) {
            List<Long> allRelationRoleCopy = new ArrayList<Long>();
            // 后面会对原本的数据进行删除，所以使用备份。
            allRelationRoleCopy.addAll(allRelationRole);
            for (Long partnerId : allRelationRoleCopy) {
                HorseMating partner = horseMateDao.findById(partnerId);
                if (partner == null) {
                    continue;
                }
                // 展示状态
                if (partner.getClientStatus() != PsHorseMateStatus.SHOW) {
                    continue;
                }
                // 检验，对方正在匹配的是不是我
                if (!Objects.equals(partner.getItem().getPartnerId(), roleId)) {
                    continue;
                }
                // 搭档的处理
                Role partnerRole = roleDao.findById(partnerId);
                var rivalHorse = getHighestPowerHorse(partnerRole);
                long max = Math.max(rivalHorse == null ? 0L : rivalHorse.value2(), roleHorse.value2());
                logger.info("坐骑计算最终战力。 {} max {} horse {} partner horse {}", role.getRoleId(), max, roleHorse.value2(), rivalHorse == null ? 0L : rivalHorse.value2());

                List<String> mailParam = new ArrayList<>();
                mailParam.add(partnerRole.getName());
                sendReward(partnerRole, partner, max , mailPartner, mailParam);
                setIdleStatus(partner, roleId);
                logger.info("跨服断开连接, 对方 {}, {}", partnerId, roleId);
            }
        }


        // 自己的
        if (horseMating.getClientStatus() == PsHorseMateStatus.SHOW) {
            Long partnerId = horseMating.getItem().getPartnerId();
            Role partnerRole = roleDao.findById(partnerId);
            var rivalHorse = getHighestPowerHorse(partnerRole);
            long max = Math.max(rivalHorse == null ? 0L : rivalHorse.value2(), roleHorse.value2());
            logger.info("坐骑计算最终战力。 {} max {} horse {} partner horse {}", role.getRoleId(), max, roleHorse.value2(), rivalHorse == null ? 0L : rivalHorse.value2());
            sendReward(role, horseMating, max, mailSelf, new ArrayList<>());
            setIdleStatus(horseMating, partnerId);
            logger.info("跨服断开连接 自己 {}, {}", roleId, horseMating.getItem().getPartnerId());
        }
    }



    /**
     *
     * @param horseMating
     */
    private void setIdleStatus(HorseMating horseMating, Long partnerId) {
        if (horseMating == null) {
            return;
        }
        // 重置状态
        horseMating.setStatus(HorseMateStatus.IDLE);
        horseMating.setClientStatus(PsHorseMateStatus.IDLE);

        horseMateDao.untiedPartner(horseMating.getRoleId(), partnerId);
        horseMateDao.save(horseMating);

        logger.info("领奖断开连接 重置状态 {}, {}", horseMating.getRoleId(), partnerId);
        // 同步
        Role role = roleDao.findById(horseMating.getRoleId());
        if (role != null) {
            pushPersonalPageStatus(role);
        }
    }

    private void sendReward(Role role, HorseMating horseMating, long power, String mailId, List<String> params) {
        try {
            if (horseMating.getStatus() == HorseMateStatus.FINISHED) {
                if (role != null) {
                    // 任务
                    srvDep.getMissionService().onMissionFinish(role,MissionType.HORSE_INVITATION_REWARD_COUNT);
                    if (JavaUtils.bool(mailId)) {
                        var dropMetaId = getHorseMatingConfig().getDropReward(power, Application.getSeason());
                        List<SimpleItem> drop = dropServiceImpl.drop(dropMetaId);
                        logger.info("玩家战马发奖。 {} mail {} reward {} param {}", role.getRoleId(), mailId, dropMetaId, params);
                        srvDep.getMailService().sendSystemEmail(role, mailId, drop, params);
                    }
                }
            }
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("战马发奖异常 卸载用户.", e);
        }
    }


    // 获取玩家最高战力坐骑
    private Tuples.Tuple2<Horse,Long> getHighestPowerHorse(Role role){
        RoleHorse roleHorses = getOrCreate(role);
        var horseMap = roleHorses.getHorseMap();
        long power = 0;
        if(!JavaUtils.bool(horseMap)){
            return null;
        }
        Horse ret = null;
        for(Horse horse : horseMap.values()){
            if(ret == null){
                ret = horse;
                power = getHorsePower(horse);
            }else{
                var curPower = getHorsePower(horse);
                if(curPower == power){
                    if(horse.getMetaId() > ret.getMetaId()){
                        ret = horse;
                    }
                    continue;
                }
                if(curPower > power){
                    ret = horse;
                    power = curPower;
                }
            }
        }
        return Tuples.create(ret,power);
    }

    // 记录交配日志
    private void horseMateRecord(Role role, Horse horse, long power, long rivalId, Horse rivalHorse, long rivalPower, boolean isInviter){
        Role rival = roleDao.findById(rivalId);
        var horseMating = getOrCreateHorseMating(role);

        var now = TimeUtil.getNow();
        var selfRecord = new HorseMatingRecord();
        // 对方信息
        selfRecord.setRivalId(rivalId);
        selfRecord.setRivalHorse(new BestHorseInfo(rivalHorse, rivalPower));
        selfRecord.setRivalName(rival.getName());
        // 其他信息
        selfRecord.setRecordId(horseMating.getNewRecordId());
        selfRecord.setTime(now);
        // 记录日志这方是否为邀请方
        selfRecord.setInviter(isInviter);
        // 己方信息
        selfRecord.setName(role.getName());
        selfRecord.setHorse(new BestHorseInfo(horse, power));
        selfRecord.setRoleId(role.getRoleId());
        // 加入至日志列表
        horseMating.getRecords().add(selfRecord);
        horseMateDao.save(horseMating);
    }

    /**
     * 获取坐骑交配日志
     */
    public void getHorseMateRecord(Role role){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_MATE)){
            return;
        }
        if(!(srvDep.getUnlockService().isUnlock(role, UnlockModule.HORSE_BUILDING))){
            logger.info("玩家坐骑系统未解锁 roleId:{}",role.getId());
            sendHorseMateRecord(role, HorseMateErrorCode.MODULE_LOCK,null);
            return;
        }
        var horseMating = getOrCreateHorseMating(role);
        sendHorseMateRecord(role, HorseMateErrorCode.SUCCESS, horseMating.getRecords());
    }

    /**
     * 获取坐骑交配奖励
     */
    public void getHorseMateReward(Role role){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_MATE)){
            return;
        }
        // 检查坐骑系统是否解锁
        if(!(srvDep.getUnlockService().isUnlock(role, UnlockModule.HORSE_BUILDING))){
            logger.info("玩家坐骑系统未解锁 roleId:{}",role.getId());
            sendHorseMateReward(role, HorseMateErrorCode.MODULE_LOCK, null, null);
            return;
        }
        // 检查是否完成邀约
        var horseMating = getOrCreateHorseMating(role);
        if(horseMating.getStatus()!=HorseMateStatus.FINISHED){
            ErrorLogUtil.errorLog("玩家坐骑邀约未完成", "roleId",role.getId());
            sendHorseMateReward(role, HorseMateErrorCode.MATE_NOT_FINISH, null, null);
            return;
        }
        var roleItem = horseMating.getItem();
        var partnerId = roleItem.getPartnerId();
        if(partnerId == null){
            ErrorLogUtil.errorLog("玩家坐骑邀约未完成", "roleId",role.getId());
            sendHorseMateReward(role, HorseMateErrorCode.MATE_NOT_FINISH, null, null);
            return;
        }
        // 检查双方的坐骑情况
        var roleHorses = getOrCreate(role);
        if(!JavaUtils.bool(roleHorses.getHorseMap())){
            ErrorLogUtil.errorLog("玩家没有坐骑", "roleId",role.getId());
            sendHorseMateReward(role, HorseMateErrorCode.NO_HORSE, null, null);
            return;
        }
        var partner = roleDao.findById(partnerId);
        // 对方跨服了
        if (partner == null) {
            return;
        }

        var rivalHorses = getOrCreate(partner);
        if(!JavaUtils.bool(rivalHorses.getHorseMap())){
            ErrorLogUtil.errorLog("玩家没有坐骑", "roleId",partnerId);
            sendHorseMateReward(role, HorseMateErrorCode.NO_HORSE, null, null);
            return;
        }
        // 获取双方信息进行发奖
        var roleHorse = getHighestPowerHorse(role);
        var rivalHorse = getHighestPowerHorse(partner);
        if(roleHorse == null || rivalHorse == null){
            return;
        }

        // 更新自己的状态
        roleItem.setSelfHorseInfo(new BestHorseInfo(roleHorse.value1(), roleHorse.value2()));
        horseMating.setStatus(HorseMateStatus.IDLE);
        if(horseMating.getTimes() == getSettingConfig().getHorseMatingCount()){
            roleItem.setPartnerHorseInfo(new BestHorseInfo(rivalHorse.value1(), rivalHorse.value2()));
            horseMating.setClientStatus(PsHorseMateStatus.SHOW);
        }else{
            horseMating.setClientStatus(PsHorseMateStatus.IDLE);
        }

        boolean isInviter = horseMating.getItem().isInviter();
        // 发送奖励并记录
        giveHorseMateReward(role, partnerId, roleHorse.value1(), rivalHorse.value1(), roleHorse.value2(), rivalHorse.value2(), isInviter);
        // 完成对应任务
        srvDep.getMissionService().onMissionFinish(role,MissionType.HORSE_INVITATION_REWARD_COUNT);

        // 空闲状态则断开连接
        if (horseMating.getClientStatus() == PsHorseMateStatus.IDLE) {
            horseMateDao.untiedPartner(role.getRoleId(), partnerId);
            logger.info("领奖断开连接 领奖 {}, {}", horseMating.getRoleId(), partnerId);
        }

        horseMateDao.save(horseMating);
        // 完成邀约后获取当前状态推送给客户端
        pushPersonalPageStatus(role);
    }

    // 发奖并记录
    private void giveHorseMateReward(Role role, long pairId, Horse roleHorse, Horse pairHorse, long roleHorsePower, long pairHorsePower, boolean isInviter){
        var config = getHorseMatingConfig();
        if(config == null){
            ErrorLogUtil.errorLog("坐骑交配配置错误");
            return;
        }
        var power = Math.max(roleHorsePower, pairHorsePower);
        var dropMetaId = config.getDropReward(power, Application.getSeason());
        Tuples.Tuple2<List<SimpleItem>, Integer> rewardItems = srvDep.getDropService().dropAndWeightByM1N(dropMetaId);
        if(rewardItems == null){
            ErrorLogUtil.errorLog("坐骑邀约匹配成功发奖失败，道具掉落错误", "dropMetaId",dropMetaId);
            return;
        }
        // 根据道具权重，划分成功等级，用于动效显示
        PsHorseMateSuccessType successType = getTypeByWeight(rewardItems.value2());
        if(successType == null){
            ErrorLogUtil.errorLog("坐骑邀约匹配成功发奖失败，配置错误");
            return;
        }
        srvDep.getItemService().give(role, rewardItems.value1(), LogReasons.ItemLogReason.HORSE_MATE_REWARD);
        logger.info("坐骑邀约匹配成功发奖: roleId:{}, pairId:{}, dropMetaId:{}, rewardItems:{}", role.getRoleId(), pairId, dropMetaId, rewardItems);



        // 打点
        if(isInviter){
            biLogUtil.horseMatingSuccess(role, HorseMatingRole.INVITER.getId(), role.getRoleId(), roleHorse.getMetaId(), roleHorsePower, pairId, pairHorse.getMetaId(), pairHorsePower, rewardItems.value1());
        }else{
            biLogUtil.horseMatingSuccess(role, HorseMatingRole.INVITEE.getId(), pairId, pairHorse.getMetaId(), pairHorsePower, role.getRoleId(), roleHorse.getMetaId(), roleHorsePower, rewardItems.value1());
        }


        // 添加记录
        horseMateRecord(role, roleHorse, roleHorsePower, pairId, pairHorse, pairHorsePower, isInviter);

        sendHorseMateReward(role, HorseMateErrorCode.SUCCESS, rewardItems.value1(), successType);
    }

    // 根据权重获取成功类型
    private PsHorseMateSuccessType getTypeByWeight(int weight){
        var config = getSettingConfig().getHorseMatingBigWinInts();
        if(config.length != 2){
            return null;
        }
        var ret = PsHorseMateSuccessType.SUCCESS;
        if(weight <= config[0]){
            ret = PsHorseMateSuccessType.OVER_SUPER_SUCCESS;
        }else if(weight <= config[1]){
            ret = PsHorseMateSuccessType.SUPER_SUCCESS;
        }
        return ret;
    }

    /**
     * 玩家取消邀约
     */
    public void horseMateCancel(Role role){
        if(role == null){
            return;
        }
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_MATE)){
            return;
        }
        if(!(srvDep.getUnlockService().isUnlock(role, UnlockModule.HORSE_BUILDING))){
            logger.info("玩家坐骑系统未解锁 roleId:{}",role.getId());
            sendHorseMateCancel(role, HorseMateErrorCode.MODULE_LOCK);
            return;
        }
        var horseMating = getOrCreateHorseMating(role);
        if(horseMating.getStatus() != HorseMateStatus.INVITING){
            ErrorLogUtil.errorLog("玩家并非坐骑邀约中，取消邀约失败", "roleId",role.getId());
            sendHorseMateCancel(role, HorseMateErrorCode.CANCEL_MATE_ERROR);
            return;
        }
        // 取消邀约状态改变
        cancelHorseMate(role);
        logger.info("玩家 {} 取消邀约成功", role.getRoleId());
        sendHorseMateCancel(role, HorseMateErrorCode.SUCCESS);
        // 推送取消后的状态
        calAndPushPersonalPageStatus(role);
    }

    // 取消邀约改变状态
    private void cancelHorseMate(Role role){
        if(role == null){
            return;
        }
        var horseMating = getOrCreateHorseMating(role);
        horseMateDao.getMatingMap().remove(role.getId());
        horseMating.setStatus(HorseMateStatus.CANCELED);
        horseMating.setClientStatus(PsHorseMateStatus.IDLE);
        horseMateDao.save(horseMating);
        broadcastHorseMateToAlliance(role);
    }

    /**
     * 推送个人邀约状态
     * @param role
     */
    public void calAndPushPersonalPageStatus(Role role){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_MATE)){
            return;
        }
        if(!(srvDep.getUnlockService().isUnlock(role, UnlockModule.HORSE_BUILDING))){
            logger.info("玩家坐骑系统未解锁 roleId:{}",role.getId());
            sendHorseMateSelf(role, HorseMateErrorCode.MODULE_LOCK, null);
            return;
        }
        // 获取自己当前最高战力坐骑信息并装载
        var horse = getHighestPowerHorse(role);
        if(horse == null){
            ErrorLogUtil.errorLog("玩家没有坐骑", "roleId",role.getId());
            sendHorseMateSelf(role, HorseMateErrorCode.NO_HORSE, null);
            return;
        }
        var horseMating = getOrCreateHorseMating(role);
        var item = horseMating.getItem();

        item.setSelfHorseInfo(new BestHorseInfo(horse.value1(), horse.value2()));

        logger.info("坐骑邀约状态推送: roleId:{}, horseId:{}, power:{}", role.getRoleId(), horse.value1().getMetaId(), horse.value2());
        sendHorseMateSelf(role, HorseMateErrorCode.SUCCESS, horseMating);
    }

    // 只进行推送信息，不重新计算
    public void pushPersonalPageStatus(Role role){
        if(!(srvDep.getUnlockService().isUnlock(role, UnlockModule.HORSE_BUILDING))){
            logger.info("玩家坐骑系统未解锁 roleId:{}",role.getId());
            sendHorseMateSelf(role, HorseMateErrorCode.MODULE_LOCK, null);
            return;
        }
        var horseMating = getOrCreateHorseMating(role);
        sendHorseMateSelf(role, HorseMateErrorCode.SUCCESS, horseMating);
    }

    /**
     *  定期检查过期情况
     */
    // 下次刷新时间
    private long nextRefreshTime = 0;
    public void lowTick(){
        // 时间是否到了
        long now = TimeUtil.getNow();
        if (now < nextRefreshTime) {
            return;
        }
        long refreshDuration = TimeUtil.SECONDS_MILLIS;
        // 设置下次刷新时间
        if (nextRefreshTime <= 0) {
            // 初始化刷新时间
            nextRefreshTime = now + RandomUtils.random(0, (int) refreshDuration);
        } else {
            nextRefreshTime = now + refreshDuration;
        }
        var matingMap = horseMateDao.getMatingMap();

        for(HorseMating horseMating:matingMap.values()){
            try {
                var id = horseMating.getRoleId();
                if (now > horseMating.getItem().getEndTime()) {
                    var role = roleDao.findById(id);
                    // 重置状态
                    horseMating.setStatus(HorseMateStatus.IDLE);
                    horseMating.setClientStatus(PsHorseMateStatus.IDLE);
                    horseMateDao.save(horseMating);
                    sendHorseMateSelf(role, HorseMateErrorCode.SUCCESS, horseMating);
                    // 移出邀请中的列表
                    matingMap.remove(id);
                    // 邮件通知
                    srvDep.getMailService().sendSystemEmail(role, EmailConstants.HORSE_MATE_OVERTIME);
                    logger.info("玩家 {} 坐骑邀约超时，已重置", role.getRoleId());
                }
            }catch(Exception e){
                ErrorLogUtil.exceptionLog("过期检查异常", e);
            }
        }
    }

    // 每日刷新
    public void onResetDayData(Role role){
        var horseMating = getOrCreateHorseMating(role);

        // 次数重置
        if(!horseMating.dailyReset()){
            return;
        }
        // 展示情况重置，因为有情况是最后一次是主动邀约并且领取过奖励，停留在展示对方坐骑的界面，第二天需要刷掉
        if(horseMating.getClientStatus() == PsHorseMateStatus.SHOW && horseMating.getStatus() != HorseMateStatus.FINISHED){
            horseMating.setClientStatus(PsHorseMateStatus.IDLE);
        }

        horseMateDao.save(horseMating);
        logger.info("坐骑重置数据 跨天 {} ", role.getRoleId());
        pushPersonalPageStatus(role);
    }

    /**
     *  坐骑邀约分享
     */
    public void horseMateShare(Role role){
        if(!srvDep.getFunctionSwitchService().isOpen(FunctionType.HORSE_MATE)){
            return;
        }
        if(!(srvDep.getUnlockService().isUnlock(role, UnlockModule.HORSE_BUILDING))){
            logger.info("玩家坐骑系统未解锁 roleId:{}",role.getId());
            sendHorseMateShare(role, HorseMateErrorCode.MODULE_LOCK);
            return;
        }
        // 检查联盟
        if(allianceManager.getAllianceById(role.getAllianceId()) == null){
            ErrorLogUtil.errorLog("玩家没有联盟，分享邀约失败", "roleId",role.getId());
            sendHorseMateShare(role, HorseMateErrorCode.ALLIANCE_ERROR);
            return;
        }
        // 检查是否邀约中
        var horseMating = getOrCreateHorseMating(role);
        if(horseMating.getStatus() != HorseMateStatus.INVITING){
            ErrorLogUtil.errorLog("玩家并非坐骑邀约中，分享邀约失败", "roleId",role.getRoleId());
            sendHorseMateShare(role, HorseMateErrorCode.SHARE_FAIL);
            return;
        }
        // 获取品质
        var horse = getHighestPowerHorse(role);
        if(horse == null){
            return;
        }
        var level = horse.value1().getLevel();
        var star = horse.value1().getStar();
        var quality = getHorseStarMeta(horse.value1().getMetaId(), star).getQuality();
        var multilingualMeta = getHorseQualityMeta(quality);
        if(multilingualMeta == null){
            ErrorLogUtil.errorLog("品质配置获取失败", "roleId",role.getRoleId(), "quality", quality);
            sendHorseMateShare(role, HorseMateErrorCode.CONFIG_ERROR);
            return;
        }
        var multilingual = multilingualMeta.getName();
        horseMating.getItem().setSelfHorseInfo(new BestHorseInfo(horse.value1(), horse.value2()));


        // 发送邀约分享至联盟频道
        allianceChatExport.inviteForHorseMate(role.getRoleId(), role.getAllianceId(), horse.value1().getMetaId(), level,star, horse.value1().getHorseName(),horse.value2(), multilingual);
        logger.info("坐骑邀约分享: roleId:{}, allianceId:{}, horseId:{}, level:{}, star:{}, power:{}, multilingual：{}", role.getRoleId(), role.getAllianceId(), horse.value1().getMetaId(), level, star, horse.value2(), multilingual);
        sendHorseMateShare(role, HorseMateErrorCode.SUCCESS);
        pushPersonalPageStatus(role);
    }

    private void broadcastHorseMateToAlliance(Role role){
        var allianceId = role.getAllianceId();
        if(allianceId == null){
            return;
        }
        var allianceMembers = allianceMemberDao.findByAllianceId(allianceId);
        for(var allianceMember:allianceMembers){
            var roleId = allianceMember.getPersistKey();
            if(Objects.equals(roleId, role.getRoleId())){
                continue;
            }
            var memberRole = roleDao.findById(roleId);
            if(memberRole == null){
                continue;
            }
            getHorseMateList(memberRole);
        }
    }

    //endregion


    //region gm命令部分

    public void gmAddHorse(Role role,int horseId){
        var roleHorse = getOrCreate(role);
        if(roleHorse.getHorseMap().containsKey(horseId)){
            return;
        }

        onHorseChanged(role,createHorse(roleHorse,horseId));

        onHorseGet(role,horseId);
    }

    public void gmAddAllHorse(Role role){
        for(var horseMeta : configService.getConfig(HorseConfig.class).getMetaAllGroup().values()){
            var horseId = Integer.parseInt(horseMeta.getId());
            gmAddHorse(role,horseId);
        }
    }

    public void gmSetHorseLevel(Role role,int horseId,int level){
        var horse = getHorse(role,horseId);
        if(horse == null){
            return;
        }
        horse.setLevel(level);
        onHorseChanged(role,horse);
        onHorseLevelUp(role,horseId);
    }

    public void gmSetAllHorseLevel(Role role,int level){
        for(var horseMeta : configService.getConfig(HorseConfig.class).getMetaAllGroup().values()){
            var horseId = Integer.parseInt(horseMeta.getId());
            gmSetHorseLevel(role,horseId,level);
        }
    }

    public void gmSetHorseStar(Role role,int horseId,int star){
        var horse = getHorse(role,horseId);
        if(horse == null){
            return;
        }
        horse.setStar(star);
        onHorseChanged(role,horse);
        srvDep.getMissionService().onMissionFinish(role, MissionType.HORSE_STAR_BY_ID, role, horseId);
    }

    public void gmSetAllHorseStar(Role role,int star){
        for(var horseMeta : configService.getConfig(HorseConfig.class).getMetaAllGroup().values()){
            var horseId = Integer.parseInt(horseMeta.getId());
            gmSetHorseStar(role,horseId,star);
        }
    }

    public void gmSetHorseEquipLevel(Role role,int horseId,int level){
        var horse = getHorse(role,horseId);
        if(horse == null){
            return;
        }
        for (var part : configService.getConfig(HorseEquipConfig.class).getHorseParts(horseId)) {
            var equip = horse.getEquipMap().get(part);
            if(equip == null){
                equip = new HorseEquip(part, 1);
                horse.getEquipMap().put(part,equip);
            }
            equip.setLevel(level);
        }
        onHorseChanged(role,horse);
        onHorseEquipLevelUp(role,horseId);
    }

    public void gmSetAllHorseEquipLevel(Role role,int level){
        for(var horseMeta : configService.getConfig(HorseConfig.class).getMetaAllGroup().values()){
            var horseId = Integer.parseInt(horseMeta.getId());
            gmSetHorseEquipLevel(role,horseId,level);
        }
    }

    public void gmAllHorseMax(Role role){
        for(var horseMeta : configService.getConfig(HorseConfig.class).getMetaAllGroup().values()){
            var horseId = Integer.parseInt(horseMeta.getId());
            gmAddHorse(role,horseId);
            gmSetHorseLevel(role,horseId,configService.getConfig(HorseLevelConfig.class).getMaxLevel(horseId));
            gmSetHorseStar(role,horseId,configService.getConfig(HorseStarConfig.class).getMaxStar(horseId));
            gmSetHorseEquipLevel(role,horseId,configService.getConfig(HorseEquipConfig.class).getMaxEquipLevel(horseId));
        }
    }

    public void gmResetHorseMate(Role role){
        var horseMating = getOrCreateHorseMating(role);

        horseMating.setTimes(0);
        horseMating.setStatus(HorseMateStatus.IDLE);
        horseMating.setClientStatus(PsHorseMateStatus.IDLE);
        horseMating.setItem(new HorseMatingItem());
        horseMateDao.getMatingMap().remove(role.getRoleId());
        horseMateDao.save(horseMating);
        calAndPushPersonalPageStatus(role);
    }

    public void gmResetHorseName(Role role, int horseId){
        RoleHorse roleHorse = getOrCreate(role);
        var horseMap = roleHorse.getHorseMap();
        if(JavaUtils.bool(horseMap)){
            var horse = horseMap.get(horseId);
            if(horse != null){
                horse.setHorseName(null);
            }
        }
        horseDao.save(roleHorse);
        sendHorseList(role);
    }

    public boolean gmResetHorseName(long roleId, int horseId){
        Role role = roleDao.findById(roleId);
        if(role != null){
            RoleHorse roleHorse = getOrCreate(role);
            var horseMap = roleHorse.getHorseMap();
            if(JavaUtils.bool(horseMap)){
                var horse = horseMap.get(horseId);
                if(horse == null){
                    return false;
                }
                horse.setHorseName(null);
                horse.setLastRenameTime(TimeUtil.getNow());
                horseDao.save(roleHorse);
                sendHorseList(role);
                return true;
            }
            return false;
        }
        return false;
    }

    //endregion
}
