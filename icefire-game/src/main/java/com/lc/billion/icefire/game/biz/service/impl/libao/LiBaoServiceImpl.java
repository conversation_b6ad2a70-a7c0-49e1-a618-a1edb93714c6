package com.lc.billion.icefire.game.biz.service.impl.libao;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.pay.TuConstants;
import com.lc.billion.icefire.game.biz.common.IGameEventHandler;
import com.lc.billion.icefire.game.biz.config.AllianceGiftDropConfig;
import com.lc.billion.icefire.game.biz.config.LiBaoConfig.LibaoMeta;
import com.lc.billion.icefire.game.biz.config.LibaoPriceConfig;
import com.lc.billion.icefire.game.biz.config.LibaoPriceConfig.LibaoPriceMeta;
import com.lc.billion.icefire.game.biz.config.SettingConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleLibaoFilterInfoDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleRechargeLogDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.LiBaoGameConfigManager;
import com.lc.billion.icefire.game.biz.manager.RoleItemManager;
import com.lc.billion.icefire.game.biz.manager.RoleLibaoManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.alliance.alliancegift.AllianceGiftType;
import com.lc.billion.icefire.game.biz.model.event.GameEventType;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.libao.*;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alliance.alliancegift.AllianceGiftServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.libao.action.LiBaoAction;
import com.lc.billion.icefire.game.biz.service.impl.libao.function.LiBaoConditionCheck;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.recharge.RechargeContext;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.game.support.LogReasons.ItemLogReason;
import com.lc.billion.icefire.protocol.constant.PsCurrency;
import com.simfun.sgf.common.tuple.TwoTuple;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2016年7月11日上午11:45:20
 * <p>
 * modify
 * 添加 time offer 类型礼包，同时重构已有方法
 * @since 2020-11-12
 *
 * 重写 by <EMAIL> 2024-04-15 18:29:35
 */
@Service
public class LiBaoServiceImpl implements IGameEventHandler {

    private static final Logger logger = LoggerFactory.getLogger(LiBaoServiceImpl.class);

    @Autowired
    private FunctionSwitchService functionSwitchService;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private ApplicationContext appCtx;
    @Autowired
    private ItemServiceImpl itemService;
    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private AllianceGiftServiceImpl allianceGiftService;
    @Autowired
    private RoleLibaoManager roleLibaoManager;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private LiBaoGameConfigManager liBaoConfigManager;
    @Autowired
    private ServerInfoDao serverInfoDao;
    @Autowired
    private RoleLibaoFilterInfoDao filterInfoDao;
    @Autowired
    private RoleRechargeLogDao rechargeLogDao;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private RoleItemManager roleItemManager;

    private Map<LiBaoFunctionType, LiBaoConditionCheck> conditionFucMap;

    private Map<LiBaoActionType, LiBaoAction> actionFuncMap;

    private boolean switchOpen;

    @PostConstruct
    public void init() {
        try {
            Map<LiBaoFunctionType, LiBaoConditionCheck> conditionFucMap = new EnumMap<>(LiBaoFunctionType.class);
            Map<String, LiBaoConditionCheck> beansOfConditionFuncOper = appCtx.getBeansOfType(LiBaoConditionCheck.class, false, true);
            for (LiBaoConditionCheck l : beansOfConditionFuncOper.values()) {
                LiBaoConditionCheck exists = conditionFucMap.put(l.getType(), l);
                if (exists != null) {
                    throw new AlertException("LiBao Condition Check Function duplicate","type",exists.getType());
                }
            }

            Map<LiBaoActionType, LiBaoAction> actionFuncMap = new EnumMap<>(LiBaoActionType.class);
            Map<String, LiBaoAction> beansOfActionOper = appCtx.getBeansOfType(LiBaoAction.class, false, true);
            for (LiBaoAction l : beansOfActionOper.values()) {
                LiBaoAction exists = actionFuncMap.put(l.getType(), l);
                if (exists != null) {
                    throw new AlertException("LiBao Action Function duplicate","type",exists.getType());
                }
            }
            this.conditionFucMap = Collections.unmodifiableMap(conditionFucMap);
            this.actionFuncMap = Collections.unmodifiableMap(actionFuncMap);
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("LiBao init CaughtException", e);
        }
    }

    public void onEnterWorld(Role role) {
        liBaoUpdateOnCheck(role, "onLogin", LiBaoFunctionType.REGISTER_NO_PAY);
        liBaoUpdateOnEnterWorld(role);
        srvDpd.getRechargeService().pushFirstRechargeDouble(role);
    }

    // 之前没有被调用过,这里调用一下,用来初始化open
    public void startService() {
        switchOpen = isOpen();
//        Collection<RoleLibaoFilterInfo> allFilterInfoLit = filterInfoDao.findAll();
//        if (CollectionUtils.isEmpty(allFilterInfoLit)) {
//            return;
//        }
//        ServerInfo serverInfo = serverInfoDao.findById((long) Application.getServerId());
//        if (serverInfo.getLastCloseTimeMs() <= 0)
//            return;
//        long intervalTime = TimeUtil.getNow() - serverInfo.getLastCloseTimeMs();
//        if (intervalTime <= 0) {
//            return;
//        }
//        for (RoleLibaoFilterInfo info : allFilterInfoLit) {
//            info.addStopServerIntervalTime(intervalTime);
//            roleLibaoManager.saveRoleLibaoFilterInfo(info);
//        }
//        serverInfo.setLastCloseTimeMs(0);
//        serverInfoDao.save(serverInfo);
    }

    /**
     * 检测已经下架或者倒计时结束的礼包，移除
     */
    public void lowTick(Role role, long now) {
//        List<String> removeList = new ArrayList<>();
        //处理 time offer 礼包
//        List<RoleLibaoRecord> addList = new ArrayList<>();
        onTick(role, now);
//        onRealLimitedTick(role, removeList);
        //发送更新消息
//        if (CollectionUtils.isNotEmpty(removeList) || CollectionUtils.isNotEmpty(addList)) {
//            role.send(LiBaoOutput.wrapperLiBaoListUpdate(role, removeList, addList, null, false, liBaoConfigManager, false));
//        }
    }

    private void onTick(Role role, long now) {
        // tick 时只检查时间
        // 暂不支持tick add。所以不用检查 add-conditions

//        Map<String, RoleLibaoRecord> records = roleLibaoManager.getRoleLiBaoRecordMapByTimeCheckType(role.getPersistKey(), LiBaoTimeCheckType.TICK);
        Map<String, RoleLibaoRecord> records = roleLibaoManager.getRoleLibaoRecordMap(role.getPersistKey());
        if (records == null || records.isEmpty()) {
            return;
        }

        LiBaoContext context = new LiBaoContext(LiBaoContextType.TICK);

        for (Entry<String, RoleLibaoRecord> entry : records.entrySet()) {
            String metaId = entry.getKey();
            RoleLibaoRecord record = entry.getValue();

            LibaoMeta libaoMeta = liBaoConfigManager.getById(metaId);
            if (libaoMeta == null) {
                context.addDelLiBao(record);
                continue;
            }
            // check del
            long delTime = record.getEndTime();
            if (delTime > 0 && delTime <= now) {
                context.addDelLiBao(record);
            }

            // check reset
            long resetTime = record.getResetTime();
            if (resetTime > 0 && resetTime < now) {
                context.addResetLiBao(record);
            }
        }

        if (! context.hasChanges()) {
            return;
        }

        logger.info("tick role: {}, context: {}", role.getId(), context);
        afterCheck(role, context);
    }

    public boolean canGive(Role role, String productId, JSONObject result,String extData) {
        return canBuyInner(role.getId(), productId, result,true,extData);

    }

//    /**
//     * 充值回调
//     *
//     * @param role
//     * @param productId
//     * @param context
//     */
//    public List<SimpleItem> onRecharge(Role role, String productId, RechargeContext context, int season, String extData) {
//        return give(role, productId, extData);
//    }

    public List<SimpleItem> give(Role role, String productId) {
        return give(role, productId, "",0,null);
    }

    public List<SimpleItem> give(Role role, String productId, String extData, int token, RechargeContext context) {
        List<SimpleItem> items = new ArrayList<>();
        LibaoMeta liBaoMeta = liBaoConfigManager.getById(productId);
        long now = TimeUtil.getNow();
//        boolean canGive = canGive(role, productId, result);
//        if (! canGive) {
//            return items;
//        }

//        if (liBaoMeta == null) {
//            logger.error("libao meta not found! roleId: {}productId: {}", role.getRoleId(), productId);
//
//            result.put("ret", "-10");
//            result.put("msg", "libao meta not found");
//            return items;
//        }

        // 用户付了款，但是礼包记录已经没有了，比如在超时的前后下单、付款，就只发礼包内容，跳过逻辑处理

        RoleLibaoRecord record = roleLibaoManager.getRoleLibaoRecord(role.getPersistKey(), productId);
//        if (record == null) {
//            return doGiveOnRecordNotExist(role, liBaoMeta, productId, now);
//        }
//
//        if (liBaoMeta.getCount() != 0 && record.getBuyCount() > liBaoMeta.getCount()) {
//            logger.error("libao buy count exceed! roleId: {}productId: {}", role.getRoleId(), productId);
//            result.put("ret", "-11");
//            result.put("msg", "buy count exceed");
//            return items;
//        }
//
//        if (! record.isEnable()) {
//            result.put("ret", "-12");
//            result.put("msg", "buy disable product");
//            throw new RuntimeException("buy disable product. role="+role.getId() +", productId="+productId);
//        }
//
//        if (record.isHide()) {
//            result.put("ret", "-13");
//            result.put("msg", "buy hide product");
//            throw new RuntimeException("buy hide product. role="+role.getId() +", productId="+productId);
//        }
        // 购买记录
        // 用于限购的
        record.addBuyTime(now);

        // 总的购买记录
        RoleLibaoFilterInfo filterInfo = roleLibaoManager.getRoleFilterInfo(role.getPersistKey());
        filterInfo.addBuyRecord(liBaoMeta.getId(), now);
        roleLibaoManager.saveRoleLibaoFilterInfo(filterInfo);

        // 购买记录带价格
        LibaoPriceMeta priceMeta = configService.getConfig(LibaoPriceConfig.class).getById(liBaoMeta.getPriceId());
        double dollar = priceMeta != null ? priceMeta.getDollar() : 0;
        rechargeLogDao.create(role, liBaoMeta.getId(), now, token > 0 ? 0 : dollar,token);

//        //做一次条件检测
//        CheckContext checkContext = new CheckContext(role);
//        checkContext.setRoleLiBaoRecord(record);
//        if (! checkAddCondition(role, checkContext, liBaoMeta, false)) {
//            logger.error("libao condition check failure! condition type: {}, roleId: {}, productId: {}",
//                    checkContext.getFunctionType(), role.getPersistKey(), productId);
//            return items;
//
//        }

//        //时间检测
//        long now = TimeUtil.getNow();
//        if (now < record.getEndTime()) {
//            logger.error("libao appear time error! roleId: {}, productId: {}, endTime: {}",
//                    role.getPersistKey(), productId, record.getEndTime());
//        }



        // buy-action
        LiBaoContext liBaoContext = new LiBaoContext(LiBaoContextType.BUY);

        liBaoContext.setExtData(extData);
        liBaoContext.setRechargeContext(context);
        
        doActions(role, liBaoContext, record, LiBaoActionGroupType.BUY);

        // 给用户礼包内容
        items = giveItems(role, liBaoMeta, liBaoContext);

        logger.info("buyAction done role: {}, libao context: {}", role.getId(), liBaoContext);

        // count-check
        long countLimit = liBaoMeta.getCount();
        boolean deleted = false;
        if (countLimit > 0 && record.getBuyCount() >= countLimit) {
            doActions(role, liBaoContext, record, LiBaoActionGroupType.SOLD_OUT);
            logger.info("soldOut action done 1 role: {}, libao context: {}", role.getId(), liBaoContext);
            if (liBaoContext.isNotDel()) {
                // 这时不执行 del actions
                liBaoContext.clearNotDel();
            } else {
                deleted = true;
                del(role, liBaoContext, record);
            }
            logger.info("soldOut action done 2 role: {}, libao context: {}", role.getId(), liBaoContext);
        }
        // 如果没删除那么就给前端推送一下更新。虽然可以更精确但是先就这样吧
        if (! deleted) {
            liBaoContext.addUpdateLiBao(record);
            logger.info("soldOut action done 3 role: {}, libao context: {}", role.getId(), liBaoContext);
        }

        // after-check
        afterCheck(role, liBaoContext);

        // 联盟宝箱
        doGiveAllianceGift(role, liBaoMeta, productId);

        // 更新礼包
        liBaoUpdateOnCheck(role, "充值后onRecharge", LiBaoFunctionType.TOTAL_PAY_TRIGGER);
        liBaoUpdateOnCheck(role, "recharge", LiBaoFunctionType.RECHARGE);

        return items;
    }

    private void doGiveAllianceGift(Role role, LibaoMeta liBaoMeta, String productId) {

        try {
            AllianceGiftDropConfig allGiftDropConfig = configService.getConfig(AllianceGiftDropConfig.class);
            for (String allianceGiftMetaId: liBaoMeta.getAllianceGifts()) {
                if (StringUtils.isNotBlank(allianceGiftMetaId)) {
                    AllianceGiftDropConfig.AllianceGiftDropMeta meta = allGiftDropConfig.getById(allianceGiftMetaId);
                    if (null != meta) {
                        allianceGiftService.gainGift(role, AllianceGiftType.UNCOMMON, meta.getId(), liBaoMeta.getName());
                    }
                }
            }
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("GiveAllianceGift CaughtException", e);
        }
    }

    private List<SimpleItem> doGiveOnRecordNotExist(Role role, LibaoMeta liBaoMeta, String productId, long now) {
        logger.info("doGiveOnRecordNotExist begin, no record, role: {}, product: {}", role.getId(), productId);
        List<SimpleItem> items = new ArrayList<>();
        LiBaoContext liBaoContext = new LiBaoContext(LiBaoContextType.BUY);
        items = giveItems(role, liBaoMeta, liBaoContext);

        // 总的购买记录
        RoleLibaoFilterInfo filterInfo = roleLibaoManager.getRoleFilterInfo(role.getPersistKey());
        filterInfo.addBuyRecord(liBaoMeta.getId(), now);

        // 购买记录带价格
        LibaoPriceMeta priceMeta = configService.getConfig(LibaoPriceConfig.class).getById(liBaoMeta.getPriceId());
        double dollar = priceMeta != null ? priceMeta.getDollar() : 0;
        rechargeLogDao.create(role, liBaoMeta.getId(), now, dollar,0);

        // 联盟宝箱奖励点
        doGiveAllianceGift(role, liBaoMeta, productId);

        logger.info("doGiveOnRecordNotExist done, no record, role: {}, product: {}", role.getId(), productId);

        return items;
    }

    private void del(Role role, LiBaoContext liBaoContext, RoleLibaoRecord record) {
        liBaoContext.addDelLiBao(record);
        roleLibaoManager.removeRoleLibaoRecord(record);
        doActions(role, liBaoContext, record, LiBaoActionGroupType.DEL);
    }


    /**
     * 发道具
     *
     * 如果buy libaoContext的 sendAsset里有东西，那就一起发。
     * 这样前端展示是在一起的。如果让前端合并处理，会很麻烦
     */
    public List<SimpleItem> giveItems(Role role, LibaoMeta liBaoMeta, LiBaoContext libaoContext) {
        List<SimpleItem> items = liBaoMeta.getItems();

        // 购买道具下发
        //2020-11-10 sean 按前端的需求修改消息下发顺序（提到给出物品消息前）
        String metaId = liBaoMeta.getId();
        if (liBaoMeta.hasTag(LiBaoTagType.FIRST_RECHARGE_DOUBLE_DIAMOND)) {
            // 购买元宝不发
            return Collections.emptyList();
        }

        List<SimpleItem> allItems = new LinkedList<>();
        if (null != items) {
            allItems.addAll(items);
        }
        for (TwoTuple<LogReasons.ItemLogReason, List<SimpleItem>> elem : libaoContext.getSendAssets()) {
            allItems.addAll(elem.getSecond());
        }
        if(liBaoMeta.getGold() >0){
            allItems.add(new SimpleItem(String.valueOf(PsCurrency.CHARGE_TOKEN.getValue()),liBaoMeta.getGold()));
        }

        role.send(LiBaoOutput.wrapperLiBaoBuyRecords(metaId, allItems));

        if (items != null && ! items.isEmpty()) {
            itemService.give(role, items, ItemLogReason.LIBAO_BUY);
        }

        for (TwoTuple<LogReasons.ItemLogReason, List<SimpleItem>> elem : libaoContext.getSendAssets()) {
            itemService.give(role, elem.getSecond(), elem.getFirst());
        }

        return allItems;
    }


    /**
     * 触发某种类型事件时触发礼包
     *
     * @param role
     */
    public void liBaoUpdateOnCheck(Role role, String reason, LiBaoFunctionType checkType, Object... input) {
        logger.info("liBaoUpdateOnCheck begin, role: {}, reason: {}, checkType: {}, input: {}",
                role.getPersistKey(), reason, checkType, input);
        LiBaoContext context = new LiBaoContext(LiBaoContextType.EVENT);
        checkDelOnEvent(role, checkType, context, input);
        logger.info("check del on event role: {}, libao context: {}", role.getId(), context);
        checkResetOnEvent(role, checkType, context, input);
        logger.info("check reset on event role: {}, libao context: {}", role.getId(), context);
        checkAddOnEvent(role, checkType, context, input);  // 检查 add-event 列和 add 列，而且add-event列检查时调用的是onCheck
        logger.info("check add on event role: {}, libao context: {}", role.getId(), context);

        // 事件发生的时候，再检查一下add条件中包含事件类型的所有礼包的添加条件，看能否触发添加
        checkOnEvent(role, context, checkType);
        logger.info("check add not-event-libao on event role: {}, libao context: {}", role.getId(), context);

        afterCheck(role, context);
    }

    private void checkOnEvent(Role role, LiBaoContext context, LiBaoFunctionType checkType) {
        Map<String, LibaoMeta> metas = liBaoConfigManager.getLiBaoMetasByFunctionType(LiBaoConditionType.ADD, checkType);
        if (metas == null || metas.isEmpty()) {
            return;
        }

        checkAdd(role, context, metas, false);
    }

    /**
     * 零点下发新的礼包
     *
     * @param role
     */
    public void onResetDayData(Role role, boolean isEnterWorld) {
        if (role.isOnline() && !isEnterWorld) {
            //跨天清理已购完礼包记录
//            roleLibaoManager.clearStoreSoldOutRecords(role.getRoleId());
            // 刷新礼包
            liBaoUpdateOnResetDay(role);
        }
    }


    /**
     * 检查完成后的处理。合并检查结果，入库，作发消息，执行动作
     *
     * @param role
     * @param context
     */
    private void afterCheck(Role role, LiBaoContext context) {

        // addLiBaoRecords 里的所有记录都是new出来的，没有在库里，切记

        logger.info("mergeChanges before role: {}, libao context: {}", role.getId(), context);
        mergeChanges(role, context);

        logger.info("mergeChanges done role: {}, libao context: {}", role.getId(), context);
        // 数据没有变化。那么看是否为登录上下文，如果是得给客户端同步libaoList，否则就什么也不用做
        if (! context.hasChanges()) {
            // 这里面会检查是否为登录，决定发哪个消息
            syncContextChange(role, context);
            return;
        }

        // 没有重复了，存库
        // save LiBao records
        saveChanges(role, context);
        // send msg to client
        syncContextChange(role, context);

        // 新的context，给 action 使用
        LiBaoContext actionContext = new LiBaoContext(LiBaoContextType.ACTION);

        // do actions
        for (RoleLibaoRecord record : context.getDel().values()) {
            doActions(role, actionContext, record, LiBaoActionGroupType.DEL);
        }

        logger.info("doActions del done role: {}, libao context: {}", role.getId(), actionContext);

        for (RoleLibaoRecord record : context.getReset().values()) {
            doActions(role, actionContext, record, LiBaoActionGroupType.RESET);
        }

        logger.info("doActions reset done role: {}, libao context: {}", role.getId(), actionContext);

        for (RoleLibaoRecord record : context.getAdd().values()) {
            doActions(role, actionContext, record, LiBaoActionGroupType.ADD);
        }

        logger.info("doActions add done role: {}, libao context: {}", role.getId(), actionContext);

        mergeChanges(role, actionContext);
        if (actionContext.hasChanges()) {
            saveChanges(role, actionContext);
            // action 会产生新的变化，因此 sync again
            syncContextChange(role, actionContext);
        }
    }

    private void saveChanges(Role role, LiBaoContext context) {
        Map<String, RoleLibaoRecord> newRecords = new HashMap<>();

        // 新增
        for (RoleLibaoRecord record : context.getAdd().values()) {
            // 已经在库里的不重复创建
            RoleLibaoRecord dbRecord = roleLibaoManager.getRoleLibaoRecord(role.getRoleId(), record.getMetaId());
            if (dbRecord == null) {
                dbRecord = roleLibaoManager.createRoleLibaoRecord(role, record.getMetaId(),
                        record.getStartTime(), record.getResetTime(), record.getEndTime());
            } else {
                // 如果库里已有了，报个错再查
                // todo: 这可能是正常情况 by <EMAIL> 2024-04-15 17:56:37
                ErrorLogUtil.errorLog("add libao record already in db", "roleId",role.getId(), "recordMeta",record.getMetaId());
            }
            newRecords.put(record.getMetaId(), dbRecord);
        }

        // 删除
        for (RoleLibaoRecord record : context.getDel().values()) {
            // 隐藏的也删除。隐藏一般与notDel action一起使用，用于保留数据，给一条龙，或者同一期互相关联的礼包做
            // 数据支持。在这种情况下，购买时的限购次数达到后不会删除礼包，不会添加到删除列表中去。
//            // 隐藏的不删除
//            if (! record.isHide()) {
                roleLibaoManager.removeRoleLibaoRecord(record);
//            }
        }

        // 重置
        for (var ite = context.getReset().values().iterator(); ite.hasNext(); ) {
        	RoleLibaoRecord record = ite.next();
        	boolean isHideBefore = record.isHide();
            // 重置就是设置新的购买记录、下次重置时间，并且取消隐藏、打开购买
            record.setBuyTimes(new ArrayList<>());
            record.setHide(false);
            record.setEnable(true);
            long now = TimeUtil.getNow();
            // 对于按条件重置的记录，没有resetTime，否则record中一定有resetTime
            long resetTime = record.getResetTime();
            // 无效的重置时间，重新计算
            if (resetTime != 0 && resetTime < now) {
                CheckContext checkContext = new CheckContext(role, LiBaoConditionType.RESET, record);
                checkResetCondition(role, checkContext, record, false);
                resetTime = checkContext.getResetTime();
                record.setResetTime(resetTime);
            }

            roleLibaoManager.saveRoleLibaoRecord(record);
            // 隐藏礼包 客户端已删除 需要走add刷新 客户端才能正常显示
            if(isHideBefore) {
            	newRecords.put(record.getMetaId(), record);
            	ite.remove();
            }
        }

        // 更新
        for (RoleLibaoRecord record : context.getUpdate().values()) {
            roleLibaoManager.saveRoleLibaoRecord(record);
        }

        // save filter-info
        var now = TimeUtil.getNow();
        RoleLibaoFilterInfo roleFilterInfo = roleLibaoManager.getRoleFilterInfo(role.getPersistKey());
        var addRecord = roleFilterInfo.getAddRecord();
        for (RoleLibaoRecord record : context.getAdd().values()) {
            addRecord.put(record.getMetaId(), now);
        }
        roleLibaoManager.saveRoleLibaoFilterInfo(roleFilterInfo);

        // dbRecords 是用来给 syncContextChange 发送给客户端用的
        context.setAdd(newRecords);
    }

    private void mergeChanges(Role role, LiBaoContext context) {
        if (! context.hasChanges()) {
            return;
        }
        Map<String, RoleLibaoRecord> addLiBaoRecords = context.getAdd();
        Map<String, RoleLibaoRecord> delLiBaoRecords = context.getDel();
        Map<String, RoleLibaoRecord> resetLiBaoRecords = context.getReset();
        Map<String, RoleLibaoRecord> updateLiBaoRecords = context.getUpdate();

        // 合并检查 add/del/reset/update排除重复
        List<String> willDelFromAddRecords = new ArrayList<>();
        for (RoleLibaoRecord record : addLiBaoRecords.values()) {
            String metaId = record.getMetaId();
            // 先删除后加的情况，因为要执行del和add actions，所以需要先删除，再加，所以不处理
            // 不过报个错看统计
            if (delLiBaoRecords.containsKey(metaId)) {
                ErrorLogUtil.errorLog("add & del -> do nothing","roleId", role.getRoleId(), "metaId",metaId,"record", record);
//                willDelFromAddRecords.add(metaId);
            }

            // 先重置又添加。添加的不能是已经存在的记录，因此这两个不应该同时出现，报错，算重置
            // 重置和添加冲突。那么可能是添加规则出问题了，报错，因为已有记录了，所以算重置。
            if (resetLiBaoRecords.containsKey(metaId)) {
                ErrorLogUtil.errorLog("add & reset -> reset", "roleId",role.getRoleId(),"metaId",metaId,"record", record);
                willDelFromAddRecords.add(metaId);
            }

            // 先更新又添加。添加的不能是已经存在的记录，因此这两个不应该同时出现，报错，算更新
            if (updateLiBaoRecords.containsKey(metaId)) {
                ErrorLogUtil.errorLog("add & update -> update", "roleId",role.getRoleId(),"metaId",metaId,"record", record);
                willDelFromAddRecords.add(metaId);
            }
        }

        logger.info("merge 1 done role: {}, libao context: {}", role.getId(), context);

        // 还没在库里，直接删除
        logger.info("merge 2 willDelFromAddRecords: {}", willDelFromAddRecords);
        willDelFromAddRecords.forEach(addLiBaoRecords::remove);
        logger.info("merge 3 done role: {}, libao context: {}", role.getId(), context);

        for (RoleLibaoRecord record: delLiBaoRecords.values()) {
            String metaId = record.getMetaId();
            // 达到重置条件和删除条件，直接算删除
            if (resetLiBaoRecords.containsKey(metaId)) {
                ErrorLogUtil.errorLog("reset & remove -> remove", "roleId",role.getRoleId(),"metaId",metaId,"record", record);
                resetLiBaoRecords.remove(record.getMetaId());
            }
            // 先删除，然后又更新，不可能出现，因为检查更新前会先检查删除。
            // 如果真出现了就报个错。这种仍按删除算
            if (updateLiBaoRecords.containsKey(metaId)) {
                ErrorLogUtil.errorLog("update -> remove", "roleId",role.getRoleId(),"metaId",metaId,"record", record);
                updateLiBaoRecords.remove(record.getMetaId());
            }
        }
        logger.info("merge 4 done role: {}, libao context: {}", role.getId(), context);

        for (RoleLibaoRecord record: resetLiBaoRecords.values()) {
            String metaId = record.getMetaId();
            // 重置和更新，算更重置
            if (updateLiBaoRecords.containsKey(metaId)) {
                ErrorLogUtil.errorLog("update & reset -> reset", "roleId",role.getRoleId(),"metaId",metaId,"record", record);
                updateLiBaoRecords.remove(record.getMetaId());
            }
        }
        logger.info("merge 5 done role: {}, libao context: {}", role.getId(), context);
    }

    private void doActions(Role role, LiBaoContext context, RoleLibaoRecord record, LiBaoActionGroupType groupType) {
        if (null == record) {
            return;
        }

        String metaId = record.getMetaId();
        LibaoMeta meta = liBaoConfigManager.getById(metaId);
        List<LiBaoActionParam> actions = meta.getActions(groupType);
        for (var actionParam: actions) {
            try {
                var action = getActionFunc(actionParam.getActionType());
                if (action == null) {
                    ErrorLogUtil.errorLog("unknown libao action type", "type",actionParam);
                } else {
                    // TODO: try catch libao-<NAME_EMAIL> 2024-03-27 11:25:13
                    try {
                        action.doAction(role, actionParam, context, record);
//                        /*
//                         * 后面的action可能会依赖前面的action，因此每次执行完都要保存，但消息可以在后面统一发
//                         */
//                        saveChanges(role, context);
                    } catch (Exception e) {
                        if (!(e instanceof ExpectedException)) {
                            ErrorLogUtil.exceptionLog("libao doAction error", e, "roleId", role.getRoleId(),
                                    "metaId", metaId, "actionParam", actionParam);
                        }
                        // 前面的 action 执行失败，后面的就不执行了，否则会因为环境错误导致更大的问题
                        break;
                    }
                }
            } catch (ExpectedException ignored) {

            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("do libao action error", e,"actionParam",actionParam);
            }
        }
    }

    private void syncContextChange(Role role, LiBaoContext context) {
        if (! role.isOnline()) {
            return;
        }

        if (! isOpen()) {
            return;
        }

        LiBaoContextType contextType = context.getType();

        // 进入世界时全发。其它时候发更新
        if (contextType == LiBaoContextType.ENTER_WORLD) {
            var allRecords = roleLibaoManager.getRoleLibaoRecordMap(role.getRoleId());
            if (allRecords == null) {
                return;
            }

            role.send(LiBaoOutput.wrapperLiBaoList(role, allRecords.values(), roleLibaoManager, liBaoConfigManager));
        } else {
            if (! context.hasChanges()) {
                return;
            }

            Map<String, RoleLibaoRecord> update = new HashMap<>();
            update.putAll(context.getUpdate());
            update.putAll(context.getReset());

            role.send(LiBaoOutput.wrapperLiBaoListUpdate(role, context.getDel().keySet(), context.getAdd().values(),
                    update.values(), contextType == LiBaoContextType.BUY,
                    liBaoConfigManager, false));
        }
    }

    private void checkDelOnEvent(Role role, LiBaoFunctionType checkType, LiBaoContext context, Object... input) {
        Map<String, LibaoMeta> checkDelMetas = liBaoConfigManager.getLiBaoMetasByFunctionType(LiBaoConditionType.DEL, checkType);
//        Map<String, LibaoMeta> checkAddMetas = liBaoConfigManager.getLiBaoMetasByFunctionType(LiBaoConditionType.ADD, checkType);
        checkDel(role, context, checkDelMetas, true, input);
    }

    private void checkDel(Role role, LiBaoContext context,
                          Map<String, LibaoMeta> metas,
                          boolean isOnEvent, Object ...eventParams) {

        Map<String, RoleLibaoRecord> recordMap = roleLibaoManager.getRoleLibaoRecordMap(role.getRoleId());
        if (recordMap == null) {
            return;
        }

        long now = TimeUtil.getNow();
        for (RoleLibaoRecord record : recordMap.values()) {
            String metaId = record.getMetaId();
            if (metas.containsKey(metaId)) {
                // 如果有结束时间那么只检查结束时间
                if (record.getEndTime() != 0 && record.getEndTime() <= now) {
                    context.addDelLiBao(record);
                } else {
                    CheckContext checkContext = new CheckContext(role, LiBaoConditionType.DEL, record);
                    // 检查条件并且如果是时间函数，获取时间
                    if (checkDelCondition(role, checkContext, record, isOnEvent, eventParams)) {
                        context.addDelLiBao(record);
                    }
                }
            }
        }

    }

    private void checkResetOnEvent(Role role, LiBaoFunctionType checkType, LiBaoContext context, Object... input) {
        Map<String, LibaoMeta> metas = liBaoConfigManager.getLiBaoMetasByFunctionType(LiBaoConditionType.RESET, checkType);
        checkReset(role, context, metas, true, input);
    }

    private void checkReset(Role role, LiBaoContext context, Map<String, LibaoMeta> metas, boolean isOnEvent, Object... input) {
        Map<String, RoleLibaoRecord> recordMap = roleLibaoManager.getRoleLibaoRecordMap(role.getRoleId());
        if (recordMap == null) {
            return;
        }

        long now = TimeUtil.getNow();
        for (RoleLibaoRecord record: recordMap.values()) {
            String metaId = record.getMetaId();
            if (metas.containsKey(metaId)) {
                // 跳过检查准备删除的
                if (context.getDel().containsKey(metaId)) {
                    continue;
                }
                // 如果有重置时间，那么就只按时间检查
                if (record.getResetTime() != 0 && record.getResetTime() <= now) {
                    context.addResetLiBao(record);
                } else {
                    // 否则检查条件并尝试获取重置时间
                    CheckContext checkContext = new CheckContext(role, LiBaoConditionType.RESET, record);
                    if (checkResetCondition(role, checkContext, record, isOnEvent, input)) {
                        context.addResetLiBao(record);
                    }
                }
            }
        }

    }

    private void checkAddOnEvent(Role role, LiBaoFunctionType checkType, LiBaoContext context, Object... input) {
        Map<String, LibaoMeta> metas = liBaoConfigManager.getLiBaoMetasByFunctionType(LiBaoConditionType.ADD_EVENT, checkType);
        filterNeverAdd(role, metas);
        if (metas.isEmpty()) {
            return;
        }

        checkAdd(role, context, metas, true, input);
    }

    /**
     * 配置的时候，有两种条件：add条件和addEvent条件，同时满足时才算
     * @param role
     * @param context
     * @param metas
     * @param isOnEvent
     * @param eventParams
     */
    public void checkAdd(Role role, LiBaoContext context, Map<String, LibaoMeta> metas, boolean isOnEvent, Object... eventParams) {
        for (LibaoMeta meta : metas.values()) {
            tryAddOne(role, context, meta, true, isOnEvent, eventParams);
        }
    }

    /**
     * 尝试检查各种条件，看看能否添加。
     * 一般都要检查添加条件的。
     * 但是如果是别的礼包在action中触发添加的，那么被触发的礼包往往没有配置添加条件，这时就不检查添加条件了。
     * 否则空的添加条件约定为false会导致添加失败
     *
     * @param role
     * @param context
     * @param meta
     * @param checkAdd    是否要检查添加条件。见上面解释
     * @param isOnEvent
     * @param eventParams
     */
    public void tryAddOne(Role role, LiBaoContext context, LibaoMeta meta, boolean checkAdd, boolean isOnEvent, Object... eventParams) {
        String metaId = meta.getId();

        // 礼包列表里已有的就不再检查了
        RoleLibaoRecord dbRecord = roleLibaoManager.getRoleLibaoRecord(role.getRoleId(), meta.getId());
        if (dbRecord != null) {
            // 将要删除的视为没在了
            if (! context.getDel().containsKey(metaId)) {
                return;
            }
        }

        RoleLibaoRecord record = new RoleLibaoRecord();
        record.setMetaId(meta.getId());

        CheckContext checkContext = new CheckContext(role, LiBaoConditionType.ADD, record);
        if (!checkAdd || checkAddCondition(role, checkContext, meta, isOnEvent, eventParams)) {
            // 由于没有检查添加条件，所以没有添加时间，给人补上
            if (!checkAdd) {
                checkContext.setBeginTime(TimeUtil.getNow());
            }

            // 检查是不是加入时已经能删除了，顺便获取删除时间
            checkContext.setConditionType(LiBaoConditionType.DEL);
            if (checkDelCondition(role, checkContext, record, false)) {
                return;
            }

            // 是否满足重置条件，顺便获取重置时间
            checkContext.setConditionType(LiBaoConditionType.RESET);
            // 添加时即使满足条件也不重置。因为一是刚添加的不应该满足重置条件，二是即使重置也跟刚添加时的状态一样
            // 但是要报个日志待查
            if (checkResetCondition(role, checkContext, record, false)) {
                ErrorLogUtil.errorLog("libao reset when add", "roleId",role.getId(), "metaId",metaId);
            }

            record.setStartTime(checkContext.getBeginTime());
            record.setResetTime(checkContext.getResetTime());
            record.setEndTime(checkContext.getEndTime());

            context.addAddLiBao(record);
        }

    }


    /**
     * 检查各种条件，看能否给用户显示此礼包
     *
     * 有些礼包是需要条件触发的，有些礼包是满足条件就显示给用户的。这两种没法都配置在add-condition中，否则无法区分，就会出现本来
     * 只在火炉2升3的时候弹出的礼包，只要用户等级大于3级那么每次检查都会被添加。
     *
     * 所以把两种分开在两列中：add-condition和add-event-condition。
     * 当事件发生的时候（也就是isOnEvent为true）时，两种条件都满足才算通过检查。
     * 其它时候都只检查只有add-condition的meta，有add-event-condition的都算false。
     *
     *
     * 检查的时候，有些条件是可以确定时间的。有两类：
     *  1、条件本身是个时间范围。比如fllowAct，betweenTime，serverWeek这种，那直接可以确认startTime和endTime
     *      但这时也有可能 reset 和del也指定了时间，并且更早
     *  2、条件只能确定开始时间。则填充startTime。这时endTime有几种情况：
     *      2.1 从reset条件来
     *      2.2 从del条件获取
     *      2.3 不指定，那就是不限时
     *  如果add条件不能确定开启时间，那么开启时间就是当前时间。这种情况下，也要检查 reset和del条件，看是否限时;
     *  所以无论如何，都要检查reset和del条件。
     *  一般条件下，reset时间要比del时间更早，但也不排除 del 就赶在 reset 前面了，所以都要检查，先查 reset 再查del
     *
     *  所有情况下，如果endTime比当前时间早，那么就判定礼包不能给用户显示。
     *
     * 在检查 reset 和 del 时，由于这两种条件跟add一样都是通用条件，需要告诉检查器是获取endTime，否则条件不知道填 startTime 还是 endTime
     *
     *
     * @param role
     * @param checkContext
     * @param meta
     * @param isOnEvent
     * @param eventParams
     * @return
     */
    private boolean checkAddCondition(Role role, CheckContext checkContext, LibaoMeta meta, boolean isOnEvent, Object ...eventParams) {
        // 带 add-event 的条目必须在事件发生时检查，没有事件发生的时候就是假
        if (!isOnEvent && !meta.getAddEventConditions().isEmpty()) {
            return false;
        }

        // 约定：条件为空相当于false，不加
        List<LiBaoFunction> conditions = meta.getAddConditions();
        if (conditions.isEmpty()) {
            return false;
        }

        for (LiBaoFunction condition : conditions) {
            LiBaoFunctionType funcType = condition.getFuncType();
            checkContext.setFunctionType(funcType);

            LiBaoConditionCheck liBaoConditionCheck = getConditionFunc(funcType);

            boolean ret = liBaoConditionCheck.check(role, condition, checkContext);
            if (funcType.isTimeFunc()) {
                long beginTime = checkContext.getBeginTime();
                ret = beginTime != 0 && beginTime <= TimeUtil.getNow();
            }

            if (!ret) {
                if (checkContext.isNever()) {
                    roleLibaoManager.getRoleFilterInfo(role.getRoleId()).getNeverAddSet().add(meta.getId());
                }
                return false;
            }

        }

        if (isOnEvent) {
            for (LiBaoFunction condition : meta.getAddEventConditions()) {
                LiBaoFunctionType funcType = condition.getFuncType();
                LiBaoConditionCheck liBaoConditionCheck = getConditionFunc(funcType);

                boolean ret = liBaoConditionCheck.onCheck(role, condition, checkContext, eventParams);
                if (funcType.isTimeFunc()) {
                    long beginTime = checkContext.getBeginTime();
                    ret = beginTime != 0 && beginTime <= TimeUtil.getNow();
                }
                if (! ret) {
                    if (checkContext.isNever()) {
                        roleLibaoManager.getRoleFilterInfo(role.getRoleId()).getNeverAddSet().add(meta.getId());
                    }
                    return false;
                }
            }
        }

        // 如果条件没有获取到开始时间，或者条件弹出礼包，那么开始时间就是现在
        long now = TimeUtil.getNow();
        long beginTime = checkContext.getBeginTime();
        if (isOnEvent || beginTime == 0) {
            beginTime = now;
        }

        checkContext.setBeginTime(beginTime);

        return true;
    }

    private boolean checkResetCondition(Role role, CheckContext checkContext, RoleLibaoRecord record, boolean isOnEvent, Object ...eventParams) {
        LibaoMeta meta = liBaoConfigManager.getById(record.getMetaId());
        if (meta == null) {
            // 没有配置了，不应该重置。否则可能会引发新的问题
            return false;
        }

        for (LiBaoFunction condition : meta.getResetConditions()) {
            LiBaoFunctionType funcType = condition.getFuncType();
            checkContext.setFunctionType(funcType);

            LiBaoConditionCheck liBaoConditionCheck = getConditionFunc(funcType);
            boolean ret;

            if (isOnEvent) {
                ret = liBaoConditionCheck.onCheck(role, condition, checkContext, eventParams);
            } else {
                ret = liBaoConditionCheck.check(role, condition, checkContext);
            }

            if (funcType.isTimeFunc()) {
                long resetTime = checkContext.getResetTime();
                ret = resetTime > 0 && resetTime <= TimeUtil.getNow();
            }

            if (ret) {
                return true;
            }
        }

        return false;
    }

    private boolean checkDelCondition(Role role, CheckContext checkContext, RoleLibaoRecord record, boolean isOnEvent, Object ...eventParams) {
        LibaoMeta meta = liBaoConfigManager.getById(record.getMetaId());
        if (meta == null) {
            // 配置已经没了，记录也没用了，删除
            return true;
        }

        for (LiBaoFunction condition : meta.getDelConditions()) {
            LiBaoFunctionType funcType = condition.getFuncType();
            checkContext.setFunctionType(funcType);

            LiBaoConditionCheck liBaoConditionCheck = getConditionFunc(funcType);
            boolean ret;

            if (isOnEvent) {
                ret = liBaoConditionCheck.onCheck(role, condition, checkContext, eventParams);
            } else {
                ret = liBaoConditionCheck.check(role, condition, checkContext);
            }

            if (funcType.isTimeFunc()) {
                long endTime = checkContext.getEndTime();
                ret = endTime > 0 && endTime <= TimeUtil.getNow();
            }

            if (ret) {
                return true;
            }
        }

        return false;
    }

    private void filterNeverAdd(Role role, Map<String, LibaoMeta> metas) {
        Set<String> neverAddSet = roleLibaoManager.getRoleFilterInfo(role.getRoleId()).getNeverAddSet();
        if (! neverAddSet.isEmpty() && ! metas.isEmpty()) {
            for (LibaoMeta meta : metas.values()) {
                if (neverAddSet.contains(meta.getId())) {
                    metas.remove(meta.getId());
                }
            }
        }
    }

    /**
     * 登录更新方法，之前都是liBaoUpdate方法，但是由于异步处理，前端依赖GcLiBaoList消息与Update消息的时序性所以将登录和零点reset拆分出来
     * 登录更新先处理普通礼包发送GcLiBaoList消息后，调用真限时礼包异步处理，然后通过update进行增量更新操作
     * <p>
     * 零点更新:{@link #liBaoUpdateOnResetDay}
     *
     * @param role
     */
    public void liBaoUpdateOnEnterWorld(Role role) {
        logger.info("liBaoUpdateOnEnterWorld begin");
        LiBaoContext context = new LiBaoContext(LiBaoContextType.ENTER_WORLD);
        // 临时修复，flyway上线后不再需要
        Map<String, RoleLibaoRecord> records = roleLibaoManager.getRoleLibaoRecordMap(role.getRoleId());
        if (records != null) {
            for (RoleLibaoRecord record : records.values()) {
                LibaoMeta meta = liBaoConfigManager.getById(record.getMetaId());
                if (meta == null) {
                    continue;
                }
                if (meta.getGroup().equals("VIP")) {
                    if (! record.isEnable()) {
                        record.setEnable(true);
                        roleLibaoManager.saveRoleLibaoRecord(record);
                    }
                }
            }
        }
        // 临时修复结束
        var metas = liBaoConfigManager.getLiBaoMetas();
        checkDel(role, context, metas, false);
        logger.info("login checkDel done role: {}, libao context: {}",
                role.getId(), context);
        checkReset(role, context, metas, false);
        logger.info("login checkReset done role: {}, libao context: {}",
                role.getId(), context);
        checkAdd(role, context, metas, false);
        logger.info("login checkAdd done role: {}, libao context: {}",
                role.getId(), context);

        afterCheck(role, context);

        logger.info("liBaoUpdateOnEnterWorld end");
    }


    /**
     * 零点更新方法，之前都是liBaoUpdate方法，但是由于异步处理，前端依赖GcLiBaoList消息与Update消息的时序性
     * 所以将登录和零点reset拆分出来，零点更新就等异步处理完真限时礼包后统一发送GcLiBaoList消息
     * <p>
     * 玩家登录:{@link #liBaoUpdateOnEnterWorld}
     *
     * @param role
     */
    public void liBaoUpdateOnResetDay(Role role) {
        LiBaoContext context = new LiBaoContext(LiBaoContextType.DAY_ZERO);

        // 跨天时也刷所有  要不整点礼包只能重登才能开启
        Map<String, LibaoMeta> metas = liBaoConfigManager.getLiBaoMetas();
		/*
		 * var records = roleLibaoManager.getRoleLibaoRecordMap(role.getRoleId()); for
		 * (var record: records.values()) { var metaId = record.getMetaId();
		 * metas.put(metaId, liBaoConfigManager.getById(metaId)); }
		 */

        checkDel(role, context, metas, false);
        logger.info("day checkDel role: {}, libao context: {}", role.getId(), context);
        checkReset(role, context, metas, false);
        logger.info("day checkReset role: {}, libao context: {}", role.getId(), context);
        checkAdd(role, context, metas, false);
        logger.info("day checkAdd role: {}, libao context: {}", role.getId(), context);

        afterCheck(role, context);
    }

    /**
     * 获取礼包Map（不包含offer与真限时）
     *
     * @param role
     * @return
     */
    @SuppressWarnings("unused")
	private Map<String, LibaoMeta> getNormalMetaMap(Role role) {
        // TODO: libao-<NAME_EMAIL> 2024-03-28 21:26:15
        //获取礼包列表
        Map<String, LibaoMeta> map = new HashMap<>();
        Map<String, LibaoMeta> metas = liBaoConfigManager.getLiBaoMetas();
        List<LibaoMeta> allMetaList = new ArrayList<>(metas.values());

        for (LibaoMeta m : fillABTest(role, allMetaList)) {
            map.put(m.getId(), m);
        }

        return map;
    }


    /**
     * AB测试过滤
     *
     * @param role
     * @param metas
     * @return
     * <AUTHOR>
     */
    private Collection<LibaoMeta> fillABTest(Role role, Collection<LibaoMeta> metas) {
        // TODO: libao-<NAME_EMAIL> 2024-03-28 21:26:15
        Map<String, String> roleABStampMap = roleManager.getEffectRoleABStampMap(role.getPersistKey());
        List<LibaoMeta> list;
        if (roleABStampMap != null && !roleABStampMap.isEmpty()) {
            list = metas.stream().filter(m -> {
                // 一个礼包的判断
                if (!StringUtils.isNotBlank(m.getAbtest())) {
                    return true;
                }

                // 取到第一个分组，是为了防止策划配置错误
                String firstGroup = null;
                String[] abGroups = StringUtils.split(m.getAbtest(), ",");
                for (String abTest : abGroups) {
                    String[] split = StringUtils.split(abTest, ":");
                    if (firstGroup == null) {
                        firstGroup = split[0];
                    }

                    // 如果出现不同分组，则抛出异常，且只使用第一个分组
                    if (!StringUtils.equals(split[0], firstGroup)) {
                        ErrorLogUtil.errorLog("fillABTest LiBao:different group","id",m.getId());
                        return false;
                    }

                    if (roleABStampMap.containsKey(split[0])) {
                        // 命中
                        if (StringUtils.equals(roleABStampMap.get(split[0]), split[1])) {
                            return true;
                        }
                    } else {
                        // 代表结束AB测试，或者老玩家，用other保底
                        if (StringUtils.equals(split[1], "other")) {
                            return true;
                        }
                    }
                }

                return false;
            }).collect(Collectors.toList());

        } else {
            // 空的ABTest数据
            list = metas.stream().filter(m -> {
                // 一个礼包的判断
                if (StringUtils.isBlank(m.getAbtest())) {
                    return true;
                }

                return StringUtils.contains(m.getAbtest(), "other");
            }).collect(Collectors.toList());
        }

        if (list != null && list.size() > 0) {
            return list;
        }

        return Collections.emptyList();
    }

    public LiBaoConditionCheck getConditionFunc(LiBaoFunctionType type) {
        return conditionFucMap.get(type);
    }

//    private LiBaoTimeCheck getTimeFunc(LiBaoTimeType type) {
//        return timeFuncMap.get(type);
//    }

    private LiBaoAction getActionFunc(LiBaoActionType type) {
        return actionFuncMap.get(type);
    }

    public boolean canBuy(long roleId, String productId,String extraData) {
        JSONObject result = new JSONObject();
        return canBuyInner(roleId, productId, result,false,extraData);
    }


    private void updateRes(JSONObject result,int code,String msg){
        result.put(TuConstants.KEY_RET, code);
        result.put(TuConstants.KEY_MSG, msg);
    }

    private boolean canBuyInner(long roleId, String productId, JSONObject result,boolean isGive,String extraData) {
        Role role = roleDao.findById(roleId);
        if (null == role) {
            return false;
        }

        LibaoMeta liBaoMeta = liBaoConfigManager.getById(productId);
        if (liBaoMeta == null) {
            logger.warn("libao check can buy: meta not found! roleId: {} productId: {}", role.getRoleId(), productId);
            updateRes(result,TuConstants.LIBAO_META_NOT_EXIST,"libao meta not found");
            return false;
        }

        RoleLibaoRecord record = roleLibaoManager.getRoleLibaoRecord(roleId, productId);
        if (null == record) {
            logger.info("libao check can buy: role={}, libao-meta-id: {}, not-has-record", roleId, productId);
            updateRes(result,TuConstants.LIBAO_RECORD_NOT_EXIST,"libao record not found");
            return false;
        }

        if (liBaoMeta.getCount() != 0 && record.getBuyCount() >= liBaoMeta.getCount()) {
            ErrorLogUtil.errorLog("libao check count exceed", "roleId",role.getRoleId(), "productId",productId, "libao",record);
            updateRes(result,TuConstants.LIBAO_OVER_BUY_COUNT,"buy count exceed");
            return false;
        }


        if (! record.isEnable()) {
            ErrorLogUtil.errorLog("libao check disable product", "roleId",role.getRoleId(), "productId",productId, "libao",record);
            updateRes(result,TuConstants.LIBAO_DISABLE,"buy disable product");
            return false;
        }

        if (record.isHide()) {
            ErrorLogUtil.errorLog("libao check hide product", "roleId",role.getRoleId(), "productId",productId, "libao",record);
            updateRes(result,TuConstants.LIBAO_HIDE,"buy hide product");
            return false;
        }

        if(liBaoMeta.getGold() > 0 && !functionSwitchService.isOpen(FunctionType.CHARGE_TOKEN)){

            //购买代币礼包
            ErrorLogUtil.errorLog("libao check token func switch close", "roleId",role.getRoleId(), "productId",productId, "libao",record);
            updateRes(result,TuConstants.LIBAO_TOKEN_CANT_BUY,"token purchase close");
            return false;
        }

        if(liBaoMeta.hasTag(LiBaoTagType.MONTH_CARD_DISCOUNT)){
            String discountItem = configService.getConfig(SettingConfig.class).getDiscountMonthcardItem();
            if(!roleItemManager.hasItem(role, discountItem, 1, false)){
                ErrorLogUtil.errorLog("libao check month card discount item error", "roleId",role.getRoleId(), "productId",productId, "libao",record);

                updateRes(result,TuConstants.LIBAO_DISCOUNT_ITEM_NOT_EXIST,"discount month card error");
                return false;
            }
        }

        LiBaoContext context = new LiBaoContext(LiBaoContextType.ACTION);
        context.setGive(isGive);
        context.setExtData(extraData);
        doActions(role, context, record, LiBaoActionGroupType.CAN_BUY);

        boolean canBuy = !JavaUtils.bool(context.getExtData()) || context.isExtraValidate();

        if (!canBuy) {
            ErrorLogUtil.errorLog("libao check other reason", "roleId",role.getRoleId(), "productId",productId, "libao",record);

            updateRes(result,TuConstants.LIBAO_OTHER,"other reason");
            return false;
        }

        return canBuy;
    }

    public void onActivityStart(String metaId) {
        for (Role role: roleDao.findAll() ) {
            // 在线的才查，不在线的上线时会检查一次
            if (role.isOnline()) {
                liBaoUpdateOnCheck(role, "onActivityStart", LiBaoFunctionType.ACTIVITY_BEGIN, metaId);
            }
        }
    }

    public void onActivityStop(String metaId) {
        for (Role role: roleDao.findAll() ) {
            if (role.isOnline()) {
                liBaoUpdateOnCheck(role, "onActivityStop", LiBaoFunctionType.ACTIVITY_END, metaId);
            }
        }
    }

    /**
     * 服务是否已开启
     * @return
     */
    private boolean isOpen() {
        return functionSwitchService.isOpen(FunctionType.LI_BAO.getId());
    }

    /**
     * 检测活动开关是否改变，改变的话，立即结束并发奖
     */
    public void onServerSwitchChange() {
        boolean isOpenNow = isOpen();
        if (! isOpenNow && switchOpen) {
            // 之前开着，现在关了，要给所有用户发送libao-list-update，清理前端礼包
            logger.info("礼包全局开关变为：关闭");

            Collection<Role> roles = roleDao.findAll();
            for (Role role : roles) {
                if (role.isOnline()) {
                    ConcurrentMap<String, RoleLibaoRecord> map = roleLibaoManager.getRoleLibaoRecordMap(role.getId());
                    List<String> removeList = new ArrayList<>(map.keySet());
                    role.send(LiBaoOutput.wrapperLiBaoListUpdate(role, removeList, null, null, false, liBaoConfigManager, false));
                }
            }
        } else if (isOpenNow && ! switchOpen) {
            // 之前关着，现在开了，要给所有用户发送libao-list-update，恢复礼包购买
            logger.info("礼包全局开关变为：打开");
            Collection<Role> roles = roleDao.findAll();
            for (Role role : roles) {
                if (role.isOnline()) {
                    ConcurrentMap<String, RoleLibaoRecord> map = roleLibaoManager.getRoleLibaoRecordMap(role.getId());
                    role.send(LiBaoOutput.wrapperLiBaoList(role, map.values(), roleLibaoManager, liBaoConfigManager));
                }
            }
        }

        switchOpen = isOpen();
    }

    /**
     * 礼包配置变化
     * 礼包可能会：
     * - 关闭
     * - 打开
     * - 删除
     * - 新增
     * - 组变化  不好处理
     * - 各种条件变化
     * - 各种数值变化
     * 最重要的是删除和关闭的，要及时给用户推送
     * 其它的，用户重新登录时可以处理好大部分，所以再说 TODO
     *
     */
    public void onLiBaoConfigChange(Map<String, LibaoMeta> oldMetas, Map<String, LibaoMeta> newMetas) {
        // 起服不处理
        if (oldMetas.isEmpty()) {
            return;
        }

        Map<String, LibaoMeta> offMap = new HashMap<>();
        Map<String, LibaoMeta> onMap = new HashMap<>();
        Map<String, LibaoMeta> delMap = new HashMap<>();
        Map<String, LibaoMeta> addMap = new HashMap<>();

        // 找到关闭的，打开的，删除的礼包配置
        for (LibaoMeta meta : oldMetas.values()) {
            String metaId = meta.getId();
            LibaoMeta newMeta = newMetas.get(metaId);
            if (newMeta == null) {
                delMap.put(metaId, meta);
            } else {
                if (! meta.isOff() && newMeta.isOff()) {
                    offMap.put(metaId, meta);
                } else if (meta.isOff() && ! newMeta.isOff()) {
                    onMap.put(metaId, newMeta);
                }
            }
        }
        logger.info("libao config change 1: onMap: {}, offMap: {}, delMap: {}, addMap: {}", onMap.keySet(), offMap.keySet(), delMap.entrySet(), addMap.keySet());

        // 找到新增的礼包配置
        for (LibaoMeta meta : newMetas.values()) {
            String metaId = meta.getId();
            if (! oldMetas.containsKey(metaId)) {
                addMap.put(metaId, meta);
            }
        }
        logger.info("libao config change 2: onMap: {}, offMap: {}, delMap: {}, addMap: {}", onMap.keySet(), offMap.keySet(), delMap.entrySet(), addMap.keySet());

        // 处理开关和已删除
        Collection<Role> roles = roleDao.findAll();
        for (Role role : roles) {
            if (! role.isOnline()) {
                continue;
            }

            List<String> removeList = new ArrayList<>();
            List<RoleLibaoRecord> addBackList = new ArrayList<>();
            ConcurrentMap<String, RoleLibaoRecord> map = roleLibaoManager.getRoleLibaoRecordMap(role.getId());
            for (Entry<String, RoleLibaoRecord> entry : map.entrySet()) {
                String metaId = entry.getKey();
                if (offMap.containsKey(metaId) || delMap.containsKey(metaId)) {
                    removeList.add(metaId);
                }
                if (onMap.containsKey(metaId)) {
                    addBackList.add(entry.getValue());
                }

            }
            role.send(LiBaoOutput.wrapperLiBaoListUpdate(role, removeList, addBackList, null, false, liBaoConfigManager, false));
        }

        // TODO: 处理配置变更
        // TODO: 新礼包
        // TODO: 已删除礼包
    }

    // GM触发礼包
    public void GMTriggerLibao(Role role, String productId, long durationTime) {
        var libaoRecord = roleLibaoManager.getRoleLibaoRecord(role.getRoleId(), productId);
        if (null != libaoRecord) {
            roleLibaoManager.removeRoleLibaoRecord(libaoRecord);
        }

        // 触发一个新的
        long now = TimeUtil.getNow();
        roleLibaoManager.createRoleLibaoRecord(role, productId, now, 0, now + TimeUtil.MINUTE_MILLIS * durationTime);
    }


    @Override
    public void onGameEvent(Role role, GameEventType type, Object... args) {
        switch(type) {
            case HERO_STAR_UP:
                liBaoUpdateOnCheck(role, "onHeroStarUp", LiBaoFunctionType.HERO_STAR, args);
                break;
        }
    }
}

