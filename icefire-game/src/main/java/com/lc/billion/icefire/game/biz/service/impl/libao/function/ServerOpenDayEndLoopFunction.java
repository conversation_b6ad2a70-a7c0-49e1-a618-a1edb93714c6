package com.lc.billion.icefire.game.biz.service.impl.libao.function;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunction;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunctionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**  服务器开服天数循环 29|28|4|0
 *   开服每28天循环一次，共4个礼包
 *  calcGroup = (当前时间-服务器开启时间)/dayMillis/day
 *  calcGroup == groupNum -> true
 *  or
 *  calcGroup >= amount -1 and groupNum == amount -1
 * */
@Service
public class ServerOpenDayEndLoopFunction implements LiBaoConditionCheck {
	@Autowired
	private ServiceDependency srvDep;

	@Override
	public LiBaoFunctionType getType() {
		return LiBaoFunctionType.SERVER_OPEN_DAY_END_LOOP;
	}

	@Override
	public boolean check(Role role, LiBaoFunction func) {
		var cp = func.getCp();
		int days = cp.getInt(0);
		int amount = cp.getInt(1);
		int groupNum = cp.getInt(2);
		// 开服时间
		long serverOpenTime = srvDep.getServerInfoService().getServerOpenTime();
		//获取按天计算的期数
		int calcGroup =  (int)((System.currentTimeMillis() - serverOpenTime) / TimeUtil.DAY_MILLIS / days);
		//与当前期相同
		if(calcGroup == groupNum)
			return true;
		//判断当前天数的组大于最后一轮并且配置的组也是最后一组
		else return calcGroup >= amount - 1 && groupNum == amount - 1;

	}
}
