package com.lc.billion.icefire.game.biz.service.impl.milestone.event;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.MilestoneConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleEquipDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.model.equip.RoleEquip;
import com.lc.billion.icefire.game.biz.model.milestone.Milestone;
import com.lc.billion.icefire.game.biz.model.milestone.MilestoneType;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.milestone.RewardType;
import com.simfun.sgf.utils.JavaUtils;

public class HeroEquipMilestoneEvent extends AbstractMilestoneEvent {

	@Override
	public MilestoneType getType() {
		return MilestoneType.HERO_EQUIP_SERVER;
	}

	@Override
	public void init(Milestone milestone, MilestoneConfig.MilestoneMeta meta) {
		if (meta.getRewardType() == RewardType.ALLIANCE.id) {
			for (var alliance : Application.getBean(AllianceDao.class).findAll()) {
				Long allianceId = alliance.getId();
				if (!JavaUtils.bool(allianceId)) {
					return;
				}
				var srvd = Application.getBean(ServiceDependency.class);
				// 遍历联盟成员的装备
				for (var allianceMember : srvd.getAllianceMemberManager().getMembers(allianceId)) {
					// 容灾,防止联盟成员重复添加
					if(milestone.getCompletedRoleIds().contains(allianceMember.getPersistKey())){
						continue;
					}
					RoleEquip roleEquip = Application.getBean(RoleEquipDao.class).findById(allianceMember.getPersistKey());
					if (roleEquip != null && roleEquip.getMaxEquipLevel() >= meta.getParam2()) {
						milestone.getCompletedRoleIds().add(roleEquip.getRoleId());
					}
				}
				updateProcessBase(milestone, meta, milestone.getCompletedRoleIds().size(), allianceId);
			}
		} else {
			for (RoleEquip roleEquip : Application.getBean(RoleEquipDao.class).findAll()){
				if (roleEquip != null && !milestone.getCompletedRoleIds().contains(roleEquip.getRoleId()) && roleEquip.getMaxEquipLevel() >= meta.getParam2()) {
					milestone.getCompletedRoleIds().add(roleEquip.getRoleId());
				}
			}
			updateProcessBase(milestone, meta, milestone.getCompletedRoleIds().size(), null);
		}
	}

	@Override
	public void updateProcess(Milestone milestone, MilestoneConfig.MilestoneMeta meta, Object... params) {
		// 过期后不在更新进度x
		if (milestone.isExpired())
			return;
		Long roleId = (Long)params[1];
		Long allianceId = (Long)params[0];
		// 是添加还是移除[退盟时移除,其他情况下添加]
		Boolean isAddMember = (Boolean) params[2];
		if (meta.getRewardType() == RewardType.ALLIANCE.id) {
			if(isAddMember){
				if (checkCondition(milestone, meta, roleId)){
					milestone.getCompletedRoleIds().add(roleId);
					updateProcessBase(milestone, meta, milestone.getCompletedRoleIds().size(), allianceId);
				}
			} else {
				// 退盟时,如果用户已经计数过,则移除
				if (milestone.getCompletedRoleIds().contains(roleId)) {
					milestone.getCompletedRoleIds().remove(roleId);
					updateProcessBase(milestone, meta, milestone.getCompletedRoleIds().size(), allianceId);
				}
			}
		} else {
			if (isAddMember && checkCondition(milestone, meta, roleId)){
				milestone.getCompletedRoleIds().add(roleId);
				updateProcessBase(milestone, meta, milestone.getCompletedRoleIds().size(), null);
			}
		}
	}

	/**
	 * 判断是否达到条件
	 */
	public boolean checkCondition(Milestone milestone, MilestoneConfig.MilestoneMeta meta, Long roleId){
		// 如果该用户已经计数过,则不计数
		if (milestone.getCompletedRoleIds().contains(roleId)) {
			return false;
		}
		int lvLimit =  meta.getParam2();
		RoleEquip roleEquip = Application.getBean(RoleEquipDao.class).findById(roleId);
		if (roleEquip == null) {
			return false;
		}
		if (roleEquip.getMaxEquipLevel() >= lvLimit) {
			return true;
		}
		return false;
	}
}
