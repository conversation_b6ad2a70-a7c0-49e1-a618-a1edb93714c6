package com.lc.billion.icefire.game.biz.service.impl.mission;

import com.google.api.client.util.Lists;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.ChapterConfig;
import com.lc.billion.icefire.game.biz.config.ChapterMissionConfig;
import com.lc.billion.icefire.game.biz.config.MissionConfig.MissionMeta;
import com.lc.billion.icefire.game.biz.manager.MissionManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.mission.chapterMission.ChapterMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.chapterMission.ChapterType;
import com.lc.billion.icefire.game.biz.model.mission.chapterMission.PlayerChapterMission;
import com.lc.billion.icefire.game.biz.model.people.PeopleBornConditionType;
import com.lc.billion.icefire.game.biz.model.popularwill.PopularWillEventType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.mission.event.MissionEvent;
import com.lc.billion.icefire.game.biz.service.impl.popularwill.PopularWillServiceImpl;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons.ItemLogReason;
import com.lc.billion.icefire.protocol.GcChapterMissionInfo;
import com.lc.billion.icefire.protocol.GcChapterMissionReward;
import com.lc.billion.icefire.protocol.GcChapterMissionRewardAll;
import com.lc.billion.icefire.protocol.constant.ChapterState;
import com.lc.billion.icefire.protocol.constant.UnlockModule;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 支线任务
 */
@Service
public class MissionChapterServiceImpl {

    public static final Logger logger = LoggerFactory.getLogger(MissionChapterServiceImpl.class);

    @Autowired
    private ConfigServiceImpl configService;

    @Autowired
    private ServiceDependency srvDpd;

    @Autowired
    private MissionManager missionManager;

    @Autowired
    private RoleManager roleManager;

    @Autowired
    private BiLogUtil biLogUtil;

    @Autowired
    private PopularWillServiceImpl popularWillService;

    @PostConstruct
    public void init() {
    }

    /**
     * 初始化（或者更新）任务
     *
     * @param role
     */
    public void onEnterWorld(Role role) {
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
        if (!chapterMission.isOpenFlag()) {
            return;
        }

        // 删除表里没有的任务
        chapterMission.getCurrMissions().entrySet().removeIf(entry -> null == entry.getValue().getMeta());
        var config = configService.getConfig(ChapterMissionConfig.class);
        chapterMission.getFinishList().removeIf(entry -> null == config.getById(entry));

        if (StringUtils.isBlank(chapterMission.getCurChapterId())) {
            chapterMission.setCurChapterId("1");
            biLogUtil.missionChapterState(role, "1", (byte) 0);
        }
        if (chapterMission.getChapterState() == ChapterState.reward) {
            beginNewChapter(role, chapterMission);
        }
        getNextChapter(role, chapterMission, true);

        //检查章节类型变化 普通->限时章节
        checkMissionTypeChange(role,chapterMission);

        // FIXME: 线上暴风雪剧情异常导致任务无法完成，采取临时方案：用户登录时检查是否为第二章，如果是第二章只剩下一个任务且是暴风雪的任务就直接算完成
        finishedBlizzardMission(role);

        sendChapterMission(role);
    }

    public void onLevelUp(Role role){
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
        if (!chapterMission.isOpenFlag()) {
            return;
        }
        if (chapterMission.getChapterState() == ChapterState.reward) {
            if(beginNewChapter(role, chapterMission)){
                getNextChapter(role, chapterMission, false);
                //发送给玩家
                sendChapterMission(role);
            }
        }
    }

    private void checkMissionTypeChange(Role role,PlayerChapterMission chapterMission){
        if(chapterMission.getStartTime() == null){
            var meta = configService.getConfig(ChapterConfig.class).getById(chapterMission.getCurChapterId());
            if(meta.getType() == ChapterType.LIMIT){
                //现有章节变成限时章节了
                //设置为当前时间
                chapterMission.setStartTime(TimeUtil.getNow());
                // 玩家未完成击杀BOSS任务，但完成了其他任务
                //更新后，玩家第九章任务进入倒计时状态
                //
                //玩家完成了击杀BOSS的任务，但没有领取任务奖励
                //更新后，章节任务直接变成已完成状态
                //玩家可以直接领取全部奖励（任务奖励+章节奖励）
                //
                //玩家完成了击杀BOSS的任务，且领取过任务奖励
                //更新后，章节任务直接变成已完成状态
                //玩家可以直接领取全部奖励（相当于玩家又领取了一次任务奖励）
                var state = chapterMission.getCurChapterState();
                if(state == ChapterState.finish){
                    //修改为完成
                    chapterMission.setChapterState(state);
                    //限时完成
                    chapterMission.setLimitFinish(true);

                    //遍历现有任务，将已经领奖的改为可以再次领奖
                    for (var mission : chapterMission.getCurrMissions().values()) {
                        if(mission.isAward()){
                            //记录一个日志
                            mission.setAward(false);
                            logger.info("章节任务 普通转为限时 领奖状态重置,玩家:{} 章节:{} 任务:{}",role.getRoleId(),chapterMission.getCurChapterId(),mission.getMissionId());
                        }
                    }
                }

                missionManager.saveChapterMission(chapterMission);
            }
        }
    }

    private void finishedBlizzardMission(Role role) {
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
        if (chapterMission == null || !chapterMission.isOpenFlag()) {
            return;
        }
        if (!"2".equals(chapterMission.getCurChapterId())) {
            return;
        }
        int remainCnt = 0;
        ChapterMissionItem blizzardMission = null;
        for (var mission : chapterMission.getCurrMissions().values()) {
            if (mission.isFinish()) {
                continue;
            }
            remainCnt++;
            if (mission.getMeta().getType() == MissionType.BLIZZARD) {
                blizzardMission = mission;
            }
        }
        if (remainCnt != 1 || blizzardMission == null) {
            return;
        }
        // 只存在一个暴风雪的任务设置为完成
        blizzardMission.setProgress(1);
        blizzardMission.setStatus(MissionConstants.MISSION_STATUS_FINISH);
        missionManager.saveChapterMission(chapterMission);
    }

    public boolean isBlizzardMissionFinished(Role role) {
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
        if (chapterMission == null || !chapterMission.isOpenFlag()) {
            return false;
        }
        if (!"2".equals(chapterMission.getCurChapterId())) {
            return false;
        }

        for (var mission : chapterMission.getCurrMissions().values()) {
            if (mission.getMeta().getType() == MissionType.BLIZZARD) {
                return mission.isFinish();
            }
        }
        return false;
    }

    public boolean isFinished(Role role, String metaId) {
        MissionMeta meta = configService.getConfig(ChapterMissionConfig.class).getById(metaId);
        if (meta == null) {
            ErrorLogUtil.errorLog("章节任务策划数据出错","metaId",metaId,"roleId",role.getId());
            throw new AlertException("章节任务策划数据出错","metaId",metaId,"roleId",role.getId());
        }

        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());

        return chapterMission != null && chapterMission.getFinishList().contains(meta.getId());
    }

    private void getNextChapter(Role role, PlayerChapterMission chapterMission, boolean needRefresh) {
        var chapterMissionCfg = configService.getConfig(ChapterConfig.class).getById(chapterMission.getCurChapterId());
        String[] taskMetaIds = chapterMissionCfg.getContents();
        for (String metaId : taskMetaIds) {
            MissionMeta meta = configService.getConfig(ChapterMissionConfig.class).getById(metaId);
            if (meta == null) {
                ErrorLogUtil.errorLog("章节任务策划数据出错","metaId",metaId,"roleId",role.getId());
                throw new AlertException("章节任务策划数据出错","metaId",metaId,"roleId",role.getId());
            }

            if (null != chapterMission.getCurrMissions().get(meta.getId()) || chapterMission.getFinishList().contains(meta.getId())) {
                continue;
            }
            ChapterMissionItem missionItem = chapterMission.createMissionItem(meta, role);
            chapterMission.getCurrMissions().put(meta.getId(), missionItem);
            // 接受任务
            roleManager.onMissionChangeState(role, meta.getId(), meta.getMissionGroup(), (byte) 0, meta.getMissionType(), "chapter");
            if (missionItem.getStatus() == MissionConstants.MISSION_STATUS_FINISH) {
                roleManager.onMissionChangeState(role, meta.getId(), meta.getMissionGroup(), (byte) 1, meta.getMissionType(), "chapter");
                srvDpd.getMissionShareService().onChapterMissionFinished(role, meta.getId());
            }
        }

        if (needRefresh) {
            try {
                // 由于任务改动频繁，玩家登录后增加任务进度修复代码；如果确定不再修改了，可以删掉此段代码
                var allMissions = chapterMission.getCurrMissions().values();
                for (ChapterMissionItem missionItem : allMissions) {
                    if (missionItem.isFinish()) {
                        continue;
                    }
                    MissionMeta missionMeta = missionItem.getMeta();
                    if (missionMeta == null) {
                        continue;
                    }
                    MissionEvent event = missionMeta.getType().getEvent();
                    if (event != null) {
                        event.start(role, missionItem, missionMeta);
                    } else {
                        ErrorLogUtil.errorLog("chapter mission,not find chapter mission event impl when createMission", "missionId",missionMeta.getId(), "type",missionMeta.getType());
                    }
                }
            }catch (ExpectedException ignored) {

            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("修复章节任务时发生异常", e);
            }
        }
    }

    private void sendChapterMission(Role role) {
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getRoleId());
        GcChapterMissionInfo gcChapterInfo = new GcChapterMissionInfo();
        gcChapterInfo.setChapterId(chapterMission.getCurChapterId());
        gcChapterInfo.setChapterState(chapterMission.getCurChapterState());
        gcChapterInfo.setMissions(new ArrayList<>());
        for (var entry : chapterMission.getCurrMissions().values()) {
            gcChapterInfo.addToMissions(entry.toMsg());
        }
        var chapterMeta = configService.getConfig(ChapterConfig.class).getById(chapterMission.getCurChapterId());
        if(chapterMeta.getType() == ChapterType.LIMIT){
            gcChapterInfo.setLimitTime(JavaUtils.get(chapterMission.getStartTime()) + TimeUtil.SECONDS_MILLIS * chapterMeta.getMaxTime());
            gcChapterInfo.setLimitFinish(JavaUtils.get(chapterMission.getLimitFinish()));
        }
        role.send(gcChapterInfo);
    }

    /**
     * 条件达成时，检测是否有相关任务完成
     *
     * @param role
     * @param missionType
     * @param params      根据任务类型来决定参数意义
     */
    public void onMissionFinish(Role role, MissionType missionType, Object... params) {
        if (role == null) {
            ErrorLogUtil.errorLog("onMissionFinish role is null");
            return;
        }
        if (!srvDpd.getUnlockService().isUnlock(role, UnlockModule.Task)) {
            return;
        }
        try {
            PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getPersistKey());
            if (chapterMission == null) {
                ErrorLogUtil.errorLog("error:章节任务数据为null,有可能玩家已被清除了内存", "playerId",role.getId());
                return;
            }
            for (ChapterMissionItem item : chapterMission.getCurrMissions().values()) {
                if (!item.isFinish()) {
                    onMissionFinish0(role, missionType, item, params);
                }
            }
        }catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("任务完成异常", e,"roleId",role.getPersistKey());
        }
    }

    private void onMissionFinish0(Role role, MissionType type, ChapterMissionItem mission, Object[] params) {
        MissionMeta meta = mission.getMeta();
        if (meta == null) {
            return;
        }
        // 任务类型不匹配
        if (type != meta.getType()) {
            return;
        }
        MissionEvent event = meta.getType().getEvent();
        if (event == null) {
            ErrorLogUtil.errorLog("not find mission event impl", "missionId",mission.getMissionId(), "type",type);
            return;
        }
        // 条件未达成
        if (!event.check(mission, meta, params)) {
            return;
        }
        boolean effect = event.effect(mission, meta, params);
        if (!effect) {// 对任务没有造成影响
            return;
        }
        var finish = mission.getStatus() == MissionConstants.MISSION_STATUS_FINISH;
        if (finish) {
            // 任务完成
            roleManager.onMissionChangeState(role, meta.getId(), meta.getMissionGroup(), (byte) 1, meta.getMissionType(), "chapter");
        }
        // 发送更新
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getPersistKey());
        if (chapterMission == null) {
            return;
        }

        if(finish){
            //更新 限时章节状态
            if(chapterMission.getChapterState() != ChapterState.finish){
                var chapterMeta = configService.getConfig(ChapterConfig.class).getById(chapterMission.getCurChapterId());
                if(chapterMeta.getType() == ChapterType.LIMIT){
                    var state = chapterMission.getCurChapterState();
                    if(state == ChapterState.finish){
                        //修改为完成
                        chapterMission.setChapterState(state);
                        if(chapterMission.getStartTime() == null || chapterMission.getStartTime() + TimeUtil.SECONDS_MILLIS * chapterMeta.getMaxTime() >= TimeUtil.getNow()){
                            //限时完成
                            chapterMission.setLimitFinish(true);
                        }

                        //通知
                        sendChapterMission(role);
                    }
                }

            }
        }

        missionManager.saveChapterMission(chapterMission);

        if (role.isOnline()) {
            role.send(MissionServiceImpl.toUpdate2(mission));
        }

        if (finish) {
            srvDpd.getMissionShareService().onChapterMissionFinished(role, meta.getId());
            srvDpd.getUnlockService().onChapterMissionFinished(role, meta.getId());
        }
    }

    /**
     * 领取 普通任务奖励
     *
     * @param role
     * @param missionId
     */
    public boolean getRewards(Role role, String missionId) {
        MissionMeta meta = this.srvDpd.getConfigService().getConfig(ChapterMissionConfig.class).getById(missionId);
        if (meta == null) {
            ErrorLogUtil.errorLog("getRewards error,mission meta is null", "missionId",missionId);
            return false;
        }
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
        if (chapterMission == null) {
            return false;
        }
        ChapterMissionItem mItem = chapterMission.getCurrMissions().get(meta.getId());
        if (mItem == null) {
            logger.info("getRewards MissionItem is null !!  missionId:{}", meta.getId());
            return false;
        }
        if (!mItem.isFinish()) {
            logger.info("getRewards MissionItem is not finish !! missionId:{}", meta.getId());
            return false;
        }
        if (mItem.isAward()) {
            logger.info("getRewards MissionItem is Award !! missionId:{}", meta.getId());
            return false;
        }
        mItem.setAward(true);
        role.send(MissionServiceImpl.toUpdate2(mItem));
        List<SimpleItem> list = srvDpd.getDropService().drop(meta.getReward());
        srvDpd.getItemService().give(role, list, ItemLogReason.MISSION_REWARD);
        // 领取任务
        roleManager.onMissionChangeState(role, meta.getId(), meta.getMissionGroup(), (byte) 2, meta.getMissionType(), "chapter");
        chapterMission.getFinishList().add(meta.getId());
        missionManager.saveChapterMission(chapterMission);
        role.send(toReward2(meta.getId(), chapterMission.getCurChapterId()));
        // 触发民心补偿检查
        popularWillService.onFinishedCheckMission(role, meta.getId());
        srvDpd.getUnlockService().onChapterMissionFinished(role, meta.getId());
        return true;
    }

    public void getChapterRewards(Role role, String chapterId) {
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
        if (chapterMission == null) {
            return;
        }
        if (chapterMission.getCurChapterState() != ChapterState.finish) {
            return;
        }
        List<String> missions = Lists.newArrayList();
        for (ChapterMissionItem chapterMissionItem : chapterMission.getCurrMissions().values()) {
            if(this.getRewards(role, chapterMissionItem.getMissionId())){
                missions.add(chapterMissionItem.getMissionId());
            }
        }
        if (!chapterMission.isAllRewarded()) {
            return;
        }

        GcChapterMissionRewardAll msg = new GcChapterMissionRewardAll();
        msg.setChapterId(chapterMission.getCurChapterId());

        var chapterMeta = configService.getConfig(ChapterConfig.class).getById(chapterMission.getCurChapterId());
        int finishedChapterId = Integer.parseInt(chapterMission.getCurChapterId());
        if(chapterMeta.getType() != ChapterType.LIMIT || JavaUtils.get(chapterMission.getLimitFinish())){
            String chapterReward = chapterMeta.getReward();
            List<SimpleItem> list = srvDpd.getDropService().drop(chapterReward);
            srvDpd.getItemService().give(role, list, ItemLogReason.CHAPTER_REWARD);
        }else{
            msg.setNoBonus(true);
        }

        if (JavaUtils.bool(missions)) {
            msg.setMissionIds(missions);
        }

        biLogUtil.missionChapterState(role, chapterMission.getCurChapterId(), (byte) 1);
        chapterMission.setChapterState(ChapterState.reward);

        role.send(msg);

        beginNewChapter(role, chapterMission);
        getNextChapter(role, chapterMission, false);

        missionManager.saveChapterMission(chapterMission);
        sendChapterMission(role);

        srvDpd.getUnlockService().onChapterFinished(role, finishedChapterId);
        // 民心事件完成
        srvDpd.getPopularWillService().onFinishedSystemTargetEvent(role, PopularWillEventType.FINISHED_CHAPTER_MISSION, finishedChapterId);
        srvDpd.getPopularWillService().onFinishedEvent(role, PopularWillEventType.FINISHED_CHAPTER_MISSION, finishedChapterId);
        // 触发小人出生
        srvDpd.getPeopleServiceImpl().onTriggerBorn(role, PeopleBornConditionType.CHAPTER_FINISHED, finishedChapterId);
    }

    private boolean beginNewChapter(Role role, PlayerChapterMission chapterMission) {
        String nextChapterId = String.valueOf(Integer.parseInt(chapterMission.getCurChapterId()) + 1);
        var nextChapterMeta = configService.getConfig(ChapterConfig.class).getById(nextChapterId);
        if (null == nextChapterMeta) {
            return false;
        }
        if(nextChapterMeta.getLevel() > role.getLevel()){
            return false;
        }
        chapterMission.setCurChapterId(nextChapterId);
        chapterMission.setChapterState(ChapterState.unFinish);
        chapterMission.getCurrMissions().clear();
        if(nextChapterMeta.getType() == ChapterType.LIMIT){
            chapterMission.setStartTime(TimeUtil.getNow());
        }else{
            chapterMission.setStartTime(null);
        }
        chapterMission.setLimitFinish(null);
        biLogUtil.missionChapterState(role, nextChapterId, (byte) 0);
        missionManager.saveChapterMission(chapterMission);
        return true;
    }

    public int getCurrentChapter(Role role) {
        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
        if (chapterMission == null) {
            return 1;
        }

        String curChapterId = chapterMission.getCurChapterId();
        if (curChapterId == null) {
            return 1;
        }

        return Integer.parseInt(chapterMission.getCurChapterId());
    }

    // 尝试解锁章节任务
    public void triggerChapterMission(Role role) {
        if (null == role) {
            return;
        }

        if (!srvDpd.getUnlockService().isUnlock(role, UnlockModule.Task)) {
            return;
        }

        PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
        if (!chapterMission.isOpenFlag()) {
            chapterMission.setOpenFlag(true);
            missionManager.saveChapterMission(chapterMission);
            this.onEnterWorld(role);
        }
    }

    public static GcChapterMissionReward toReward2(String metaId, String chapterId) {
        GcChapterMissionReward msg = new GcChapterMissionReward();
        msg.setFinishMissionId(metaId);
        msg.setChapterId(chapterId);
        return msg;
    }

    public static GcChapterMissionRewardAll toRewardAll(String chapterId) {
        GcChapterMissionRewardAll msg = new GcChapterMissionRewardAll();
        msg.setChapterId(chapterId);
        return msg;
    }
}
