package com.lc.billion.icefire.game.biz.service.impl.mission.event;

import com.lc.billion.icefire.game.biz.config.MissionConfig.MissionMeta;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.AbstractBaseMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.role.Role;

/**
 *
 * <AUTHOR>
 *
 */
public class ArmySoldierNumMissionEvent extends AbstractMissionEvent {

	@Override
	public MissionType getType() {
		return MissionType.ARMY_SOLDIER_NUM;
	}

	@Override
	public void start(Role role, IMissionItem item, MissionMeta meta) {
		long amount = 0;
		setAmount(item, meta, amount);
	}

	@Override
	public boolean check(IMissionItem item, MissionMeta meta, Object... params) {
		long amount = (long) params[0];
		if (amount <= item.getProgress()) {
			return false;
		}
		return true;
	}

	@Override
	public boolean effect(IMissionItem item, MissionMeta meta, Object... params) {
		long amount = (long) params[0];
		setAmount(item, meta, amount);
		return true;
	}

}
