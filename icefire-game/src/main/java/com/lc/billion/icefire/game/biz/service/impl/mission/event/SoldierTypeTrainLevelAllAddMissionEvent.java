package com.lc.billion.icefire.game.biz.service.impl.mission.event;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.MissionConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.SoldierManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.soldier.Soldier;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName SoldierTypeTrainLevelAllAddMissionEvent
 * @Description
 * <AUTHOR>
 * @Date 2024/8/28 20:32
 * @Version 1.0
 */
public class SoldierTypeTrainLevelAllAddMissionEvent extends AbstractMissionEvent {
    @Override
    public MissionType getType() {
        return MissionType.SOLDIER_TYPE_LEVEL_GET_NUM_ALL;
    }

    @Override
    public void start(Role role, IMissionItem item, MissionConfig.MissionMeta meta) {
        long amount = getRealProgress(role, meta.getPara1Int(), meta.getPara3Int());
        setAmount(item, meta, amount, true);
    }

    @Override
    public boolean check(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        int soldierType = (Integer) params[0];
        // 士兵类型错误
        if (!(soldierType == 1 || soldierType == 2 || soldierType == 3 || soldierType == -1)) {
            return false;
        }

        int meta_type = meta.getPara1Int();
        int meta_level = meta.getPara3Int();
        // 不是同一兵种，不计数，直接返回
        if (meta_type != -1 && meta_type != soldierType) {
            return false;
        }

        int level = (Integer) params[1];
        if (meta_level > level) {
            return false;
        }

        return true;
    }

    @Override
    public boolean effect(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        int idNum = (int) params[2];
        long amount = item.getProgress() + idNum;
        setAmount(item, meta, amount, true);
        return true;
    }


    public long getRealProgress(Role role, int typeId, int level) {
        long result = 0;
        SoldierManager soldierManager = Application.getBean(SoldierManager.class);
        for (Soldier soldier : soldierManager.getAllSoldier(role).values()) {
            if (typeId == -1 || (soldier.getMeta().getType().getValue() == typeId && soldier.getMeta().getEra() >= level)) {
                result += soldier.getCount();
            }
        }
        ArmyDao armyDao = Application.getBean(ArmyDao.class);
        Collection<ArmyInfo> armys = armyDao.findByRoleId(role.getPersistKey());
        if (armys != null) {
            ArmyManager armyManager = Application.getBean(ArmyManager.class);
            for (ArmyInfo army : armys) {
                List<Soldier> soldierList = armyManager.getSolders(army);
                for (Soldier soldier : soldierList) {
                    if (typeId == -1 || (soldier.getMeta().getType().getValue() == typeId && soldier.getMeta().getEra() >= level)) {
                        result += soldier.getCount();
                    }
                }
            }
        }
        return result;
    }
}
