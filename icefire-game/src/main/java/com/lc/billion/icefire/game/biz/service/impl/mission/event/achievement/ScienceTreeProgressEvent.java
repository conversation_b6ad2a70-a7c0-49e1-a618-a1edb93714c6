package com.lc.billion.icefire.game.biz.service.impl.mission.event.achievement;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.MissionConfig;
import com.lc.billion.icefire.game.biz.config.ScienceConfig;
import com.lc.billion.icefire.game.biz.manager.RoleTechManager;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.AbstractBaseMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.mission.event.AbstractMissionEvent;

import java.util.Map;
import java.util.Set;

public class ScienceTreeProgressEvent extends AbstractMissionEvent {

    @Override
    public MissionType getType() {
        return MissionType.SCIENCE_TREE_PROGRESS;
    }

    @Override
    public boolean check(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        Role role = (Role) params[0];
        long realProgress = getRealProgress(role, 0);
        return realProgress > 0;
    }

    @Override
    public void start(Role role, IMissionItem item, MissionConfig.MissionMeta meta) {
        long realProgress = getRealProgress(role, 0);
        setAmount(item, meta, realProgress, false);
    }

    @Override
    public boolean effect(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        Role role = (Role) params[0];
        long realProgress = getRealProgress(role, 0);
        setAmount(item, meta, realProgress, false);
        return true;
    }
    @Override
    public long getRealProgress(Role role, int param) {
        int sum=0;
        Double value=1.0D;
        ConfigServiceImpl configService =Application.getBean(ConfigServiceImpl.class);
        RoleTechManager roleTechManager =Application.getBean(RoleTechManager.class);
        Map<Integer, Map<Integer, ScienceConfig.ScienceMeta>> typeMetaMap = configService.getConfig(ScienceConfig.class).getTypeMetaMap();
        if(typeMetaMap!=null && typeMetaMap.size()>0){
            Set<Integer> types = typeMetaMap.keySet();
            if(types!=null && types.size()>0){
                for(Integer type:types){
                    double scienceProcessByType = roleTechManager.getScienceProcessByType(role.getId(), type);
                    if(value.equals(scienceProcessByType)){
                        sum++;
                    }
                }
            }

        }
        return sum;
    }
}
