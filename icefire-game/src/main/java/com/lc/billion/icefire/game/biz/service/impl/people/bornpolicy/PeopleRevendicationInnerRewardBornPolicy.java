package com.lc.billion.icefire.game.biz.service.impl.people.bornpolicy;

import com.lc.billion.icefire.game.biz.model.people.PeopleBornConditionType;
import com.lc.billion.icefire.game.biz.model.people.RolePeople;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @ClassName PeopleRevendicationInnerRewardBornPolicy
 * @Description
 * <AUTHOR>
 * @Date 2024/6/27 15:13
 * @Version 1.0
 */
public class PeopleRevendicationInnerRewardBornPolicy extends AbstractPeopleBornConditionPolicy {
    private final Logger logger = LoggerFactory.getLogger(PeopleRevendicationInnerRewardBornPolicy.class);
    @Override
    public PeopleBornConditionType getBornConditionType() {
        return PeopleBornConditionType.REVENDICATION_INNER_REWARD;
    }

    @Override
    public boolean onTrigger(Role role, RolePeople rolePeople, Object... params) {
        if (params == null || params.length != 1) {
            return false;
        }
        // 收复地块的是个列表
        List<Integer> levelList = new ArrayList<>((Collection) params[0]);
        var metas = getMetas();
        if (metas.isEmpty()) {
            ErrorLogUtil.errorLog("revendication inner reward meta is empty", "roleId",role.getId());
            return false;
        }
        // 根据参数获取
        for (var meta : metas) {
            if (meta.getParams().length != 1) {
                continue;
            }
            if (levelList.contains(meta.getParams()[0])) {
                born(role, rolePeople, meta, false, true);
            }
        }
        return true;
    }
}
