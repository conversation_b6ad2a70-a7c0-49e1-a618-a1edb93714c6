package com.lc.billion.icefire.game.biz.service.impl.player;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.persistence.util.IdPrefixUtil;
import com.lc.billion.icefire.core.support.TickTimer;
import com.lc.billion.icefire.core.support.Time;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.core.utils.ShortKeyUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.BizConstants;
import com.lc.billion.icefire.game.biz.async.arena.ArenaUpdateRoleOperation;
import com.lc.billion.icefire.game.biz.async.message.BroadcastOperation;
import com.lc.billion.icefire.game.biz.async.player.ChangePlayerNameStep1Operation;
import com.lc.billion.icefire.game.biz.async.player.ChangePlayerNameStep3Operation;
import com.lc.billion.icefire.game.biz.async.player.PlayerLoadOrCreateOperation;
import com.lc.billion.icefire.game.biz.async.player.PlayerNameExistStep2Operation;
import com.lc.billion.icefire.game.biz.config.CampConfig;
import com.lc.billion.icefire.game.biz.config.CampConfig.CampMeta;
import com.lc.billion.icefire.game.biz.config.ItemConfig;
import com.lc.billion.icefire.game.biz.config.ItemConfig.ItemMeta;
import com.lc.billion.icefire.game.biz.config.SettingConfig;
import com.lc.billion.icefire.game.biz.config.SoldierConfig;
import com.lc.billion.icefire.game.biz.config.SoldierConfig.SoldierMeta;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.PlayerHeroDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.model.city.BuildingGroup;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.head.RoleHeadInfo;
import com.lc.billion.icefire.game.biz.model.head.UploadStatus;
import com.lc.billion.icefire.game.biz.model.hero.Hero;
import com.lc.billion.icefire.game.biz.model.player.LoginExtParam;
import com.lc.billion.icefire.game.biz.model.player.OtherLoginedPlayer;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.player.PlayerConstants;
import com.lc.billion.icefire.game.biz.model.prop.NumberPropsContainer;
import com.lc.billion.icefire.game.biz.model.role.*;
import com.lc.billion.icefire.game.biz.service.AbstractOnlineState;
import com.lc.billion.icefire.game.biz.service.AbstractOnlineState.Type;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.CheckStringServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.gvg.GVGGameService;
import com.lc.billion.icefire.game.biz.service.impl.gvg.permission.GVGOperationEvent;
import com.lc.billion.icefire.game.biz.service.impl.gvg.permission.GVGOperationType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.game.core.CoreLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.PlayerAction;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierUpdateReasonType;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons.ItemLogReason;
import com.lc.billion.icefire.game.support.LogReasons.MoneyLogReason;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGBattleService;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.LegionManager;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.protocol.GcPlayerChangeCamp;
import com.lc.billion.icefire.protocol.GcPlayerGenderChange;
import com.lc.billion.icefire.protocol.GcPlayerInvalidChar;
import com.lc.billion.icefire.protocol.structure.PsCityInfo;
import com.lc.billion.icefire.protocol.structure.PsPlayerInfo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.simfun.sgf.net.NetSession;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PlayerServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(PlayerServiceImpl.class);

    @Autowired
    private AsyncOperationServiceImpl asyncOperSrv;
    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private WorldServiceImpl worldSrv;
    @Autowired
    private PlayerSessionServiceImpl playerSessionSrv;
    @Autowired
    private SceneServiceImpl sceneSrv;
    @Autowired
    private ConfigServiceImpl configSrv;
    @Autowired
    private CheckStringServiceImpl checkStringSrv;
    @Autowired
    private RoleItemManager itemMgr;
    @Autowired
    private RoleCityManager roleCityManager;
    @Autowired
    private RoleDeviceManager roleDeviceManager;
    @Autowired
    private RoleExtraManager roleExtraManager;
    @Autowired
    private RoleCurrencyManager roleCurrencyManager;
    @Autowired
    private SoldierManager soldierManager;
    @Autowired
    private PlayerHeroDao heroDao;
    @Autowired
    private RoleHeadManager roleHeadManager;
    @Autowired
    private GVGGameService gvgGameService;
    @Autowired
    private RoleServerInfoManager roleServerInfoManager;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private RoleServerInfoDao roleServerInfoDao;
    @Autowired
    private LegionManager legionManager;
    @Autowired
    private LoginServiceImpl loginService;
    @Autowired
    private BiLogUtil biLogUtil;
    @Autowired
    private CoreLogUtil coreLogUtil;
    @Autowired
    private GVGBattleService gvgBattleService;
    /**
     * key: roleId
     */
    @Getter
    private Map<Long, Player> playerMap = new ConcurrentHashMap<>();

    public Player get(long id) {
        return playerMap.get(id);
    }

    public void add(Player player) {
        Player player2 = get(player.getId());
        playerMap.put(player.getId(), player);
        if (logger.isInfoEnabled()) {
            if (player2 != null) {
                logger.info("新增内存player发现内存已有player,old{}new{}", player2, player);
            }
            logger.info("Add Player [id={}], Online Count: {},{}", player.getId(), playerMap.size(), player);
        }
    }

    /**
     * @param player
     */
    public void initPlayer(Player player) {
        player.init();

        // 跨天时间
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(player.getId());
        try {
            srvDpd.getZeroScheduleService().initPlayer(player, roleExtra.getLastResetDayDataTime());
        } catch (Exception e) {
            ErrorLogUtil.errorLog("roleExtra为null");
            throw new RuntimeException(e);
        }
    }

    public void remove(long id) {
        Player player = playerMap.remove(id);
        if (player != null) {
            if (logger.isInfoEnabled()) {
                logger.info("Remove Player [id={}], Online Count: {},{}", id, playerMap.size(), player);
            }
        } else {
            logger.info("Player not found while removing: " + id);
        }
    }

    public PsPlayerInfo player2PsPlayerInfo(Player player, Role role) {
        PsPlayerInfo ps = new PsPlayerInfo();
        ps.setAccId(role.getAccountId());
        ps.setRoleId(role.getPersistKey());
        ps.setName(role.getName());
        ps.setLastChangeNameTime(role.getLastRenameTime());
        // 判断玩家是否有在上传头像中
        RoleHeadInfo headInfo = roleHeadManager.getRoleHeadInfo(role.getPersistKey());
        if (headInfo != null) {
            if (headInfo.getStatus() == UploadStatus.CHECKING) {
                ps.setAvatarURL(headInfo.getCheckingHead() == null ? "" : headInfo.getCheckingHead());
            } else if (headInfo.isLock()) {
                ps.setAvatarBanTime(headInfo.getExpireTime());
            }
        }
        ps.setFigure(role.getFigure());
        ps.setHead(role.getHead());
        ps.setCreateTime(role.getCreateTime());
        Long allianceId = role.getAllianceId();
        if (allianceId == null) {
            ps.setAllianceId("");
        } else {
            ps.setAllianceId(String.valueOf(allianceId));
            Legion legion = legionManager.findLegionByAllianceId(allianceId);
            if (legion != null) {
                ps.setLegionId(legion.getId());
            }
        }

        RoleCity city = roleCityManager.getRoleCity(role.getId());
        if (city != null && city.getPosition() != null) {
            ps.setX(city.getPosition().getX());
            ps.setY(city.getPosition().getY());
        }
        ps.setCountry(roleDeviceManager.getRoleDevice(role.getPersistKey()).getCountry());

        // 属性
        NumberPropsContainer numProps = role.getNumberProps();
        Map<Integer, Long> intPropMap = numProps.toInfoInt();
        Map<Integer, Double> doublePropMap = numProps.toInfoDouble();
        ps.setIntProperties(intPropMap);
        ps.setDoubleProperties(doublePropMap);
        ps.setServerId(role.getCurrentServerId());
        //
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getPersistKey());
        if (!StringUtils.isEmpty(role.getCampId())) {
            ps.setCampId(role.getCampId());
        }
        ps.setSex(role.getSex());
        ps.setContinuousLoginDays(roleExtra.getContinuousLoginDays());
        ps.setVipContinuousLoginDays(roleExtra.getVipContinuousLoginDays());
        ps.setIsGm(roleExtra.isGm());
        ps.setModOrGmFlag(roleExtra.getModOrGmFlag());
        ps.setResHelpAmount(roleExtra.getResHelpAmountDayLimt());

        SettingConfig settingCfg = configSrv.getConfig(SettingConfig.class);
        ps.setIsOpenCodUidExport(settingCfg.isOpenCodUidExport());
        Map<Integer, Integer> extraBuildingNums = roleExtra.getExtraBuildingNums();
        if (extraBuildingNums != null && !extraBuildingNums.isEmpty()) {
            Map<Integer, Integer> newExtraBuildings = extraBuildingNums.entrySet().stream().filter(entry -> {
                // 军火供应修改:建筑由购买礼包解锁改为大本等级解锁
                boolean isArmsSupplyBuildings = entry.getKey() == BuildingGroup.BUILD_CENTER.getGroup();
                return !isArmsSupplyBuildings;
            }).collect(Collectors.toMap(k -> k.getKey(), v -> v.getValue()));
            if (newExtraBuildings != null && !newExtraBuildings.isEmpty()) {
                ps.setExtraBuildingNums(extraBuildingNums);
            }
        }
        ps.setIsRecharge(roleExtra.getTotalDollar() > 0);
        ps.setTotalDollar(roleExtra.getTotalDollar());

        ps.setMigrateFirstLogin(roleExtra.isMigrateFirstLogin());
        ps.setMigrateLastTime(roleExtra.getMigrateLastTime());

        // 大厅
        int castleGroupId = BuildingGroup.CASTLE.getGroup();
        List<CityBuild> mainCityBuild = roleCityManager.getBuildingsByGroupId(role.getId(), castleGroupId);
        // rolecity有了，主建筑没有
        if (mainCityBuild != null) {
            CityBuild cityBuild = mainCityBuild.getFirst();
            ps.setCityHallX(cityBuild.getX());
            ps.setCityHallY(cityBuild.getY());
        } else {
            ErrorLogUtil.errorLog("城市PsPlayerInfo出错,库里找不到", "roleId", role.getId(), "groupId", castleGroupId);
        }
        ps.setAllianceGiftAnonymity(roleExtra.isAllianceGiftAnonymity());

        // cd key value
        ps.setRoleCDKey(ShortKeyUtil.parseShortKey(player.getUserId()));
        // modOrGmFlag
        ps.setModOrGmFlag(roleExtra.getModOrGmFlag());

        ServerType serverType = configCenter.getServerType(role.getCurrentServerId());
        RoleServerInfo roleServerInfo = roleServerInfoManager.findRoleServerInfo(role.getId());
        int homeServerId = role.getCurrentServerId();
        if (configCenter.isBattleServer(role.getCurrentServerId()) || serverType == ServerType.KVK_SEASON) {
            homeServerId = role.getoServerId();
        }

        ps.setHomeServerId(homeServerId);
        ps.setSeason(Application.getSeason());
        if (configCenter.getCurrentGameServerType() == ServerType.KVK_SEASON) {
            int kServerId = configCenter.getCurrentKvkSeasonServerGroupConfig().getKServerId();
            ps.setKvkServerId(kServerId);
            ps.setKvkGroupServerIds(Application.getKVKGroupServerIdsStr());
        }
        if (roleServerInfo != null && roleServerInfo.getCsaHomeServerId() > 0) {
            ps.setCsaHomeServerId(roleServerInfo.getCsaHomeServerId());
        }

        if (configCenter.getCurrentGameServerType() == ServerType.GVG_BATTLE) {
            ps.setSeason(gvgBattleService.getBattleServerSeason(role));
            KvkSeasonServerGroupConfig seasonConfig = configCenter.getKvkSeasonServerGroupConfigByOServerId(role.getoServerId(), TimeUtil.getNow());
            if (seasonConfig != null) {
                ps.setKvkServerId(seasonConfig.getKServerId());
            }
        }

        ps.setOpenServerRhythmOldOrNew(false);
        ps.setCanAttendAllianceTime(srvDpd.getRoleExtraManager().getCanAttendAllianceTime(role, roleExtra));
        ps.setWxCollection(role.getWxCollection());
        ps.setRoleInfo(role.toPsRoleInfo());

        return ps;
    }

    public void onEnterWorld(Role role) {
        srvDpd.getSettingService().getCommonSettingInfo(role);
        srvDpd.getAllianceGiftService().onlineSendRedPoint(role);
    }

    public void tick(Player player, long now) {
        // 检查重连
        AbstractOnlineState onlineState = player.getOnlineState();
        if (onlineState.getType() == Type.RECONNECTING) {
            if (!srvDpd.getGameLimitService().checkReconnect(player, now)) {
                return;
            }
        }

        this.onlineTimePlus(player);
        player.sendBatchMsg();
    }

    public void checkNameExistStep2(Role role, String name) {
        PlayerNameExistStep2Operation operation = new PlayerNameExistStep2Operation(role, name);
        asyncOperSrv.execute(operation);
    }

    /**
     * 玩家在线时间累积
     *
     * @param player
     */
    public void onlineTimePlus(Player player) {
        TickTimer onlineTickTime = player.getOnlineTickTimer();
        if (player.getOnlineState().getType() != Type.GAMING) {
            return;
        }

        if (!onlineTickTime.isPeriod(TimeUtil.getNow())) {
            return;
        }

        Role role = worldSrv.getWorld().getRole(player.getRoleId());
        if (role == null) {
            return;
        }

        RoleExtra roleBaseMgr = roleExtraManager.getRoleExtra(role.getPersistKey());

        // 本次累计增加秒数
        long secPlus = onlineTickTime.getInterval() / Time.SEC;
        // 单位 秒
        roleBaseMgr.setTimeSecOnline((int) (roleBaseMgr.getTimeSecOnline() + secPlus));

        // 本日登录时间累积

        // 如果跨天，对管理开始时间清零，当日历史累计在线时长清零，重新计算提醒时间
        long lastLoginTime = role.getLastLoginTime();
        long lastLogoutTime = role.getLastLogoutTime();

        long now = TimeUtil.getNow();
        if (!TimeUtil.isSameDay(lastLoginTime, now)) {// 跨天
            int time = (int) ((now - TimeUtil.getBeginOfDay(now)) / Time.SEC);
            roleBaseMgr.setTimeSecOnlineToday(time);
        } else if (lastLogoutTime == BizConstants.INVALID_TIME || !TimeUtil.isSameDay(lastLogoutTime, now)) {// 新建账号，或者今天没有登出过
            roleBaseMgr.setTimeSecOnlineToday((int) ((now - lastLoginTime) / Time.SEC));
        } else {// 今天有过登出，说明今日登陆时间有值
            roleBaseMgr.setTimeSecOnlineToday((int) (roleBaseMgr.getTimeSecOnlineToday() + secPlus));
        }
    }

    public void loadOrCreateRole(Player player) {
        PlayerLoadOrCreateOperation operation = new PlayerLoadOrCreateOperation(player, srvDpd);
        asyncOperSrv.execute(operation);
    }

    public static String createDefaultRoleName(long roleId, long timestamp) {
        long serverId = Application.getServerId();
        long idInOneServer = IdPrefixUtil.getIdInOneServer(roleId);
        return ServerConfigManager.getInstance().getGameConfig().getPlayerNamePrefix() + "" + serverId + idInOneServer;
    }

    private boolean isDefaultName(String name) {
        return false;
    }

    private static String getPlayerNamePerfix(long timestamp) {
        return PlayerConstants.NAME_PERFIX[(int) (timestamp % PlayerConstants.NAME_PERFIX_COUNT)];
    }

    public void changeName(Role role, String name, String itemMetaId) {
        name = name.trim();
        // 检查名字
        if (StringUtils.isEmpty(name)) {
            return;
        }

        char firstChar = name.charAt(0);
        if (firstChar >= '0' && firstChar <= '9') { // 首字符不能是数字
            role.send(new GcPlayerInvalidChar(name));
            return;
        }

        GVGOperationEvent gvgOperationEvent = gvgGameService.getGVGOperationEvent(GVGOperationType.CHANGENAME);
        boolean check = gvgOperationEvent.check(role.getCurrentServerId(), null);
        if (!check) {
            ErrorLogUtil.errorLog("GVG战斗服不允许改名");
            return;
        }

        if (isDefaultName(name)) { // 不能是默认名字
            role.send(new GcPlayerInvalidChar(name));
            return;
        }

        // 判断是否含有表情
        if (Utils.isEmo(name)) {
            role.send(new GcPlayerInvalidChar(name));
            return;
        }

        if (!checkStringSrv.checkName(name, PlayerConstants.MAX_PLAYER_NAME_LENGTH)) {
            role.send(new GcPlayerInvalidChar(name));
            return;
        }

        // 检查是否冷却时间已过
        var changeNameCoolDown = configSrv.getConfig(SettingConfig.class).getChangeNameCoolDown();
        if (changeNameCoolDown > 0) {
            if (role.getLastRenameTime() + changeNameCoolDown * TimeUtil.SECONDS_MILLIS > TimeUtil.getNow()) {
                return;
            }
        }

        changeNameStep1(role, name);
    }

    public void changeNameStep1(Role role, String name) {
        SettingConfig settingCfg = configSrv.getConfig(SettingConfig.class);
        // 改名，有改名卡先用改名卡，无改名卡扣除钻石
        ItemConfig itemCfg = configSrv.getConfig(ItemConfig.class);
        ItemMeta renameMeta = itemCfg.getRenameItem();
        String itemMetaId = renameMeta.getId();
        if (!itemMgr.hasItem(role, renameMeta, 1, false)) {
            itemMetaId = null;
            int price = settingCfg.getPlayerRenamePrice();
            if (price > 0 && !roleCurrencyManager.check(role, Currency.DIAMOND, price)) {
                logger.info("changeName not have enough money");
                return;
            }
        }

        // 检查重名并进行下一步
        asyncOperSrv.execute(new ChangePlayerNameStep1Operation(role, srvDpd, name, itemMetaId));
    }

    public void changeNameStep3(Role role, String name, String itemMetaId) {
        // 检查物品或钱
        if (!StringUtils.isEmpty(itemMetaId)) { // 使用物品
            if (!itemMgr.hasItem(role, itemMetaId, 1, false)) {
                logger.info("changeName not have item");
                return;
            }
            itemMgr.removeItem(role, itemMetaId, 1, ItemLogReason.CHANGE_NAME);
        } else { // 用钱
            SettingConfig settingCfg = configSrv.getConfig(SettingConfig.class);
            int price = settingCfg.getPlayerRenamePrice();

            if (price > 0 && !roleCurrencyManager.checkAndCost(role, Currency.DIAMOND, price, MoneyLogReason.CHANGE_NAME)) {
                logger.info("changeName not have enough money");
                return;
            }
        }
        asyncOperSrv.execute(new ChangePlayerNameStep3Operation(role.getId(), srvDpd, name));
    }

    public void bindRole(Player player, Role role) {
        // player.setRole(role);
        role.setPlayer(player);

        LoginExtParam extParam = player.getLoginContext().getExtParam();

        RoleDevice roleDevice = roleDeviceManager.getRoleDevice(role.getPersistKey()); // 设置设备信息
        roleDevice.setDeviceType(extParam.getDeviceType());
        roleDevice.setDeviceId(extParam.getDeviceId());
        roleDevice.setWxOpenId(extParam.getWxOpenId());
        roleDevice.setPlatform(extParam.getPlatform());
        roleDevice.setPushPlatform(extParam.getPushPlatformType());
        if (StringUtils.isEmpty(roleDevice.getCountry())) {
            roleDevice.setCountry(extParam.getCountry());
        }
        // 设置语言
        String rawLanguage = extParam.getLanguage();
        roleDeviceManager.setRawLanguage(role, rawLanguage);
        roleDevice.setLocaleLanguage(srvDpd.getLocaleService().parseLocale(rawLanguage));
        if (!player.getLoginContext().isNewRole()) {
            roleDevice.setLastClientVersion(roleDevice.getClientVersion());
        }
        roleDevice.setClientVersion(extParam.getClientVersion());
        roleDevice.setGaid(extParam.getGaid());
        roleDeviceManager.setIp(role, extParam.getIp());
    }

    public void waitReconnect(Player player) {
        player.getOnlineState().waitReconnect(player);
        Role role = worldSrv.getWorld().getRole(player.getRoleId());
        role.onWaitReconnect();
        // player.onWaitReconnect();
    }

    public void removePlayer(Player player) {
        if (player == null) {
            ErrorLogUtil.errorLog("[NET]removePlayer,player is null");
            return;
        }

        if (player.getOnlineState().getType() == Type.DISCONNECTED) {
            logger.info("[NET]removePlayer, state error");
        }

        try {
            // 先把状态变成disconnect
            player.getOnlineState().onRemovePlayer(player);
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[NET]removePlayer,change state to disconnect error", e);
        }

        Role role = worldSrv.getWorld().getRole(player.getRoleId());
        try {
            // 记录下线时间
            if (role != null && role.getPlayer() == player) {
                role.setLastLogoutTime(TimeUtil.getNow());
                long playTime = TimeUtil.getNow() - role.getLastLoginTime();
                role.addTotalOnlineTime(playTime);

                // 下线bi日志
                biLogUtil.roleLogoutProfile(role);
                // 临时处理TODO 未找到踢下线
                biLogUtil.logout(role, playTime, false);
                coreLogUtil.logout(role, playTime, false);
            } else {
                logger.info("[NET]removePlayer, setLastLogoutTime fail: not bind role");
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[NET]removePlayer,record offline time error", e);
        }

        try {
            // 调用各个模块的退出逻辑
            if (role != null && role.getPlayer() == player) {
                role.onLeaveWorld();
            } else {
                logger.info("[NET]removePlayer, onLeaveWorld fail: not bind role");
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[NET]removePlayer,leave world error", e);
        }

        try {
            // 1. 从AOI中删除
            if (srvDpd.getSceneService().contain(player)) {
                srvDpd.getSceneService().remove(player);
            } else {
                logger.info("[NET]removePlayer, aoi not contain player: {}, {}", player.getChannelId(), player.getPosition());
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[NET]removePlayer,leave aoi error", e);
        }

        try {
            // 2. World里的PlayerUnitManager playerManager
            if (worldSrv.getWorld().getPlayerManagerForAll().get(player.getId()) == player) {
                worldSrv.getWorld().removePlayer(player);
            } else {
                logger.info("[NET]removePlayer, world.playerMgr not contain player: {}, {}", player.getChannelId(), player.getId());
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[NET]removePlayer,remove from World.playerManager error", e);
        }

        try {
            // 3. PlayerServiceImpl里的LongMap<Player> playerMap
            if (get(player.getId()) == player) {
                remove(player.getId());
            } else {
                logger.info("[NET]removePlayer, playerSrv not contain player: {}, {}", player.getChannelId(), player.getId());
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[NET]removePlayer,remove from playerSrv error", e);
        }

        try {
            // 4. PlayerSessionServiceImpl里的ConcurrentMap<Player, NetSession> players
            playerSessionSrv.remove(player);
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[NET]removePlayer,remove from playerSessionSrv error", e);
        }

        // 解除绑定关系player<-X->session
        NetSession netSession = player.getNetSession();
        if (netSession != null) {
            player.setNetSession(null);
            if (netSession.getAttachment() == player) {
                netSession.setAttachment(null);
            } else {
                ErrorLogUtil.errorLog("[NET]removePlayer,player's binding session dose not bind to player");
            }
        }

        // 解除绑定关系player<-X->role
        if (role != null) {
            if (role.getPlayer() == player) {
                role.setPlayer(null);
            } else {
                ErrorLogUtil.errorLog("[NET]removePlayer,player's binding role dose not bind to player");
            }
        }

        // 委托数据推送
        // 需要在player被设置为null后调用,里面会有判断玩家是否在线的判断
        srvDpd.getTrusteeshipService().onLeaveWord(role);

        // 最后清理
        player.destory();

        // 让踢掉这个玩家的Player执行上线流程
        OtherLoginedPlayer otherLoginedPlayer = player.getLoginContext().getOtherLoginedPlayer();
        if (otherLoginedPlayer != null) {
            Player waitLoginPlayer = otherLoginedPlayer.getPlayer();
            logger.info("[NET]removePlayer, process other login: {}", waitLoginPlayer);

            if (waitLoginPlayer.getOnlineState().getType() == Type.DISCONNECTED) {
                logger.info("[NET]removePlayer, other player already closed");
            } else if (waitLoginPlayer.isClosing()) {
                logger.info("[NET]removePlayer, other player is closing: {}", waitLoginPlayer.getOnlineState().getType());
            } else if (waitLoginPlayer.getOnlineState().getType() == Type.LOGINED) {
                player.getLoginContext().setOtherLoginedPlayer(null);
                loginService.login(waitLoginPlayer);
            } else {
                ErrorLogUtil.errorLog("[NET]removePlayer,other player login state error");
            }
        }
    }

    /**
     * 玩家选择，变换阵营
     *
     * @param campId
     */
    public void changeCamp(Role role, String campId, int sex) {
        if (role != null) {
            ConfigServiceImpl configService = srvDpd.getConfigService();
            CampMeta meta = this.srvDpd.getConfigService().getConfig(CampConfig.class).get(campId);
            if (meta != null) {
                role.setCampId(campId);
            }
            role.setSex(sex);

            GcPlayerChangeCamp msg = new GcPlayerChangeCamp();
            msg.setCamp(role.getCampId());
            msg.setSex(sex);
            role.send(msg);
            // Hero hero = role.getHeroManager().getHeroByHeroId(meta.getHero());
            Hero hero = heroDao.getPlayerHero(role.getId(), meta.getHero());
            if (null == hero) {
                hero = this.srvDpd.getHeroService().heroGet(role, meta.getHero(), ItemLogReason.SWITCH_CAMP);
                if (hero != null) {
                    // BILog
                    srvDpd.getBiLogUtil().heroAcquisition(role, hero.getPersistKey() + "", hero.getStar(), hero.getMetaId(), "changeCamp");
                }
            }
            String troopsGetID = meta.getTroopsGetID();
            SoldierMeta soldierMeta = configService.getConfig(SoldierConfig.class).get(troopsGetID);
            if (soldierMeta != null) {
                int troopsGetNum = meta.getTroopsGetNum();
                soldierManager.add(role, soldierMeta, troopsGetNum, SoldierUpdateReasonType.CHANGE_CAMP, Strings.EMPTY, true);
            }

            SettingConfig settingConfig = configService.getConfig(SettingConfig.class);
            String[] initHero = settingConfig.getInitHeros();
            if (initHero != null && initHero.length > 0) {
                for (String heroId : initHero) {
                    hero = heroDao.getPlayerHero(role.getId(), heroId);
                    if (null == hero) {
                        hero = this.srvDpd.getHeroService().heroGet(role, heroId, ItemLogReason.SWITCH_CAMP);
                        // BILog
                        srvDpd.getBiLogUtil().heroAcquisition(role, hero.getPersistKey() + "", 1, heroId, "changeCamp");
                    }
                }
            }

            srvDpd.getAsyncOperService().execute(new ArenaUpdateRoleOperation(srvDpd, role, false));
        }
    }

    /**
     * 修改玩家性别
     *
     * @param sex 1男2女
     */
    public void changeGender(Role role, int sex) {
        if (role == null) {
            return;
        }

        // 修改
        role.setSex(sex);

        // 通知前端
        GcPlayerGenderChange msg = new GcPlayerGenderChange();
        msg.setSex((byte) sex);
        role.send(msg);

        // 广播
        RoleCity playerCity = roleCityManager.getRoleCity(role.getId());
        sceneSrv.update(playerCity, updater -> {
            PsCityInfo psCityInfo = updater.getCity();
            psCityInfo.setSex((byte) sex);
        });

        srvDpd.getAsyncOperService().execute(new ArenaUpdateRoleOperation(srvDpd, role, false));
    }

    public void broadcast(TBase<?, ?> msg) {
        broadcast(0, 0, msg);
    }

    public void broadcast(int oServerId, int currentServerId, TBase<?, ?> msg) {
        asyncOperSrv.execute(new BroadcastOperation(oServerId, currentServerId, srvDpd, msg, 0));
    }

    public void broadcastKvK(int kServerId, TBase<?, ?> msg) {
        asyncOperSrv.execute(new BroadcastOperation(kServerId, srvDpd, msg, 0));
    }

    public void broadcastForSystemMarquee(TBase<?, ?> msg, int lvLimit) {
        broadcastForSystemMarquee(0, 0, msg, lvLimit);
    }

    public void broadcastForSystemMarquee(int oServerId, int currentServerId, TBase<?, ?> msg, int lvLimit) {
        asyncOperSrv.execute(new BroadcastOperation(oServerId, currentServerId, srvDpd, msg, lvLimit));
    }

    public void playerForEach(PlayerAction action) {
        for (Player player : this.playerMap.values()) {
            try {
                action.action(player);
            } catch (ExpectedException ignored) {
            } catch (Exception ex) {
                ErrorLogUtil.exceptionLog(ex);
            }
        }
    }

    public int getRoleOriginSeason(Role role) {
        int season = 0;
        long now = TimeUtil.getNow();
        ServerType serverType = Application.getServerType();
        if (configCenter.serverTypeIsGAME_or_KVKSEASON(serverType)) {
            season = Application.getSeason();
        } else if (Application.isBattleServer()) {
            RoleServerInfo roleServerInfo = roleServerInfoDao.findById(role.getRoleId());
            int homeServerId = roleServerInfo.getHomeServerId();
            ServerType homeServerType = configCenter.getServerType(homeServerId);
            if (!configCenter.serverTypeIsGAME_or_KVKSEASON(homeServerType)) {
                ErrorLogUtil.errorLog("role in battle server,but home is not game or season", "homeServerId", homeServerId);
                return 0;
            }

            if (homeServerType == ServerType.GAME) {
                KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByOServerId(homeServerId, now);
                if (kvkSeasonServerGroupConfig == null) {
                    season = 1;
                } else {
                    season = kvkSeasonServerGroupConfig.getSeason();
                }
            } else if (homeServerType == ServerType.KVK_SEASON) {
                KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByKServerId(homeServerId, now);
                if (kvkSeasonServerGroupConfig == null) {
                    ErrorLogUtil.errorLog("kServer config is null", "kServerIdd", homeServerId);
                    return 0;
                }

                season = kvkSeasonServerGroupConfig.getSeason();
            } else {
                // 理论上不会出现
                ErrorLogUtil.errorLog("home server error", "homeServerId", homeServerId);
            }
        }

        return season;
    }
}
