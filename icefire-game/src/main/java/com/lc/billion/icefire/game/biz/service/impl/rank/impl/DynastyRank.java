package com.lc.billion.icefire.game.biz.service.impl.rank.impl;

import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankConstants;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.game.biz.service.impl.regioncapital.RegionCapitalService;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.protocol.GcRankList;
import com.lc.billion.icefire.protocol.structure.PsRankMember;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.resps.Tuple;

/**
 * 王朝排行榜
 *
 * <AUTHOR>
 */
@Service
public class DynastyRank extends BaseRank {

    @Autowired
    protected WorldServiceImpl worldService;
    @Autowired
    protected AllianceServiceImpl allianceService;
    @Autowired
    protected RegionCapitalService regionCapitalService;
    @Autowired
    protected AllianceDao allianceDao;

    @Override
    protected boolean addMemberInfo(PsRankMember rankMember, RankType type, String memberId, String... params) {
        return super.addMemberInfo(rankMember, type, memberId, params);
    }

    @Override
    public void getRank(Role role, RankType type, int serverId, int group, int page, String ext, String... params) {
        if (page < 1) {
            return;
        }
        int start = (page - 1) * RankConstants.PAGE_SIZE;
        int end = Math.min(page * RankConstants.PAGE_SIZE, type.getMaxSize());
        if (start >= type.getMaxSize()) {
            ErrorLogUtil.errorLog("getRank player rank max", "roleId", role.getId(), "type", type.getId());
            return;
        }

        GcRankList msg = new GcRankList(type.getType());
        msg.setExt(ext);

        var rankType = RankType.SEASON_WARM_UP_DYNASTY;
        String key = rankType.getHandler().getKey(rankType, serverId, params);
        // 获取排行榜数据
        var rankSet = rankDao.getRank(key, start, end, type.getRedisType());
        if (rankSet == null) {
            role.send(msg);
            return;
        }

        // 获取我的排行
        if (page == 1) {
            String member = String.valueOf(role.getoServerId());
            Long ownerRank = rankDao.getRank(key, member, type.getRedisType());
            if (ownerRank != null) {
                msg.setOwnerRank(ownerRank.intValue() + 1);
                var score = rankDao.getScore(key, member, type.getRedisType());
                score = score == null ? 0L : score;
                msg.setOwnerValue(score.intValue());
            }
        }

        int rank = 1;
        for (var rankInfo : rankSet) {
            var score = Double.valueOf(rankInfo.getScore()).longValue();
            var rankServerId = Integer.parseInt(rankInfo.getElement());
            PsRankMember rankMember = new PsRankMember(rank, score);
            rankMember.setServerId(rankServerId);

            // 王盟旗帜
            var royalNode = regionCapitalService.getRoyalNode(serverId);
            if (null != royalNode) {
                Alliance alliance = allianceDao.findById(royalNode.getBelongAllianceId());
                if (alliance != null) {
                    rankMember.setAllianceName(alliance.getName());
                    rankMember.setAllianceAlias(alliance.getAliasName());
                    rankMember.setFlagInfo(alliance.toPsAllianceFlagInfo());
                }
            }
            msg.addToMembers(rankMember);
            rank++;
        }
        role.send(msg);
    }

    /**
     * 清理排行榜脏数据
     *
     * @param rankType 排行榜类型
     */
    @Override
    public void clean(RankType rankType, String... params) {
        String key = getKey(rankType, params);
        cleanRank(rankType, key);
        String oldKey = getOldKey(rankType, params);
        cleanRank(rankType, oldKey);
    }

    @Override
    public void clean(RankType rankType, int serverId, String... params) {
        String key = getKey(rankType, serverId, params);
        cleanRank(rankType, key);
        String oldKey = getOldKey(rankType, serverId, params);
        cleanRank(rankType, oldKey);
    }

    private void cleanRank(RankType rankType, String key) {
        var rankSet = rankDao.getRank(key, 0, 200, rankType.getRedisType());
        for (Tuple rank : rankSet) {
            long dynastyId = Long.parseLong(rank.getElement());
            logger.debug("清理排行榜数据 类型{} key:{} dynastyId={}", rankType, key, dynastyId);
            rankDao.asyncDeleteRank(key, rank.getElement(), rankType.getRedisType());
        }
    }

    /**
     * 个人排行榜
     * 用来表示：这个排行榜，是否在玩家被清理出内存时，删除
     *
     * @return 是否
     */
    @Override
    public boolean isRoleSelf() {
        return true;
    }

    /**
     * 公会排行榜
     *
     * @return 是否是公会排行榜
     */
    @Override
    public boolean isAlliance() {
        return false;
    }

    /**
     * 是否有旧数据
     */
    @Override
    public boolean hasOld() {
        return false;
    }
}
