package com.lc.billion.icefire.game.biz.service.impl.recharge;

import com.lc.billion.icefire.game.biz.config.LiBaoConfig.LibaoMeta;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.currency.CurrencyCostInfo;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.protocol.constant.PsCurrency;
import com.simfun.sgf.utils.JavaUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/** 充值上下文 */
public class RechargeContext {
    private String productId;

    private LibaoMeta liBaoMeta;

    private double dollar;

    /** 获得的奖励，包含配置中的，以及额外可能给的，比如：ab测试给予 */
    private List<SimpleItem> items;
    /** 基础钻石 */
    private long diamond;
    /** 额外钻石 */
    private long extDiamond;
    /** 基础档位首冲翻倍的钻石，属于赠送，不参与活动和计入记录 */
    private long baseDoubleDiamond;

    private boolean firstRecharge;

    /** 额外送的道具（新加instantget里的值） */
    private List<SimpleItem> extItems;
    @Getter @Setter
    private long chargeToken;
    @Getter @Setter
    private String logDetailReason;
    @Getter @Setter
    private boolean useToken;

    public RechargeContext(LibaoMeta liBaoMeta, double dollar) {
        this.liBaoMeta = liBaoMeta;
        this.productId = liBaoMeta.getId();
        this.dollar = dollar;
        this.diamond = liBaoMeta.getDiamond();
        this.extDiamond = liBaoMeta.getExtDiamond();
        this.chargeToken = liBaoMeta.getGold();
    }

    /** 充值获得钻石总量 */
    public long getTotalDiamond(){
        return diamond + extDiamond + baseDoubleDiamond;
    }
    /** 充值获得可加入计入记录、计入活动的钻石总量 */
    public long getTotalEffectDiamond(){
        return diamond + extDiamond;
    }

    public List<SimpleItem> getRewards(){
        List<SimpleItem> rewards = new ArrayList<>();
        if(getTotalDiamond() > 0){
            SimpleItem diamondItem = new SimpleItem(String.valueOf(PsCurrency.DIAMOND.getValue()), (int)getTotalDiamond());
            rewards.add(diamondItem);
        }
        if(getChargeToken() >0){
            SimpleItem tokenItem = new SimpleItem(String.valueOf(PsCurrency.CHARGE_TOKEN.getValue()), (int) getChargeToken());
            rewards.add(tokenItem);
        }
        if(JavaUtils.bool(items)){
            rewards.addAll(items);
        }
        if(JavaUtils.bool(extItems)){
            rewards.addAll(extItems);
        }
        return rewards;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public LibaoMeta getLiBaoMeta() {
        return liBaoMeta;
    }

    public void setLiBaoMeta(LibaoMeta liBaoMeta) {
        this.liBaoMeta = liBaoMeta;
    }

    public double getDollar() {
        return dollar;
    }

    public void setDollar(double dollar) {
        this.dollar = dollar;
    }

    public List<SimpleItem> getItems() {
        return items;
    }

    public void setItems(List<SimpleItem> items) {
        this.items = items;
    }

    public long getDiamond() {
        return diamond;
    }

    public void setDiamond(long diamond) {
        this.diamond = diamond;
    }

    public long getExtDiamond() {
        return extDiamond;
    }

    public void setExtDiamond(long extDiamond) {
        this.extDiamond = extDiamond;
    }

    public long getBaseDoubleDiamond() {
        return baseDoubleDiamond;
    }

    public void setBaseDoubleDiamond(long baseDoubleDiamond) {
        this.baseDoubleDiamond = baseDoubleDiamond;
    }

    public boolean isFirstRecharge() {
        return firstRecharge;
    }

    public void setFirstRecharge(boolean firstRecharge) {
        this.firstRecharge = firstRecharge;
    }

    public List<SimpleItem> getExtItems() {
        return extItems;
    }

    public void setExtItems(List<SimpleItem> extItems) {
        this.extItems = extItems;
    }
}
