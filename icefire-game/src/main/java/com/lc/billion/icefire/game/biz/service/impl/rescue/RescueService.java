package com.lc.billion.icefire.game.biz.service.impl.rescue;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.SettingConfig;
import com.lc.billion.icefire.game.biz.config.setting.DefusalRewardConfig;
import com.lc.billion.icefire.game.biz.manager.RoleExtraManager;
import com.lc.billion.icefire.game.biz.manager.RoleItemManager;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.protocol.GcRescueProgress;
import com.lc.billion.icefire.protocol.GcRescueResult;
import com.lc.billion.icefire.protocol.GcRescueRewardResult;
import com.lc.billion.icefire.protocol.constant.PsRescueErrorCode;
import com.simfun.sgf.monitoring.annotation.Counted;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.lc.billion.icefire.game.support.LogReasons.ItemLogReason.RESCUE_CONSUME;
import static com.lc.billion.icefire.game.support.LogReasons.ItemLogReason.RESCUE_REWARD;

/**
 * 救援服务实现类
 */
@Service
public class RescueService {

    @Autowired
    private RoleExtraManager roleExtraManager;
    
    @Autowired
    private ConfigServiceImpl configService;
    
    @Autowired
    private ItemServiceImpl itemService;

    @Autowired
    private RoleItemManager itemManager;

    /**
     * 获取营救进度
     */
    @Counted(name = "rescue.progress.invocations", description = "获取救援进度调用次数")
    public GcRescueProgress getRescueProgress(Role role) {
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getRoleId());
        SettingConfig settingConfig = configService.getConfig(SettingConfig.class);
        DefusalRewardConfig config = settingConfig.getDefusalRewardConfig();
        
        GcRescueProgress response = new GcRescueProgress();
        response.setCurrent(roleExtra.getRescueProgress());
        
        if (config != null) {
            // 设置总进度
            response.setTotal(config.totalSubmissionsRequired());
            
            response.setRewards(List.of(config.rewardItem().toPsObject()));
        } else {
            response.setTotal(0);
            response.setRewards(List.of());
        }
        
        response.setIsReward(roleExtra.isRescueRewardReceived());
        
        return response;
    }

    /**
     * 执行营救
     */
    public GcRescueResult rescue(Role role) {
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getRoleId());
        SettingConfig settingConfig = configService.getConfig(SettingConfig.class);
        DefusalRewardConfig config = settingConfig.getDefusalRewardConfig();
        
        GcRescueResult response = new GcRescueResult();
        
        if (config == null) {
            response.setErrorCode(PsRescueErrorCode.CONDITION_NOT_MET);
            return response;
        }
        
        // 检查道具是否足够
        if (!itemManager.hasItem(role, config.consumeItem().getMetaId(), config.consumeItem().getCount(), false)) {
            response.setErrorCode(PsRescueErrorCode.CONDITION_NOT_MET);
            return response;
        }
        
        // 消耗道具
        itemManager.removeItem(role, config.consumeItem().getMetaId(), config.consumeItem().getCount(), RESCUE_CONSUME);
        
        // 增加营救进度
        roleExtra.setRescueProgress(roleExtra.getRescueProgress() + 1);
        roleExtraManager.saveRoleExtra(roleExtra);
        
        response.setErrorCode(PsRescueErrorCode.SUCCESS);
        response.setCurrent(roleExtra.getRescueProgress());
        
        return response;
    }

    /**
     * 获取营救奖励
     */
    public GcRescueRewardResult getRescueReward(Role role) {
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getRoleId());
        SettingConfig settingConfig = configService.getConfig(SettingConfig.class);
        DefusalRewardConfig config = settingConfig.getDefusalRewardConfig();
        
        GcRescueRewardResult response = new GcRescueRewardResult();
        
        // 检查是否已领取奖励
        if (roleExtra.isRescueRewardReceived()) {
            response.setErrorCode(PsRescueErrorCode.ALREADY_GET_REWARD);
            return response;
        }
        
        if (config == null) {
            response.setErrorCode(PsRescueErrorCode.CONDITION_NOT_MET);
            return response;
        }
        
        // 检查进度是否达到要求
        if (roleExtra.getRescueProgress() < config.totalSubmissionsRequired()) {
            response.setErrorCode(PsRescueErrorCode.CONDITION_NOT_MET);
            return response;
        }
        

        // 发放奖励
        itemService.give(role, config.rewardItem(), RESCUE_REWARD);
        
        // 标记已领取
        roleExtra.setRescueRewardReceived(true);
        roleExtraManager.saveRoleExtra(roleExtra);
        
        response.setErrorCode(PsRescueErrorCode.SUCCESS);
        response.setRewards(List.of(config.rewardItem().toPsObject()));
        
        return response;
    }

}
