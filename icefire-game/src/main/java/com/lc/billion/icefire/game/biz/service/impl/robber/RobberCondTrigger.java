package com.lc.billion.icefire.game.biz.service.impl.robber;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.unlock.UnlockCondRelationship;
import com.lc.billion.icefire.game.biz.service.impl.unlock.ConditionTrigger;

import java.util.List;

public class RobberCondTrigger extends ConditionTrigger {
    public RobberCondTrigger(long roleId, List<int[]> conds, UnlockCondRelationship relationship) {
        super(roleId, conds, relationship);
    }

    @Override
    public void run() {
        RobberServiceImpl robberService = Application.getBean(RobberServiceImpl.class);
        if (robberService == null) {
            return;
        }
        robberService.onCondTrigger(roleId);
    }
}
