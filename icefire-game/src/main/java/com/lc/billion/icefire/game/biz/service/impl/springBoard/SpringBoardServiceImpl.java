package com.lc.billion.icefire.game.biz.service.impl.springBoard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.config.CommunityLinkageSettingConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleExtraDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.IntegratedSpringBoardDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ServerInfoDao;
import com.lc.billion.icefire.game.biz.model.ServerInfo;
import com.lc.billion.icefire.game.biz.model.email.SystemEmail;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.model.springBoard.IntegratedSpringBoard;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.email.MailCreator;
import com.lc.billion.icefire.game.biz.service.impl.email.MailSender;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.GcIntegratedSpringboard;
import com.lc.billion.icefire.protocol.structure.PsIntegratedSpringboard;
import com.longtech.ls.config.ServerType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 游戏内集成弹板功能
 * <AUTHOR>
 */
@Service
public class SpringBoardServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(SpringBoardServiceImpl.class);

    @Autowired
    private IntegratedSpringBoardDao springBoardDao;
    @Autowired
    private ServerInfoDao serverInfoDao;
    @Autowired
    private RoleExtraDao roleExtraDao;
    @Autowired
    private MailCreator mailCreator;
    @Autowired
    private MailSender mailSender;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private DropServiceImpl dropService;

    /**
     * 给GM控制台提供的新增弹板方法
     * @param newSpringBoard 集成弹板的数据。从GM发过来的都是JSON对象。
     * @return 创建集成弹板的结果。返回true表示创建成功；返回false表示创建失败。
     */
    public boolean addNewSpringBoard(String newSpringBoard) {
        try {
            IntegratedSpringBoard newData = JSONObject.parseObject(newSpringBoard, IntegratedSpringBoard.class);
            if (newData.getId() == null || newData.getId() == 0) {
                springBoardDao.create(newData);
            } else {
                IntegratedSpringBoard oldData = springBoardDao.findById(newData.getId());
                // 更新参数
                oldData.setType(newData.getType());
                oldData.setPriority(newData.getPriority());
                oldData.setAnnouncementType(newData.getAnnouncementType());
                oldData.setBannerPath(newData.getBannerPath());
                oldData.setStartTime(newData.getStartTime());
                oldData.setEndTime(newData.getEndTime());
                oldData.setIconPriority(newData.getIconPriority());
                oldData.setBoardTitle(newData.getBoardTitle());
                oldData.setBoardContent(newData.getBoardContent());
                oldData.setShortTitle(newData.getShortTitle());
                oldData.setShortSubTitle(newData.getShortSubTitle());
                oldData.setBackgroundPicture(newData.getBackgroundPicture());
                oldData.setPrefabPath(newData.getPrefabPath());
                oldData.setButtonText(newData.getButtonText());
                oldData.setUiButtonName(newData.getUiButtonName());
                oldData.setPushTimeStart(newData.getPushTimeStart());
                oldData.setPushTimeEnd(newData.getPushTimeEnd());
                oldData.setPushFrequencyCondition(newData.getPushFrequencyCondition());
                oldData.setLink(newData.getLink());
                oldData.setShowCountdown(newData.getShowCountdown());
                oldData.setShowOpenTime(newData.getShowOpenTime());
                oldData.setCastleLevel(newData.getCastleLevel());
                oldData.setCountry(newData.getCountry());
                oldData.setServerIds(newData.getServerIds());
                oldData.setServerType(newData.getServerType());
                oldData.setTestId(newData.getTestId());
                oldData.setPlatformId(newData.getPlatformId());
                oldData.setSeason(newData.getSeason());
                oldData.setTest(newData.isTest());
                oldData.setRelease(newData.isRelease());
                oldData.setDeleted(newData.isDeleted());
                springBoardDao.save(oldData);
            }
            return true;
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("集成弹板:创建集成弹板失败", e);
            }
            return false;
        }
    }

    /**
     * 给玩家发送集成弹板消息
     * @param role 玩家信息
     * @param platformId 玩家的渠道信息
     */
    public void sendSpringBoard(Role role, String platformId) {
        // 待发送的弹板信息
        List<IntegratedSpringBoard> springBoardsToSend = new ArrayList<>();
        // 库里保存的所有弹板信息
        List<IntegratedSpringBoard> allSpringBoard = new ArrayList<>(springBoardDao.findAll());

        // 校验
        if (allSpringBoard.size() == 0) {
            logger.info("【集成弹板】：获取到的集成弹板列表大小为{}，因此直接返回，不给玩家发送任何消息", allSpringBoard);
            return;
        }
        List<Long> testSpringBoardList = new ArrayList<>();
        // 筛选符合要求的集成弹板
        springBoardsToSend = allSpringBoard.stream()
                .filter(springBoard -> !springBoard.isDeleted())
                .filter(springBoard -> !springBoard.isTest())
                .filter(IntegratedSpringBoard::isRelease)
                .filter(springBoard -> checkServerId(springBoard.getServerIds(), role.getCurrentServerId()))
                .filter(springBoard -> checkPlatformId(springBoard.getPlatformId(), platformId))
                .filter(springBoard -> checkServerType(springBoard.getServerType(), role.getCurrentServerId()))
                .filter(springBoard -> checkSeason(springBoard.getSeason(), role.getCurrentServerId()))
                .collect(Collectors.toList());


        // 根据玩家角色找一遍测试中的集成弹板，将这些弹板加入发送列表中，并将其状态改为未测试
        testSpringBoardList = allSpringBoard.stream()
                .filter(springBoard -> !springBoard.isDeleted())
                .filter(IntegratedSpringBoard::isTest)
                .filter(springBoard -> springBoard.getTestId() == role.getRoleId())
                .map(IntegratedSpringBoard::getId)
                .collect(Collectors.toList());

        for (long springBoardId : testSpringBoardList) {
            IntegratedSpringBoard temp = springBoardDao.findById(springBoardId);
            springBoardsToSend.add(temp);
            temp.setTest(false);
            springBoardDao.save(temp);
        }

        // 封装PsIntegratedSpringBoard对象
        List<PsIntegratedSpringboard> psSpringBoardList = new ArrayList<>();
        for (IntegratedSpringBoard eachSpringBoard : springBoardsToSend) {
            psSpringBoardList.add(packagePsInfo(eachSpringBoard));
        }

        // 给玩家发送集成弹板信息
        GcIntegratedSpringboard gcIntegratedSpringboard = new GcIntegratedSpringboard();
        gcIntegratedSpringboard.setSpringboards(psSpringBoardList);
        role.send(gcIntegratedSpringboard);
    }

    /**
     * 根据条件查询集成弹板的数据
     * @param condition 查询条件。0表示查询所有；1表示查询"推荐"；2表示查询"活动"；3表示查询"公告"
     * @return 将集成弹板的数据组成集合后，转成json字符串
     */
    public String getSpringBoardListByCondition(int condition) {
        List<IntegratedSpringBoard> springBoardList = new ArrayList<>();
        if (condition == 0) {
            springBoardList = new ArrayList<>(springBoardDao.getDatas().values());
        } else {
            springBoardList = springBoardDao.getDatas().values().stream()
                    .filter(integratedSpringBoard -> integratedSpringBoard.getAnnouncementType() == condition)
                    .collect(Collectors.toList());
        }
        if (springBoardList.size() > 0) {
            return JSON.toJSONString(springBoardList);
        } else {
            return "";
        }
    }

    /**
     * 判断玩家所在的服务器是否符合弹板的服务器ID配置
     * @param springBoardServerIds 弹板信息配置的服务器信息
     * @param roleCurrentServerId 玩家当前所在的服务器ID
     * @return true表示这个弹板应该在这个服务器弹出；false表示不应该弹出
     */
    private boolean checkServerId(String springBoardServerIds, int roleCurrentServerId) {
        logger.info("【集成弹板调试】：校验弹板的服务器配置{}，玩家当前服务器为：{}", springBoardServerIds, roleCurrentServerId);
        if (StringUtils.isBlank(springBoardServerIds) || springBoardServerIds.equals("-1")) {
            // 弹板在所有服务器都弹出
            return true;
        }
        try {
            String[] eachServerIds = springBoardServerIds.split(",");
            for (String serverId : eachServerIds) {
                if (serverId.equals("-1")) {
                    return true;
                }
                String[] eachServerId = serverId.split("-");
                if (eachServerId.length == 1 && eachServerId[0].equals(String.valueOf(roleCurrentServerId))) {
                    return true;
                } else if (eachServerId.length == 2) {
                    if (Integer.parseInt(eachServerId[0]) < roleCurrentServerId && roleCurrentServerId < Integer.parseInt(eachServerId[1])) {
                        return true;
                    }
                }
            }
        } catch (ExpectedException ignored) {

        } catch(Exception e) {
            ErrorLogUtil.exceptionLog("集成弹板:解析某个弹板的服务器配置时发生错误",e);
        }
        logger.info("【集成弹板调试】：服务器校验未通过：{}, {}", springBoardServerIds, roleCurrentServerId);
        return false;
    }

    /**
     * 判断用户的渠道是否符合集成弹板的配置
     * @param boardPlatform 集成弹板配置的渠道
     * @param rolePlatform 玩家的渠道信息
     * @return  true表示符合；false表示不符合
     */
    private boolean checkPlatformId(String boardPlatform, String rolePlatform) {
        logger.info("【集成弹板调试】：校验弹板的渠道配置{}，玩家当前渠道为：{}", boardPlatform, rolePlatform);
        if (StringUtils.isBlank(boardPlatform) || boardPlatform.equals("-1")) {
            return true;
        }
        try {
            String[] platforms = boardPlatform.split(";");
            for (String eachPlatform : platforms) {
                if (eachPlatform.equals("-1") || eachPlatform.equals(rolePlatform)) {
                    return true;
                }
            }
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("集成弹板:解析某个弹板的渠道配置时发生错误",e);
        }
        logger.info("【集成弹板调试】：渠道校验未通过：{}, {}", boardPlatform, rolePlatform);
        return false;
    }

    /**
     * 检查玩家当前服务器的类型是否满足弹板的配置
     * @param boardServerType 弹板配置的服务器类型信息
     * @param roleServerId 玩家当前所在的服务器ID
     * @return true表示符合，应当弹板；false表示不符合，不给玩家推送该弹板信息
     */
    private boolean checkServerType(String boardServerType, int roleServerId) {
        logger.info("【集成弹板调试】：校验弹板的服务器类型，配置为{}，玩家当前服务器ID为{}", boardServerType, roleServerId);
        if (StringUtils.isBlank(boardServerType)) {
            return true;
        }
        String[] serverTypes = boardServerType.split("\\|");
        ServerType currentType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(roleServerId);
        for (String serverType : serverTypes) {
            if (serverType.equals(String.valueOf(currentType.getId()))) {
                return true;
            }
        }
        logger.info("【集成弹板调试】：校验未通过，配置为{}，当前服务器类型为{}", boardServerType, currentType.getId());
        return false;
    }

    /**
     * 检查集成弹板配置的赛季信息是否与当前赛季相符
     * @param boardSeason 集成弹板中配置的赛季信息
     * @param currentServerId 玩家当前所在的服务器ID
     * @return true表示相符；false表示这个集成弹板不是当前赛季的
     */
    private boolean checkSeason(String boardSeason, int currentServerId) {
        logger.info("【集成弹板调试】：校验集成弹板的赛季信息配置，当前配置为{}，当前赛季为{}", boardSeason, currentServerId);
        if (StringUtils.isBlank(boardSeason) || boardSeason.equals("0")) {
            return true;
        }
        // 获取当前赛季信息
        ServerInfo serverInfo = serverInfoDao.findById((long) currentServerId);
        int currentSeason = serverInfo.getSeason();
        try {
            String[] seasons = boardSeason.split(",");
            // 校验
            for (String eachSeason : seasons) {
                String[] season = eachSeason.split("-");
                if (season[0].equals("0")) {
                    return true;
                }
                if (season.length == 1 && season[0].equals(String.valueOf(currentSeason))) {
                    return true;
                }
                if (season.length == 2) {
                    if (Integer.parseInt(seasons[0]) < currentSeason && currentSeason < Integer.parseInt(seasons[1])) {
                        return true;
                    }
                }
            }
        } catch (ExpectedException ignored) {

        } catch(Exception e) {
            ErrorLogUtil.exceptionLog("集成弹板:解析某个弹板的赛季配置时发生错误",e);
        }
        logger.info("【集成弹板调试】：赛季校验未通过，当前配置{}，当前赛季为{}", boardSeason, currentSeason);
        return false;
    }

    /**
     * 组装数据
     * @param integratedSpringBoard 数据库中捞出来的数据
     * @return 组装完之后要发给客户端的数据
     */
    private PsIntegratedSpringboard packagePsInfo(IntegratedSpringBoard integratedSpringBoard) {
        PsIntegratedSpringboard psIntegratedSpringboard = new PsIntegratedSpringboard();
        psIntegratedSpringboard.setType(integratedSpringBoard.getType());
        psIntegratedSpringboard.setPriority(integratedSpringBoard.getPriority());
        psIntegratedSpringboard.setAnnouncementType(integratedSpringBoard.getAnnouncementType());
        psIntegratedSpringboard.setBannerPath(integratedSpringBoard.getBannerPath());
        psIntegratedSpringboard.setCreateTime(integratedSpringBoard.getCreateTime());
        psIntegratedSpringboard.setStartTime(integratedSpringBoard.getStartTime());
        psIntegratedSpringboard.setEndTime(integratedSpringBoard.getEndTime());
        psIntegratedSpringboard.setIconPriority(integratedSpringBoard.getIconPriority());
        psIntegratedSpringboard.setBoardTitle(integratedSpringBoard.getBoardTitle());
        psIntegratedSpringboard.setBoardContent(integratedSpringBoard.getBoardContent());
        psIntegratedSpringboard.setShortTitle(integratedSpringBoard.getShortTitle());
        psIntegratedSpringboard.setShortSubTitle(integratedSpringBoard.getShortSubTitle());
        psIntegratedSpringboard.setBackgroundPicture(integratedSpringBoard.getBackgroundPicture());
        psIntegratedSpringboard.setPrefabPath(integratedSpringBoard.getPrefabPath());
        psIntegratedSpringboard.setButtonText(integratedSpringBoard.getButtonText());
        psIntegratedSpringboard.setUiButtonName(integratedSpringBoard.getUiButtonName());
        psIntegratedSpringboard.setPushTimeStart(integratedSpringBoard.getPushTimeStart());
        psIntegratedSpringboard.setPushTimeEnd(integratedSpringBoard.getPushTimeEnd());
        psIntegratedSpringboard.setPushFrequencyCondition(integratedSpringBoard.getPushFrequencyCondition());
        psIntegratedSpringboard.setPushIntervalCondition(integratedSpringBoard.getPushIntervalCondition());
        psIntegratedSpringboard.setLink(integratedSpringBoard.getLink());
        psIntegratedSpringboard.setShowCountdown(integratedSpringBoard.getShowCountdown());
        psIntegratedSpringboard.setShowOpenTime(integratedSpringBoard.getShowOpenTime());
        psIntegratedSpringboard.setCastleLevel(integratedSpringBoard.getCastleLevel());
        psIntegratedSpringboard.setCountry(integratedSpringBoard.getCountry());
        psIntegratedSpringboard.setId(integratedSpringBoard.getId());
        return psIntegratedSpringboard;
    }

    /**
     * 【集成弹板】执行操作
     * @param id 被操作的弹板ID
     * @param actionType 1表示测试；2表示发布；3表示删除
     * @return 操作结果
     */
    public String action(long id, int actionType) {
        IntegratedSpringBoard data = springBoardDao.findById(id);
        if (data == null) {
            return "false";
        }
        if (actionType == 1) {
            // 测试
            if (data.getTestId() != 0) {
                data.setTest(true);
                springBoardDao.save(data);
            } else {
                return "没有测试账号";
            }

        } else if (actionType == 2) {
            // 发布
            data.setRelease(true);
            springBoardDao.save(data);

        } else if (actionType == 3) {
            // 删除
            data.setDeleted(true);
            springBoardDao.save(data);
        }
        return "success";
    }

    /**
     * 继承弹板领奖。该领奖由客户端发送请求，是否有资格领奖的逻辑是在H5页面上，服务端这边只校验玩家领奖的次数以及领取时间。
     * @param role 领奖的玩家
     * @param key 奖励对应的key
     */
    public void springBoardReward(Role role, String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        RoleExtra roleExtra = roleExtraDao.findById(role.getRoleId());
        // 从配表中获取奖励key对应的最大领取次数、邮件key、奖励内容key以及活动时间等参数
        CommunityLinkageSettingConfig config = configService.getConfig(CommunityLinkageSettingConfig.class);
        CommunityLinkageSettingConfig.CommunityLinkageSettingMeta meta = config.getMetaByKey(key);
        // 对获取的配置的校验还需要补全
        if (meta == null || StringUtils.isBlank(meta.getMail()) || StringUtils.isBlank(meta.getTimeRange())
                || StringUtils.isBlank(meta.getReward())) {
            logger.info("【集成弹板】：玩家{}领取奖励{}失败，因为有一些相关的配置为空。", role.getRoleId(), key);
            return;
        }
        // 校验玩家的领取时间是否符合要求
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startTime = sdf.parse(meta.getTimeRange().split("\\|")[0]);
            Date endTime = sdf.parse(meta.getTimeRange().split("\\|")[1]);
            long currentTime = TimeUtil.getNow();
            if (currentTime < startTime.getTime() || currentTime > endTime.getTime()) {
                logger.info("【集成弹板】：玩家{}领奖{}不符合时间要求，当前时间{}，奖励时间段：{}->{}。", role.getRoleId(),
                        key, TimeUtil.parseTime2UtcStr(currentTime), startTime.getTime(), endTime.getTime());
                return;
            }
        } catch(Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("集成弹板:奖励生效的时间格式不符合要求", e, "timeRange", meta.getTimeRange());
            }
            return;
        }
        if (roleExtra.getSpringBoardReward() == null
                || roleExtra.getSpringBoardReward().get(key) == null
                || roleExtra.getSpringBoardReward().get(key) < meta.getReceiveCount()) {
            // 玩家没有领取过奖励，或者领取过奖励的次数小于次数限制
            // 根据key从奖励表中获取奖励内容
            List<SimpleItem> itemList = dropService.drop(meta.getReward());
            if (itemList == null || itemList.size() == 0) {
                logger.info("【集成弹板】：玩家{}领取{}奖励对应的物品列表为空", role.getRoleId(), key);
                return;
            }
            // 给玩家发奖
            SystemEmail systemEmail = mailCreator.createSystemMail(
                    role.getRoleId(),
                    role.getCurrentServerId(),
                    meta.getMail(),
                    itemList,
                    null,
                    false,
                    LogReasons.ItemLogReason.SPRING_BOARD_REWARD
            );

            if (systemEmail != null) {
                mailSender.sendOneMail(systemEmail);
            } else {
                ErrorLogUtil.errorLog("mail构建邮件失败", "roleId",role.getRoleId());
            }

            logger.info("[集成弹板]：玩家{}领取{}奖励。", role.getRoleId(), key);
            if (roleExtra.getSpringBoardReward() == null) {
                roleExtra.setSpringBoardReward(new HashMap<>());
            }
            roleExtra.getSpringBoardReward().compute(key, (k, v) -> v == null ? 1 : v + 1);
            roleExtraDao.save(roleExtra);
        } else {
            logger.info("[集成弹板]：玩家{}领取{}奖励{}次，超过上限{}，领取失败", role.getRoleId(), key,
                    roleExtra.getSpringBoardReward().get(key), meta.getReceiveCount());
        }
    }

}
