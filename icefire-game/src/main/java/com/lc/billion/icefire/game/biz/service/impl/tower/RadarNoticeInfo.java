package com.lc.billion.icefire.game.biz.service.impl.tower;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 雷达 通知枚举类 editor: bugyang date: 2019/10/14
 */
public enum RadarNoticeInfo {

	NOTHING(0, null), // 基本信息
	BASE_INFO(1, null), // 攻击发起方的姓名和坐标
	BASE_INFO_TARGET(2, null), // 攻击发起方的攻击目标（基地、采集中的资源田、大世界建筑、防守的王城等）类型 和 坐标
	BASE_INFO_TIME(3, null), // 攻击发起方队伍到达时间 (看前端算不算)
	BASE_INFO_PROFESSION(4, null), // 攻击发起方的职业

	FRIEND_INFO(5, null, false), // 友方向玩家行军的队伍信息（集结/援助）（所有信息）//TODO

	TROOP_TOTAL_NUM(6, null), // 攻击发起方的部队总数（车上所有病）
	TROOP_UNIT_ID(7, Group.TROOP_INFO), // 攻击发起方部队各小队的兵种信息（3个unit的id）
	HERO_INFO_ID(8, Group.HERO_INFO), // 攻击发起方部队各小队的英雄名称、品质、头像信息（英雄id）
	RALLY_HERO_INFO_ID(9, Group.RALLY_HERO_INFO), // 攻击发起方集结的部队（如果有）各小队的英雄名称、品质、头像信息
	TROOP_UNIT_NUM(10, Group.TROOP_INFO), // 攻击发起方部队各小队的士兵数量信息 （士兵数量）
	MISSILE_INFO(11, null), // 飞弹剩余到达时间（暂无）//TODO
	HERO_INFO_SKILL(12, Group.HERO_INFO), // 攻击发起方部队各小队的英雄等级、技能解锁情况、技能等级信息
	RALLY_HERO_INFO_SKILL(13, Group.RALLY_HERO_INFO), // 攻击发起方集结的部队（如果有）各小队的英雄等级、技能解锁情况、技能等级信息

	;

	private int lv;
	private Group group;
	private boolean isEnemy;

	RadarNoticeInfo(int lv, Group group) {
		this.lv = lv;
		this.group = group;
		this.isEnemy = true;
	}

	RadarNoticeInfo(int lv, Group group, boolean isEnemy) {
		this.lv = lv;
		this.group = group;
		this.isEnemy = isEnemy;
	}

	public static Collection<RadarNoticeInfo> getByProp(int propLevel) {

		Map<Integer, RadarNoticeInfo> groupTypeMap = new LinkedHashMap<>();
		int startNullPos = 10000000;
		for (RadarNoticeInfo noticeInfo : values()) {
			if (propLevel < noticeInfo.lv) {
				break;
			}
			if (noticeInfo.group == null) {
				groupTypeMap.put(startNullPos++, noticeInfo);
			} else {
				groupTypeMap.put(noticeInfo.group.ordinal(), noticeInfo);
			}
		}
		return groupTypeMap.values();
	}

	private enum Group {
		TROOP_INFO, HERO_INFO, RALLY_HERO_INFO,;
	}

}
