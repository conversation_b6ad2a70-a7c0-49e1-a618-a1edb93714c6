package com.lc.billion.icefire.game.biz.service.impl.worldExploreEvent;

import com.happysky.misc.Time;
import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.graph.MapUtil;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.exploreEvent.WorldExploreOperation;
import com.lc.billion.icefire.game.biz.config.CaravanAIConfig;
import com.lc.billion.icefire.game.biz.config.ExploreEventCommonLvConfig;
import com.lc.billion.icefire.game.biz.config.ExploreEventCommonLvConfig.ExploreEventCommonLvMeta;
import com.lc.billion.icefire.game.biz.config.ExploreEventConfig;
import com.lc.billion.icefire.game.biz.config.SettingConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.PlayerWorldExploreEventDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.PlayerWorldExploreRecordDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RegionCapitalNodeDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.RoleCityManager;
import com.lc.billion.icefire.game.biz.manager.WorldExploreEventManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.city.BuildingGroup;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.scene.MapData;
import com.lc.billion.icefire.game.biz.model.scene.MapGrid;
import com.lc.billion.icefire.game.biz.model.scene.MapGridLoopIterator;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.game.biz.model.worldExploreEvent.*;
import com.lc.billion.icefire.game.biz.service.ScheduleService;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.migrate.MigrateService;
import com.lc.billion.icefire.game.biz.service.impl.milestone.MilestoneService;
import com.lc.billion.icefire.game.biz.service.impl.push.PushHelper;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.unlock.UnlockServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.worldarea.WorldAreaService;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.snspush.StringUtils;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.UnlockModule;
import com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent;
import com.lc.billion.icefire.protocol.structure.PsSimpleItem;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * @author: huyafei
 * @Date: 2021/8/5 5:41 下午
 * @Description: 世界探索事件业务处理类
 **/
@Service
public class WorldExploreEventServiceImpl {
    public static final Logger logger = LoggerFactory.getLogger(WorldExploreEventServiceImpl.class);

    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private WorldExploreEventManager worldExploreEventManager;
    @Autowired
    private PlayerWorldExploreEventDao playerWorldExploreEventDao;
    @Autowired
    private FunctionSwitchService functionSwitchService;
    @Autowired
    private WorldServiceImpl worldService;
    @Autowired
    private SceneServiceImpl sceneService;
    @Autowired
    private WorldAreaService worldAreaService;
    @Autowired
    private MilestoneService milestoneService;
    @Autowired
    private RoleCityManager roleCityManager;
    @Autowired
    private PlayerWorldExploreRecordDao playerWorldExploreRecordDao;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private AllianceDao allianceDao;
    @Autowired
    private MigrateService migrateService;
    @Autowired
    private UnlockServiceImpl unlockService;
    @Autowired
    private ServiceDependency srvDep;
    @Autowired
    private PushHelper pushHelper;
    @Autowired
    private ArmyManager armyManager;
    @Autowired
    private RegionCapitalNodeDao regionCapitalNodeDao;

    private List<Long> refreshTimes = new ArrayList<>();

    /**
     * 查找点的时候优先在附近的点，不去随机
     */
    private final boolean findNearbyPoint = false;

    private static final String LOGIN_PREFIX = "login_";
    private static final String LOW_TICK_PREFIX = "lowTick_";
    private static final String PAY_PREFIX = "pay_";
    private static final String BOSS_META_ID = "400000";

    @PostConstruct
    public void init() {
    }

    public void updateRefreshTimes() {
        refreshTimes = configService.getConfig(SettingConfig.class).getExploreEventRefreshTimes();
        StringBuilder info = new StringBuilder("refreshTimes: ");
        for (var time : refreshTimes) {
            info.append(TimeUtil.formatTime(time, "[MM-dd HH:mm] "));
        }
        logger.info(info.toString());
    }

    public void onZero() {
        updateRefreshTimes();
    }

    public void startService() {
        // 更新刷新间隔
        updateRefreshTimes();
    }

    public long[] getExploreEventRefreshTime() {
        long[] result = {0, 0};
        long time = Time.now();
        for (int i = 0; i < refreshTimes.size() - 1; i++) {
            var start = refreshTimes.get(i);
            var end = refreshTimes.get(i + 1);
            if (time >= start && time <= end) {
                result[0] = start;
                result[1] = end;
                return result;
            }
        }

        return result;
    }

    public long getLastRefreshTime() {
        var refreshTime = getExploreEventRefreshTime();
        return refreshTime[0];
    }

    public void onSettingConfigChange() {
        logger.info("onSettingConfigChange");

        // 更新刷新间隔
        updateRefreshTimes();

        // 处理开关和已删除
        Collection<Role> roles = roleDao.findAll();
        for (Role role : roles) {
            if (!role.isOnline()) {
                continue;
            }
            PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
            if (record == null) {
                return;
            }
            // 发送一个空列表 只更新列表外其他信息
            sendEventList(role, null);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 17:45:59.868
     * @Param []
     * @Return boolean
     * @Description 判断当前服务器，是否需要发送探索事件的消息
     */
    public boolean checkServerType() {
        return Application.getServerType() == ServerType.GAME || Application.getServerType() == ServerType.KVK_SEASON;
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 11:41:23.624
     * @Param [role]
     * @Return void
     * @Description 初始化 创建逻辑只会执行一次
     */
    public void initExploreRecord(Role role) {
        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            logger.info("create playerWorldExploreRecord roleId:{}", role.getRoleId());
            playerWorldExploreRecordDao.create(role);
        }
    }

    public void onEnterWorld(Role role) {
        // 只在当前服务器处理
        if (!checkServerType()) {
            return;
        }

        // 功能开关
        if (!functionSwitchService.isOpen(FunctionType.WORLD_EVENT_FUNTION.getId(), role)) {
            return;
        }

        // 功能是否解锁
        if (!unlockService.isUnlock(role, UnlockModule.Watchtower)) {
            return;
        }

        // 只会初始化一次
        initExploreRecord(role);

        // 清除过期事件 清除不可用事件（配置错误）
        clearExpiredAndInvalidEvent(role);

        // 检测玩家坐标是否改变
        checkCityPosition(role);

        // bugfix 解决侦查中立建筑无法进行的问题
        // 原因为州府坐标改变
        // 处理方式：刷新一下侦查事件的位置
        var events = playerWorldExploreEventDao.findByRoleId(role.getRoleId());
        for (var event : events) {
            if (event.getLinkType() != EventType.EVENT_RECON.getId()) {
                continue;
            }
            var points = findPointsCapital(role, 1);
            if (points.size() == 1) {
                playerWorldExploreEventDao.updataWorldExploreEventPoint(event, points.get(0));
            }
        }

        // 刷新事件
        refreshEventAfterLogin(role, o -> pushAllEventsForShowing(role));

        // 发送事件列表
        sendEventListAfterLogin(role);
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 11:43:58.761
     * @Param [role]
     * @Return void
     * @Description 处理玩家被打飞了，但是打飞了没有坐标，等下次登入游戏，重新赋值坐标之后事件坐标点需要改变一下
     */
    public void checkCityPosition(Role role) {
        if (role == null) {
            return;
        }

        RoleCity city = roleCityManager.getRoleCity(role.getPersistKey());
        if (city == null) {
            return;
        }

        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            return;
        }

        if (record.getPoint() == null) {
            record.setPoint(city.getPosition());
            playerWorldExploreRecordDao.save(record);
        } else if (!record.getPoint().equals(city.getPosition())) {
            // 坐标改变，同迁城业务一样处理
            record.setPoint(city.getPosition());
            worldExploreEventManager.playerMoveCityHandler(role);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 14:08:17.821
     * @Param [role, consumer]
     * @Return void
     * @Description 登录后处理刷新 根据离线时长折算
     */
    public void refreshEventAfterLogin(Role role, Consumer<Object> consumer) {
        if (!functionSwitchService.isOpen(FunctionType.WORLD_EVENT_FUNTION.getId(), role)) {
            return;
        }

        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            return;
        }

        var lastRefreshTime = record.getLastRefreshTime();
        if (lastRefreshTime == 0) {
            return;
        }

        // 找到错过的刷新点
        SettingConfig config = configService.getConfig(SettingConfig.class);
        var maxOfflineRefreshTimes = 2;
        List<Long> refreshTimeMissed = new ArrayList<Long>();
        for (int i = 0; i < refreshTimes.size() - 1; i++) {
            var refreshTime = refreshTimes.get(i);
            if (lastRefreshTime < refreshTime && refreshTime < Time.now()) {
                refreshTimeMissed.add(refreshTime);
            }
        }

        // 只留最后两个刷新点
        while (refreshTimeMissed.size() > maxOfflineRefreshTimes) {
            refreshTimeMissed.remove(0);
        }

        StringBuilder refreshTimeMissedStr = new StringBuilder();
        for (var time : refreshTimeMissed) {
            refreshTimeMissedStr.append(TimeUtil.formatTime(time, "[MM-dd HH:mm] "));
        }

        logger.info("refreshEventAfterLogin roleId:{},lastLogoutTime:{},lastRefreshTime:{},refreshTimeMissed:{}",
                role.getRoleId(),
                TimeUtil.formatTime(role.getLastLoginTime()),
                TimeUtil.formatTime(lastRefreshTime),
                refreshTimeMissedStr.toString());

        // 根据错过的刷新节点进行刷新
        for (var refreshTime : refreshTimeMissed) {
            refreshPlainEvent(EventTriggerType.LOGIN_HANDLER_PLAIN_EVENT_FIXED_TIME_REFRESH_PLAIN, LOGIN_PREFIX, role, refreshTime, consumer);
        }

        // 如果错过刷新节点 重置boss刷新次数
        if (!refreshTimeMissed.isEmpty()) {
            record.setBossRefreshCount(0);
            playerWorldExploreRecordDao.save(record);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 17:48:58.686
     * @Param [role]
     * @Return void
     * @Description 登录进入处理 发送事件列表 玩家登陆设置标记都为false 下发的几个设置为true
     */
    public void sendEventListAfterLogin(Role role) {
        if (!functionSwitchService.isOpen(FunctionType.WORLD_EVENT_FUNTION.getId(), role)) {
            return;
        }

        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            return;
        }

        var allEventList = worldExploreEventManager.findAllPlayerWorldExploreEvent(role);
        if (allEventList == null || allEventList.isEmpty()) {
            // 即使列表为空也要推送，因为需要下发下次刷新时间
            sendEventList(role, allEventList);
            return;
        }

        // 初始化为不发给客户端
        allEventList.forEach(e -> e.setSendClient(false));
        // 需要显示的事件
        var listForShowing = worldExploreEventManager.findAllShowPlayerWorldExploreEvent(role, record.getEventLevel());
        // 下发给客户端的事件设置为true
        listForShowing.forEach(e -> e.setSendClient(true));

        // 发送列表
        sendEventList(role, listForShowing);
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 18:05:26.697
     * @Param [role, eventList]
     * @Return void
     * @Description 发送列表
     */
    public void sendEventList(Role role, List<PlayerWorldExploreEvent> eventList) {
        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            return;
        }

        // 即使列表为空也要推送，因为需要下发下次刷新时间
        var message = buildGcPlayerWorldExploreRecord(ErrorCodeStatus.SUCCESS, record, eventList, worldExploreEventManager.getAttackNpcMaxLevel(role));
        role.send(message);
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-18 04:15:04.456
     * @Param [role]
     * @Return void
     * @Description 给玩家推送所有显示的事件 发送单个事件
     */
    public void pushAllEventsForShowing(Role role) {
        if (role == null || !role.isOnline()) {
            return;
        }

        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            return;
        }

        List<PlayerWorldExploreEvent> listForShowing = worldExploreEventManager.findAllShowPlayerWorldExploreEvent(role, record.getEventLevel());
        if (listForShowing == null || listForShowing.isEmpty()) {
            return;
        }

        // 只发送未发送过得事件
        listForShowing.forEach(e -> {
            if (e.isSendClient()) {
                return;
            }

            e.setSendClient(true);
            role.send(buildGcAddPlayerWorldExploreEvent(e));
        });
    }

    /**
     * <AUTHOR>
     * @Date 2024-03-13 15:32:51.866
     * @Param [role]
     * @Return int
     * @Description 获取玩家探索事件等级
     */
    public int getEventExploreLevel(Role role) {
        return worldExploreEventManager.getPlayerEventLevel(role);
    }

    /**
     * 玩家在内存，且处于迁服状态
     *
     * @param role
     * @return
     */
    public boolean isMigrating(Role role) {
        if (role == null || role.getId() == null) {
            return false;
        }

        if (roleDao.findById(role.getId()) == null) {
            return false;
        }

        return migrateService.isRoleInMigrating(role.getId());
    }

    /**
     * <AUTHOR>
     * @Date 2024-03-19 02:43:35.629
     * @Param [role, mapGrid]
     * @Return boolean
     * @Description 检查地块是否可用来生成事件
     */
    private boolean gridValidCheck(Role role, MapGrid mapGrid) {
        if (worldExploreEventManager.eventPointExists(role.getCurrentServerId(), role.getRoleId(), mapGrid.getPosition())) {
            return false;
        }

        var key = "worldExplore";
        if (!Application.getFixData().containsKey(key)) {
            // todo 解决玩家过于集中烽火台事件无法刷新问题
            if (mapGrid.isBlock()) {
                return false;
            }

            Point position = mapGrid.getPosition();
            SceneNode sceneNode = sceneService.getSceneNode(role.getCurrentServerId(), position);
            if (sceneNode != null) {
                return false;
            }
        } else {
            if (!sceneService.canBirthForNpcAndResourceAndBoss(role.getCurrentServerId(), mapGrid)) {
                return false;
            }
        }

        if (!mapGrid.isRefreshNpc()) {
            return false;
        }

        if (!worldAreaService.canEnterArea(role.getCurrentServerId(), mapGrid.getPosition().getX(), mapGrid.getPosition().getY())) {
            return false;
        }
        if (!srvDep.getKvkSeasonService().isCanEnterArea(role.getoServerId(), role.getCurrentServerId(), mapGrid.getPosition().getX(), mapGrid.getPosition().getY())) {
            return false;
        }

        return true;
    }

    /**
     * <AUTHOR>
     * @Date 2024-03-25 19:58:02.895
     * @Param [point, center, range]
     * @Return boolean
     * @Description 判断点是否在 以center为中心 半径range的区域中
     */
    private boolean isInRegin(Point point, Point center, int range) {
        return isInRegin(point, center, range, range);
    }

    private boolean isInRegin(Point point, Point center, int rangeX, int rangeY) {
        if (point.getX() < center.getX() - rangeX || point.getX() > center.getX() + rangeX) {
            return false;
        } else if (point.getY() < center.getY() - rangeY || point.getY() > center.getY() + rangeY) {
            return false;
        }

        return true;
    }

    public List<Point> findPointsInRegion(Role role, int[] refreshRange, int num) {
        return findPointsInRegion(role, refreshRange, num, false);
    }

    public List<Point> findPointsInRegion(Role role, int[] refreshRange, int num, boolean isCapital) {
        List<Point> points = new ArrayList<>();
        List<Point> nodesResult = new ArrayList<>();

        // 如果查找州府地址
        if (isCapital) {
            return findPointsCapital(role, num);
        }

        RoleCity city = roleCityManager.getRoleCity(role.getPersistKey());
        if (city == null || city.getX() < 0 || city.getY() < 0) {
            return points;
        }

        return findPointsInRegion(role, city.getPosition(), refreshRange[0], refreshRange[1], num);
    }

    private List<Point> findPointsCapital(Role role, int num) {
        RoleCity city = roleCityManager.getRoleCity(role.getRoleId());
        List<RegionCapitalNode> targetList = new ArrayList<>();
        List<Point> targetPointList = new ArrayList<>();
        var nodes = regionCapitalNodeDao.findByCurrentServerId(role.getCurrentServerId());
        for (var node : nodes) {
            if (worldExploreEventManager.eventPointExists(role.getCurrentServerId(), role.getRoleId(), node.getPosition())) {
                continue;
            }

            if (!worldAreaService.canEnterArea(role.getCurrentServerId(), node.getPosition().getX(), node.getPosition().getY())) {
                continue;
            }
            if (!srvDep.getKvkSeasonService().isCanEnterArea(role.getoServerId(), role.getCurrentServerId(), node.getPosition().getX(), node.getPosition().getY())) {
                continue;
            }
            targetList.add(node);
        }

        targetList.sort((node1, node2) -> {
            var point1 = node1.getPosition();
            var point2 = node2.getPosition();
            if (node1.getType().equals(node2.getType())) {
                var distance1 = MapUtil.getDistance(point1.toPosition(), city.getPosition().toPosition());
                var distance2 = MapUtil.getDistance(point2.toPosition(), city.getPosition().toPosition());
                return (int) (distance1 - distance2);
            }

            return node2.getType().getValue() - node1.getType().getValue();
        });

        targetList = targetList.subList(0, num);
        targetList.forEach(node -> {
            targetPointList.add(node.getPosition());
        });

        return targetPointList;
    }

    private List<Point> findPointsInRegion(Role role, Point center, int radiusLow, int radiusHigh, int num) {
        logger.info("findPointsInRegion roleId:{} center:{} radius:{}-{} num:{}", role.getRoleId(), center, radiusLow, radiusHigh, num);
        MapData mapData = worldService.getWorld().getMapData(role.getCurrentServerId());
        List<Point> points = new ArrayList<>();

        // 取范围内的点
        Iterator<MapGrid> npcFreshItr = new MapGridLoopIterator(mapData, center.getX(), center.getY(), radiusHigh);
        while (npcFreshItr.hasNext()) {
            MapGrid mapGrid = npcFreshItr.next();
            var target = mapGrid.getPosition();

            // 在小范围内跳过
            if (isInRegin(target, center, radiusLow)) {
                continue;
            }

            // 不合法跳过
            if (!gridValidCheck(role, mapGrid)) {
                continue;
            }

            // 已存在事件跳过
            if (worldExploreEventManager.eventPointExists(role.getCurrentServerId(), role.getRoleId(), target)) {
                continue;
            }

            points.add(target);
        }

        // 如果点不够则扩大一个区块再找 最多找到30格
        if (radiusHigh >= 30) {
            logger.warn("findPointsInRegion area out of limit, roleId:{} center:{} radius:{}-{} num:{} pointsCount:{}", role.getRoleId(), center, radiusLow, radiusHigh, num, points.stream().count());
        } else if (points.isEmpty() || points.size() < num) {
            return findPointsInRegion(role, center, radiusLow, radiusHigh + 1, num);
        }

        // 随机查找 num个
        var randomList = RandomUtils.randomItemList(points, num);
        logger.info("findPointsInRegion roleId:{} center:{} radius:{}-{} num:{} points:{}", role.getRoleId(), center, radiusLow, radiusHigh, num, randomList);
        return randomList;
    }

    public String display(Role role) {
        long totalEventNum = playerWorldExploreEventDao.getTotalEventNum();
        long totalEventNum2 = playerWorldExploreEventDao.getTotalEventNum2();
        String allEventIdJoin = playerWorldExploreEventDao.getAllEventIdJoin();
        String allEventIdJoin2 = playerWorldExploreEventDao.getAllEventIdJoin2();
        ConcurrentHashMap<Point, PlayerWorldExploreEvent> eventResourceZoneManagerMap = playerWorldExploreEventDao.getEventResourceZoneManagerMap(role.getCurrentServerId(), role.getRoleId());
        List<Point> collect = null;
        int size = 0;
        if (eventResourceZoneManagerMap != null && !eventResourceZoneManagerMap.isEmpty()) {
            collect = new ArrayList<>(eventResourceZoneManagerMap.keySet());
            size = collect.size();
        }
        StringBuilder _str = new StringBuilder();
        if (collect != null && !collect.isEmpty()) {
            collect.forEach(e -> _str.append(e.toString()).append(","));
        }
        return String.format("  event pointSize:%s, pointList:%s,totalEventNum:%s,totalEventNum2:%s,allEventIdJoin.equalsIgnoreCase(allEventIdJoin2):%s",
                size, _str.toString(), totalEventNum, totalEventNum2, allEventIdJoin.equalsIgnoreCase(allEventIdJoin2));
    }

    /**
     * 低频心跳
     * 这个是在RoleTick里面处理的目前是
     * 具体到RoleServerImpl  每次隔5秒调1次
     * 自己需要这个，需要去具体到RoleServerImpl.lowTick 去处理一下
     * 如果要判断 任务事件倒计时，可以每次只判断一个，这样减轻此处调佣时间
     *
     * @param role
     * @param now
     */
    public void tick(Role role, long now) {
        try {
            // 只在当前服务器处理
            if (!checkServerType()) {
                return;
            }
            if (Application.getConfigCenter().isNewPlayerGameServer()) {
                return;
            }
            // 定时刷新普通事件 有开关控制
            if (!functionSwitchService.isOpen(FunctionType.WORLD_EVENT_FUNTION.getId(), role)) {
                return;
            }

            // 离线不刷新
            if (!role.isOnline()) {
                return;
            }

            // 功能是否解锁
            if (!unlockService.isUnlock(role, UnlockModule.Watchtower)) {
                return;
            }

            //刷新boss
            refreshBossEvent(role, EventTriggerType.NORMAL_REFRESH_BOSS_EVENT.getOriginDesc());

            // 清除过期事件 清除不可用事件（配置错误）
            clearExpiredAndInvalidEvent(role);

            // 根据玩家当前事件等级去找合适的配置
            refreshPlainEventInLowTick(role, now, o -> {
                // 给玩家推送所有事件
                pushAllEventsForShowing(role);
            });
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("Error-lowTick" ,e);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 11:34:54.204
     * @Param [role]
     * @Return void
     * @Description 清理玩家过期及不可用的事件 事件完成但未领取时，事件永久存在
     */
    public void clearExpiredAndInvalidEvent(Role role) {
        var now = TimeUtil.getNow();
        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            return;
        }

        List<PlayerWorldExploreEvent> eventList = worldExploreEventManager.findAllPlayerWorldExploreEvent(role);
        if (eventList == null || eventList.isEmpty()) {
            return;
        }

        var armys = armyManager.findByRoleId(role.getRoleId());
        var nodes = new ArrayList<>();
        if (armys != null && !armys.isEmpty()) {
            armys.forEach(armyInfo -> {
                nodes.add(armyInfo.getTargetNode());
            });
        }

        List<PlayerWorldExploreEvent> cacheEList = new ArrayList<>();
        List<PlayerWorldExploreEvent> deleteList = new ArrayList<>();
        ExploreEventConfig config = configService.getConfig(ExploreEventConfig.class);
        eventList.forEach(e -> {
            if (playerWorldExploreEventDao.findById(e.getId()) == null) {
                cacheEList.add(e);
                return;
            }
            if (e.getDropList().isEmpty()) {
                ErrorLogUtil.errorLog("clearExpiredAndInvalidEvent getDropList is empty", "roleId",role.getRoleId(), "eventId",e.getEventId(), "id",e.getId());
                deleteList.add(e);
                return;
            }

            var meta = config.getById(e.getEventId());
            if (meta == null) {
                ErrorLogUtil.errorLog("clearExpiredAndInvalidEvent meta is null","roleId",role.getRoleId(), "eventId",e.getEventId(), "id",e.getId());
                deleteList.add(e);
                return;
            }

            //事件完成但未领取时，事件永久存在
            if (e.getStatus() != EventStatus.INIT.getId()) {
                return;
            }

            // 已领奖
            if (e.isAward()) {
                ErrorLogUtil.errorLog("clearExpiredAndInvalidEvent event award issued", "roleId",role.getRoleId(), "eventId",e.getEventId(), "id",e.getId());
                deleteList.add(e);
                return;
            }

            // 是army的目标
            if (nodes.contains(e)) {
                return;
            }

            // 未过期
            if ((e.getEndTime() <= 0 || now < e.getEndTime())) {
                // 非主线事件
                worldExploreEventManager.checkAssistCompleted(role, e, deleteList);
                return;
            }

            // 已过期删除并记录日志
            logger.info("clearExpiredAndInvalidEvent event expired, roleId:{},eventId:{},id:{},endTime:{}", role.getRoleId(), e.getEventId(), e.getId(), TimeUtil.formatTime(e.getEndTime()));
            deleteList.add(e);
        });

        // 删除事件
        if (!deleteList.isEmpty()) {
            deleteList.forEach(e -> {
                worldExploreEventManager.deletePlayerWorldExploreEvent(e, role.getRoleId(), true);
            });
        }
        // 处理内存不一致的事件
        if (!cacheEList.isEmpty()) {
            for (PlayerWorldExploreEvent event : cacheEList) {
                playerWorldExploreEventDao.delCacheEvent(event);
                if (role.isOnline()) {
                    role.send(buildGcDeletePlayerWorldExploreEvent(event.getPersistKey().toString()));
                }
                ErrorLogUtil.warnLog("clearExpiredAndInvalidEvent event cache error", "roleId", role.getRoleId(), "eventId", event.getEventId(), "id", event.getId(), "endTime", TimeUtil.formatTime(event.getEndTime()));
            }
        }
    }

        /**
     * 付费刷新烽火台事件
     * @Param runtimeParams [LiBaoContext libaoContext, RoleLibaoRecord record]
     *
     */
    public void onBuy(Role role) {
        try {
            // 只在当前服务器处理
            if (!checkServerType()) {
                return ;
            }
            // 功能开关
            if (!functionSwitchService.isOpen(FunctionType.WORLD_EVENT_FUNTION.getId(), role)) {
                return ;
            }
            // 功能是否解锁
            if (!unlockService.isUnlock(role, UnlockModule.Watchtower)) {
                return ;
            }

            // 清除过期事件 清除不可用事件（配置错误）
            clearExpiredAndInvalidEvent(role);

            refreshPlainEventInPay(role, o -> {
                // 给玩家推送所有事件
                pushAllEventsForShowing(role);
            });
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("exception in pay refresh explore event" ,e);
        }
    }

    private void refreshPlainEventInPay(Role role,  Consumer<Object> consumer) {
        // 刷新事件
        long now = System.currentTimeMillis();
        refreshPlainEvent(EventTriggerType.PAY_REFRESH_PLAIN_EVENT, PAY_PREFIX, role, now, o -> {
            if (consumer != null) {
                consumer.accept(o);
            }
        });

        // 发送一个空列表 只更新列表外其他信息
        sendEventList(role, null);
    }

    /**
     * 根据EventTriggerType.OriginDesc判断是否为付费刷新事件
     */
    private boolean isRefreshPay(String originDesc) {
        return EventTriggerType.PAY_REFRESH_PLAIN_EVENT.getOriginDesc().equals(originDesc);
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 15:36:20.464
     * @Param [eventTriggerType, origin_prefix, role, createTime, refreshNum, consumer]
     * @Return void
     * @Description 刷新普通事件
     */
    public void refreshPlainEvent(EventTriggerType eventTriggerType, String origin_prefix, Role role,
                                  long refreshTime, Consumer<Object> consumer) {
        String callMethodDesc = "";
        callMethodDesc = origin_prefix + "timeKeyHandlerPlainEventRefresh";
        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            ErrorLogUtil.errorLog("Error record is null roleId", "callMethodDesc",callMethodDesc, "roleId",role.getRoleId());
            return;
        }

        ExploreEventCommonLvConfig commonLvConfig = configService.getConfig(ExploreEventCommonLvConfig.class);
        var commonLvMeta = commonLvConfig.getByLevel(record.getEventLevel());
        if (commonLvMeta == null) {
            ErrorLogUtil.errorLog("Error ExploreEventCommonLvMeta is null", "callMethodDesc",callMethodDesc, "roleId",role.getRoleId(), "eventLevel",record.getEventLevel());
            return;
        }

        // 更新刷新时间
        record.setLastRefreshTime(refreshTime);
        playerWorldExploreRecordDao.save(record);

        // 刷新普通事件
        var result = refreshPlainEvent(role, commonLvMeta, eventTriggerType.getOriginDesc(), callMethodDesc, refreshTime, o -> {
            if (consumer != null) {
                consumer.accept(o);
            }
        });

        // 消息推送 烽火台已刷新
        if (result) {
            pushHelper.exploreNewEvent(role);
        }
    }

    public void refreshPlainEvent(Role role, ExploreEventCommonLvMeta commonLvMeta,
                                  String originDesc, String callMethodDesc, Consumer<Object> consumer) {
        refreshPlainEvent(role, commonLvMeta, originDesc, callMethodDesc, getLastRefreshTime(), consumer);
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 16:23:00.365
     * @Param [role, commonLvMeta, originDesc, callMethodDesc, refreshTime, consumer]
     * @Return boolean
     * @Description 刷新普通事件 配置文件的对应记录刷新
     */
    public boolean refreshPlainEvent(Role role, ExploreEventCommonLvMeta commonLvMeta,
                                     String originDesc, String callMethodDesc, long refreshTime, Consumer<Object> consumer) {
        boolean commonEvent = refreshCommonEvent(role, commonLvMeta, originDesc, callMethodDesc, refreshTime, consumer);
        boolean assistEvent = refreshAssistEvent(role, commonLvMeta, originDesc, callMethodDesc, refreshTime, consumer);
        return commonEvent || assistEvent;
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 16:23:00.365
     * @Param [role, commonLvMeta, originDesc, callMethodDesc, refreshTime, consumer]
     * @Return boolean
     * @Description 刷新普通事件 配置文件的对应记录刷新
     */
    private boolean refreshCommonEvent(Role role, ExploreEventCommonLvMeta commonLvMeta,
                                       String originDesc, String callMethodDesc, long refreshTime, Consumer<Object> consumer) {
        if (role == null || commonLvMeta == null) {
            return false;
        }

        // 检查玩家普通事件上限
        if (!worldExploreEventManager.checkEventGroupLimit(role, EventGroupType.GROUP_COMMON.getId())) {
            logger.info("checkEventGroupLimit return false, roleId:{}", role.getRoleId());
            return false;
        }

        // 本次需要固定刷新的事件个数
        int eventFreshCount = 0;
        if (isRefreshPay(originDesc)) {
            SettingConfig settingConfig = configService.getConfig(SettingConfig.class);
            // 付费刷新的事件个数
            eventFreshCount = settingConfig.getPayEventRefreshCount();
        }else {
            eventFreshCount = commonLvMeta.getEventFreshCount();
        }
        // 本次优先刷新数量
        int highPriorityRefreshCount = commonLvMeta.getHighPriorityRefreshCount();

        if (highPriorityRefreshCount > eventFreshCount) {
            logger.warn("{} 触发普通事件刷新错误，优先数量配置的大于固定数量", callMethodDesc);
            return false;
        }
        if (eventFreshCount <= 0) {
            logger.warn("{}} 触发普通事件刷新错误，eventFreshCount<=0", callMethodDesc);
            return false;
        }

        // 优先级随机事件
        Map<Integer, Integer> eventGroupMap = new HashMap<>();
        getRandomGroupIdFromPool(eventGroupMap, commonLvMeta.getPoolNum1(), commonLvMeta.getHighPriorityEventGroupIdPool1List());
        getRandomGroupIdFromPool(eventGroupMap, commonLvMeta.getPoolNum2(), commonLvMeta.getHighPriorityEventGroupIdPool2List());
        getRandomGroupIdFromPool(eventGroupMap, commonLvMeta.getPoolNum3(), commonLvMeta.getHighPriorityEventGroupIdPool3List());
        getRandomGroupIdFromPool(eventGroupMap, commonLvMeta.getPoolNum4(), commonLvMeta.getHighPriorityEventGroupIdPool4List());

        // 编年史 世界boss未开放 则不能刷出集结任务
        ExploreEventConfig exploreEventConfig = configService.getConfig(ExploreEventConfig.class);
        boolean worldBossUnlock = milestoneService.getBossMaxLevel(role.getoServerId()) > 0;
        var groupIdPool = commonLvMeta.getCommonEventGroupIdPoolList();
        if (!worldBossUnlock) {
            for (var item : groupIdPool) {
                if (item.length != 2) {
                    continue;
                }

                if (exploreEventConfig.getGroupIdsByEventType(EventType.EVENT_RALLY).contains(String.valueOf(item[0]))) {
                    logger.info("refreshPlainEvent world boss not unlock, roleId:{} pool:{}", role.getRoleId(), item);
                    item[1] = 0;
                }
            }
        }

        int commonCount = eventFreshCount - highPriorityRefreshCount;
        for (int i = 0; i < commonCount; i++) {
            getRandomGroupIdFromPool(eventGroupMap, groupIdPool);
        }

        // 根据火炉等级去创建新事件
        int level = roleCityManager.getBuildingMaxLevelByGroupId(role.getPersistKey(), BuildingGroup.CASTLE.getGroup());
        for (Map.Entry<Integer, Integer> entry : eventGroupMap.entrySet()) {
            if (entry.getKey() == null) {
                continue;
            }
            var exploreEventMeta = exploreEventConfig.getByGroupIdAndLv(entry.getKey().toString(), level);
            if (exploreEventMeta == null) {
                continue;
            }
            scheduleService.asyncSchedule(new WorldExploreOperation(role, originDesc, exploreEventMeta, entry.getValue(),
                    exploreEventMeta.getRefreshRange(), EventGroupType.GROUP_COMMON, refreshTime, consumer) {
            }, 10);
        }

        return true;
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 16:23:00.365
     * @Param [role, commonLvMeta, originDesc, callMethodDesc, refreshTime, consumer]
     * @Return boolean
     * @Description 刷新普通事件 配置文件的对应记录刷新
     */
    private boolean refreshAssistEvent(Role role, ExploreEventCommonLvMeta commonLvMeta,
                                       String originDesc, String callMethodDesc, long refreshTime, Consumer<Object> consumer) {
        // 付费刷新时屏蔽
        if (isRefreshPay(originDesc)) {
            return false;
        }
        if (role == null || commonLvMeta == null) {
            return false;
        }
        // 未加入联盟
        if (!JavaUtils.bool(role.getAllianceId())) {
            return false;
        }
        // 检查玩家协助事件上限
        if (!worldExploreEventManager.checkEventGroupLimit(role, EventGroupType.GROUP_ASSIST.getId())) {
            logger.info("checkEventGroupLimit return false, roleId:{}", role.getRoleId());
            return false;
        }
        // 协助事件
        Map<Integer, Integer> eventGroupMap = new HashMap<>();
        getRandomGroupIdFromPool(eventGroupMap, commonLvMeta.getPoolNum5(), commonLvMeta.getHighPriorityEventGroupIdPool5List());

        // 编年史 世界boss未开放 则不能刷出集结任务
        ExploreEventConfig exploreEventConfig = configService.getConfig(ExploreEventConfig.class);
        // 根据火炉等级去创建新事件
        int level = roleCityManager.getBuildingMaxLevelByGroupId(role.getPersistKey(), BuildingGroup.CASTLE.getGroup());
        for (Map.Entry<Integer, Integer> entry : eventGroupMap.entrySet()) {
            if (entry.getKey() == null) {
                continue;
            }
            var exploreEventMeta = exploreEventConfig.getByGroupIdAndLv(entry.getKey().toString(), level);
            if (exploreEventMeta == null) {
                continue;
            }
            scheduleService.asyncSchedule(new WorldExploreOperation(role, originDesc, exploreEventMeta, entry.getValue(),
                    exploreEventMeta.getRefreshRange(), EventGroupType.GROUP_ASSIST, refreshTime, consumer) {
            }, 10);
        }

        return true;
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-15 15:42:45.631
     * @Param [role, now, consumer]
     * @Return void
     * @Description low tick 中调用刷新
     */
    public void refreshPlainEventInLowTick(Role role, long now, Consumer<Object> consumer) {
        PlayerWorldExploreRecord record = playerWorldExploreRecordDao.findById(role.getPersistKey());
        if (record == null) {
            return;
        }

        // 上次刷新时间在当前的刷新间隔内 则表示刷新过
        var refreshTime = getExploreEventRefreshTime();
        if (record.getLastRefreshTime() >= refreshTime[0] && record.getLastRefreshTime() <= refreshTime[1]) {
            return;
        }

        // 重置boss刷新次数
        record.setBossRefreshCount(0);
        playerWorldExploreRecordDao.save(record);

        // 刷新事件
        refreshPlainEvent(EventTriggerType.LOGIN_HANDLER_PLAIN_EVENT_FIXED_TIME_REFRESH_PLAIN, LOW_TICK_PREFIX, role, refreshTime[0], o -> {
            if (consumer != null) {
                consumer.accept(o);
            }
        });

        // 发送一个空列表 只更新列表外其他信息
        sendEventList(role, null);
    }

    /**
     * <AUTHOR>
     * @Date 2024-04-08 21:36:22.428
     * @Param [role]
     * @Return void
     * @Description 初始化 boss event
     */
    public void refreshBossEvent(Role role, String origin) {
        var record = playerWorldExploreRecordDao.findById(role.getId());
        if (record == null) {
            return;
        }

        var settingConfig = configService.getConfig(SettingConfig.class);
        if (record.getEventLevel() < settingConfig.getExploreEventUnlockBoss()) {
            return;
        }

        if (record.getBossRefreshCount() > settingConfig.getExploreEventLimitBoss()) {
            return;
        }

        var eventsMap = playerWorldExploreEventDao.getPlayerWorldExploreEventGroup(role.getRoleId());
        var events = eventsMap.get(EventGroupType.GROUP_BOSS);
        if (events != null && !events.isEmpty()) {
            return;
        }

        var exploreEventConfig = configService.getConfig(ExploreEventConfig.class);
        var eventGroupId = BOSS_META_ID;
        var eventLevel = record.getBossLevel();
        var _meta = exploreEventConfig.getByGroupIdAndLv(eventGroupId, eventLevel);
        if (_meta == null) {
            ErrorLogUtil.errorLog("initBossEvent meta not found", "roleId",role.getRoleId(), "eventGroupId",eventGroupId, "eventLevel",eventLevel);
            return;
        }

        // 自增boss刷新次数
        record.setBossRefreshCount(record.getBossRefreshCount() + 1);
        playerWorldExploreRecordDao.save(record);

        scheduleService.asyncSchedule(new WorldExploreOperation(role, origin,
                _meta, 1, _meta.getRefreshRange(),
                EventGroupType.GROUP_BOSS, TimeUtil.getNow(), o -> {
            pushAllEventsForShowing(role);
        }) {
        }, 10);
    }

    /**
     * <AUTHOR>
     * @Date 2024-04-08 21:35:56.420
     * @Param [role, event, origin]
     * @Return void
     * @Description 设置 boss event
     */
    public void setBossEventLevel(Role role, int level) {
        var record = playerWorldExploreRecordDao.findById(role.getId());
        if (record == null) {
            return;
        }

        record.setBossLevel(level);
        playerWorldExploreRecordDao.save(record);
    }

    public void getRandomGroupIdFromPool(Map<Integer, Integer> eventGroupMap, int num, List<int[]> poolList) {
        if (num > 0) {
            for (int i = 0; i < num; i++) {
                getRandomGroupIdFromPool(eventGroupMap, poolList);
            }
        }
    }

    public void getRandomGroupIdFromPool(Map<Integer, Integer> eventGroupMap, List<int[]> poolList) {
        if (poolList == null || poolList.isEmpty()) {
            return;
        }

        // 计算总的权重值
        int[] weights = new int[poolList.size()];
        int[] e = null;
        boolean allow = false;
        for (int i = 0, len = poolList.size(); i < len; i++) {
            e = poolList.get(i);
            if (e != null && e.length == 2 && e[1] >= 0) {
                weights[i] = e[1];
                allow = true;
            }
        }

        // 配置错误
        if (!allow) {
            return;
        }

        int index = RandomUtils.weightRandom(weights);
        if (index < 0 || index >= poolList.size()) {
            return;
        }

        var groupId = poolList.get(index)[0];
        eventGroupMap.compute(groupId, (k, v) -> v == null ? 1 : v + 1);
    }

    /**
     * 攻击npc之后需要触发，给客户端传递一下已攻击的npc等级，客户端需要展示
     *
     * @param attackNpcMaxLevel
     * @param role
     */
    public void attackNpcMaxLevelSendMessage(int attackNpcMaxLevel, Role role) {
        /**
         * 只在当前服务器处理
         */
        if (!checkServerType()) {
            return;
        }
        if (functionSwitchService.isOpen(FunctionType.WORLD_EVENT_FUNTION.getId(), role) && role != null && role.isOnline()) {
            role.send(buildGcPlayerShowWorldExploreEventNpcLevel(attackNpcMaxLevel));
        }
    }

    /**
     * 构建 玩家探索事件记录，包含玩家能展示的事件
     *
     * @param errorCodeStatus
     * @param record
     * @param eventList
     * @return
     */
    public GcPlayerWorldExploreRecord buildGcPlayerWorldExploreRecord(ErrorCodeStatus errorCodeStatus,
                                                                      PlayerWorldExploreRecord record, List<PlayerWorldExploreEvent> eventList, int attackNpcMaxLevel) {
        GcPlayerWorldExploreRecord gcPlayerWorldExploreRecord = new GcPlayerWorldExploreRecord();
        gcPlayerWorldExploreRecord.setNewPlayerFlag(true);
        if (errorCodeStatus != null && record != null) {
            gcPlayerWorldExploreRecord.setErrorCode(errorCodeStatus.getStatus());
            gcPlayerWorldExploreRecord.setEventLevel(record.getEventLevel());
            gcPlayerWorldExploreRecord.setRewardLevel(record.getRewardLevel());
            gcPlayerWorldExploreRecord.setExp(record.getExp());
            gcPlayerWorldExploreRecord.setAttackNpcMaxLevel(attackNpcMaxLevel);
            List<PsPlayerWorldExploreEvent> eventArrayList = new ArrayList<>();
            if (eventList != null && !eventList.isEmpty()) {
                eventList.forEach(e -> {
                    eventArrayList.add(buildPsPlayerWorldExploreEvent(e));
                });
            }
            gcPlayerWorldExploreRecord.setEventList(eventArrayList);

            // 刷新时间段
            var refreshTime = getExploreEventRefreshTime();
            gcPlayerWorldExploreRecord.setRefreshTime(refreshTime[1]);
        }
        return gcPlayerWorldExploreRecord;
    }

    public PsPlayerWorldExploreEvent buildPsPlayerWorldExploreEvent(PlayerWorldExploreEvent e) {
        PsPlayerWorldExploreEvent psPlayerWorldExploreEvent = new PsPlayerWorldExploreEvent();
        psPlayerWorldExploreEvent.setId(e.getPersistKey().toString());
        psPlayerWorldExploreEvent.setEventId(e.getEventId());
        psPlayerWorldExploreEvent.setStatus(e.getStatus());
        psPlayerWorldExploreEvent.setEventType(e.getLinkType());
        psPlayerWorldExploreEvent.setGroupId(e.getEventGroupId());
        psPlayerWorldExploreEvent.setEventLv(e.getEventLv());
        e.getDropList().forEach(item -> {
            psPlayerWorldExploreEvent.addToDropItems(item.toPsObject());
        });
        /**
         * 增加一个 linkId
         */
        if (e.getLinkType() == EventType.EVENT_NPC.getId()
                || e.getLinkType() == EventType.EVENT_RESCUE.getId()
                || e.getLinkType() == EventType.EVENT_PVE.getId()) {
            psPlayerWorldExploreEvent.setLinkMetaId(e.getLinkId());
        } else if (!StringUtils.isBlank(e.getSpecialMetaId())) {
            psPlayerWorldExploreEvent.setLinkMetaId(e.getSpecialMetaId());
            if (!StringUtils.isBlank(e.getSpecialMeta2Id())) {
                psPlayerWorldExploreEvent.setLinkMetaId2(e.getSpecialMeta2Id());
            }
        } else {
            psPlayerWorldExploreEvent.setLinkMetaId(e.getLinkId());
        }

        psPlayerWorldExploreEvent.setEndTime(e.getEndTime());
        psPlayerWorldExploreEvent.setX(e.getPoint().getX());
        psPlayerWorldExploreEvent.setY(e.getPoint().getY());
        psPlayerWorldExploreEvent.setEventGroupType(e.getEventGroupType());
        psPlayerWorldExploreEvent.setResTotal(e.getResTotal());
        psPlayerWorldExploreEvent.setResReversed(e.getResReversed());
        psPlayerWorldExploreEvent.setAssistCompleted(e.isAssistCompleted());
        if (e.getAssistCompletedName() != null) {
            psPlayerWorldExploreEvent.setAssistCompletedName(e.getAssistCompletedName());
        }
        if (e.getAssistCompletedHead() != null) {
            psPlayerWorldExploreEvent.setAssistCompletedHead(e.getAssistCompletedHead());
        }
        if (e.getAssistCompletedHeadFrame() != null) {
            psPlayerWorldExploreEvent.setAssistCompletedHeadFrame(e.getAssistCompletedHeadFrame());
        }
        if (e.getLinkType() == EventType.EVENT_ASSIST.getId()) {
            boolean randomName = true;
            long assistRid = e.getAssistRoleId();
            if (assistRid > 0) {
                Role assistRole = roleDao.findById(assistRid);
                if (assistRole != null) {
                    randomName = false;
                    String aliasName = "";
                    Long allianceId = assistRole.getAllianceId();
                    if (allianceId != null && allianceId > 0) {
                        Alliance alliance = allianceDao.findById(allianceId);
                        if (alliance != null) {
                            aliasName = "[" + alliance.getAliasName() + "]";
                        }
                    }
                    psPlayerWorldExploreEvent.setAssistHead(assistRole.getHead());
                    psPlayerWorldExploreEvent.setAssistHeadFrame(assistRole.getHeadFrame());
                    psPlayerWorldExploreEvent.setAssistRid(assistRid);
                    psPlayerWorldExploreEvent.setAssistName(aliasName + assistRole.getName());
                }
            }
            if (randomName) {
                String aliasName = "";
                Role role = roleDao.findById(e.getRoleId());
                if (role != null) {
                    Long allianceId = role.getAllianceId();
                    if (allianceId != null && allianceId > 0) {
                        Alliance alliance = allianceDao.findById(allianceId);
                        if (alliance != null) {
                            aliasName = "[" + alliance.getAliasName() + "]";
                        }
                    }
                }
                psPlayerWorldExploreEvent.setAssistRid(0);
                if (StringUtils.isEmpty(e.getAssistRoleName())) {
                    CaravanAIConfig caravanAIConfig = configService.getConfig(CaravanAIConfig.class);
                    CaravanAIConfig.CaravanAIMeta meta = RandomUtils.random(caravanAIConfig.getMetas());
                    String name = meta != null ? meta.getName() : "9527";
                    String head = meta != null ? String.valueOf(meta.getHead()) : "2";
                    e.setAssistRoleName(name);
                    e.setAssistRoleHead(head);
                }
                psPlayerWorldExploreEvent.setAssistName(aliasName + e.getAssistRoleName());
                psPlayerWorldExploreEvent.setAssistHead(e.getAssistRoleHead());
            }
        }
        return psPlayerWorldExploreEvent;
    }

    /**
     * 构建 查看一个事件
     *
     * @param errorCodeStatus
     * @param event
     * @return
     */
    public GcPlayerWorldExploreEvent buildGcPlayerWorldExploreEvent(ErrorCodeStatus errorCodeStatus, PlayerWorldExploreEvent event) {
        GcPlayerWorldExploreEvent gcPlayerWorldExploreEvent = new GcPlayerWorldExploreEvent();
        gcPlayerWorldExploreEvent.setErrorCode(errorCodeStatus.getStatus());
        gcPlayerWorldExploreEvent.setEvent(buildPsPlayerWorldExploreEvent(event));
        return gcPlayerWorldExploreEvent;
    }

    /**
     * 构建添加一个事件
     *
     * @param event
     * @return
     */
    public GcAddPlayerWorldExploreEvent buildGcAddPlayerWorldExploreEvent(PlayerWorldExploreEvent event) {
        GcAddPlayerWorldExploreEvent gcAddPlayerWorldExploreEvent = new GcAddPlayerWorldExploreEvent();
        gcAddPlayerWorldExploreEvent.setEvent(buildPsPlayerWorldExploreEvent(event));
        return gcAddPlayerWorldExploreEvent;
    }

    /**
     * 构建 删除一个事件
     *
     * @param id
     * @return
     */
    public GcDeletePlayerWorldExploreEvent buildGcDeletePlayerWorldExploreEvent(String id) {
        GcDeletePlayerWorldExploreEvent gcDeletePlayerWorldExploreEvent = new GcDeletePlayerWorldExploreEvent();
        gcDeletePlayerWorldExploreEvent.setId(id);
        return gcDeletePlayerWorldExploreEvent;
    }

    /**
     * 构建 更新一个事件
     *
     * @param event
     * @return
     */
    public GcUpdatePlayerWorldExploreEvent buildGcUpdatePlayerWorldExploreEvent(PlayerWorldExploreEvent event, int exp) {
        GcUpdatePlayerWorldExploreEvent gcUpdatePlayerWorldExploreEvent = new GcUpdatePlayerWorldExploreEvent();
        gcUpdatePlayerWorldExploreEvent.setEvent(buildPsPlayerWorldExploreEvent(event));
        gcUpdatePlayerWorldExploreEvent.setExp(exp);
        return gcUpdatePlayerWorldExploreEvent;
    }

    /**
     * 攻击npc 最大等级 更新
     *
     * @param attackNpcMaxLevel
     * @return
     */
    public GcPlayerShowWorldExploreEventNpcLevel buildGcPlayerShowWorldExploreEventNpcLevel(int attackNpcMaxLevel) {
        GcPlayerShowWorldExploreEventNpcLevel gcPlayerShowWorldExploreEventNpcLevel = new GcPlayerShowWorldExploreEventNpcLevel();
        gcPlayerShowWorldExploreEventNpcLevel.setAttackNpcMaxLevel(attackNpcMaxLevel);
        return gcPlayerShowWorldExploreEventNpcLevel;
    }

    public GcExploreEventRewardUp buildGcExploreEventRewardUp(ErrorCodeStatus errorCodeStatus, int rewardLevel) {
        GcExploreEventRewardUp gcExploreEventRewardUp = new GcExploreEventRewardUp();
        if (errorCodeStatus != null) {
            gcExploreEventRewardUp.setErrorCode(errorCodeStatus.getStatus());
            gcExploreEventRewardUp.setRewardLevel(rewardLevel);
        }
        return gcExploreEventRewardUp;
    }

    /**
     * 构建 玩家奖励等级升级
     *
     * @param beforeRewardLevel
     * @param afterRewardLevel
     * @return
     */
    public GcPlayerWorldExploreRewardUp buildGcPlayerWorldExploreRewardUp(int beforeRewardLevel, int afterRewardLevel, int exp) {
        GcPlayerWorldExploreRewardUp gcPlayerWorldExploreRewardUp = new GcPlayerWorldExploreRewardUp();
        gcPlayerWorldExploreRewardUp.setBeforeRewardLevel(beforeRewardLevel);
        gcPlayerWorldExploreRewardUp.setAfterRewardLevel(afterRewardLevel);
        gcPlayerWorldExploreRewardUp.setExp(exp);
        return gcPlayerWorldExploreRewardUp;
    }

    /**
     * 构建 回复领奖
     *
     * @param errorCodeStatus
     * @param id
     * @return
     */
    public GcPlayerWorldExploreEventReciveReward buildGcPlayerWorldExploreEventReciveReward(ErrorCodeStatus errorCodeStatus, String id, int exp, boolean up,
                                                                                            int eventLevel, String metaId, List<PsSimpleItem> list) {
        GcPlayerWorldExploreEventReciveReward gcPlayerWorldExploreEventReciveReward = new GcPlayerWorldExploreEventReciveReward();
        gcPlayerWorldExploreEventReciveReward.setErrorCode(errorCodeStatus.getStatus());
        gcPlayerWorldExploreEventReciveReward.setId(id);
        gcPlayerWorldExploreEventReciveReward.setExp(exp);
        gcPlayerWorldExploreEventReciveReward.setUp(up);
        gcPlayerWorldExploreEventReciveReward.setMetaId(metaId);
        if (list != null && !list.isEmpty()) {
            gcPlayerWorldExploreEventReciveReward.setDropItems(list);
        }
        gcPlayerWorldExploreEventReciveReward.setEventLevel(eventLevel);
        return gcPlayerWorldExploreEventReciveReward;
    }
}
