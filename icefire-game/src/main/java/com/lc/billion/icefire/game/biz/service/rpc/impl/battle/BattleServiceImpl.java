package com.lc.billion.icefire.game.biz.service.rpc.impl.battle;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.model.battle.BtlArenaVerifyResult;
import com.lc.billion.icefire.game.biz.model.battle.BtlExpeditionVerifyResult;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.simfun.sgf.monitoring.httpclient.HttpClientInstrumentation;
import com.simfun.sgf.thread.NamedThreadFactory;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

/**
 * @ClassName BattleServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/4/8 11:34
 * @Version 1.0
 */
@Service
public class BattleServiceImpl {

    private static Logger logger = LoggerFactory.getLogger(BattleServiceImpl.class);

    @Autowired
    private ConfigCenter configCenter;

    @Autowired
    private HttpClientInstrumentation httpClientInstrumentation;

    private final Map<String, String> headers = new HashMap<>();
    @Getter
    private final Executor executor = Executors.newFixedThreadPool(8, new NamedThreadFactory("sanguo2-battleWorker"));
    @Getter
    private HttpClient client;


    private enum BattleType {
        expedition,
        arena
    }

    public BattleServiceImpl() {
        this.headers.put("accept", "application/json");
        this.headers.put("Content-Type", "application/json");
    }

    @PostConstruct
    public void initializeHttpClient() {
        this.client = httpClientInstrumentation.createInstrumentedClient(
                "battle-service-client",
                executor,
                Duration.ofSeconds(10)
        );
        logger.info("Battle服务HTTP客户端初始化完成，已添加监控指标");
    }

    private String getUrl(BattleType type) {
//        GameConfig gameConfig = ServerConfigManager.getInstance().getGameConfig();
        String url = "";
        switch (type) {
            case expedition:
                url = "/api/sanguo2/v1/javaExpeditionBtlStart";
                break;
            case arena:
                url = "/api/sanguo2/v1/javaArenaBtlStart";
                break;
        }
        return configCenter.getBattleServerHost() + url;
    }


    public BtlExpeditionVerifyResult expeditionVerify(String objJsonStr) {
        try {
            var resp = HttpUtil.postJSON(getUrl(BattleType.expedition), objJsonStr, this.headers);
            var result = JSONObject.parseObject(resp, BtlExpeditionVerifyResult.class);
            if (result.getRoleId().equals(0L)) {
                ErrorLogUtil.errorLog("expeditionVerify error", "resp", resp);
                return null;
            } else {
                return result;
            }
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("expeditionVerify error", e);
            }
            return null;
        }
    }

    public BtlArenaVerifyResult arenaVerifyResult(String objJsonStr) {
        try {
            var resp = HttpUtil.postJSON(getUrl(BattleType.arena), objJsonStr, this.headers);
            var result = JSONObject.parseObject(resp, BtlArenaVerifyResult.class);
            if (result.getRoleId().equals(0L)) {
                ErrorLogUtil.errorLog("arenaVerifyResult error", "resp", resp);
                return null;
            }
            return result;
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("arenaVerifyResult error", e, ErrorLogUtil.ALERT_TYPE_KEY, "battleServerError");
            }
            return null;
        }
    }

    public void arenaVerifyResult(String objJsonStr, Consumer<BtlArenaVerifyResult> consumer, Consumer<Throwable> exceptionConsumer) {
        var url = getUrl(BattleType.arena);
        var builder = HttpRequest.newBuilder();
        builder.uri(URI.create(url));
        for (var entry : this.headers.entrySet()) {
            builder.header(entry.getKey(), entry.getValue());
        }
        builder.POST(HttpRequest.BodyPublishers.ofString(objJsonStr));
        var request = builder.build();
        long startTime = TimeUtil.getNow();
        client.sendAsync(request, HttpResponse.BodyHandlers.ofString()).thenAcceptAsync(res -> {
            logger.info("arenaVerifyResult http cost: {}", TimeUtil.getNow() - startTime);
            if (ServerConfigManager.isDev()) {
                logger.info("arenaVerifyResult http resp: {}", res.body());
            }
            if (res.statusCode() == 200) {
                dealArenaResponse(objJsonStr, res, consumer);
            } else {
                logger.info("arenaVerifyResult error, http code: {}, resp: {}", res.statusCode(), res.body());
                try {
                    client.sendAsync(request, HttpResponse.BodyHandlers.ofString()).thenAccept(res2 -> {
                        dealArenaResponse(objJsonStr, res2, consumer);
                    });
                } catch (Exception e) {
                    ErrorLogUtil.exceptionLog("arenaVerifyResult error", e, ErrorLogUtil.ALERT_TYPE_KEY, "battleServerError", "param", objJsonStr, "resp", res.body());
                }
            }
        }).exceptionally(e -> {
            ErrorLogUtil.exceptionLog("arenaVerifyResult error", e, ErrorLogUtil.ALERT_TYPE_KEY, "battleServerError", "param", objJsonStr);
            if (exceptionConsumer != null) {
                exceptionConsumer.accept(e);
            }
            return null;
        });
    }

    private void dealArenaResponse(String request, HttpResponse<String> res, Consumer<BtlArenaVerifyResult> consumer) {
        if (res.statusCode() != 200) {
            return;
        }
        try {
            var result = JSONObject.parseObject(res.body(), BtlArenaVerifyResult.class);
            if (result.getErrCode() != null && result.getErrCode() == 100) {
                ErrorLogUtil.exceptionLog("arenaVerifyResult timeout error", new Throwable(), "resp", res.body());
                result = null;
            }
            consumer.accept(result);
        } catch (Exception e) {
            // 逻辑异常,统一收集
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("arenaVerifyResult", e, "param", request, "resp", res.body());
            }
        }
    }

    public void expeditionVerify(String objJsonStr, Consumer<BtlExpeditionVerifyResult> consumer) {
        var url = getUrl(BattleType.expedition);
        var builder = HttpRequest.newBuilder();
        builder.uri(URI.create(url));
        for (var entry : this.headers.entrySet()) {
            builder.header(entry.getKey(), entry.getValue());
        }
        builder.POST(HttpRequest.BodyPublishers.ofString(objJsonStr));
        var request = builder.build();
        long startTime = TimeUtil.getNow();
        client.sendAsync(request, HttpResponse.BodyHandlers.ofString()).thenAcceptAsync(res -> {
            logger.info("expeditionVerify http cost: {}", TimeUtil.getNow() - startTime);
            if (res.statusCode() == 200) {
                dealExpeditionResponse(objJsonStr, res, consumer);
            } else {
                logger.info("expeditionVerify error, http code: {},  resp: {}", res.statusCode(), res.body());
                // 错误码为502 重试
                if (res.statusCode() == 502) {
                    client.sendAsync(request, HttpResponse.BodyHandlers.ofString()).thenAcceptAsync(res2 -> {
                        dealExpeditionResponse(objJsonStr, res2, consumer);
                    });
                }
            }

        }).exceptionally(e -> {
            ErrorLogUtil.exceptionLog("expeditionVerify error", e, ErrorLogUtil.ALERT_TYPE_KEY, "battleServerError", "param", objJsonStr);
            consumer.accept(null);
            return null;
        });
    }

    private void dealExpeditionResponse(String request, HttpResponse<String> res,
                                        Consumer<BtlExpeditionVerifyResult> consumer) {
        if (res.statusCode() != 200) {
            return;
        }
        try {
            var result = JSONObject.parseObject(res.body(), BtlExpeditionVerifyResult.class);
            if (result.getErrCode() != null && result.getErrCode() == 100) {
                ErrorLogUtil.exceptionLog("expeditionVerify timeout error", new Throwable(), "resp", res.body());
                result = null;
            }
            consumer.accept(result);
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("expeditionVerify error", e, ErrorLogUtil.ALERT_TYPE_KEY,
                    "battleServerError", "param", request, "resp", res.body());
            consumer.accept(null);
        }
    }

    public void shutdown() {
        this.client.close();
    }
}
