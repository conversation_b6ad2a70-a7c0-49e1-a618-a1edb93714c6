package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class GameAsyncProcessMetrics {
    public static final String NAME_FMT = "game.async.process.%s";
    public static final String TIMER_DESCRIPTION = "game async %s duration";
    public static final String COUNTER_DESCRIPTION = "game async process count";

    private final MeterRegistryManager meterRegistryManager;
    private final ConcurrentHashMap<String, AsyncProcessMetrics> asyncProcessMetricsMap = new ConcurrentHashMap<>();

    public GameAsyncProcessMetrics(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }

    public void handleAsyncProcessSpan(String processId, long startTime, long endTime) {
        meterRegistryManager.safeExecuteAsync(() -> {
            AsyncProcessMetrics metrics = getOrCreateProcessMetrics(processId);
            metrics.counter.increment();
            metrics.timer.record(endTime - startTime, TimeUnit.MILLISECONDS);
        });
    }
    private AsyncProcessMetrics getOrCreateProcessMetrics(String processId) {
        return asyncProcessMetricsMap.computeIfAbsent(processId, id -> {
            Counter counter = meterRegistryManager.counter(
                    String.format(NAME_FMT, "counter"),
                    COUNTER_DESCRIPTION,
                    "process.id", processId
            );
            Timer timer = meterRegistryManager.timer(
                    String.format(NAME_FMT, "timer"),
                    String.format(TIMER_DESCRIPTION, "process"),
                    "process.id", processId
            );
            return new AsyncProcessMetrics(counter, timer);
        });
    }

    private record AsyncProcessMetrics(Counter counter, Timer timer) {
    }
}
