package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class GameInternalMsgMetrics {
    public static final String NAME_FMT = "game.internal.%s";
    public static final String TIMER_DESCRIPTION = "game internal %s duration";
    public static final String COUNTER_DESCRIPTION = "game internal count";

    private final MeterRegistryManager meterRegistryManager;
    private final ConcurrentHashMap<String, InternalMsgMetrics> internalMsgMetricsMap = new ConcurrentHashMap<>();

    public GameInternalMsgMetrics(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }

    public void handleInternalSpan(String internalId, long exeStartTime, long exeEndTime){
        meterRegistryManager.safeExecuteAsync(() -> {
            InternalMsgMetrics internalMsgMetrics = getOrCreateInternalMsgMetrics(internalId);
            internalMsgMetrics.counter.increment();
            internalMsgMetrics.timer.record(exeEndTime - exeStartTime, TimeUnit.MILLISECONDS);
        });
    }

    private InternalMsgMetrics getOrCreateInternalMsgMetrics(String internalId) {
        return internalMsgMetricsMap.computeIfAbsent(internalId, id -> {
            Counter counter = meterRegistryManager.counter(
                    String.format(NAME_FMT, "counter"),
                    COUNTER_DESCRIPTION,
                    "internal.id", internalId
            );
            Timer timer = meterRegistryManager.timer(
                    String.format(NAME_FMT, "timer"),
                    String.format(TIMER_DESCRIPTION, "process"),
                    "internal.id", internalId
            );
            return new InternalMsgMetrics(counter, timer);
        });
    }

    private record InternalMsgMetrics (Counter counter, Timer timer) {
    }
}
