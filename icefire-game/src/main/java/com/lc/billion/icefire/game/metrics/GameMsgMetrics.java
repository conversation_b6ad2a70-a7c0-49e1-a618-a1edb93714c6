package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 *  游戏消息指标记录
 */
public class GameMsgMetrics {

    public static final String NAME_FMT = "game.msg.process.%s";
    public static final String TIMER_DESCRIPTION = "game msg %s duration";
    public static final String COUNTER_DESCRIPTION = "game msg process count";

    private final MeterRegistryManager meterRegistryManager;
    private final ConcurrentHashMap<Integer, MsgMetrics> msgMetricsMap = new ConcurrentHashMap<>();

    public GameMsgMetrics(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }

    /**
     * 获取或创建消息指标
     * @param msgId
     * @return
     */
    private MsgMetrics getOrCreateMsgMetrics(int msgId) {
        return msgMetricsMap.computeIfAbsent(msgId, id -> {
            String msgIdStr = String.valueOf(id);
            Counter counter = meterRegistryManager.counter(
                    String.format(NAME_FMT, "counter"),
                    COUNTER_DESCRIPTION,
                    "msg.id", msgIdStr
            );
            Timer waitTimer = meterRegistryManager.timer(
                    String.format(NAME_FMT, "waitTimer"),
                    String.format(TIMER_DESCRIPTION, "wait"),
                    "msg.id", msgIdStr
            );
            Timer totalTimer = meterRegistryManager.timer(
                    String.format(NAME_FMT, "totalTimer"),
                    String.format(TIMER_DESCRIPTION, "total"),
                    "msg.id", msgIdStr
            );
            Timer processTimer = meterRegistryManager.timer(
                    String.format(NAME_FMT, "processTimer"),
                    String.format(TIMER_DESCRIPTION, "process"),
                    "msg.id", msgIdStr
            );
            return new MsgMetrics(counter, waitTimer, totalTimer, processTimer);
        });
    }

    /**
     * 处理消息
     * @param msgId 消息id
     * @param name 消息名称
     * @param playerId 玩家id
     * @param msgCreateTime 消息创建时间
     * @param exeStartTime 执行开始时间
     * @param exeEndTime 执行结束时间
     */
    public void handleMsgSpan(int msgId, String name, long playerId, long msgCreateTime, long exeStartTime, long exeEndTime) {
        meterRegistryManager.safeExecuteAsync(() -> {
            GameMsgRecord record = new GameMsgRecord(msgId, name, playerId, msgCreateTime, exeStartTime, exeEndTime);
            MsgMetrics metrics = getOrCreateMsgMetrics(msgId);

            // Record metrics
            metrics.counter.increment();
            //TODO 改为纳秒
            metrics.waitTimer.record(record.getWaitCost(), TimeUnit.MILLISECONDS);
            metrics.totalTimer.record(record.getTotalCost(), TimeUnit.MILLISECONDS);
            metrics.processTimer.record(record.getProcessCost(), TimeUnit.MILLISECONDS);
        });
    }

    /**
     * 消息指标
     * @param counter
     * @param waitTimer
     * @param totalTimer
     * @param processTimer
     */
    private record MsgMetrics(Counter counter, Timer waitTimer, Timer totalTimer, Timer processTimer) {
    }
}
