package com.lc.billion.icefire.game.metrics;

/**
 * 游戏消息记录
 */
public record GameMsgRecord(
        int msgId,
        String name,
        long playerId,
        long msgCreateTime,
        long exeStartTime,
        long exeEndTime
) {

    public long getProcessCost() {
        return getTotalCost() - getWaitCost();
    }

    public long getWaitCost() {
        return exeStartTime - msgCreateTime;
    }

    public long getTotalCost() {
        return exeEndTime - msgCreateTime;
    }
}
