package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 游戏网络包指标记录
 */
@Slf4j
public class GamePacketMetrics {

    private final MeterRegistry meterRegistry;
    private final GamePacketMetricsConfig config;
    private final Map<String, PacketTypeMetrics> metricsMap = new ConcurrentHashMap<>();

    public GamePacketMetrics(MeterRegistryManager meterRegistryManager, GamePacketMetricsConfig config) {
        this.meterRegistry = meterRegistryManager.getRegistry();
        this.config = config;
    }

    /**
     * 记录消息包指标
     * @param packetInfo 消息包信息
     */
    public void record(PacketInfo packetInfo) {
        getTypeMetrics(packetInfo).record(packetInfo);
    }

    private PacketTypeMetrics getTypeMetrics(PacketInfo packetInfo) {
        return metricsMap.computeIfAbsent(getPacketTypeKey(packetInfo), key -> new PacketTypeMetrics(config, meterRegistry,
                packetInfo.type, packetInfo.inbound));
    }

    private static String getPacketTypeKey(PacketInfo packetInfo) {
        return String.format("%s.%s", packetInfo.type(), packetInfo.inbound ? "in" : "out");
    }

    /**
     * 消息包信息
     */
    public record PacketInfo(boolean inbound, int length, String type) {

    }

    /**
     * 消息类型指标
     */
    public static class PacketTypeMetrics {

        private final Counter counter;
        private final DistributionSummary lengthSummary;
        private final GamePacketMetricsConfig config;

        public PacketTypeMetrics(GamePacketMetricsConfig config, MeterRegistry meterRegistry, String type, boolean inbound) {
            this.config = config;
            String name = String.format("game.packet.%s", inbound ? "in" : "out");
            counter = Counter.builder(name)
                    .description("game packet count")
                    .tag("type", type)
                    .register(meterRegistry);
            lengthSummary = DistributionSummary.builder(name + ".len")
                    .description("game packet length")
                    .tag("type", type)
                    .register(meterRegistry);
        }

        void record(PacketInfo packetInfo) {
            counter.increment();
            lengthSummary.record(packetInfo.length);
            // 如果超过阈值，打印日志
            if (packetInfo.inbound()) {
                if (packetInfo.length > config.getInboundLengthErrorThreshold()) {
                    log.error("inbound packet length too long: {}, msgType: {}", packetInfo.length, packetInfo.type);
                } else if (packetInfo.length > config.getInboundLengthWarnThreshold()) {
                    log.warn("inbound packet length too long: {}, msgType: {}", packetInfo.length, packetInfo.type);
                }
            } else {
                if (packetInfo.length > config.getOutboundLengthErrorThreshold()) {
                    log.error("outbound packet length too long: {}, msgType: {}", packetInfo.length, packetInfo.type);
                } else if (packetInfo.length > config.getOutboundLengthWarnThreshold()) {
                    log.warn("outbound packet length too long: {}, msgType: {}", packetInfo.length, packetInfo.type);
                }
            }
        }
    }
}
