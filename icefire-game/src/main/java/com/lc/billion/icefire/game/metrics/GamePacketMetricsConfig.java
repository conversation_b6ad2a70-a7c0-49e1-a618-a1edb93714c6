package com.lc.billion.icefire.game.metrics;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class GamePacketMetricsConfig {

    @Value("${game.monitor.metricsEnabled:true}")
    private boolean enabled = true;

    @Value("${game.monitor.packetInboundLengthWarnThreshold:4096}")
    private int inboundLengthWarnThreshold = 4096;//默认4KB

    @Value("${game.monitor.packetOutboundLengthWarnThreshold:262144}")
    private int outboundLengthWarnThreshold = 262144;//默认256KB

    @Value("${game.monitor.packetInboundLengthErrorThreshold:16384}")
    private int inboundLengthErrorThreshold = 16384;//默认16KB

    @Value("${game.monitor.packetOutboundLengthErrorThreshold:1048576}")
    private int outboundLengthErrorThreshold = 1048576; //默认1MB
}
