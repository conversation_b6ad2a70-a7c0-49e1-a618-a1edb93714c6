package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 游戏 Ticker 指标记录类
 */
public class GameTickerMetrics {

    private static final Logger log = LoggerFactory.getLogger(GameTickerMetrics.class);

    private final MeterRegistryManager meterRegistryManager;

    // 缓存 Timer 实例，避免重复创建
    private final Map<String, Timer> timerCache = new ConcurrentHashMap<>();

    public GameTickerMetrics(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }

    /**
     * 记录 Ticker 执行时间
     *
     * @param tickerName    Ticker 名称
     * @param durationNanos 执行时间（纳秒）
     */
    public void recordTickerExecution(String tickerName, long durationNanos) {
        meterRegistryManager.safeExecuteAsync(() -> {
            Timer timer = getOrCreateTimer(tickerName, "游戏 Ticker 执行时间");
            timer.record(durationNanos, TimeUnit.NANOSECONDS);
        });
    }

    /**
     * 获取或创建 Timer
     */
    private Timer getOrCreateTimer(String name, String description, String... tags) {
        String cacheKey = buildCacheKey(name, tags);
        return timerCache.computeIfAbsent(cacheKey,
                k -> meterRegistryManager.timer(name, description, tags));
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String name, String... tags) {
        if (tags == null || tags.length == 0) {
            return name;
        }
        StringBuilder sb = new StringBuilder(name);
        for (String tag : tags) {
            sb.append(":").append(tag);
        }
        return sb.toString();
    }


}
