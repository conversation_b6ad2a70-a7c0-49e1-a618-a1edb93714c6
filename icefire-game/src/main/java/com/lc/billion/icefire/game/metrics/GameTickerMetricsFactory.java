package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * GameTickerMetrics 工厂类
 */
@Component
public class GameTickerMetricsFactory {

    @Autowired
    private MeterRegistryManager meterRegistryManager;

    /**
     * 创建 GameTickerMetrics 实例
     */
    public GameTickerMetrics create() {
        return new GameTickerMetrics(meterRegistryManager);
    }
}
