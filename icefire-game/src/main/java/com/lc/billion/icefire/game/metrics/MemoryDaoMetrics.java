package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;

import java.util.function.Supplier;

/**
 * MemoryDao 指标记录类
 * 用于记录 MemoryDao 相关的监控指标
 */
public class MemoryDaoMetrics {

    private final MeterRegistryManager meterRegistryManager;
    private final Counter createCounter;
    private final Counter updateCounter;
    private final Counter deleteCounter;

    public MemoryDaoMetrics(MeterRegistryManager meterRegistryManager, Class<?> entityClass, Supplier<Number> entitySizeSupplier) {
        this.meterRegistryManager = meterRegistryManager;
        meterRegistryManager.gauge("game.dao.memory.size", "memory dao size", entitySizeSupplier, "entityClass", entityClass.getSimpleName());
        createCounter = newCounter(entityClass, "create");
        updateCounter = newCounter(entityClass, "update");
        deleteCounter = newCounter(entityClass, "delete");
    }

    /**
     * 记录创建操作
     */
    public void recordCreate() {
        createCounter.increment();
    }

    /**
     * 记录更新操作
     */
    public void recordUpdate() {
        updateCounter.increment();
    }

    /**
     * 记录删除操作
     */
    public void recordDelete() {
        deleteCounter.increment();
    }

    private Counter newCounter(Class<?> entityClass, String op) {
        String[] tags = {"entityClass" ,  entityClass.getSimpleName(), "op", op};
        return meterRegistryManager.counter("game.dao.memory","memory dao",tags);

    }
}
