package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * MemoryDaoMetrics 工厂类
 * 用于创建 MemoryDaoMetrics 实例，自动注入 MeterRegistryManager 依赖
 */
@Component
public class MemoryDaoMetricsFactory {

    @Autowired
    private MeterRegistryManager meterRegistryManager;

    /**
     * 创建 MemoryDaoMetrics 实例
     *
     * @param entityClass 实体类
     * @return MemoryDaoMetrics 实例
     */
    public MemoryDaoMetrics create(Class<?> entityClass, Supplier<Number> entitySizeSupplier) {
        return new MemoryDaoMetrics(meterRegistryManager, entityClass, entitySizeSupplier);
    }
}
