package com.lc.billion.icefire.game.metrics;

import com.mongodb.event.CommandFailedEvent;
import com.mongodb.event.CommandListener;
import com.mongodb.event.CommandStartedEvent;
import com.mongodb.event.CommandSucceededEvent;
import org.bson.BsonDocument;
import org.bson.BsonValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * MongoDB命令监控监听器
 * 负责收集MongoDB命令执行的性能指标和监控数据
 */
public class MongoCommandMetricsListener implements CommandListener {

    private static final Logger log = LoggerFactory.getLogger(MongoCommandMetricsListener.class);

    // 存储命令开始时间和数据库名称
    private final Map<Integer, CommandContext> commandContexts = new ConcurrentHashMap<>();


    @Override
    public void commandStarted(CommandStartedEvent event) {
        try {
            MongoDbMetrics metrics = MongoDbMetricsHolder.getMongoDbMetrics();
            if (metrics != null) {
                String commandName = event.getCommandName();
                String connectionId = event.getConnectionDescription().getConnectionId().toString();
                String databaseName = event.getDatabaseName();
                String collectionName = extractCollectionName(event);

                // 记录命令开始并获取开始时间
                long startTime = metrics.recordCommandStart(commandName, connectionId, databaseName, collectionName);

                // 缓存命令上下文信息，用于后续计算执行时间
                commandContexts.put(event.getRequestId(), new CommandContext(startTime, databaseName, collectionName));
            }
        } catch (Exception e) {
            log.warn("MongoDB监控记录命令开始事件失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void commandSucceeded(CommandSucceededEvent event) {
        try {
            MongoDbMetrics metrics = MongoDbMetricsHolder.getMongoDbMetrics();
            if (metrics != null) {
                String commandName = event.getCommandName();
                long elapsedTimeNanos = event.getElapsedTime(TimeUnit.NANOSECONDS);
                // 获取并移除命令上下文
                CommandContext context = commandContexts.remove(event.getRequestId());
                if (context != null) {
                    metrics.recordCommandSuccess(commandName, context.databaseName, context.collectionName, elapsedTimeNanos);
                }
            } else {
                commandContexts.remove(event.getRequestId());
            }
        } catch (Exception e) {
            log.warn("MongoDB监控记录命令成功事件失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void commandFailed(CommandFailedEvent event) {
        try {
            // 添加监控指标记录
            MongoDbMetrics metrics = MongoDbMetricsHolder.getMongoDbMetrics();
            if (metrics != null) {
                String commandName = event.getCommandName();
                long elapsedTimeNanos = event.getElapsedTime(TimeUnit.NANOSECONDS);
                Throwable throwable = event.getThrowable();

                // 获取并移除命令上下文
                CommandContext context = commandContexts.remove(event.getRequestId());

                if (context != null) {
                    metrics.recordCommandFailure(commandName, context.databaseName, context.collectionName, elapsedTimeNanos, throwable);
                }
            } else {
                commandContexts.remove(event.getRequestId());
            }
        } catch (Exception e) {
            log.warn("MongoDB监控记录命令失败事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从MongoDB命令中提取集合名称
     *
     * @param event 命令开始事件
     * @return 集合名称，如果无法提取则返回"unknown"
     */
    private String extractCollectionName(CommandStartedEvent event) {
        try {
            BsonDocument command = event.getCommand();
            String commandName = event.getCommandName();

            // 大多数MongoDB命令的集合名称是第一个字段的值
            BsonValue collectionValue = command.get(commandName);
            if (collectionValue != null && collectionValue.isString()) {
                return collectionValue.asString().getValue();
            }

            // 特殊处理一些命令
            switch (commandName) {
                case "aggregate":
                case "count":
                case "distinct":
                case "find":
                case "insert":
                case "update":
                case "delete":
                case "findAndModify":
                    if (collectionValue != null && collectionValue.isString()) {
                        return collectionValue.asString().getValue();
                    }
                    break;
                case "drop":
                    BsonValue dropValue = command.get("drop");
                    if (dropValue != null && dropValue.isString()) {
                        return dropValue.asString().getValue();
                    }
                    break;
                default:
                    // 其他命令尝试查找常见的集合名称字段
                    for (String field : new String[]{"collection", "coll", "ns"}) {
                        BsonValue value = command.get(field);
                        if (value != null && value.isString()) {
                            return value.asString().getValue();
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            log.debug("无法从MongoDB命令中提取集合名称: {}", e.getMessage());
        }

        return "unknown";
    }
    
    /**
     * 命令上下文，保存开始时间、数据库名称和集合名称
     */
    private record CommandContext(long startTime, String databaseName, String collectionName) {
    }
} 