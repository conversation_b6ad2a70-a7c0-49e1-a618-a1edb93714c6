package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * MongoDB 指标收集器
 * <p>
 * 收集MongoDB命令执行的各种监控指标，包括：
 * - 命令执行时间
 * - 命令执行次数
 * - 成功/失败统计
 * - 不同命令类型的分布
 * </p>
 */
public class MongoDbMetrics {
    
    public static final String COMMAND_TIMER_NAME = "mongodb.command.duration";
    public static final String COMMAND_COUNTER_NAME = "mongodb.command.total";
    public static final String COMMAND_TIMER_DESCRIPTION = "MongoDB command execution time";
    public static final String COMMAND_COUNTER_DESCRIPTION = "MongoDB command execution count";
    
    private final MeterRegistryManager meterRegistryManager;
    private final ConcurrentHashMap<String, MongoCommandMetrics> commandMetricsMap = new ConcurrentHashMap<>();
    
    public MongoDbMetrics(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }
    
    /**
     * 记录命令开始事件
     * 
     * @param commandName 命令名称
     * @param connectionId 连接ID
     * @param databaseName 数据库名称
     * @param collectionName 集合名称
     * @return 开始时间戳（用于计算执行时间）
     */
    public long recordCommandStart(String commandName, String connectionId, String databaseName, String collectionName) {
        meterRegistryManager.safeExecuteAsync(() -> {
            MongoCommandMetrics metrics = getOrCreateCommandMetrics(commandName, databaseName, collectionName);
            metrics.totalCounter.increment();
        });
        return System.nanoTime();
    }
    
    /**
     * 记录命令成功完成事件
     * 
     * @param commandName 命令名称
     * @param databaseName 数据库名称
     * @param collectionName 集合名称
     * @param elapsedTimeNanos 执行时间（纳秒）
     */
    public void recordCommandSuccess(String commandName, String databaseName, String collectionName, long elapsedTimeNanos) {
        meterRegistryManager.safeExecuteAsync(() -> {
            MongoCommandMetrics metrics = getOrCreateCommandMetrics(commandName, databaseName, collectionName);
            metrics.successCounter.increment();
            metrics.durationTimer.record(elapsedTimeNanos, TimeUnit.NANOSECONDS);
        });
    }
    
    /**
     * 记录命令失败事件
     * 
     * @param commandName 命令名称
     * @param databaseName 数据库名称
     * @param collectionName 集合名称
     * @param elapsedTimeNanos 执行时间（纳秒）
     * @param throwable 异常信息
     */
    public void recordCommandFailure(String commandName, String databaseName, String collectionName, long elapsedTimeNanos, Throwable throwable) {
        meterRegistryManager.safeExecuteAsync(() -> {
            MongoCommandMetrics metrics = getOrCreateCommandMetrics(commandName, databaseName, collectionName);
            metrics.failureCounter.increment();
            metrics.durationTimer.record(elapsedTimeNanos, TimeUnit.NANOSECONDS);
            
            // 记录具体异常类型的计数器
            String exceptionType = throwable != null ? throwable.getClass().getSimpleName() : "Unknown";
            String errorCounterName = "mongodb.command.errors";
            meterRegistryManager.incrementCounter(errorCounterName, 
                "command", commandName,
                "database", databaseName,
                "collection", collectionName,
                "exception", exceptionType);
        });
    }
    
    /**
     * 获取或创建命令指标
     */
    private MongoCommandMetrics getOrCreateCommandMetrics(String commandName, String databaseName, String collectionName) {
        String cacheKey = buildCacheKey(commandName, databaseName, collectionName);
        return commandMetricsMap.computeIfAbsent(cacheKey, key -> {
            String[] tags = {"command", commandName, "database", databaseName, "collection", collectionName};
            
            Timer durationTimer = meterRegistryManager.timer(
                COMMAND_TIMER_NAME,
                COMMAND_TIMER_DESCRIPTION,
                tags
            );
            
            Counter totalCounter = meterRegistryManager.counter(
                COMMAND_COUNTER_NAME,
                COMMAND_COUNTER_DESCRIPTION,
                combineArrays(tags, new String[]{"result", "total"})
            );
            
            Counter successCounter = meterRegistryManager.counter(
                COMMAND_COUNTER_NAME,
                COMMAND_COUNTER_DESCRIPTION,
                combineArrays(tags, new String[]{"result", "success"})
            );
            
            Counter failureCounter = meterRegistryManager.counter(
                COMMAND_COUNTER_NAME,
                COMMAND_COUNTER_DESCRIPTION,
                combineArrays(tags, new String[]{"result", "failure"})
            );
            
            return new MongoCommandMetrics(durationTimer, totalCounter, successCounter, failureCounter);
        });
    }
    
    /**
     * 构建缓存键
     */
    private String buildCacheKey(String commandName, String databaseName, String collectionName) {
        return commandName + ":" + databaseName + ":" + collectionName;
    }
    
    /**
     * 合并数组
     */
    private String[] combineArrays(String[] array1, String[] array2) {
        String[] result = new String[array1.length + array2.length];
        System.arraycopy(array1, 0, result, 0, array1.length);
        System.arraycopy(array2, 0, result, array1.length, array2.length);
        return result;
    }
    
    /**
     * MongoDB命令指标容器
     */
    private record MongoCommandMetrics(
        Timer durationTimer,
        Counter totalCounter,
        Counter successCounter,
        Counter failureCounter
    ) {}
} 