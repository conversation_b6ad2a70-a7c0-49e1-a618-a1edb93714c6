package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * MongoDbMetrics 工厂类
 * <p>
 * 提供统一的MongoDB监控指标实例创建接口
 * </p>
 */
@Component
public class MongoDbMetricsFactory {
    
    @Autowired
    private MeterRegistryManager meterRegistryManager;
    
    /**
     * Spring初始化后自动创建并设置全局MongoDB指标实例
     */
    @PostConstruct
    public void init() {
        MongoDbMetrics mongoDbMetrics = create();
        MongoDbMetricsHolder.setMongoDbMetrics(mongoDbMetrics);
    }

    /**
     * Spring容器关闭前清理MongoDB监控资源
     */
    @PreDestroy
    public void destroy() {
        MongoDbMetricsHolder.shutdown();
    }

    /**
     * 创建 MongoDbMetrics 实例
     */
    public MongoDbMetrics create() {
        return new MongoDbMetrics(meterRegistryManager);
    }
} 