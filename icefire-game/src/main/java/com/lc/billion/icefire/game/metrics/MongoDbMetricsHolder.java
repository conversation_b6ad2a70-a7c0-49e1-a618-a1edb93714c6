package com.lc.billion.icefire.game.metrics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * MongoDB指标静态持有者
 * <p>
 * 由于MongoDB客户端在Spring容器完全初始化之前就需要创建，
 * 使用静态持有者模式来管理MongoDbMetrics实例的获取
 * </p>
 */
public class MongoDbMetricsHolder {
    
    private static final Logger log = LoggerFactory.getLogger(MongoDbMetricsHolder.class);
    
    private static volatile MongoDbMetrics mongoDbMetrics;
    private static volatile boolean shutdown = false;
    
    /**
     * 设置MongoDbMetrics实例
     * 
     * @param metrics MongoDbMetrics实例
     */
    public static void setMongoDbMetrics(MongoDbMetrics metrics) {
        if (shutdown) {
            log.debug("MongoDB监控已关闭，忽略设置操作");
            return;
        }
        mongoDbMetrics = metrics;
        log.info("MongoDB监控指标已初始化");
    }
    
    /**
     * 获取MongoDbMetrics实例
     * 
     * @return MongoDbMetrics实例，如果未初始化或已关闭则返回null
     */
    public static MongoDbMetrics getMongoDbMetrics() {
        return shutdown ? null : mongoDbMetrics;
    }
    
    /**
     * 检查MongoDbMetrics是否已初始化
     * 
     * @return true如果已初始化且未关闭，false否则
     */
    public static boolean isInitialized() {
        return !shutdown && mongoDbMetrics != null;
    }
    
    /**
     * 关闭MongoDB监控
     * 在应用关闭时调用，确保不再处理新的监控请求
     */
    public static void shutdown() {
        shutdown = true;
        mongoDbMetrics = null;
        log.info("MongoDB监控指标已关闭");
    }
    
    /**
     * 检查是否已关闭
     * 
     * @return true如果已关闭，false否则
     */
    public static boolean isShutdown() {
        return shutdown;
    }
} 