package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Redis 指标收集器
 * 收集Redis操作的各种监控指标，包括：
 * - 操作执行时间
 * - 操作执行次数
 * - 成功/失败统计
 * - 慢查询统计
 */
public class RedisMetrics {

    public static final String OPERATION_TIMER_NAME = "redis.operation.duration";
    public static final String OPERATION_COUNTER_NAME = "redis.operation.total";
    public static final String SLOW_OPERATION_COUNTER_NAME = "redis.operation.slow";
    public static final String ERROR_COUNTER_NAME = "redis.operation.errors";

    public static final String TIMER_DESCRIPTION = "Redis operation execution time";
    public static final String COUNTER_DESCRIPTION = "Redis operation execution count";
    public static final String SLOW_COUNTER_DESCRIPTION = "Redis slow operation count";
    public static final String ERROR_COUNTER_DESCRIPTION = "Redis operation error count";

    private final MeterRegistryManager meterRegistryManager;
    private final Map<String, RedisOperationMetrics> operationMetricsMap = new ConcurrentHashMap<>();

    // 慢查询阈值（毫秒）
    private static final long SLOW_OPERATION_THRESHOLD_MS = 100L;

    public RedisMetrics(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }

    /**
     * 记录Redis操作指标
     *
     * @param dataType   Redis数据类型
     * @param operation  操作类型（如：get, set, zadd等）
     * @param costTimeMs 执行耗时（毫秒）
     * @param success    是否成功
     */
    public void recordOperation(String dataType, String operation, long costTimeMs, boolean success) {
        meterRegistryManager.safeExecuteAsync(() -> {
            try {
                RedisOperationMetrics metrics = getOrCreateOperationMetrics(dataType, operation);

                // 记录执行时间
                metrics.timer.record(costTimeMs, TimeUnit.MILLISECONDS);

                // 记录总次数
                metrics.totalCounter.increment();

                // 记录成功/失败次数
                if (success) {
                    metrics.successCounter.increment();
                } else {
                    metrics.errorCounter.increment();
                }

                // 记录慢查询
                if (costTimeMs >= SLOW_OPERATION_THRESHOLD_MS) {
                    metrics.slowCounter.increment();
                }

            } catch (Exception e) {
                // 监控异常不应影响业务，静默处理
            }
        });
    }

    /**
     * 记录Redis操作异常
     *
     * @param dataType  Redis数据类型
     * @param operation 操作类型
     * @param exception 异常
     */
    public void recordException(String dataType, String operation, Exception exception) {
        meterRegistryManager.safeExecuteAsync(() -> {
            try {
                RedisOperationMetrics metrics = getOrCreateOperationMetrics(dataType, operation);
                metrics.totalCounter.increment();
                metrics.errorCounter.increment();
            } catch (Exception e) {
                // 监控异常不应影响业务，静默处理
            }
        });
    }

    /**
     * 获取或创建操作指标
     */
    private RedisOperationMetrics getOrCreateOperationMetrics(String dataType, String operation) {
        String key = dataType + ":" + operation;
        return operationMetricsMap.computeIfAbsent(key, k -> {
            String[] tags = {
                    "dataType", dataType,
                    "operation", operation
            };

            Timer timer = meterRegistryManager.timer(OPERATION_TIMER_NAME, TIMER_DESCRIPTION, tags);
            Counter totalCounter = meterRegistryManager.counter(OPERATION_COUNTER_NAME, COUNTER_DESCRIPTION,
                    "dataType", dataType, "operation", operation, "result", "total");
            Counter successCounter = meterRegistryManager.counter(OPERATION_COUNTER_NAME, COUNTER_DESCRIPTION,
                    "dataType", dataType, "operation", operation, "result", "success");
            Counter errorCounter = meterRegistryManager.counter(ERROR_COUNTER_NAME, ERROR_COUNTER_DESCRIPTION,
                    "dataType", dataType, "operation", operation, "result", "error");
            Counter slowCounter = meterRegistryManager.counter(SLOW_OPERATION_COUNTER_NAME, SLOW_COUNTER_DESCRIPTION, tags);

            return new RedisOperationMetrics(timer, totalCounter, successCounter, errorCounter, slowCounter);
        });
    }

    /**
     * Redis操作指标容器
     */
    private record RedisOperationMetrics(Timer timer, Counter totalCounter, Counter successCounter,
                                         Counter errorCounter, Counter slowCounter) {
    }
} 