package com.lc.billion.icefire.game.metrics;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Redis指标工厂类
 * 负责创建和管理Redis监控指标实例
 */
@Component
public class RedisMetricsFactory {

    private static final Logger log = LoggerFactory.getLogger(RedisMetricsFactory.class);

    @Autowired
    private MeterRegistryManager meterRegistryManager;

    @PostConstruct
    public void init() {
        try {
            RedisMetrics redisMetrics = new RedisMetrics(meterRegistryManager);
            RedisMetricsHolder.setRedisMetrics(redisMetrics);
            log.info("Redis监控指标初始化成功");
        } catch (Exception e) {
            log.error("Redis监控指标初始化失败", e);
        }
    }
} 