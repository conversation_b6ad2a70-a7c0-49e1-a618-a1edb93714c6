package com.lc.billion.icefire.game.metrics;

/**
 * Redis指标静态持有者
 * 提供静态方式访问Redis监控指标实例
 */
public class RedisMetricsHolder {
    
    private static volatile RedisMetrics redisMetrics;
    
    /**
     * 设置Redis监控指标实例
     * 由RedisMetricsFactory在Spring初始化完成后调用
     */
    public static void setRedisMetrics(RedisMetrics metrics) {
        redisMetrics = metrics;
    }
    
    /**
     * 获取Redis监控指标实例
     * @return Redis监控指标实例，如果未初始化则返回null
     */
    public static RedisMetrics getRedisMetrics() {
        return redisMetrics;
    }
    
    /**
     * 检查Redis监控指标是否已初始化
     */
    public static boolean isInitialized() {
        return redisMetrics != null;
    }
} 