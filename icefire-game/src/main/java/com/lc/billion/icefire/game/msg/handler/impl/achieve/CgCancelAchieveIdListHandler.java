package com.lc.billion.icefire.game.msg.handler.impl.achieve;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.achieve.AchievementServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgCancelAchieveIdList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * @Author: huy<PERSON><PERSON>
 * @Date: 2022/1/25 19:16
 * @Description: 取消选择的勋章墙
 **/
@Controller
public class CgCancelAchieveIdListHandler extends CgAbstractMessageHandler<CgCancelAchieveIdList> {
    @Autowired
    private AchievementServiceImpl achievementService;

    @Override
    protected void handle(Role role, CgCancelAchieveIdList message) {
        achievementService.handlerCancelAchieveIdList(role, message);
    }
}
