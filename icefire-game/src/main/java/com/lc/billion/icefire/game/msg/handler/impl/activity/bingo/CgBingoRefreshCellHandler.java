package com.lc.billion.icefire.game.msg.handler.impl.activity.bingo;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.bingo.BingoActivityServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgBingoRefreshCell;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @date 2022/3/22 15:49
 */

@Controller
public class CgBingoRefreshCellHandler extends CgAbstractMessageHandler<CgBingoRefreshCell> {

    @Autowired
    private BingoActivityServiceImpl bingoActivityService;

    @Override
    protected void handle(Role role, CgBingoRefreshCell message) {

        bingoActivityService.refreshRoleCell(role);
    }
}
