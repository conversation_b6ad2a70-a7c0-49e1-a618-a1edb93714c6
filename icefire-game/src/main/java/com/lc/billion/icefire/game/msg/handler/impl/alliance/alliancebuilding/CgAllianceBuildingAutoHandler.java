package com.lc.billion.icefire.game.msg.handler.impl.alliance.alliancebuilding;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.alliancebuilding.AllianceBuildingServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.regioncapital.RegionCapitalService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgAllianceBuildingAuto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class CgAllianceBuildingAutoHandler extends CgAbstractMessageHandler<CgAllianceBuildingAuto> {
	@Autowired
	AllianceBuildingServiceImpl allianceBuildingService;

	@Override
	public void handle(Role role, CgAllianceBuildingAuto message) {
		allianceBuildingService.stationAuto(role, message.getId(), message.isAuto());
	}
}
