package com.lc.billion.icefire.game.msg.handler.impl.alliance.reinforce;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.reinforce.AllianceReinforceService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgAllianceReinforceKick;

/**
 * 
 * 踢回某个 援助玩家
 * 
 * <AUTHOR> 2019-04-05
 *
 */
@Controller
public class CgAllianceReinforceKickHandler extends CgAbstractMessageHandler<CgAllianceReinforceKick> {

	@Autowired
	private AllianceReinforceService reinforceService;

	@Override
	public void handle(Role role, CgAllianceReinforceKick message) {
		reinforceService.kick(role, message.getRoleId());
	}

}
