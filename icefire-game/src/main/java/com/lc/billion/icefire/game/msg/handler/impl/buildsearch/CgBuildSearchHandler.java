package com.lc.billion.icefire.game.msg.handler.impl.buildsearch;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.buildsearch.BuildSearchService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgBuildSearch;
import com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Slf4j
@Controller
public class CgBuildSearchHandler extends CgAbstractMessageHandler<CgBuildSearch> {
    
    @Autowired
    private BuildSearchService buildSearchService;

    @Override
    protected void handle(Role role, CgBuildSearch message) {
        try {
            int buildId = message.getBuildId();
            int pointId = message.getPointId();
            
            log.debug("处理建筑探索请求: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
            
            // 执行建筑探索，获取完整结果
            var searchResult = buildSearchService.doBuildSearch(role, buildId, pointId);
            
            if (searchResult.getErrorCode() == PsBuildSearchErrorCode.SUCCESS) {
                
                log.debug("建筑探索成功: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
            } else {
                log.warn("建筑探索失败: roleId={}, buildId={}, pointId={}, errorCode={}", 
                    role.getId(), buildId, pointId, searchResult.getErrorCode());
            }
            // 发送响应消息
            role.send(searchResult);
            
        } catch (Exception e) {
            log.error("处理建筑探索请求异常: roleId={}, message={}", role.getId(), message, e);
        }
    }
    

}
