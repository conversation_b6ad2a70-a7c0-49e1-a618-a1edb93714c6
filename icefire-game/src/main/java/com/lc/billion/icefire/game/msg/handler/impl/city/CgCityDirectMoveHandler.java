package com.lc.billion.icefire.game.msg.handler.impl.city;

import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.trusteeship.TrusteeshipType;
import com.lc.billion.icefire.game.biz.service.impl.role.RoleCityServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.trusteeship.TrusteeshipService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgCityDirectMove;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * 
 * 玩家高级迁城(指定目标点)
 * 
 * <AUTHOR> 2019-02-18
 *
 */
@Controller
public class CgCityDirectMoveHandler extends CgAbstractMessageHandler<CgCityDirectMove> {
	@Autowired
	private RoleCityServiceImpl citySrv;
	@Autowired
	private TrusteeshipService trusteeshipService;

	@Override
	public void handle(Role role, CgCityDirectMove message) {
		if (!message.isIsTrusteeship()) {
			citySrv.directMoveCity(role, Point.getInstance(message.getX(), message.getY()), message.isAllianceMove(), true, false);
		} else {
			Role other = trusteeshipService.checkTrusteeshipOp(role, TrusteeshipType.DIRECT_MOVE);
			if (null != other) {
				if (message.isAllianceMove()) {
					logger.info("handle trusteeship not use alliance move role:{} other:{}", role.getRoleId(), other.getRoleId());
					return;
				}
				boolean isSuc = citySrv.directMoveCity(other, Point.getInstance(message.getX(), message.getY()), message.isAllianceMove(), false, false);
				if (isSuc) {
					trusteeshipService.reduceTrusteeshipItem(role, TrusteeshipType.DIRECT_MOVE);
				}
			}
		}
	}
}
