package com.lc.billion.icefire.game.msg.handler.impl.people;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.guide.StoryServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgPeopleDieEventInfo;
import com.lc.billion.icefire.protocol.constant.PsErrorCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * @ClassName CgPeopleDieEventInfoHandler
 * @Description
 * <AUTHOR>
 * @Date 2024/8/30 14:27
 * @Version 1.0
 */
@Controller
public class CgPeopleDieEventInfoHandler extends CgAbstractMessageHandler<CgPeopleDieEventInfo> {
    @Autowired
    private StoryServiceImpl storyService;

    @Override
    protected void handle(Role role, CgPeopleDieEventInfo message) {
        storyService.sendPeopleDieEventUpdate(role, PsErrorCode.SUCCESS);
    }
}
