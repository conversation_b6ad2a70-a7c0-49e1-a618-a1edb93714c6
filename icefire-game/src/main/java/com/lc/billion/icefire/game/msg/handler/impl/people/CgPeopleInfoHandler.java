package com.lc.billion.icefire.game.msg.handler.impl.people;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.people.PeopleServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgPeopleInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * @ClassName CgPeopleInfoHandler
 * @Description
 * <AUTHOR>
 * @Date 2024/6/25 16:30
 * @Version 1.0
 */
@Controller
public class CgPeopleInfoHandler extends CgAbstractMessageHandler<CgPeopleInfo> {
    @Autowired
    private PeopleServiceImpl peopleService;

    @Override
    protected void handle(Role role, CgPeopleInfo message) {
        peopleService.getPeopleInfos(role);
    }
}
