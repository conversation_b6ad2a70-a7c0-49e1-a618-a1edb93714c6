package com.lc.billion.icefire.game.msg.handler.impl.rank;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;
import com.lc.billion.icefire.protocol.CgLegionRank;

/**
 * <AUTHOR>
 *
 */
@Controller
public class CgLegionRankHandler extends CgAbstractMessageHandler<CgLegionRank> {

	@Autowired
	private LegionService legionService;

	@Override
	protected void handle(Role role, CgLegionRank message) {
		RankType rankType = RankType.findById(message.getType().getValue());
		legionService.cgLegionRank(role, rankType, message.getPage());
	}

}
