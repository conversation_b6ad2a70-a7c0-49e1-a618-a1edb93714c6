package com.lc.billion.icefire.game.msg.handler.impl.recharge;

import com.lc.billion.icefire.core.application.ServerType;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.recharge.RechargeServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgTokenBuyProduct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class CgTokenBuyProductHandler extends CgAbstractMessageHandler<CgTokenBuyProduct> {
    @Autowired
    private RechargeServiceImpl rechargeService;
    @Autowired
    private FunctionSwitchService switchService;

    @Override
    public void handle(Role role, CgTokenBuyProduct message) {
        //海外不开，目前定死
        if(ServerConfigManager.getInstance().getGameConfig().getPlatform() == ServerType.OVERSEAS){
            return;
        }
        if(!switchService.isOpen(FunctionType.CHARGE_TOKEN)){
            //功能未开放
            return;
        }

        rechargeService.tokenRecharge(role, message.getProductId(),message.getExtData());
    }
}