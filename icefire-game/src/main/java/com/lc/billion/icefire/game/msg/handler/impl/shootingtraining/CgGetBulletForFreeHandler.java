package com.lc.billion.icefire.game.msg.handler.impl.shootingtraining;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.shootingtraining.ShootingTrainingServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgGetBulletForFree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * 玩家免费领取瑞克射击活动的活动道具
 * <AUTHOR>
 */
@Controller
public class CgGetBulletForFreeHandler extends CgAbstractMessageHandler<CgGetBulletForFree> {

    @Autowired
    ShootingTrainingServiceImpl shootingTrainingService;

    @Override
    protected void handle(Role role, CgGetBulletForFree message) {
        shootingTrainingService.getBulletsForFree(role);
    }

}
