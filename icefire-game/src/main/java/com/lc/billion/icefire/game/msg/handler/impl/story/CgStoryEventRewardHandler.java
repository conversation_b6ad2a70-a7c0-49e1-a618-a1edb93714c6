package com.lc.billion.icefire.game.msg.handler.impl.story;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.guide.StoryServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgStoryEventReward;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * @ClassName CgStoryEventRewardHandler
 * @Description
 * <AUTHOR>
 * @Date 2024/3/25 15:37
 * @Version 1.0
 */
@Controller
public class CgStoryEventRewardHandler extends CgAbstractMessageHandler<CgStoryEventReward> {
    @Autowired
    private StoryServiceImpl storyService;

    @Override
    protected void handle(Role role, CgStoryEventReward message) {
        storyService.eventReward(role, message.getMainEventId(), message.getRandEventId(),
                message.getStoryEventId(), message.getSelect(), message.getPeopleIds());
    }
}
