package com.lc.billion.icefire.game.net;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.metrics.GamePacketMetricsConfig;
import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import com.simfun.sgf.net.NetConfig;
import com.simfun.sgf.net.NetMessageDecoder;
import com.simfun.sgf.net.NetMessageEncoder;
import com.simfun.sgf.net.codec.thrift.ThriftBodyDecoder;
import com.simfun.sgf.net.codec.thrift.ThriftBodyEncoder;
import com.simfun.sgf.net.compress.lz4.Lz4BodyCompressor;
import com.simfun.sgf.net.compress.lz4.Lz4BodyDecompressor;
import com.simfun.sgf.net.msg.MessageConfig;
import com.simfun.sgf.net.msg.MessageConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 指标编解码器工厂类
 */
public class MetricsCodecFactory {
    
    private static final Logger log = LoggerFactory.getLogger(MetricsCodecFactory.class);
    
    /**
     * 创建带指标收集的消息编码器
     *
     * @return 编码器实例，如果指标系统不可用则返回普通编码器
     */
    public static NetMessageEncoder createMetricsMessageEncoder(
            NetConfig gcNetConfig, 
            MessageConfigManager<MessageConfig.MessageMeta> messageConfigMgr) {

        try {
            MeterRegistryManager meterRegistryManager = Application.getBean(MeterRegistryManager.class);
            GamePacketMetricsConfig metricsConfig = Application.getBean(GamePacketMetricsConfig.class);

            // 使用带指标收集的编码器
            return new MetricsNetMessageEncoder(
                    gcNetConfig,
                    messageConfigMgr,
                    new ThriftBodyEncoder(),
                    new Lz4BodyCompressor(),
                    meterRegistryManager,
                    metricsConfig
            );
        } catch (Exception e) {
            log.warn("无法创建带指标收集的消息编码器，回退到普通编码器", e);
            return new NetMessageEncoder(gcNetConfig, messageConfigMgr, new ThriftBodyEncoder(), new Lz4BodyCompressor());
        }
    }
    
    /**
     * 创建带指标收集的消息解码器
     * 
     * @return 解码器实例，如果指标系统不可用则返回普通解码器
     */
    public static NetMessageDecoder createMetricsMessageDecoder(
            NetConfig gcNetConfig, 
            MessageConfigManager<MessageConfig.MessageMeta> messageConfigMgr) {

        ThriftBodyDecoder bodyDecoder = new ThriftBodyDecoder();
        bodyDecoder.setMessageConfigManager(messageConfigMgr);
        try {
            MeterRegistryManager meterRegistryManager = Application.getBean(MeterRegistryManager.class);
            GamePacketMetricsConfig metricsConfig = Application.getBean(GamePacketMetricsConfig.class);

            // 使用带指标收集的解码器
            return new MetricsNetMessageDecoder(
                    gcNetConfig,
                    bodyDecoder,
                    new Lz4BodyDecompressor(),
                    meterRegistryManager,
                    metricsConfig
            );
        } catch (Exception e) {
            log.warn("无法创建带指标收集的消息解码器，回退到普通解码器", e);
            return new NetMessageDecoder(gcNetConfig, bodyDecoder, new Lz4BodyDecompressor());
        }
    }

}
