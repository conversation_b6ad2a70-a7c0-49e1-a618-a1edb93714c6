package com.lc.billion.icefire.game.net;

import com.lc.billion.icefire.game.metrics.GamePacketMetrics;
import com.lc.billion.icefire.game.metrics.GamePacketMetricsConfig;
import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import com.simfun.sgf.net.NetConfig;
import com.simfun.sgf.net.NetMessageDecoder;
import com.simfun.sgf.net.codec.MessageBodyDecoder;
import com.simfun.sgf.net.compress.MessageBodyDecompressor;
import com.simfun.sgf.net.msg.NetMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 带指标收集的 NetMessage 解码器
 */
public class MetricsNetMessageDecoder extends NetMessageDecoder {

    private static final Logger log = LoggerFactory.getLogger(MetricsNetMessageDecoder.class);

    private final GamePacketMetrics gamePacketMetrics;

    public MetricsNetMessageDecoder(
            NetConfig netConfig,
            MessageBodyDecoder bodyDecoder,
            MessageBodyDecompressor bodyDecompressor,
            MeterRegistryManager meterRegistryManager,
            GamePacketMetricsConfig metricsConfig) {
        super(netConfig, bodyDecoder, bodyDecompressor);
        this.gamePacketMetrics = new GamePacketMetrics(meterRegistryManager, metricsConfig);
    }

    public MetricsNetMessageDecoder(
            NetConfig netConfig,
            MessageBodyDecoder bodyDecoder,
            MessageBodyDecompressor bodyDecompressor,
            int errorBodyLength,
            int exceptionBodyLength,
            MeterRegistryManager meterRegistryManager,
            GamePacketMetricsConfig metricsConfig) {
        super(netConfig, bodyDecoder, bodyDecompressor, errorBodyLength, exceptionBodyLength);
        this.gamePacketMetrics = new GamePacketMetrics(meterRegistryManager, metricsConfig);
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 记录解码前的缓冲区位置
        int readerIndexBefore = in.readerIndex();

        // 调用父类解码逻辑
        super.decode(ctx, in, out);

        // 解码成功，则记录指标
        if (!out.isEmpty()) {
            Object lastDecoded = out.getLast();
            if (lastDecoded instanceof NetMessage netMessage) {
                recordInboundMetrics(netMessage, in.readerIndex() - readerIndexBefore);
            }
        }
    }

    /**
     * 记录入站消息指标
     */
    private void recordInboundMetrics(NetMessage netMessage, int totalLength) {
        try {
            // 创建包信息并记录指标
            var packetInfo = new GamePacketMetrics.PacketInfo(
                    true,        // inbound = true
                    totalLength, // 消息总长度（包含头部）
                    Integer.toString(netMessage.getType())  // 消息类型
            );

            gamePacketMetrics.record(packetInfo);

            if (log.isDebugEnabled()) {
                log.debug("记录入站消息指标: type={}, length={}", netMessage.getType(), totalLength);
            }
        } catch (Exception e) {
            log.warn("记录入站消息指标时发生异常", e);
        }
    }

}