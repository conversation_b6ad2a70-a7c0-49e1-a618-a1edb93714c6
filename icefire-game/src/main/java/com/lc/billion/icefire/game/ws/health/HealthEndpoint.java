package com.lc.billion.icefire.game.ws.health;

import jakarta.jws.HandlerChain;
import jakarta.jws.WebMethod;
import jakarta.jws.WebService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.ws.impl.GmWsImpl;

/**
 *
 * <AUTHOR>
 * @sine 2016年1月7日 下午4:07:15
 *
 */
@Service
@WebService(serviceName = "Health")
@HandlerChain(file = "webservice/handler-chain.xml")
public class HealthEndpoint {

	private static Logger LOG = LoggerFactory.getLogger(HealthEndpoint.class);

	@Autowired
	private GmWsImpl gmws;

	@WebMethod
	public String health() {
		LOG.info("HealthEndpoint.health() called");
		return gmws.info();
	}

}
