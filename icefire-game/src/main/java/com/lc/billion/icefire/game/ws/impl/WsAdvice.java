package com.lc.billion.icefire.game.ws.impl;


import jakarta.xml.ws.WebServiceContext;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.BizException;
import com.lc.billion.icefire.game.biz.exec.MainWorker;
import com.simfun.sgf.net.msg.InternalMessage;
import com.simfun.sgf.thread.DefaultSimpleFuture;
import com.simfun.sgf.thread.SimpleFuture;
import com.simfun.sgf.utils.JavaUtils;

/**
 * Web Service 相关 Advice
 *
 * <AUTHOR>
 */
public class WsAdvice {
	/**
	 * Logger for this class
	 */
	private static final Logger logger = LoggerFactory.getLogger(WsAdvice.class);

	private static final int EXEC_WAIT_TIME = 10000;

	@Autowired
	private WebServiceContext wsctx;

	@Autowired
	private MainWorker mainWorker;

	public Object execute(ProceedingJoinPoint pjp) {
		return mainWorkerExec(pjp);
	}

	private Object mainWorkerExec(final ProceedingJoinPoint pjp) {
		final SimpleFuture<Object> future = new DefaultSimpleFuture<>();
		// 放入mainWorker中执行
		mainWorker.put(new InternalMessage() {

			@Override
			public void execute() {
				long now = TimeUtil.getNow();
				try {
					Object ret = pjp.proceed();
					future.setSuccess(ret);
				} catch (Throwable e) {
					future.setFailure(e);
				}
				long use = TimeUtil.getNow() - now;
				if (use > 100) {
					ErrorLogUtil.errorLog("execute gm cmd long time use pjp ", "use",use, "pjp",pjp);
				}
			}
		});
		// 等待执行结果
		boolean await = false;
		try {
			await = future.await(EXEC_WAIT_TIME);
		} catch (InterruptedException e) {
			throw new BizException("execute is interrupted","proceedingJoinPoint",pjp.toString());
		}
		if (!await) { // timeout
			throw new BizException("execute is timeout","proceedingJoinPoint",pjp.toString());
		}
		if (future.isSuccess()) {
			return future.tryGet();
		} else { // 不会cancel，前面的逻辑都没有调用future的cancel，所以只能是发生异常了
			throw JavaUtils.sneakyThrow(future.getCause());
		}
	}

}
