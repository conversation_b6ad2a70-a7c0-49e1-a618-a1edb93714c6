package com.lc.billion.icefire.rpc.vo.gvg;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lc.billion.icefire.gvgcontrol.biz.model.RZERoomInfo;
import com.lc.billion.icefire.protocol.constant.PsRZEErrorCode;
import org.springframework.beans.BeanUtils;

import com.lc.billion.icefire.rpc.vo.AbstractRPCVo;

/**
 * <AUTHOR>
 *
 */
public class RZERoomInfoVo extends AbstractRPCVo {

	private Long id;
	private Long allianceId;
	private int serverId;
	private int roomType;
	private int status;												// 状态
	private long fightPower;											// 分数
	private int signUpTimeIndex;										// 选择的时间点
	private PsRZEErrorCode errorCode;												// 0-成功，1-报名人数已满，2-没有资格
	private List<Long> formalMemberIds = new ArrayList<>();			// 正式工
	private List<Long> tempMemberIds = new ArrayList<>();				// 临时工
	private Map<Long, Integer> applyList = new HashMap<>();			// 申请列表
	private Map<Long, Integer> inviteList = new HashMap<>();			// 邀请列表
	private Long enemyAllianceId;										// 应战联盟Id
	private int enemyServerId;											// 应战联盟服Id
	private List<Long> enemyFormalMemberIds = new ArrayList<>();		// 应战联盟正式工
	private List<Long> enemyTempMemberIds = new ArrayList<>();		// 应战临时工
	private int belong;												// 审核房间的归属 0 ：己方  1:他方

	public RZERoomInfoVo() {

	}
	public RZERoomInfoVo(RZERoomInfo RZERoomInfo) {
		copy(RZERoomInfo);
	}

	public void copy(RZERoomInfo RZERoomInfo) {
		BeanUtils.copyProperties(RZERoomInfo, this);
	}

	public void setId(Long id) {
		this.id = id;
	}
	public Long getId() {
		return id;
	}

	public Long getAllianceId() {
		return allianceId;
	}
	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	public void setStatus(int status) {
		this.status = status;
	}
	public int getStatus() {
		return status;
	}

	public int getServerId() {
		return serverId;
	}
	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public int getRoomType() {
		return roomType;
	}
	public void setRoomType(int roomType) {
		this.roomType = roomType;
	}

	public void setSignUpTimeIndex(int signUpTimeIndex) {
		this.signUpTimeIndex = signUpTimeIndex;
	}
	public int getSignUpTimeIndex() {
		return signUpTimeIndex;
	}

	public void setFormalMemberIds(List<Long> formalMemberIds) {
		this.formalMemberIds = formalMemberIds;
	}
	public List<Long> getFormalMemberIds() {
		return formalMemberIds;
	}

	public void setTempMemberIds(List<Long> tempMemberIds) {
		this.tempMemberIds = tempMemberIds;
	}
	public List<Long> getTempMemberIds() {
		return tempMemberIds;
	}

	public void setEnemyAllianceId(Long enemyAllianceId) {
		this.enemyAllianceId = enemyAllianceId;
	}
	public Long getEnemyAllianceId() {
		return enemyAllianceId;
	}

	public long getFightPower() {
		return fightPower;
	}
	public void setFightPower(long fightPower) {
		this.fightPower = fightPower;
	}

	public PsRZEErrorCode getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(PsRZEErrorCode errorCode) {
		this.errorCode = errorCode;
	}

	public void setEnemyServerId(int enemyServerId) {
		this.enemyServerId = enemyServerId;
	}
	public int getEnemyServerId() {
		return enemyServerId;
	}

	public void setEnemyFormalMemberIds(List<Long> enemyFormalMemberIds) {
		this.enemyFormalMemberIds = enemyFormalMemberIds;
	}
	public List<Long> getEnemyFormalMemberIds() {
		return enemyFormalMemberIds;
	}

	public Map<Long, Integer> getApplyList() {
		return applyList;
	}
	public void setApplyList(Map<Long, Integer> applyList) {
		this.applyList = applyList;
	}

	public List<Long> getEnemyTempMemberIds() {
		return enemyTempMemberIds;
	}
	public void setEnemyTempMemberIds(List<Long> enemyTempMemberIds) {
		this.enemyTempMemberIds = enemyTempMemberIds;
	}

	public Map<Long, Integer> getInviteList() {
		return inviteList;
	}
	public void setInviteList(Map<Long, Integer> inviteList) {
		this.inviteList = inviteList;
	}

	public int getBelong() {
		return belong;
	}
	public void setBelong(int belong) {
		this.belong = belong;
	}

	public boolean canEnter(Long roleId) {
		return formalMemberIds.contains(roleId);
	}

	@Override
	public String toString() {
		return "allianceId=" + allianceId + ",serverId=" + serverId + ",fightPower=" + fightPower + ",signUpTimeIndex=" + signUpTimeIndex + ",formalMemberIds=" + formalMemberIds
				+ ",tempMemberIds=" + tempMemberIds;
	}
}
