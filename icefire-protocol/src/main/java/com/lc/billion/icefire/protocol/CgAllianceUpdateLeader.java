/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 盟主转让
 * @Message(839)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgAllianceUpdateLeader implements org.apache.thrift.TBase<CgAllianceUpdateLeader, CgAllianceUpdateLeader._Fields>, java.io.Serializable, Cloneable, Comparable<CgAllianceUpdateLeader> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgAllianceUpdateLeader");

  private static final org.apache.thrift.protocol.TField ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleId", org.apache.thrift.protocol.TType.I64, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgAllianceUpdateLeaderStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgAllianceUpdateLeaderTupleSchemeFactory();

  /**
   * 被转让玩家ID
   */
  public long roleId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 被转让玩家ID
     */
    ROLE_ID((short)1, "roleId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROLE_ID
          return ROLE_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ROLEID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("roleId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgAllianceUpdateLeader.class, metaDataMap);
  }

  public CgAllianceUpdateLeader() {
  }

  public CgAllianceUpdateLeader(
    long roleId)
  {
    this();
    this.roleId = roleId;
    setRoleIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgAllianceUpdateLeader(CgAllianceUpdateLeader other) {
    __isset_bitfield = other.__isset_bitfield;
    this.roleId = other.roleId;
  }

  public CgAllianceUpdateLeader deepCopy() {
    return new CgAllianceUpdateLeader(this);
  }

  @Override
  public void clear() {
    setRoleIdIsSet(false);
    this.roleId = 0;
  }

  /**
   * 被转让玩家ID
   */
  public long getRoleId() {
    return this.roleId;
  }

  /**
   * 被转让玩家ID
   */
  public CgAllianceUpdateLeader setRoleId(long roleId) {
    this.roleId = roleId;
    setRoleIdIsSet(true);
    return this;
  }

  public void unsetRoleId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  /** Returns true if field roleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  public void setRoleIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ROLEID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ROLE_ID:
      if (value == null) {
        unsetRoleId();
      } else {
        setRoleId((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ROLE_ID:
      return getRoleId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ROLE_ID:
      return isSetRoleId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgAllianceUpdateLeader)
      return this.equals((CgAllianceUpdateLeader)that);
    return false;
  }

  public boolean equals(CgAllianceUpdateLeader that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_roleId = true;
    boolean that_present_roleId = true;
    if (this_present_roleId || that_present_roleId) {
      if (!(this_present_roleId && that_present_roleId))
        return false;
      if (this.roleId != that.roleId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(roleId);

    return hashCode;
  }

  @Override
  public int compareTo(CgAllianceUpdateLeader other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRoleId(), other.isSetRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleId, other.roleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgAllianceUpdateLeader(");
    boolean first = true;

    sb.append("roleId:");
    sb.append(this.roleId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'roleId' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgAllianceUpdateLeaderStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgAllianceUpdateLeaderStandardScheme getScheme() {
      return new CgAllianceUpdateLeaderStandardScheme();
    }
  }

  private static class CgAllianceUpdateLeaderStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgAllianceUpdateLeader> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgAllianceUpdateLeader struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleId = iprot.readI64();
              struct.setRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetRoleId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'roleId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgAllianceUpdateLeader struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ROLE_ID_FIELD_DESC);
      oprot.writeI64(struct.roleId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgAllianceUpdateLeaderTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgAllianceUpdateLeaderTupleScheme getScheme() {
      return new CgAllianceUpdateLeaderTupleScheme();
    }
  }

  private static class CgAllianceUpdateLeaderTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgAllianceUpdateLeader> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgAllianceUpdateLeader struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.roleId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgAllianceUpdateLeader struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.roleId = iprot.readI64();
      struct.setRoleIdIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

