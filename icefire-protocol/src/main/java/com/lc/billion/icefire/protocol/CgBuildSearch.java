/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 请求建筑内探索
 * @Message(7843)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgBuildSearch implements org.apache.thrift.TBase<CgBuildSearch, CgBuildSearch._Fields>, java.io.Serializable, Cloneable, Comparable<CgBuildSearch> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgBuildSearch");

  private static final org.apache.thrift.protocol.TField BUILD_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("buildId", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField POINT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("pointId", org.apache.thrift.protocol.TType.I32, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgBuildSearchStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgBuildSearchTupleSchemeFactory();

  /**
   * 建筑ID
   */
  public int buildId; // required
  /**
   * 探索点ID
   */
  public int pointId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 建筑ID
     */
    BUILD_ID((short)1, "buildId"),
    /**
     * 探索点ID
     */
    POINT_ID((short)2, "pointId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BUILD_ID
          return BUILD_ID;
        case 2: // POINT_ID
          return POINT_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BUILDID_ISSET_ID = 0;
  private static final int __POINTID_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BUILD_ID, new org.apache.thrift.meta_data.FieldMetaData("buildId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.POINT_ID, new org.apache.thrift.meta_data.FieldMetaData("pointId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgBuildSearch.class, metaDataMap);
  }

  public CgBuildSearch() {
  }

  public CgBuildSearch(
    int buildId,
    int pointId)
  {
    this();
    this.buildId = buildId;
    setBuildIdIsSet(true);
    this.pointId = pointId;
    setPointIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgBuildSearch(CgBuildSearch other) {
    __isset_bitfield = other.__isset_bitfield;
    this.buildId = other.buildId;
    this.pointId = other.pointId;
  }

  public CgBuildSearch deepCopy() {
    return new CgBuildSearch(this);
  }

  @Override
  public void clear() {
    setBuildIdIsSet(false);
    this.buildId = 0;
    setPointIdIsSet(false);
    this.pointId = 0;
  }

  /**
   * 建筑ID
   */
  public int getBuildId() {
    return this.buildId;
  }

  /**
   * 建筑ID
   */
  public CgBuildSearch setBuildId(int buildId) {
    this.buildId = buildId;
    setBuildIdIsSet(true);
    return this;
  }

  public void unsetBuildId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BUILDID_ISSET_ID);
  }

  /** Returns true if field buildId is set (has been assigned a value) and false otherwise */
  public boolean isSetBuildId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BUILDID_ISSET_ID);
  }

  public void setBuildIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BUILDID_ISSET_ID, value);
  }

  /**
   * 探索点ID
   */
  public int getPointId() {
    return this.pointId;
  }

  /**
   * 探索点ID
   */
  public CgBuildSearch setPointId(int pointId) {
    this.pointId = pointId;
    setPointIdIsSet(true);
    return this;
  }

  public void unsetPointId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __POINTID_ISSET_ID);
  }

  /** Returns true if field pointId is set (has been assigned a value) and false otherwise */
  public boolean isSetPointId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __POINTID_ISSET_ID);
  }

  public void setPointIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __POINTID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case BUILD_ID:
      if (value == null) {
        unsetBuildId();
      } else {
        setBuildId((java.lang.Integer)value);
      }
      break;

    case POINT_ID:
      if (value == null) {
        unsetPointId();
      } else {
        setPointId((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case BUILD_ID:
      return getBuildId();

    case POINT_ID:
      return getPointId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case BUILD_ID:
      return isSetBuildId();
    case POINT_ID:
      return isSetPointId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgBuildSearch)
      return this.equals((CgBuildSearch)that);
    return false;
  }

  public boolean equals(CgBuildSearch that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_buildId = true;
    boolean that_present_buildId = true;
    if (this_present_buildId || that_present_buildId) {
      if (!(this_present_buildId && that_present_buildId))
        return false;
      if (this.buildId != that.buildId)
        return false;
    }

    boolean this_present_pointId = true;
    boolean that_present_pointId = true;
    if (this_present_pointId || that_present_pointId) {
      if (!(this_present_pointId && that_present_pointId))
        return false;
      if (this.pointId != that.pointId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + buildId;

    hashCode = hashCode * 8191 + pointId;

    return hashCode;
  }

  @Override
  public int compareTo(CgBuildSearch other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetBuildId(), other.isSetBuildId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBuildId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.buildId, other.buildId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPointId(), other.isSetPointId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPointId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pointId, other.pointId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgBuildSearch(");
    boolean first = true;

    sb.append("buildId:");
    sb.append(this.buildId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("pointId:");
    sb.append(this.pointId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'buildId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'pointId' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgBuildSearchStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgBuildSearchStandardScheme getScheme() {
      return new CgBuildSearchStandardScheme();
    }
  }

  private static class CgBuildSearchStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgBuildSearch> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgBuildSearch struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BUILD_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.buildId = iprot.readI32();
              struct.setBuildIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // POINT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pointId = iprot.readI32();
              struct.setPointIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetBuildId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'buildId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPointId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'pointId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgBuildSearch struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(BUILD_ID_FIELD_DESC);
      oprot.writeI32(struct.buildId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(POINT_ID_FIELD_DESC);
      oprot.writeI32(struct.pointId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgBuildSearchTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgBuildSearchTupleScheme getScheme() {
      return new CgBuildSearchTupleScheme();
    }
  }

  private static class CgBuildSearchTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgBuildSearch> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgBuildSearch struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.buildId);
      oprot.writeI32(struct.pointId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgBuildSearch struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.buildId = iprot.readI32();
      struct.setBuildIdIsSet(true);
      struct.pointId = iprot.readI32();
      struct.setPointIdIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

