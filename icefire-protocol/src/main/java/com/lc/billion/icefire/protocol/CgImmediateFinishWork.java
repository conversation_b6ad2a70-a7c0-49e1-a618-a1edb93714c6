/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 请求立即完成队列
 * @Message(9003)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgImmediateFinishWork implements org.apache.thrift.TBase<CgImmediateFinishWork, CgImmediateFinishWork._Fields>, java.io.Serializable, Cloneable, Comparable<CgImmediateFinishWork> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgImmediateFinishWork");

  private static final org.apache.thrift.protocol.TField QUEUE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("queueId", org.apache.thrift.protocol.TType.STRING, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgImmediateFinishWorkStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgImmediateFinishWorkTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.lang.String queueId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    QUEUE_ID((short)1, "queueId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // QUEUE_ID
          return QUEUE_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.QUEUE_ID, new org.apache.thrift.meta_data.FieldMetaData("queueId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgImmediateFinishWork.class, metaDataMap);
  }

  public CgImmediateFinishWork() {
  }

  public CgImmediateFinishWork(
    java.lang.String queueId)
  {
    this();
    this.queueId = queueId;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgImmediateFinishWork(CgImmediateFinishWork other) {
    if (other.isSetQueueId()) {
      this.queueId = other.queueId;
    }
  }

  public CgImmediateFinishWork deepCopy() {
    return new CgImmediateFinishWork(this);
  }

  @Override
  public void clear() {
    this.queueId = null;
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getQueueId() {
    return this.queueId;
  }

  public CgImmediateFinishWork setQueueId(@org.apache.thrift.annotation.Nullable java.lang.String queueId) {
    this.queueId = queueId;
    return this;
  }

  public void unsetQueueId() {
    this.queueId = null;
  }

  /** Returns true if field queueId is set (has been assigned a value) and false otherwise */
  public boolean isSetQueueId() {
    return this.queueId != null;
  }

  public void setQueueIdIsSet(boolean value) {
    if (!value) {
      this.queueId = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case QUEUE_ID:
      if (value == null) {
        unsetQueueId();
      } else {
        setQueueId((java.lang.String)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case QUEUE_ID:
      return getQueueId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case QUEUE_ID:
      return isSetQueueId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgImmediateFinishWork)
      return this.equals((CgImmediateFinishWork)that);
    return false;
  }

  public boolean equals(CgImmediateFinishWork that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_queueId = true && this.isSetQueueId();
    boolean that_present_queueId = true && that.isSetQueueId();
    if (this_present_queueId || that_present_queueId) {
      if (!(this_present_queueId && that_present_queueId))
        return false;
      if (!this.queueId.equals(that.queueId))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetQueueId()) ? 131071 : 524287);
    if (isSetQueueId())
      hashCode = hashCode * 8191 + queueId.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(CgImmediateFinishWork other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetQueueId(), other.isSetQueueId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetQueueId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.queueId, other.queueId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgImmediateFinishWork(");
    boolean first = true;

    sb.append("queueId:");
    if (this.queueId == null) {
      sb.append("null");
    } else {
      sb.append(this.queueId);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (queueId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'queueId' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgImmediateFinishWorkStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgImmediateFinishWorkStandardScheme getScheme() {
      return new CgImmediateFinishWorkStandardScheme();
    }
  }

  private static class CgImmediateFinishWorkStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgImmediateFinishWork> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgImmediateFinishWork struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // QUEUE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.queueId = iprot.readString();
              struct.setQueueIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgImmediateFinishWork struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.queueId != null) {
        oprot.writeFieldBegin(QUEUE_ID_FIELD_DESC);
        oprot.writeString(struct.queueId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgImmediateFinishWorkTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgImmediateFinishWorkTupleScheme getScheme() {
      return new CgImmediateFinishWorkTupleScheme();
    }
  }

  private static class CgImmediateFinishWorkTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgImmediateFinishWork> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgImmediateFinishWork struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.queueId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgImmediateFinishWork struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.queueId = iprot.readString();
      struct.setQueueIdIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

