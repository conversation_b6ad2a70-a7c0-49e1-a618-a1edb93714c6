/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 获取成员列表
 * @Message(5703)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgLegionMemberList implements org.apache.thrift.TBase<CgLegionMemberList, CgLegionMemberList._Fields>, java.io.Serializable, Cloneable, Comparable<CgLegionMemberList> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgLegionMemberList");

  private static final org.apache.thrift.protocol.TField LEGION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("legionId", org.apache.thrift.protocol.TType.I64, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgLegionMemberListStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgLegionMemberListTupleSchemeFactory();

  /**
   * 军团id
   */
  public long legionId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 军团id
     */
    LEGION_ID((short)1, "legionId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // LEGION_ID
          return LEGION_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __LEGIONID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.LEGION_ID, new org.apache.thrift.meta_data.FieldMetaData("legionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgLegionMemberList.class, metaDataMap);
  }

  public CgLegionMemberList() {
  }

  public CgLegionMemberList(
    long legionId)
  {
    this();
    this.legionId = legionId;
    setLegionIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgLegionMemberList(CgLegionMemberList other) {
    __isset_bitfield = other.__isset_bitfield;
    this.legionId = other.legionId;
  }

  public CgLegionMemberList deepCopy() {
    return new CgLegionMemberList(this);
  }

  @Override
  public void clear() {
    setLegionIdIsSet(false);
    this.legionId = 0;
  }

  /**
   * 军团id
   */
  public long getLegionId() {
    return this.legionId;
  }

  /**
   * 军团id
   */
  public CgLegionMemberList setLegionId(long legionId) {
    this.legionId = legionId;
    setLegionIdIsSet(true);
    return this;
  }

  public void unsetLegionId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LEGIONID_ISSET_ID);
  }

  /** Returns true if field legionId is set (has been assigned a value) and false otherwise */
  public boolean isSetLegionId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LEGIONID_ISSET_ID);
  }

  public void setLegionIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LEGIONID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case LEGION_ID:
      if (value == null) {
        unsetLegionId();
      } else {
        setLegionId((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case LEGION_ID:
      return getLegionId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case LEGION_ID:
      return isSetLegionId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgLegionMemberList)
      return this.equals((CgLegionMemberList)that);
    return false;
  }

  public boolean equals(CgLegionMemberList that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_legionId = true;
    boolean that_present_legionId = true;
    if (this_present_legionId || that_present_legionId) {
      if (!(this_present_legionId && that_present_legionId))
        return false;
      if (this.legionId != that.legionId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(legionId);

    return hashCode;
  }

  @Override
  public int compareTo(CgLegionMemberList other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetLegionId(), other.isSetLegionId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLegionId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.legionId, other.legionId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgLegionMemberList(");
    boolean first = true;

    sb.append("legionId:");
    sb.append(this.legionId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'legionId' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgLegionMemberListStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgLegionMemberListStandardScheme getScheme() {
      return new CgLegionMemberListStandardScheme();
    }
  }

  private static class CgLegionMemberListStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgLegionMemberList> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgLegionMemberList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // LEGION_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.legionId = iprot.readI64();
              struct.setLegionIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetLegionId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'legionId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgLegionMemberList struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(LEGION_ID_FIELD_DESC);
      oprot.writeI64(struct.legionId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgLegionMemberListTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgLegionMemberListTupleScheme getScheme() {
      return new CgLegionMemberListTupleScheme();
    }
  }

  private static class CgLegionMemberListTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgLegionMemberList> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgLegionMemberList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.legionId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgLegionMemberList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.legionId = iprot.readI64();
      struct.setLegionIdIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

