/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 州府站战斗详细战斗记录
 * @Message(6965)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgRegionBattleRecord implements org.apache.thrift.TBase<CgRegionBattleRecord, CgRegionBattleRecord._Fields>, java.io.Serializable, Cloneable, Comparable<CgRegionBattleRecord> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgRegionBattleRecord");

  private static final org.apache.thrift.protocol.TField RECORD_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("recordId", org.apache.thrift.protocol.TType.I64, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgRegionBattleRecordStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgRegionBattleRecordTupleSchemeFactory();

  /**
   * 记录id
   */
  public long recordId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 记录id
     */
    RECORD_ID((short)1, "recordId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RECORD_ID
          return RECORD_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RECORDID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RECORD_ID, new org.apache.thrift.meta_data.FieldMetaData("recordId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgRegionBattleRecord.class, metaDataMap);
  }

  public CgRegionBattleRecord() {
  }

  public CgRegionBattleRecord(
    long recordId)
  {
    this();
    this.recordId = recordId;
    setRecordIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgRegionBattleRecord(CgRegionBattleRecord other) {
    __isset_bitfield = other.__isset_bitfield;
    this.recordId = other.recordId;
  }

  public CgRegionBattleRecord deepCopy() {
    return new CgRegionBattleRecord(this);
  }

  @Override
  public void clear() {
    setRecordIdIsSet(false);
    this.recordId = 0;
  }

  /**
   * 记录id
   */
  public long getRecordId() {
    return this.recordId;
  }

  /**
   * 记录id
   */
  public CgRegionBattleRecord setRecordId(long recordId) {
    this.recordId = recordId;
    setRecordIdIsSet(true);
    return this;
  }

  public void unsetRecordId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RECORDID_ISSET_ID);
  }

  /** Returns true if field recordId is set (has been assigned a value) and false otherwise */
  public boolean isSetRecordId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RECORDID_ISSET_ID);
  }

  public void setRecordIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RECORDID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case RECORD_ID:
      if (value == null) {
        unsetRecordId();
      } else {
        setRecordId((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case RECORD_ID:
      return getRecordId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case RECORD_ID:
      return isSetRecordId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgRegionBattleRecord)
      return this.equals((CgRegionBattleRecord)that);
    return false;
  }

  public boolean equals(CgRegionBattleRecord that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_recordId = true;
    boolean that_present_recordId = true;
    if (this_present_recordId || that_present_recordId) {
      if (!(this_present_recordId && that_present_recordId))
        return false;
      if (this.recordId != that.recordId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(recordId);

    return hashCode;
  }

  @Override
  public int compareTo(CgRegionBattleRecord other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRecordId(), other.isSetRecordId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRecordId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.recordId, other.recordId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgRegionBattleRecord(");
    boolean first = true;

    sb.append("recordId:");
    sb.append(this.recordId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'recordId' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgRegionBattleRecordStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgRegionBattleRecordStandardScheme getScheme() {
      return new CgRegionBattleRecordStandardScheme();
    }
  }

  private static class CgRegionBattleRecordStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgRegionBattleRecord> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgRegionBattleRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RECORD_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.recordId = iprot.readI64();
              struct.setRecordIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetRecordId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'recordId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgRegionBattleRecord struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RECORD_ID_FIELD_DESC);
      oprot.writeI64(struct.recordId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgRegionBattleRecordTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgRegionBattleRecordTupleScheme getScheme() {
      return new CgRegionBattleRecordTupleScheme();
    }
  }

  private static class CgRegionBattleRecordTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgRegionBattleRecord> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgRegionBattleRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.recordId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgRegionBattleRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.recordId = iprot.readI64();
      struct.setRecordIdIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

