/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 修改委托参数
 * @Message(7559)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgTrusteeshipModify implements org.apache.thrift.TBase<CgTrusteeshipModify, CgTrusteeshipModify._Fields>, java.io.Serializable, Cloneable, Comparable<CgTrusteeshipModify> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgTrusteeshipModify");

  private static final org.apache.thrift.protocol.TField TRUSTEESHIP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("trusteeshipId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField HOSPITAL_CAPACITY_FIELD_DESC = new org.apache.thrift.protocol.TField("hospitalCapacity", org.apache.thrift.protocol.TType.BOOL, (short)2);
  private static final org.apache.thrift.protocol.TField USE_ITEM_LIMIT_FIELD_DESC = new org.apache.thrift.protocol.TField("useItemLimit", org.apache.thrift.protocol.TType.MAP, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgTrusteeshipModifyStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgTrusteeshipModifyTupleSchemeFactory();

  public long trusteeshipId; // required
  public boolean hospitalCapacity; // optional
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.Integer> useItemLimit; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    TRUSTEESHIP_ID((short)1, "trusteeshipId"),
    HOSPITAL_CAPACITY((short)2, "hospitalCapacity"),
    USE_ITEM_LIMIT((short)3, "useItemLimit");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TRUSTEESHIP_ID
          return TRUSTEESHIP_ID;
        case 2: // HOSPITAL_CAPACITY
          return HOSPITAL_CAPACITY;
        case 3: // USE_ITEM_LIMIT
          return USE_ITEM_LIMIT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __TRUSTEESHIPID_ISSET_ID = 0;
  private static final int __HOSPITALCAPACITY_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.HOSPITAL_CAPACITY,_Fields.USE_ITEM_LIMIT};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TRUSTEESHIP_ID, new org.apache.thrift.meta_data.FieldMetaData("trusteeshipId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.HOSPITAL_CAPACITY, new org.apache.thrift.meta_data.FieldMetaData("hospitalCapacity", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.USE_ITEM_LIMIT, new org.apache.thrift.meta_data.FieldMetaData("useItemLimit", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgTrusteeshipModify.class, metaDataMap);
  }

  public CgTrusteeshipModify() {
  }

  public CgTrusteeshipModify(
    long trusteeshipId)
  {
    this();
    this.trusteeshipId = trusteeshipId;
    setTrusteeshipIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgTrusteeshipModify(CgTrusteeshipModify other) {
    __isset_bitfield = other.__isset_bitfield;
    this.trusteeshipId = other.trusteeshipId;
    this.hospitalCapacity = other.hospitalCapacity;
    if (other.isSetUseItemLimit()) {
      java.util.Map<java.lang.String,java.lang.Integer> __this__useItemLimit = new java.util.HashMap<java.lang.String,java.lang.Integer>(other.useItemLimit);
      this.useItemLimit = __this__useItemLimit;
    }
  }

  public CgTrusteeshipModify deepCopy() {
    return new CgTrusteeshipModify(this);
  }

  @Override
  public void clear() {
    setTrusteeshipIdIsSet(false);
    this.trusteeshipId = 0;
    setHospitalCapacityIsSet(false);
    this.hospitalCapacity = false;
    this.useItemLimit = null;
  }

  public long getTrusteeshipId() {
    return this.trusteeshipId;
  }

  public CgTrusteeshipModify setTrusteeshipId(long trusteeshipId) {
    this.trusteeshipId = trusteeshipId;
    setTrusteeshipIdIsSet(true);
    return this;
  }

  public void unsetTrusteeshipId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TRUSTEESHIPID_ISSET_ID);
  }

  /** Returns true if field trusteeshipId is set (has been assigned a value) and false otherwise */
  public boolean isSetTrusteeshipId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TRUSTEESHIPID_ISSET_ID);
  }

  public void setTrusteeshipIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TRUSTEESHIPID_ISSET_ID, value);
  }

  public boolean isHospitalCapacity() {
    return this.hospitalCapacity;
  }

  public CgTrusteeshipModify setHospitalCapacity(boolean hospitalCapacity) {
    this.hospitalCapacity = hospitalCapacity;
    setHospitalCapacityIsSet(true);
    return this;
  }

  public void unsetHospitalCapacity() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __HOSPITALCAPACITY_ISSET_ID);
  }

  /** Returns true if field hospitalCapacity is set (has been assigned a value) and false otherwise */
  public boolean isSetHospitalCapacity() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __HOSPITALCAPACITY_ISSET_ID);
  }

  public void setHospitalCapacityIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __HOSPITALCAPACITY_ISSET_ID, value);
  }

  public int getUseItemLimitSize() {
    return (this.useItemLimit == null) ? 0 : this.useItemLimit.size();
  }

  public void putToUseItemLimit(java.lang.String key, int val) {
    if (this.useItemLimit == null) {
      this.useItemLimit = new java.util.HashMap<java.lang.String,java.lang.Integer>();
    }
    this.useItemLimit.put(key, val);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.String,java.lang.Integer> getUseItemLimit() {
    return this.useItemLimit;
  }

  public CgTrusteeshipModify setUseItemLimit(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.Integer> useItemLimit) {
    this.useItemLimit = useItemLimit;
    return this;
  }

  public void unsetUseItemLimit() {
    this.useItemLimit = null;
  }

  /** Returns true if field useItemLimit is set (has been assigned a value) and false otherwise */
  public boolean isSetUseItemLimit() {
    return this.useItemLimit != null;
  }

  public void setUseItemLimitIsSet(boolean value) {
    if (!value) {
      this.useItemLimit = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case TRUSTEESHIP_ID:
      if (value == null) {
        unsetTrusteeshipId();
      } else {
        setTrusteeshipId((java.lang.Long)value);
      }
      break;

    case HOSPITAL_CAPACITY:
      if (value == null) {
        unsetHospitalCapacity();
      } else {
        setHospitalCapacity((java.lang.Boolean)value);
      }
      break;

    case USE_ITEM_LIMIT:
      if (value == null) {
        unsetUseItemLimit();
      } else {
        setUseItemLimit((java.util.Map<java.lang.String,java.lang.Integer>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case TRUSTEESHIP_ID:
      return getTrusteeshipId();

    case HOSPITAL_CAPACITY:
      return isHospitalCapacity();

    case USE_ITEM_LIMIT:
      return getUseItemLimit();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case TRUSTEESHIP_ID:
      return isSetTrusteeshipId();
    case HOSPITAL_CAPACITY:
      return isSetHospitalCapacity();
    case USE_ITEM_LIMIT:
      return isSetUseItemLimit();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgTrusteeshipModify)
      return this.equals((CgTrusteeshipModify)that);
    return false;
  }

  public boolean equals(CgTrusteeshipModify that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_trusteeshipId = true;
    boolean that_present_trusteeshipId = true;
    if (this_present_trusteeshipId || that_present_trusteeshipId) {
      if (!(this_present_trusteeshipId && that_present_trusteeshipId))
        return false;
      if (this.trusteeshipId != that.trusteeshipId)
        return false;
    }

    boolean this_present_hospitalCapacity = true && this.isSetHospitalCapacity();
    boolean that_present_hospitalCapacity = true && that.isSetHospitalCapacity();
    if (this_present_hospitalCapacity || that_present_hospitalCapacity) {
      if (!(this_present_hospitalCapacity && that_present_hospitalCapacity))
        return false;
      if (this.hospitalCapacity != that.hospitalCapacity)
        return false;
    }

    boolean this_present_useItemLimit = true && this.isSetUseItemLimit();
    boolean that_present_useItemLimit = true && that.isSetUseItemLimit();
    if (this_present_useItemLimit || that_present_useItemLimit) {
      if (!(this_present_useItemLimit && that_present_useItemLimit))
        return false;
      if (!this.useItemLimit.equals(that.useItemLimit))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(trusteeshipId);

    hashCode = hashCode * 8191 + ((isSetHospitalCapacity()) ? 131071 : 524287);
    if (isSetHospitalCapacity())
      hashCode = hashCode * 8191 + ((hospitalCapacity) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetUseItemLimit()) ? 131071 : 524287);
    if (isSetUseItemLimit())
      hashCode = hashCode * 8191 + useItemLimit.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(CgTrusteeshipModify other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetTrusteeshipId(), other.isSetTrusteeshipId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTrusteeshipId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.trusteeshipId, other.trusteeshipId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHospitalCapacity(), other.isSetHospitalCapacity());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHospitalCapacity()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.hospitalCapacity, other.hospitalCapacity);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetUseItemLimit(), other.isSetUseItemLimit());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUseItemLimit()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.useItemLimit, other.useItemLimit);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgTrusteeshipModify(");
    boolean first = true;

    sb.append("trusteeshipId:");
    sb.append(this.trusteeshipId);
    first = false;
    if (isSetHospitalCapacity()) {
      if (!first) sb.append(", ");
      sb.append("hospitalCapacity:");
      sb.append(this.hospitalCapacity);
      first = false;
    }
    if (isSetUseItemLimit()) {
      if (!first) sb.append(", ");
      sb.append("useItemLimit:");
      if (this.useItemLimit == null) {
        sb.append("null");
      } else {
        sb.append(this.useItemLimit);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'trusteeshipId' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgTrusteeshipModifyStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgTrusteeshipModifyStandardScheme getScheme() {
      return new CgTrusteeshipModifyStandardScheme();
    }
  }

  private static class CgTrusteeshipModifyStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgTrusteeshipModify> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgTrusteeshipModify struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TRUSTEESHIP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.trusteeshipId = iprot.readI64();
              struct.setTrusteeshipIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // HOSPITAL_CAPACITY
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.hospitalCapacity = iprot.readBool();
              struct.setHospitalCapacityIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // USE_ITEM_LIMIT
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map0 = iprot.readMapBegin();
                struct.useItemLimit = new java.util.HashMap<java.lang.String,java.lang.Integer>(2*_map0.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _key1;
                int _val2;
                for (int _i3 = 0; _i3 < _map0.size; ++_i3)
                {
                  _key1 = iprot.readString();
                  _val2 = iprot.readI32();
                  struct.useItemLimit.put(_key1, _val2);
                }
                iprot.readMapEnd();
              }
              struct.setUseItemLimitIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetTrusteeshipId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'trusteeshipId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgTrusteeshipModify struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(TRUSTEESHIP_ID_FIELD_DESC);
      oprot.writeI64(struct.trusteeshipId);
      oprot.writeFieldEnd();
      if (struct.isSetHospitalCapacity()) {
        oprot.writeFieldBegin(HOSPITAL_CAPACITY_FIELD_DESC);
        oprot.writeBool(struct.hospitalCapacity);
        oprot.writeFieldEnd();
      }
      if (struct.useItemLimit != null) {
        if (struct.isSetUseItemLimit()) {
          oprot.writeFieldBegin(USE_ITEM_LIMIT_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32, struct.useItemLimit.size()));
            for (java.util.Map.Entry<java.lang.String, java.lang.Integer> _iter4 : struct.useItemLimit.entrySet())
            {
              oprot.writeString(_iter4.getKey());
              oprot.writeI32(_iter4.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgTrusteeshipModifyTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgTrusteeshipModifyTupleScheme getScheme() {
      return new CgTrusteeshipModifyTupleScheme();
    }
  }

  private static class CgTrusteeshipModifyTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgTrusteeshipModify> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgTrusteeshipModify struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.trusteeshipId);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetHospitalCapacity()) {
        optionals.set(0);
      }
      if (struct.isSetUseItemLimit()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetHospitalCapacity()) {
        oprot.writeBool(struct.hospitalCapacity);
      }
      if (struct.isSetUseItemLimit()) {
        {
          oprot.writeI32(struct.useItemLimit.size());
          for (java.util.Map.Entry<java.lang.String, java.lang.Integer> _iter5 : struct.useItemLimit.entrySet())
          {
            oprot.writeString(_iter5.getKey());
            oprot.writeI32(_iter5.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgTrusteeshipModify struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.trusteeshipId = iprot.readI64();
      struct.setTrusteeshipIdIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.hospitalCapacity = iprot.readBool();
        struct.setHospitalCapacityIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map6 = iprot.readMapBegin(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32); 
          struct.useItemLimit = new java.util.HashMap<java.lang.String,java.lang.Integer>(2*_map6.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _key7;
          int _val8;
          for (int _i9 = 0; _i9 < _map6.size; ++_i9)
          {
            _key7 = iprot.readString();
            _val8 = iprot.readI32();
            struct.useItemLimit.put(_key7, _val8);
          }
        }
        struct.setUseItemLimitIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

