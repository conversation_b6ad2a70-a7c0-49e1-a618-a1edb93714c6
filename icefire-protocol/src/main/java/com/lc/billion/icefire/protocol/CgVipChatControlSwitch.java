/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * VIP聊天控制开关
 * @Message(159)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgVipChatControlSwitch implements org.apache.thrift.TBase<CgVipChatControlSwitch, CgVipChatControlSwitch._Fields>, java.io.Serializable, Cloneable, Comparable<CgVipChatControlSwitch> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgVipChatControlSwitch");

  private static final org.apache.thrift.protocol.TField IS_ON_FIELD_DESC = new org.apache.thrift.protocol.TField("isOn", org.apache.thrift.protocol.TType.BOOL, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgVipChatControlSwitchStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgVipChatControlSwitchTupleSchemeFactory();

  public boolean isOn; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    IS_ON((short)1, "isOn");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // IS_ON
          return IS_ON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ISON_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.IS_ON, new org.apache.thrift.meta_data.FieldMetaData("isOn", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgVipChatControlSwitch.class, metaDataMap);
  }

  public CgVipChatControlSwitch() {
  }

  public CgVipChatControlSwitch(
    boolean isOn)
  {
    this();
    this.isOn = isOn;
    setIsOnIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgVipChatControlSwitch(CgVipChatControlSwitch other) {
    __isset_bitfield = other.__isset_bitfield;
    this.isOn = other.isOn;
  }

  public CgVipChatControlSwitch deepCopy() {
    return new CgVipChatControlSwitch(this);
  }

  @Override
  public void clear() {
    setIsOnIsSet(false);
    this.isOn = false;
  }

  public boolean isIsOn() {
    return this.isOn;
  }

  public CgVipChatControlSwitch setIsOn(boolean isOn) {
    this.isOn = isOn;
    setIsOnIsSet(true);
    return this;
  }

  public void unsetIsOn() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ISON_ISSET_ID);
  }

  /** Returns true if field isOn is set (has been assigned a value) and false otherwise */
  public boolean isSetIsOn() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ISON_ISSET_ID);
  }

  public void setIsOnIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ISON_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case IS_ON:
      if (value == null) {
        unsetIsOn();
      } else {
        setIsOn((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case IS_ON:
      return isIsOn();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case IS_ON:
      return isSetIsOn();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgVipChatControlSwitch)
      return this.equals((CgVipChatControlSwitch)that);
    return false;
  }

  public boolean equals(CgVipChatControlSwitch that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_isOn = true;
    boolean that_present_isOn = true;
    if (this_present_isOn || that_present_isOn) {
      if (!(this_present_isOn && that_present_isOn))
        return false;
      if (this.isOn != that.isOn)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isOn) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(CgVipChatControlSwitch other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetIsOn(), other.isSetIsOn());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsOn()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isOn, other.isOn);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgVipChatControlSwitch(");
    boolean first = true;

    sb.append("isOn:");
    sb.append(this.isOn);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'isOn' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgVipChatControlSwitchStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgVipChatControlSwitchStandardScheme getScheme() {
      return new CgVipChatControlSwitchStandardScheme();
    }
  }

  private static class CgVipChatControlSwitchStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgVipChatControlSwitch> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgVipChatControlSwitch struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // IS_ON
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isOn = iprot.readBool();
              struct.setIsOnIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetIsOn()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'isOn' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgVipChatControlSwitch struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(IS_ON_FIELD_DESC);
      oprot.writeBool(struct.isOn);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgVipChatControlSwitchTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgVipChatControlSwitchTupleScheme getScheme() {
      return new CgVipChatControlSwitchTupleScheme();
    }
  }

  private static class CgVipChatControlSwitchTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgVipChatControlSwitch> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgVipChatControlSwitch struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeBool(struct.isOn);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgVipChatControlSwitch struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.isOn = iprot.readBool();
      struct.setIsOnIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

