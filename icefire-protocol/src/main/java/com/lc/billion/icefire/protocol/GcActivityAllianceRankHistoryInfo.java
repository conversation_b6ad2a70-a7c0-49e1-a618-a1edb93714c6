/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 查询玩家排行榜历史信息返回
 * @Message(7384)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcActivityAllianceRankHistoryInfo implements org.apache.thrift.TBase<GcActivityAllianceRankHistoryInfo, GcActivityAllianceRankHistoryInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcActivityAllianceRankHistoryInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcActivityAllianceRankHistoryInfo");

  private static final org.apache.thrift.protocol.TField ACTIVITY_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("activityMetaId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField RANKS_FIELD_DESC = new org.apache.thrift.protocol.TField("ranks", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcActivityAllianceRankHistoryInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcActivityAllianceRankHistoryInfoTupleSchemeFactory();

  /**
   * 活动的metaId
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String activityMetaId; // required
  /**
   * 排名联盟
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsRankAlliance> ranks; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 活动的metaId
     */
    ACTIVITY_META_ID((short)1, "activityMetaId"),
    /**
     * 排名联盟
     */
    RANKS((short)2, "ranks");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACTIVITY_META_ID
          return ACTIVITY_META_ID;
        case 2: // RANKS
          return RANKS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.RANKS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACTIVITY_META_ID, new org.apache.thrift.meta_data.FieldMetaData("activityMetaId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RANKS, new org.apache.thrift.meta_data.FieldMetaData("ranks", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsRankAlliance.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcActivityAllianceRankHistoryInfo.class, metaDataMap);
  }

  public GcActivityAllianceRankHistoryInfo() {
  }

  public GcActivityAllianceRankHistoryInfo(
    java.lang.String activityMetaId)
  {
    this();
    this.activityMetaId = activityMetaId;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcActivityAllianceRankHistoryInfo(GcActivityAllianceRankHistoryInfo other) {
    if (other.isSetActivityMetaId()) {
      this.activityMetaId = other.activityMetaId;
    }
    if (other.isSetRanks()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsRankAlliance> __this__ranks = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRankAlliance>(other.ranks.size());
      for (com.lc.billion.icefire.protocol.structure.PsRankAlliance other_element : other.ranks) {
        __this__ranks.add(new com.lc.billion.icefire.protocol.structure.PsRankAlliance(other_element));
      }
      this.ranks = __this__ranks;
    }
  }

  public GcActivityAllianceRankHistoryInfo deepCopy() {
    return new GcActivityAllianceRankHistoryInfo(this);
  }

  @Override
  public void clear() {
    this.activityMetaId = null;
    this.ranks = null;
  }

  /**
   * 活动的metaId
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getActivityMetaId() {
    return this.activityMetaId;
  }

  /**
   * 活动的metaId
   */
  public GcActivityAllianceRankHistoryInfo setActivityMetaId(@org.apache.thrift.annotation.Nullable java.lang.String activityMetaId) {
    this.activityMetaId = activityMetaId;
    return this;
  }

  public void unsetActivityMetaId() {
    this.activityMetaId = null;
  }

  /** Returns true if field activityMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetActivityMetaId() {
    return this.activityMetaId != null;
  }

  public void setActivityMetaIdIsSet(boolean value) {
    if (!value) {
      this.activityMetaId = null;
    }
  }

  public int getRanksSize() {
    return (this.ranks == null) ? 0 : this.ranks.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsRankAlliance> getRanksIterator() {
    return (this.ranks == null) ? null : this.ranks.iterator();
  }

  public void addToRanks(com.lc.billion.icefire.protocol.structure.PsRankAlliance elem) {
    if (this.ranks == null) {
      this.ranks = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRankAlliance>();
    }
    this.ranks.add(elem);
  }

  /**
   * 排名联盟
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsRankAlliance> getRanks() {
    return this.ranks;
  }

  /**
   * 排名联盟
   */
  public GcActivityAllianceRankHistoryInfo setRanks(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsRankAlliance> ranks) {
    this.ranks = ranks;
    return this;
  }

  public void unsetRanks() {
    this.ranks = null;
  }

  /** Returns true if field ranks is set (has been assigned a value) and false otherwise */
  public boolean isSetRanks() {
    return this.ranks != null;
  }

  public void setRanksIsSet(boolean value) {
    if (!value) {
      this.ranks = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ACTIVITY_META_ID:
      if (value == null) {
        unsetActivityMetaId();
      } else {
        setActivityMetaId((java.lang.String)value);
      }
      break;

    case RANKS:
      if (value == null) {
        unsetRanks();
      } else {
        setRanks((java.util.List<com.lc.billion.icefire.protocol.structure.PsRankAlliance>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ACTIVITY_META_ID:
      return getActivityMetaId();

    case RANKS:
      return getRanks();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ACTIVITY_META_ID:
      return isSetActivityMetaId();
    case RANKS:
      return isSetRanks();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcActivityAllianceRankHistoryInfo)
      return this.equals((GcActivityAllianceRankHistoryInfo)that);
    return false;
  }

  public boolean equals(GcActivityAllianceRankHistoryInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_activityMetaId = true && this.isSetActivityMetaId();
    boolean that_present_activityMetaId = true && that.isSetActivityMetaId();
    if (this_present_activityMetaId || that_present_activityMetaId) {
      if (!(this_present_activityMetaId && that_present_activityMetaId))
        return false;
      if (!this.activityMetaId.equals(that.activityMetaId))
        return false;
    }

    boolean this_present_ranks = true && this.isSetRanks();
    boolean that_present_ranks = true && that.isSetRanks();
    if (this_present_ranks || that_present_ranks) {
      if (!(this_present_ranks && that_present_ranks))
        return false;
      if (!this.ranks.equals(that.ranks))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetActivityMetaId()) ? 131071 : 524287);
    if (isSetActivityMetaId())
      hashCode = hashCode * 8191 + activityMetaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetRanks()) ? 131071 : 524287);
    if (isSetRanks())
      hashCode = hashCode * 8191 + ranks.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcActivityAllianceRankHistoryInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetActivityMetaId(), other.isSetActivityMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActivityMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.activityMetaId, other.activityMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRanks(), other.isSetRanks());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRanks()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ranks, other.ranks);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcActivityAllianceRankHistoryInfo(");
    boolean first = true;

    sb.append("activityMetaId:");
    if (this.activityMetaId == null) {
      sb.append("null");
    } else {
      sb.append(this.activityMetaId);
    }
    first = false;
    if (isSetRanks()) {
      if (!first) sb.append(", ");
      sb.append("ranks:");
      if (this.ranks == null) {
        sb.append("null");
      } else {
        sb.append(this.ranks);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (activityMetaId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'activityMetaId' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcActivityAllianceRankHistoryInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcActivityAllianceRankHistoryInfoStandardScheme getScheme() {
      return new GcActivityAllianceRankHistoryInfoStandardScheme();
    }
  }

  private static class GcActivityAllianceRankHistoryInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcActivityAllianceRankHistoryInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcActivityAllianceRankHistoryInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACTIVITY_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.activityMetaId = iprot.readString();
              struct.setActivityMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANKS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.ranks = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRankAlliance>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRankAlliance _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsRankAlliance();
                  _elem1.read(iprot);
                  struct.ranks.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRanksIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcActivityAllianceRankHistoryInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.activityMetaId != null) {
        oprot.writeFieldBegin(ACTIVITY_META_ID_FIELD_DESC);
        oprot.writeString(struct.activityMetaId);
        oprot.writeFieldEnd();
      }
      if (struct.ranks != null) {
        if (struct.isSetRanks()) {
          oprot.writeFieldBegin(RANKS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.ranks.size()));
            for (com.lc.billion.icefire.protocol.structure.PsRankAlliance _iter3 : struct.ranks)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcActivityAllianceRankHistoryInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcActivityAllianceRankHistoryInfoTupleScheme getScheme() {
      return new GcActivityAllianceRankHistoryInfoTupleScheme();
    }
  }

  private static class GcActivityAllianceRankHistoryInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcActivityAllianceRankHistoryInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcActivityAllianceRankHistoryInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.activityMetaId);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRanks()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetRanks()) {
        {
          oprot.writeI32(struct.ranks.size());
          for (com.lc.billion.icefire.protocol.structure.PsRankAlliance _iter4 : struct.ranks)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcActivityAllianceRankHistoryInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.activityMetaId = iprot.readString();
      struct.setActivityMetaIdIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.ranks = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRankAlliance>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRankAlliance _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsRankAlliance();
            _elem6.read(iprot);
            struct.ranks.add(_elem6);
          }
        }
        struct.setRanksIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

