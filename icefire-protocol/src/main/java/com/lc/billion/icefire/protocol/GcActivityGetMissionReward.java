/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 领取奖励返回
 * @Message(7376)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcActivityGetMissionReward implements org.apache.thrift.TBase<GcActivityGetMissionReward, GcActivityGetMissionReward._Fields>, java.io.Serializable, Cloneable, Comparable<GcActivityGetMissionReward> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcActivityGetMissionReward");

  private static final org.apache.thrift.protocol.TField ACTIVITY_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("activityMetaId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField REWARD_MISSIONS_FIELD_DESC = new org.apache.thrift.protocol.TField("rewardMissions", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField BP_INDEX_FIELD_DESC = new org.apache.thrift.protocol.TField("bpIndex", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField ITEMS_FIELD_DESC = new org.apache.thrift.protocol.TField("items", org.apache.thrift.protocol.TType.LIST, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcActivityGetMissionRewardStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcActivityGetMissionRewardTupleSchemeFactory();

  /**
   * 活动的metaId
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String activityMetaId; // required
  /**
   * 领取成功的任务id
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> rewardMissions; // optional
  /**
   * 领取的方式 0 默认奖励 1-n 额外的bp奖励 *
   */
  public int bpIndex; // optional
  /**
   * 获得的道具
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> items; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 活动的metaId
     */
    ACTIVITY_META_ID((short)1, "activityMetaId"),
    /**
     * 领取成功的任务id
     */
    REWARD_MISSIONS((short)2, "rewardMissions"),
    /**
     * 领取的方式 0 默认奖励 1-n 额外的bp奖励 *
     */
    BP_INDEX((short)3, "bpIndex"),
    /**
     * 获得的道具
     */
    ITEMS((short)4, "items");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACTIVITY_META_ID
          return ACTIVITY_META_ID;
        case 2: // REWARD_MISSIONS
          return REWARD_MISSIONS;
        case 3: // BP_INDEX
          return BP_INDEX;
        case 4: // ITEMS
          return ITEMS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BPINDEX_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.REWARD_MISSIONS,_Fields.BP_INDEX,_Fields.ITEMS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACTIVITY_META_ID, new org.apache.thrift.meta_data.FieldMetaData("activityMetaId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REWARD_MISSIONS, new org.apache.thrift.meta_data.FieldMetaData("rewardMissions", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.BP_INDEX, new org.apache.thrift.meta_data.FieldMetaData("bpIndex", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ITEMS, new org.apache.thrift.meta_data.FieldMetaData("items", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcActivityGetMissionReward.class, metaDataMap);
  }

  public GcActivityGetMissionReward() {
  }

  public GcActivityGetMissionReward(
    java.lang.String activityMetaId)
  {
    this();
    this.activityMetaId = activityMetaId;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcActivityGetMissionReward(GcActivityGetMissionReward other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetActivityMetaId()) {
      this.activityMetaId = other.activityMetaId;
    }
    if (other.isSetRewardMissions()) {
      java.util.List<java.lang.String> __this__rewardMissions = new java.util.ArrayList<java.lang.String>(other.rewardMissions);
      this.rewardMissions = __this__rewardMissions;
    }
    this.bpIndex = other.bpIndex;
    if (other.isSetItems()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other.items.size());
      for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element : other.items) {
        __this__items.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element));
      }
      this.items = __this__items;
    }
  }

  public GcActivityGetMissionReward deepCopy() {
    return new GcActivityGetMissionReward(this);
  }

  @Override
  public void clear() {
    this.activityMetaId = null;
    this.rewardMissions = null;
    setBpIndexIsSet(false);
    this.bpIndex = 0;
    this.items = null;
  }

  /**
   * 活动的metaId
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getActivityMetaId() {
    return this.activityMetaId;
  }

  /**
   * 活动的metaId
   */
  public GcActivityGetMissionReward setActivityMetaId(@org.apache.thrift.annotation.Nullable java.lang.String activityMetaId) {
    this.activityMetaId = activityMetaId;
    return this;
  }

  public void unsetActivityMetaId() {
    this.activityMetaId = null;
  }

  /** Returns true if field activityMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetActivityMetaId() {
    return this.activityMetaId != null;
  }

  public void setActivityMetaIdIsSet(boolean value) {
    if (!value) {
      this.activityMetaId = null;
    }
  }

  public int getRewardMissionsSize() {
    return (this.rewardMissions == null) ? 0 : this.rewardMissions.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.String> getRewardMissionsIterator() {
    return (this.rewardMissions == null) ? null : this.rewardMissions.iterator();
  }

  public void addToRewardMissions(java.lang.String elem) {
    if (this.rewardMissions == null) {
      this.rewardMissions = new java.util.ArrayList<java.lang.String>();
    }
    this.rewardMissions.add(elem);
  }

  /**
   * 领取成功的任务id
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.String> getRewardMissions() {
    return this.rewardMissions;
  }

  /**
   * 领取成功的任务id
   */
  public GcActivityGetMissionReward setRewardMissions(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> rewardMissions) {
    this.rewardMissions = rewardMissions;
    return this;
  }

  public void unsetRewardMissions() {
    this.rewardMissions = null;
  }

  /** Returns true if field rewardMissions is set (has been assigned a value) and false otherwise */
  public boolean isSetRewardMissions() {
    return this.rewardMissions != null;
  }

  public void setRewardMissionsIsSet(boolean value) {
    if (!value) {
      this.rewardMissions = null;
    }
  }

  /**
   * 领取的方式 0 默认奖励 1-n 额外的bp奖励 *
   */
  public int getBpIndex() {
    return this.bpIndex;
  }

  /**
   * 领取的方式 0 默认奖励 1-n 额外的bp奖励 *
   */
  public GcActivityGetMissionReward setBpIndex(int bpIndex) {
    this.bpIndex = bpIndex;
    setBpIndexIsSet(true);
    return this;
  }

  public void unsetBpIndex() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BPINDEX_ISSET_ID);
  }

  /** Returns true if field bpIndex is set (has been assigned a value) and false otherwise */
  public boolean isSetBpIndex() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BPINDEX_ISSET_ID);
  }

  public void setBpIndexIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BPINDEX_ISSET_ID, value);
  }

  public int getItemsSize() {
    return (this.items == null) ? 0 : this.items.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getItemsIterator() {
    return (this.items == null) ? null : this.items.iterator();
  }

  public void addToItems(com.lc.billion.icefire.protocol.structure.PsSimpleItem elem) {
    if (this.items == null) {
      this.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>();
    }
    this.items.add(elem);
  }

  /**
   * 获得的道具
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getItems() {
    return this.items;
  }

  /**
   * 获得的道具
   */
  public GcActivityGetMissionReward setItems(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> items) {
    this.items = items;
    return this;
  }

  public void unsetItems() {
    this.items = null;
  }

  /** Returns true if field items is set (has been assigned a value) and false otherwise */
  public boolean isSetItems() {
    return this.items != null;
  }

  public void setItemsIsSet(boolean value) {
    if (!value) {
      this.items = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ACTIVITY_META_ID:
      if (value == null) {
        unsetActivityMetaId();
      } else {
        setActivityMetaId((java.lang.String)value);
      }
      break;

    case REWARD_MISSIONS:
      if (value == null) {
        unsetRewardMissions();
      } else {
        setRewardMissions((java.util.List<java.lang.String>)value);
      }
      break;

    case BP_INDEX:
      if (value == null) {
        unsetBpIndex();
      } else {
        setBpIndex((java.lang.Integer)value);
      }
      break;

    case ITEMS:
      if (value == null) {
        unsetItems();
      } else {
        setItems((java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ACTIVITY_META_ID:
      return getActivityMetaId();

    case REWARD_MISSIONS:
      return getRewardMissions();

    case BP_INDEX:
      return getBpIndex();

    case ITEMS:
      return getItems();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ACTIVITY_META_ID:
      return isSetActivityMetaId();
    case REWARD_MISSIONS:
      return isSetRewardMissions();
    case BP_INDEX:
      return isSetBpIndex();
    case ITEMS:
      return isSetItems();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcActivityGetMissionReward)
      return this.equals((GcActivityGetMissionReward)that);
    return false;
  }

  public boolean equals(GcActivityGetMissionReward that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_activityMetaId = true && this.isSetActivityMetaId();
    boolean that_present_activityMetaId = true && that.isSetActivityMetaId();
    if (this_present_activityMetaId || that_present_activityMetaId) {
      if (!(this_present_activityMetaId && that_present_activityMetaId))
        return false;
      if (!this.activityMetaId.equals(that.activityMetaId))
        return false;
    }

    boolean this_present_rewardMissions = true && this.isSetRewardMissions();
    boolean that_present_rewardMissions = true && that.isSetRewardMissions();
    if (this_present_rewardMissions || that_present_rewardMissions) {
      if (!(this_present_rewardMissions && that_present_rewardMissions))
        return false;
      if (!this.rewardMissions.equals(that.rewardMissions))
        return false;
    }

    boolean this_present_bpIndex = true && this.isSetBpIndex();
    boolean that_present_bpIndex = true && that.isSetBpIndex();
    if (this_present_bpIndex || that_present_bpIndex) {
      if (!(this_present_bpIndex && that_present_bpIndex))
        return false;
      if (this.bpIndex != that.bpIndex)
        return false;
    }

    boolean this_present_items = true && this.isSetItems();
    boolean that_present_items = true && that.isSetItems();
    if (this_present_items || that_present_items) {
      if (!(this_present_items && that_present_items))
        return false;
      if (!this.items.equals(that.items))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetActivityMetaId()) ? 131071 : 524287);
    if (isSetActivityMetaId())
      hashCode = hashCode * 8191 + activityMetaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetRewardMissions()) ? 131071 : 524287);
    if (isSetRewardMissions())
      hashCode = hashCode * 8191 + rewardMissions.hashCode();

    hashCode = hashCode * 8191 + ((isSetBpIndex()) ? 131071 : 524287);
    if (isSetBpIndex())
      hashCode = hashCode * 8191 + bpIndex;

    hashCode = hashCode * 8191 + ((isSetItems()) ? 131071 : 524287);
    if (isSetItems())
      hashCode = hashCode * 8191 + items.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcActivityGetMissionReward other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetActivityMetaId(), other.isSetActivityMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActivityMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.activityMetaId, other.activityMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRewardMissions(), other.isSetRewardMissions());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewardMissions()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewardMissions, other.rewardMissions);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBpIndex(), other.isSetBpIndex());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBpIndex()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bpIndex, other.bpIndex);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetItems(), other.isSetItems());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItems()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.items, other.items);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcActivityGetMissionReward(");
    boolean first = true;

    sb.append("activityMetaId:");
    if (this.activityMetaId == null) {
      sb.append("null");
    } else {
      sb.append(this.activityMetaId);
    }
    first = false;
    if (isSetRewardMissions()) {
      if (!first) sb.append(", ");
      sb.append("rewardMissions:");
      if (this.rewardMissions == null) {
        sb.append("null");
      } else {
        sb.append(this.rewardMissions);
      }
      first = false;
    }
    if (isSetBpIndex()) {
      if (!first) sb.append(", ");
      sb.append("bpIndex:");
      sb.append(this.bpIndex);
      first = false;
    }
    if (isSetItems()) {
      if (!first) sb.append(", ");
      sb.append("items:");
      if (this.items == null) {
        sb.append("null");
      } else {
        sb.append(this.items);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (activityMetaId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'activityMetaId' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcActivityGetMissionRewardStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcActivityGetMissionRewardStandardScheme getScheme() {
      return new GcActivityGetMissionRewardStandardScheme();
    }
  }

  private static class GcActivityGetMissionRewardStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcActivityGetMissionReward> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcActivityGetMissionReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACTIVITY_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.activityMetaId = iprot.readString();
              struct.setActivityMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // REWARD_MISSIONS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.rewardMissions = new java.util.ArrayList<java.lang.String>(_list0.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = iprot.readString();
                  struct.rewardMissions.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRewardMissionsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BP_INDEX
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.bpIndex = iprot.readI32();
              struct.setBpIndexIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ITEMS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list3.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                  _elem4.read(iprot);
                  struct.items.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setItemsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcActivityGetMissionReward struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.activityMetaId != null) {
        oprot.writeFieldBegin(ACTIVITY_META_ID_FIELD_DESC);
        oprot.writeString(struct.activityMetaId);
        oprot.writeFieldEnd();
      }
      if (struct.rewardMissions != null) {
        if (struct.isSetRewardMissions()) {
          oprot.writeFieldBegin(REWARD_MISSIONS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.rewardMissions.size()));
            for (java.lang.String _iter6 : struct.rewardMissions)
            {
              oprot.writeString(_iter6);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetBpIndex()) {
        oprot.writeFieldBegin(BP_INDEX_FIELD_DESC);
        oprot.writeI32(struct.bpIndex);
        oprot.writeFieldEnd();
      }
      if (struct.items != null) {
        if (struct.isSetItems()) {
          oprot.writeFieldBegin(ITEMS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.items.size()));
            for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter7 : struct.items)
            {
              _iter7.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcActivityGetMissionRewardTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcActivityGetMissionRewardTupleScheme getScheme() {
      return new GcActivityGetMissionRewardTupleScheme();
    }
  }

  private static class GcActivityGetMissionRewardTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcActivityGetMissionReward> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcActivityGetMissionReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.activityMetaId);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRewardMissions()) {
        optionals.set(0);
      }
      if (struct.isSetBpIndex()) {
        optionals.set(1);
      }
      if (struct.isSetItems()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetRewardMissions()) {
        {
          oprot.writeI32(struct.rewardMissions.size());
          for (java.lang.String _iter8 : struct.rewardMissions)
          {
            oprot.writeString(_iter8);
          }
        }
      }
      if (struct.isSetBpIndex()) {
        oprot.writeI32(struct.bpIndex);
      }
      if (struct.isSetItems()) {
        {
          oprot.writeI32(struct.items.size());
          for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter9 : struct.items)
          {
            _iter9.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcActivityGetMissionReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.activityMetaId = iprot.readString();
      struct.setActivityMetaIdIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list10 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRING);
          struct.rewardMissions = new java.util.ArrayList<java.lang.String>(_list10.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _elem11;
          for (int _i12 = 0; _i12 < _list10.size; ++_i12)
          {
            _elem11 = iprot.readString();
            struct.rewardMissions.add(_elem11);
          }
        }
        struct.setRewardMissionsIsSet(true);
      }
      if (incoming.get(1)) {
        struct.bpIndex = iprot.readI32();
        struct.setBpIndexIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list13 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.items = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list13.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem14;
          for (int _i15 = 0; _i15 < _list13.size; ++_i15)
          {
            _elem14 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
            _elem14.read(iprot);
            struct.items.add(_elem14);
          }
        }
        struct.setItemsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

