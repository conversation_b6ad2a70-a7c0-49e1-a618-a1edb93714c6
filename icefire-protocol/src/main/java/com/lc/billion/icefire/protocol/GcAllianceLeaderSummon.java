/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 盟主请求盟主召唤回复
 * @Message(3391)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAllianceLeaderSummon implements org.apache.thrift.TBase<GcAllianceLeaderSummon, GcAllianceLeaderSummon._Fields>, java.io.Serializable, Cloneable, Comparable<GcAllianceLeaderSummon> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAllianceLeaderSummon");

  private static final org.apache.thrift.protocol.TField ERR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("errCode", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField LAST_USE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("lastUseTime", org.apache.thrift.protocol.TType.I64, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAllianceLeaderSummonStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAllianceLeaderSummonTupleSchemeFactory();

  /**
   * 0:成功 ，1:联盟不存在，2：权限不足，3：cd不足，除了1，都会同步时间
   */
  public int errCode; // required
  /**
   * 上次召唤时间
   */
  public long lastUseTime; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 0:成功 ，1:联盟不存在，2：权限不足，3：cd不足，除了1，都会同步时间
     */
    ERR_CODE((short)1, "errCode"),
    /**
     * 上次召唤时间
     */
    LAST_USE_TIME((short)2, "lastUseTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ERR_CODE
          return ERR_CODE;
        case 2: // LAST_USE_TIME
          return LAST_USE_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ERRCODE_ISSET_ID = 0;
  private static final int __LASTUSETIME_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ERR_CODE, new org.apache.thrift.meta_data.FieldMetaData("errCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.LAST_USE_TIME, new org.apache.thrift.meta_data.FieldMetaData("lastUseTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAllianceLeaderSummon.class, metaDataMap);
  }

  public GcAllianceLeaderSummon() {
  }

  public GcAllianceLeaderSummon(
    int errCode,
    long lastUseTime)
  {
    this();
    this.errCode = errCode;
    setErrCodeIsSet(true);
    this.lastUseTime = lastUseTime;
    setLastUseTimeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAllianceLeaderSummon(GcAllianceLeaderSummon other) {
    __isset_bitfield = other.__isset_bitfield;
    this.errCode = other.errCode;
    this.lastUseTime = other.lastUseTime;
  }

  public GcAllianceLeaderSummon deepCopy() {
    return new GcAllianceLeaderSummon(this);
  }

  @Override
  public void clear() {
    setErrCodeIsSet(false);
    this.errCode = 0;
    setLastUseTimeIsSet(false);
    this.lastUseTime = 0;
  }

  /**
   * 0:成功 ，1:联盟不存在，2：权限不足，3：cd不足，除了1，都会同步时间
   */
  public int getErrCode() {
    return this.errCode;
  }

  /**
   * 0:成功 ，1:联盟不存在，2：权限不足，3：cd不足，除了1，都会同步时间
   */
  public GcAllianceLeaderSummon setErrCode(int errCode) {
    this.errCode = errCode;
    setErrCodeIsSet(true);
    return this;
  }

  public void unsetErrCode() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ERRCODE_ISSET_ID);
  }

  /** Returns true if field errCode is set (has been assigned a value) and false otherwise */
  public boolean isSetErrCode() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ERRCODE_ISSET_ID);
  }

  public void setErrCodeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ERRCODE_ISSET_ID, value);
  }

  /**
   * 上次召唤时间
   */
  public long getLastUseTime() {
    return this.lastUseTime;
  }

  /**
   * 上次召唤时间
   */
  public GcAllianceLeaderSummon setLastUseTime(long lastUseTime) {
    this.lastUseTime = lastUseTime;
    setLastUseTimeIsSet(true);
    return this;
  }

  public void unsetLastUseTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LASTUSETIME_ISSET_ID);
  }

  /** Returns true if field lastUseTime is set (has been assigned a value) and false otherwise */
  public boolean isSetLastUseTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LASTUSETIME_ISSET_ID);
  }

  public void setLastUseTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LASTUSETIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ERR_CODE:
      if (value == null) {
        unsetErrCode();
      } else {
        setErrCode((java.lang.Integer)value);
      }
      break;

    case LAST_USE_TIME:
      if (value == null) {
        unsetLastUseTime();
      } else {
        setLastUseTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ERR_CODE:
      return getErrCode();

    case LAST_USE_TIME:
      return getLastUseTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ERR_CODE:
      return isSetErrCode();
    case LAST_USE_TIME:
      return isSetLastUseTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAllianceLeaderSummon)
      return this.equals((GcAllianceLeaderSummon)that);
    return false;
  }

  public boolean equals(GcAllianceLeaderSummon that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_errCode = true;
    boolean that_present_errCode = true;
    if (this_present_errCode || that_present_errCode) {
      if (!(this_present_errCode && that_present_errCode))
        return false;
      if (this.errCode != that.errCode)
        return false;
    }

    boolean this_present_lastUseTime = true;
    boolean that_present_lastUseTime = true;
    if (this_present_lastUseTime || that_present_lastUseTime) {
      if (!(this_present_lastUseTime && that_present_lastUseTime))
        return false;
      if (this.lastUseTime != that.lastUseTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + errCode;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(lastUseTime);

    return hashCode;
  }

  @Override
  public int compareTo(GcAllianceLeaderSummon other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetErrCode(), other.isSetErrCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetErrCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.errCode, other.errCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetLastUseTime(), other.isSetLastUseTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLastUseTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lastUseTime, other.lastUseTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAllianceLeaderSummon(");
    boolean first = true;

    sb.append("errCode:");
    sb.append(this.errCode);
    first = false;
    if (!first) sb.append(", ");
    sb.append("lastUseTime:");
    sb.append(this.lastUseTime);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'errCode' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'lastUseTime' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAllianceLeaderSummonStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceLeaderSummonStandardScheme getScheme() {
      return new GcAllianceLeaderSummonStandardScheme();
    }
  }

  private static class GcAllianceLeaderSummonStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAllianceLeaderSummon> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAllianceLeaderSummon struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ERR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.errCode = iprot.readI32();
              struct.setErrCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // LAST_USE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.lastUseTime = iprot.readI64();
              struct.setLastUseTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetErrCode()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'errCode' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetLastUseTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'lastUseTime' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAllianceLeaderSummon struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ERR_CODE_FIELD_DESC);
      oprot.writeI32(struct.errCode);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(LAST_USE_TIME_FIELD_DESC);
      oprot.writeI64(struct.lastUseTime);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAllianceLeaderSummonTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceLeaderSummonTupleScheme getScheme() {
      return new GcAllianceLeaderSummonTupleScheme();
    }
  }

  private static class GcAllianceLeaderSummonTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAllianceLeaderSummon> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAllianceLeaderSummon struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.errCode);
      oprot.writeI64(struct.lastUseTime);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAllianceLeaderSummon struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.errCode = iprot.readI32();
      struct.setErrCodeIsSet(true);
      struct.lastUseTime = iprot.readI64();
      struct.setLastUseTimeIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

