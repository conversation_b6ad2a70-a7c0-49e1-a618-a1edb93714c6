/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 获取同盟R4权限
 * @Message(884)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAllianceRankAuthSettingInfo implements org.apache.thrift.TBase<GcAllianceRankAuthSettingInfo, GcAllianceRankAuthSettingInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcAllianceRankAuthSettingInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAllianceRankAuthSettingInfo");

  private static final org.apache.thrift.protocol.TField R4AUTHS_FIELD_DESC = new org.apache.thrift.protocol.TField("r4auths", org.apache.thrift.protocol.TType.LIST, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAllianceRankAuthSettingInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAllianceRankAuthSettingInfoTupleSchemeFactory();

  /**
   * R4权限
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> r4auths; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * R4权限
     */
    R4AUTHS((short)1, "r4auths");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // R4AUTHS
          return R4AUTHS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.R4AUTHS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.R4AUTHS, new org.apache.thrift.meta_data.FieldMetaData("r4auths", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAllianceRankAuthSettingInfo.class, metaDataMap);
  }

  public GcAllianceRankAuthSettingInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAllianceRankAuthSettingInfo(GcAllianceRankAuthSettingInfo other) {
    if (other.isSetR4auths()) {
      java.util.List<java.lang.String> __this__r4auths = new java.util.ArrayList<java.lang.String>(other.r4auths);
      this.r4auths = __this__r4auths;
    }
  }

  public GcAllianceRankAuthSettingInfo deepCopy() {
    return new GcAllianceRankAuthSettingInfo(this);
  }

  @Override
  public void clear() {
    this.r4auths = null;
  }

  public int getR4authsSize() {
    return (this.r4auths == null) ? 0 : this.r4auths.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.String> getR4authsIterator() {
    return (this.r4auths == null) ? null : this.r4auths.iterator();
  }

  public void addToR4auths(java.lang.String elem) {
    if (this.r4auths == null) {
      this.r4auths = new java.util.ArrayList<java.lang.String>();
    }
    this.r4auths.add(elem);
  }

  /**
   * R4权限
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.String> getR4auths() {
    return this.r4auths;
  }

  /**
   * R4权限
   */
  public GcAllianceRankAuthSettingInfo setR4auths(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> r4auths) {
    this.r4auths = r4auths;
    return this;
  }

  public void unsetR4auths() {
    this.r4auths = null;
  }

  /** Returns true if field r4auths is set (has been assigned a value) and false otherwise */
  public boolean isSetR4auths() {
    return this.r4auths != null;
  }

  public void setR4authsIsSet(boolean value) {
    if (!value) {
      this.r4auths = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case R4AUTHS:
      if (value == null) {
        unsetR4auths();
      } else {
        setR4auths((java.util.List<java.lang.String>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case R4AUTHS:
      return getR4auths();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case R4AUTHS:
      return isSetR4auths();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAllianceRankAuthSettingInfo)
      return this.equals((GcAllianceRankAuthSettingInfo)that);
    return false;
  }

  public boolean equals(GcAllianceRankAuthSettingInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_r4auths = true && this.isSetR4auths();
    boolean that_present_r4auths = true && that.isSetR4auths();
    if (this_present_r4auths || that_present_r4auths) {
      if (!(this_present_r4auths && that_present_r4auths))
        return false;
      if (!this.r4auths.equals(that.r4auths))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetR4auths()) ? 131071 : 524287);
    if (isSetR4auths())
      hashCode = hashCode * 8191 + r4auths.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcAllianceRankAuthSettingInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetR4auths(), other.isSetR4auths());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetR4auths()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.r4auths, other.r4auths);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAllianceRankAuthSettingInfo(");
    boolean first = true;

    if (isSetR4auths()) {
      sb.append("r4auths:");
      if (this.r4auths == null) {
        sb.append("null");
      } else {
        sb.append(this.r4auths);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAllianceRankAuthSettingInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceRankAuthSettingInfoStandardScheme getScheme() {
      return new GcAllianceRankAuthSettingInfoStandardScheme();
    }
  }

  private static class GcAllianceRankAuthSettingInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAllianceRankAuthSettingInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAllianceRankAuthSettingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // R4AUTHS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.r4auths = new java.util.ArrayList<java.lang.String>(_list0.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = iprot.readString();
                  struct.r4auths.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setR4authsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAllianceRankAuthSettingInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.r4auths != null) {
        if (struct.isSetR4auths()) {
          oprot.writeFieldBegin(R4AUTHS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.r4auths.size()));
            for (java.lang.String _iter3 : struct.r4auths)
            {
              oprot.writeString(_iter3);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAllianceRankAuthSettingInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceRankAuthSettingInfoTupleScheme getScheme() {
      return new GcAllianceRankAuthSettingInfoTupleScheme();
    }
  }

  private static class GcAllianceRankAuthSettingInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAllianceRankAuthSettingInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAllianceRankAuthSettingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetR4auths()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetR4auths()) {
        {
          oprot.writeI32(struct.r4auths.size());
          for (java.lang.String _iter4 : struct.r4auths)
          {
            oprot.writeString(_iter4);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAllianceRankAuthSettingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRING);
          struct.r4auths = new java.util.ArrayList<java.lang.String>(_list5.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = iprot.readString();
            struct.r4auths.add(_elem6);
          }
        }
        struct.setR4authsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

