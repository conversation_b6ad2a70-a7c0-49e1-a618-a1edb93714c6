/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 获取推荐联盟
 * @Message(1524)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAllianceSuggest implements org.apache.thrift.TBase<GcAllianceSuggest, GcAllianceSuggest._Fields>, java.io.Serializable, Cloneable, Comparable<GcAllianceSuggest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAllianceSuggest");

  private static final org.apache.thrift.protocol.TField ALLIANCES_FIELD_DESC = new org.apache.thrift.protocol.TField("alliances", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField IS_FIRST_LOGIN_PUSH_FIELD_DESC = new org.apache.thrift.protocol.TField("isFirstLoginPush", org.apache.thrift.protocol.TType.BOOL, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAllianceSuggestStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAllianceSuggestTupleSchemeFactory();

  /**
   * 推荐联盟列表
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceInfo> alliances; // required
  /**
   * 当日首次登陆的服务器主动推送
   */
  public boolean isFirstLoginPush; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 推荐联盟列表
     */
    ALLIANCES((short)1, "alliances"),
    /**
     * 当日首次登陆的服务器主动推送
     */
    IS_FIRST_LOGIN_PUSH((short)2, "isFirstLoginPush");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ALLIANCES
          return ALLIANCES;
        case 2: // IS_FIRST_LOGIN_PUSH
          return IS_FIRST_LOGIN_PUSH;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ISFIRSTLOGINPUSH_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.IS_FIRST_LOGIN_PUSH};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ALLIANCES, new org.apache.thrift.meta_data.FieldMetaData("alliances", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsAllianceInfo.class))));
    tmpMap.put(_Fields.IS_FIRST_LOGIN_PUSH, new org.apache.thrift.meta_data.FieldMetaData("isFirstLoginPush", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAllianceSuggest.class, metaDataMap);
  }

  public GcAllianceSuggest() {
  }

  public GcAllianceSuggest(
    java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceInfo> alliances)
  {
    this();
    this.alliances = alliances;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAllianceSuggest(GcAllianceSuggest other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetAlliances()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceInfo> __this__alliances = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsAllianceInfo>(other.alliances.size());
      for (com.lc.billion.icefire.protocol.structure.PsAllianceInfo other_element : other.alliances) {
        __this__alliances.add(new com.lc.billion.icefire.protocol.structure.PsAllianceInfo(other_element));
      }
      this.alliances = __this__alliances;
    }
    this.isFirstLoginPush = other.isFirstLoginPush;
  }

  public GcAllianceSuggest deepCopy() {
    return new GcAllianceSuggest(this);
  }

  @Override
  public void clear() {
    this.alliances = null;
    setIsFirstLoginPushIsSet(false);
    this.isFirstLoginPush = false;
  }

  public int getAlliancesSize() {
    return (this.alliances == null) ? 0 : this.alliances.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsAllianceInfo> getAlliancesIterator() {
    return (this.alliances == null) ? null : this.alliances.iterator();
  }

  public void addToAlliances(com.lc.billion.icefire.protocol.structure.PsAllianceInfo elem) {
    if (this.alliances == null) {
      this.alliances = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsAllianceInfo>();
    }
    this.alliances.add(elem);
  }

  /**
   * 推荐联盟列表
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceInfo> getAlliances() {
    return this.alliances;
  }

  /**
   * 推荐联盟列表
   */
  public GcAllianceSuggest setAlliances(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceInfo> alliances) {
    this.alliances = alliances;
    return this;
  }

  public void unsetAlliances() {
    this.alliances = null;
  }

  /** Returns true if field alliances is set (has been assigned a value) and false otherwise */
  public boolean isSetAlliances() {
    return this.alliances != null;
  }

  public void setAlliancesIsSet(boolean value) {
    if (!value) {
      this.alliances = null;
    }
  }

  /**
   * 当日首次登陆的服务器主动推送
   */
  public boolean isIsFirstLoginPush() {
    return this.isFirstLoginPush;
  }

  /**
   * 当日首次登陆的服务器主动推送
   */
  public GcAllianceSuggest setIsFirstLoginPush(boolean isFirstLoginPush) {
    this.isFirstLoginPush = isFirstLoginPush;
    setIsFirstLoginPushIsSet(true);
    return this;
  }

  public void unsetIsFirstLoginPush() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ISFIRSTLOGINPUSH_ISSET_ID);
  }

  /** Returns true if field isFirstLoginPush is set (has been assigned a value) and false otherwise */
  public boolean isSetIsFirstLoginPush() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ISFIRSTLOGINPUSH_ISSET_ID);
  }

  public void setIsFirstLoginPushIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ISFIRSTLOGINPUSH_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ALLIANCES:
      if (value == null) {
        unsetAlliances();
      } else {
        setAlliances((java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceInfo>)value);
      }
      break;

    case IS_FIRST_LOGIN_PUSH:
      if (value == null) {
        unsetIsFirstLoginPush();
      } else {
        setIsFirstLoginPush((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ALLIANCES:
      return getAlliances();

    case IS_FIRST_LOGIN_PUSH:
      return isIsFirstLoginPush();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ALLIANCES:
      return isSetAlliances();
    case IS_FIRST_LOGIN_PUSH:
      return isSetIsFirstLoginPush();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAllianceSuggest)
      return this.equals((GcAllianceSuggest)that);
    return false;
  }

  public boolean equals(GcAllianceSuggest that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_alliances = true && this.isSetAlliances();
    boolean that_present_alliances = true && that.isSetAlliances();
    if (this_present_alliances || that_present_alliances) {
      if (!(this_present_alliances && that_present_alliances))
        return false;
      if (!this.alliances.equals(that.alliances))
        return false;
    }

    boolean this_present_isFirstLoginPush = true && this.isSetIsFirstLoginPush();
    boolean that_present_isFirstLoginPush = true && that.isSetIsFirstLoginPush();
    if (this_present_isFirstLoginPush || that_present_isFirstLoginPush) {
      if (!(this_present_isFirstLoginPush && that_present_isFirstLoginPush))
        return false;
      if (this.isFirstLoginPush != that.isFirstLoginPush)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetAlliances()) ? 131071 : 524287);
    if (isSetAlliances())
      hashCode = hashCode * 8191 + alliances.hashCode();

    hashCode = hashCode * 8191 + ((isSetIsFirstLoginPush()) ? 131071 : 524287);
    if (isSetIsFirstLoginPush())
      hashCode = hashCode * 8191 + ((isFirstLoginPush) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(GcAllianceSuggest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetAlliances(), other.isSetAlliances());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAlliances()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.alliances, other.alliances);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetIsFirstLoginPush(), other.isSetIsFirstLoginPush());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsFirstLoginPush()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isFirstLoginPush, other.isFirstLoginPush);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAllianceSuggest(");
    boolean first = true;

    sb.append("alliances:");
    if (this.alliances == null) {
      sb.append("null");
    } else {
      sb.append(this.alliances);
    }
    first = false;
    if (isSetIsFirstLoginPush()) {
      if (!first) sb.append(", ");
      sb.append("isFirstLoginPush:");
      sb.append(this.isFirstLoginPush);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (alliances == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'alliances' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAllianceSuggestStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceSuggestStandardScheme getScheme() {
      return new GcAllianceSuggestStandardScheme();
    }
  }

  private static class GcAllianceSuggestStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAllianceSuggest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAllianceSuggest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ALLIANCES
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.alliances = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsAllianceInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsAllianceInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsAllianceInfo();
                  _elem1.read(iprot);
                  struct.alliances.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setAlliancesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // IS_FIRST_LOGIN_PUSH
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isFirstLoginPush = iprot.readBool();
              struct.setIsFirstLoginPushIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAllianceSuggest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.alliances != null) {
        oprot.writeFieldBegin(ALLIANCES_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.alliances.size()));
          for (com.lc.billion.icefire.protocol.structure.PsAllianceInfo _iter3 : struct.alliances)
          {
            _iter3.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsFirstLoginPush()) {
        oprot.writeFieldBegin(IS_FIRST_LOGIN_PUSH_FIELD_DESC);
        oprot.writeBool(struct.isFirstLoginPush);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAllianceSuggestTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceSuggestTupleScheme getScheme() {
      return new GcAllianceSuggestTupleScheme();
    }
  }

  private static class GcAllianceSuggestTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAllianceSuggest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAllianceSuggest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      {
        oprot.writeI32(struct.alliances.size());
        for (com.lc.billion.icefire.protocol.structure.PsAllianceInfo _iter4 : struct.alliances)
        {
          _iter4.write(oprot);
        }
      }
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetIsFirstLoginPush()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetIsFirstLoginPush()) {
        oprot.writeBool(struct.isFirstLoginPush);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAllianceSuggest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      {
        org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.alliances = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsAllianceInfo>(_list5.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsAllianceInfo _elem6;
        for (int _i7 = 0; _i7 < _list5.size; ++_i7)
        {
          _elem6 = new com.lc.billion.icefire.protocol.structure.PsAllianceInfo();
          _elem6.read(iprot);
          struct.alliances.add(_elem6);
        }
      }
      struct.setAlliancesIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.isFirstLoginPush = iprot.readBool();
        struct.setIsFirstLoginPushIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

