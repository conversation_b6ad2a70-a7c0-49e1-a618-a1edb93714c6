/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 联盟捐献获得的联盟荣誉（积分）和联盟资金
 * @Message(2368)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAllianceTechChange implements org.apache.thrift.TBase<GcAllianceTechChange, GcAllianceTechChange._Fields>, java.io.Serializable, Cloneable, Comparable<GcAllianceTechChange> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAllianceTechChange");

  private static final org.apache.thrift.protocol.TField HONOUR_FIELD_DESC = new org.apache.thrift.protocol.TField("honour", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField FUNDS_FIELD_DESC = new org.apache.thrift.protocol.TField("funds", org.apache.thrift.protocol.TType.I64, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAllianceTechChangeStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAllianceTechChangeTupleSchemeFactory();

  public long honour; // required
  public long funds; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    HONOUR((short)1, "honour"),
    FUNDS((short)2, "funds");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // HONOUR
          return HONOUR;
        case 2: // FUNDS
          return FUNDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __HONOUR_ISSET_ID = 0;
  private static final int __FUNDS_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.HONOUR, new org.apache.thrift.meta_data.FieldMetaData("honour", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FUNDS, new org.apache.thrift.meta_data.FieldMetaData("funds", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAllianceTechChange.class, metaDataMap);
  }

  public GcAllianceTechChange() {
  }

  public GcAllianceTechChange(
    long honour,
    long funds)
  {
    this();
    this.honour = honour;
    setHonourIsSet(true);
    this.funds = funds;
    setFundsIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAllianceTechChange(GcAllianceTechChange other) {
    __isset_bitfield = other.__isset_bitfield;
    this.honour = other.honour;
    this.funds = other.funds;
  }

  public GcAllianceTechChange deepCopy() {
    return new GcAllianceTechChange(this);
  }

  @Override
  public void clear() {
    setHonourIsSet(false);
    this.honour = 0;
    setFundsIsSet(false);
    this.funds = 0;
  }

  public long getHonour() {
    return this.honour;
  }

  public GcAllianceTechChange setHonour(long honour) {
    this.honour = honour;
    setHonourIsSet(true);
    return this;
  }

  public void unsetHonour() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __HONOUR_ISSET_ID);
  }

  /** Returns true if field honour is set (has been assigned a value) and false otherwise */
  public boolean isSetHonour() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __HONOUR_ISSET_ID);
  }

  public void setHonourIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __HONOUR_ISSET_ID, value);
  }

  public long getFunds() {
    return this.funds;
  }

  public GcAllianceTechChange setFunds(long funds) {
    this.funds = funds;
    setFundsIsSet(true);
    return this;
  }

  public void unsetFunds() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __FUNDS_ISSET_ID);
  }

  /** Returns true if field funds is set (has been assigned a value) and false otherwise */
  public boolean isSetFunds() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __FUNDS_ISSET_ID);
  }

  public void setFundsIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __FUNDS_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case HONOUR:
      if (value == null) {
        unsetHonour();
      } else {
        setHonour((java.lang.Long)value);
      }
      break;

    case FUNDS:
      if (value == null) {
        unsetFunds();
      } else {
        setFunds((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case HONOUR:
      return getHonour();

    case FUNDS:
      return getFunds();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case HONOUR:
      return isSetHonour();
    case FUNDS:
      return isSetFunds();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAllianceTechChange)
      return this.equals((GcAllianceTechChange)that);
    return false;
  }

  public boolean equals(GcAllianceTechChange that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_honour = true;
    boolean that_present_honour = true;
    if (this_present_honour || that_present_honour) {
      if (!(this_present_honour && that_present_honour))
        return false;
      if (this.honour != that.honour)
        return false;
    }

    boolean this_present_funds = true;
    boolean that_present_funds = true;
    if (this_present_funds || that_present_funds) {
      if (!(this_present_funds && that_present_funds))
        return false;
      if (this.funds != that.funds)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(honour);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(funds);

    return hashCode;
  }

  @Override
  public int compareTo(GcAllianceTechChange other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetHonour(), other.isSetHonour());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHonour()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.honour, other.honour);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetFunds(), other.isSetFunds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFunds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.funds, other.funds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAllianceTechChange(");
    boolean first = true;

    sb.append("honour:");
    sb.append(this.honour);
    first = false;
    if (!first) sb.append(", ");
    sb.append("funds:");
    sb.append(this.funds);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'honour' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'funds' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAllianceTechChangeStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceTechChangeStandardScheme getScheme() {
      return new GcAllianceTechChangeStandardScheme();
    }
  }

  private static class GcAllianceTechChangeStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAllianceTechChange> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAllianceTechChange struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // HONOUR
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.honour = iprot.readI64();
              struct.setHonourIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // FUNDS
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.funds = iprot.readI64();
              struct.setFundsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetHonour()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'honour' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetFunds()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'funds' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAllianceTechChange struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(HONOUR_FIELD_DESC);
      oprot.writeI64(struct.honour);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FUNDS_FIELD_DESC);
      oprot.writeI64(struct.funds);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAllianceTechChangeTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceTechChangeTupleScheme getScheme() {
      return new GcAllianceTechChangeTupleScheme();
    }
  }

  private static class GcAllianceTechChangeTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAllianceTechChange> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAllianceTechChange struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.honour);
      oprot.writeI64(struct.funds);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAllianceTechChange struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.honour = iprot.readI64();
      struct.setHonourIsSet(true);
      struct.funds = iprot.readI64();
      struct.setFundsIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

