/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 修改联盟宣言
 * @Message(832)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAllianceUpdateDeclaration implements org.apache.thrift.TBase<GcAllianceUpdateDeclaration, GcAllianceUpdateDeclaration._Fields>, java.io.Serializable, Cloneable, Comparable<GcAllianceUpdateDeclaration> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAllianceUpdateDeclaration");

  private static final org.apache.thrift.protocol.TField DECLARATION_FIELD_DESC = new org.apache.thrift.protocol.TField("declaration", org.apache.thrift.protocol.TType.STRING, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAllianceUpdateDeclarationStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAllianceUpdateDeclarationTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.lang.String declaration; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    DECLARATION((short)1, "declaration");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // DECLARATION
          return DECLARATION;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.DECLARATION, new org.apache.thrift.meta_data.FieldMetaData("declaration", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAllianceUpdateDeclaration.class, metaDataMap);
  }

  public GcAllianceUpdateDeclaration() {
  }

  public GcAllianceUpdateDeclaration(
    java.lang.String declaration)
  {
    this();
    this.declaration = declaration;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAllianceUpdateDeclaration(GcAllianceUpdateDeclaration other) {
    if (other.isSetDeclaration()) {
      this.declaration = other.declaration;
    }
  }

  public GcAllianceUpdateDeclaration deepCopy() {
    return new GcAllianceUpdateDeclaration(this);
  }

  @Override
  public void clear() {
    this.declaration = null;
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getDeclaration() {
    return this.declaration;
  }

  public GcAllianceUpdateDeclaration setDeclaration(@org.apache.thrift.annotation.Nullable java.lang.String declaration) {
    this.declaration = declaration;
    return this;
  }

  public void unsetDeclaration() {
    this.declaration = null;
  }

  /** Returns true if field declaration is set (has been assigned a value) and false otherwise */
  public boolean isSetDeclaration() {
    return this.declaration != null;
  }

  public void setDeclarationIsSet(boolean value) {
    if (!value) {
      this.declaration = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case DECLARATION:
      if (value == null) {
        unsetDeclaration();
      } else {
        setDeclaration((java.lang.String)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case DECLARATION:
      return getDeclaration();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case DECLARATION:
      return isSetDeclaration();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAllianceUpdateDeclaration)
      return this.equals((GcAllianceUpdateDeclaration)that);
    return false;
  }

  public boolean equals(GcAllianceUpdateDeclaration that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_declaration = true && this.isSetDeclaration();
    boolean that_present_declaration = true && that.isSetDeclaration();
    if (this_present_declaration || that_present_declaration) {
      if (!(this_present_declaration && that_present_declaration))
        return false;
      if (!this.declaration.equals(that.declaration))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetDeclaration()) ? 131071 : 524287);
    if (isSetDeclaration())
      hashCode = hashCode * 8191 + declaration.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcAllianceUpdateDeclaration other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetDeclaration(), other.isSetDeclaration());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDeclaration()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.declaration, other.declaration);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAllianceUpdateDeclaration(");
    boolean first = true;

    sb.append("declaration:");
    if (this.declaration == null) {
      sb.append("null");
    } else {
      sb.append(this.declaration);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (declaration == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'declaration' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAllianceUpdateDeclarationStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceUpdateDeclarationStandardScheme getScheme() {
      return new GcAllianceUpdateDeclarationStandardScheme();
    }
  }

  private static class GcAllianceUpdateDeclarationStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAllianceUpdateDeclaration> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAllianceUpdateDeclaration struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // DECLARATION
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.declaration = iprot.readString();
              struct.setDeclarationIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAllianceUpdateDeclaration struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.declaration != null) {
        oprot.writeFieldBegin(DECLARATION_FIELD_DESC);
        oprot.writeString(struct.declaration);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAllianceUpdateDeclarationTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceUpdateDeclarationTupleScheme getScheme() {
      return new GcAllianceUpdateDeclarationTupleScheme();
    }
  }

  private static class GcAllianceUpdateDeclarationTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAllianceUpdateDeclaration> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAllianceUpdateDeclaration struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.declaration);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAllianceUpdateDeclaration struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.declaration = iprot.readString();
      struct.setDeclarationIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

