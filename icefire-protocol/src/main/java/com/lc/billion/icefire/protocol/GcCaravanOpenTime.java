/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 商队玩法倒计时
 * @Message(7002)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcCaravanOpenTime implements org.apache.thrift.TBase<GcCaravanOpenTime, GcCaravanOpenTime._Fields>, java.io.Serializable, Cloneable, Comparable<GcCaravanOpenTime> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcCaravanOpenTime");

  private static final org.apache.thrift.protocol.TField OPEN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("openTime", org.apache.thrift.protocol.TType.I64, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcCaravanOpenTimeStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcCaravanOpenTimeTupleSchemeFactory();

  public long openTime; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    OPEN_TIME((short)2, "openTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 2: // OPEN_TIME
          return OPEN_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __OPENTIME_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.OPEN_TIME};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.OPEN_TIME, new org.apache.thrift.meta_data.FieldMetaData("openTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcCaravanOpenTime.class, metaDataMap);
  }

  public GcCaravanOpenTime() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcCaravanOpenTime(GcCaravanOpenTime other) {
    __isset_bitfield = other.__isset_bitfield;
    this.openTime = other.openTime;
  }

  public GcCaravanOpenTime deepCopy() {
    return new GcCaravanOpenTime(this);
  }

  @Override
  public void clear() {
    setOpenTimeIsSet(false);
    this.openTime = 0;
  }

  public long getOpenTime() {
    return this.openTime;
  }

  public GcCaravanOpenTime setOpenTime(long openTime) {
    this.openTime = openTime;
    setOpenTimeIsSet(true);
    return this;
  }

  public void unsetOpenTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __OPENTIME_ISSET_ID);
  }

  /** Returns true if field openTime is set (has been assigned a value) and false otherwise */
  public boolean isSetOpenTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __OPENTIME_ISSET_ID);
  }

  public void setOpenTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __OPENTIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case OPEN_TIME:
      if (value == null) {
        unsetOpenTime();
      } else {
        setOpenTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case OPEN_TIME:
      return getOpenTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case OPEN_TIME:
      return isSetOpenTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcCaravanOpenTime)
      return this.equals((GcCaravanOpenTime)that);
    return false;
  }

  public boolean equals(GcCaravanOpenTime that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_openTime = true && this.isSetOpenTime();
    boolean that_present_openTime = true && that.isSetOpenTime();
    if (this_present_openTime || that_present_openTime) {
      if (!(this_present_openTime && that_present_openTime))
        return false;
      if (this.openTime != that.openTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetOpenTime()) ? 131071 : 524287);
    if (isSetOpenTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(openTime);

    return hashCode;
  }

  @Override
  public int compareTo(GcCaravanOpenTime other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetOpenTime(), other.isSetOpenTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOpenTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.openTime, other.openTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcCaravanOpenTime(");
    boolean first = true;

    if (isSetOpenTime()) {
      sb.append("openTime:");
      sb.append(this.openTime);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcCaravanOpenTimeStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcCaravanOpenTimeStandardScheme getScheme() {
      return new GcCaravanOpenTimeStandardScheme();
    }
  }

  private static class GcCaravanOpenTimeStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcCaravanOpenTime> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcCaravanOpenTime struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 2: // OPEN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.openTime = iprot.readI64();
              struct.setOpenTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcCaravanOpenTime struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetOpenTime()) {
        oprot.writeFieldBegin(OPEN_TIME_FIELD_DESC);
        oprot.writeI64(struct.openTime);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcCaravanOpenTimeTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcCaravanOpenTimeTupleScheme getScheme() {
      return new GcCaravanOpenTimeTupleScheme();
    }
  }

  private static class GcCaravanOpenTimeTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcCaravanOpenTime> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcCaravanOpenTime struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetOpenTime()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetOpenTime()) {
        oprot.writeI64(struct.openTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcCaravanOpenTime struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.openTime = iprot.readI64();
        struct.setOpenTimeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

