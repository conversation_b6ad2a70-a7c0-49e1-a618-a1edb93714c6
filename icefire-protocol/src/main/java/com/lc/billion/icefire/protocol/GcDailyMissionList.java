/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 日常任务列表
 * @Message(1355)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcDailyMissionList implements org.apache.thrift.TBase<GcDailyMissionList, GcDailyMissionList._Fields>, java.io.Serializable, Cloneable, Comparable<GcDailyMissionList> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcDailyMissionList");

  private static final org.apache.thrift.protocol.TField LIVENESS_GOAL_INFOS_FIELD_DESC = new org.apache.thrift.protocol.TField("livenessGoalInfos", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField LIVENESS_FIELD_DESC = new org.apache.thrift.protocol.TField("liveness", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField MISSIONS_FIELD_DESC = new org.apache.thrift.protocol.TField("missions", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField NEXT_REFRESH_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("nextRefreshTime", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("level", org.apache.thrift.protocol.TType.I32, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcDailyMissionListStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcDailyMissionListTupleSchemeFactory();

  /**
   * 活跃度奖励领取状态
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo> livenessGoalInfos; // required
  /**
   * 活跃度
   */
  public int liveness; // required
  /**
   * 任务
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsMission> missions; // required
  /**
   * 下次重置时间
   */
  public long nextRefreshTime; // required
  /**
   * 今日派发日常任务时主堡
   */
  public int level; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 活跃度奖励领取状态
     */
    LIVENESS_GOAL_INFOS((short)1, "livenessGoalInfos"),
    /**
     * 活跃度
     */
    LIVENESS((short)2, "liveness"),
    /**
     * 任务
     */
    MISSIONS((short)3, "missions"),
    /**
     * 下次重置时间
     */
    NEXT_REFRESH_TIME((short)4, "nextRefreshTime"),
    /**
     * 今日派发日常任务时主堡
     */
    LEVEL((short)5, "level");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // LIVENESS_GOAL_INFOS
          return LIVENESS_GOAL_INFOS;
        case 2: // LIVENESS
          return LIVENESS;
        case 3: // MISSIONS
          return MISSIONS;
        case 4: // NEXT_REFRESH_TIME
          return NEXT_REFRESH_TIME;
        case 5: // LEVEL
          return LEVEL;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __LIVENESS_ISSET_ID = 0;
  private static final int __NEXTREFRESHTIME_ISSET_ID = 1;
  private static final int __LEVEL_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.LIVENESS_GOAL_INFOS, new org.apache.thrift.meta_data.FieldMetaData("livenessGoalInfos", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo.class))));
    tmpMap.put(_Fields.LIVENESS, new org.apache.thrift.meta_data.FieldMetaData("liveness", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MISSIONS, new org.apache.thrift.meta_data.FieldMetaData("missions", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsMission.class))));
    tmpMap.put(_Fields.NEXT_REFRESH_TIME, new org.apache.thrift.meta_data.FieldMetaData("nextRefreshTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.LEVEL, new org.apache.thrift.meta_data.FieldMetaData("level", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcDailyMissionList.class, metaDataMap);
  }

  public GcDailyMissionList() {
  }

  public GcDailyMissionList(
    java.util.List<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo> livenessGoalInfos,
    int liveness,
    java.util.List<com.lc.billion.icefire.protocol.structure.PsMission> missions,
    long nextRefreshTime,
    int level)
  {
    this();
    this.livenessGoalInfos = livenessGoalInfos;
    this.liveness = liveness;
    setLivenessIsSet(true);
    this.missions = missions;
    this.nextRefreshTime = nextRefreshTime;
    setNextRefreshTimeIsSet(true);
    this.level = level;
    setLevelIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcDailyMissionList(GcDailyMissionList other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetLivenessGoalInfos()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo> __this__livenessGoalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo>(other.livenessGoalInfos.size());
      for (com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo other_element : other.livenessGoalInfos) {
        __this__livenessGoalInfos.add(new com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo(other_element));
      }
      this.livenessGoalInfos = __this__livenessGoalInfos;
    }
    this.liveness = other.liveness;
    if (other.isSetMissions()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsMission> __this__missions = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsMission>(other.missions.size());
      for (com.lc.billion.icefire.protocol.structure.PsMission other_element : other.missions) {
        __this__missions.add(new com.lc.billion.icefire.protocol.structure.PsMission(other_element));
      }
      this.missions = __this__missions;
    }
    this.nextRefreshTime = other.nextRefreshTime;
    this.level = other.level;
  }

  public GcDailyMissionList deepCopy() {
    return new GcDailyMissionList(this);
  }

  @Override
  public void clear() {
    this.livenessGoalInfos = null;
    setLivenessIsSet(false);
    this.liveness = 0;
    this.missions = null;
    setNextRefreshTimeIsSet(false);
    this.nextRefreshTime = 0;
    setLevelIsSet(false);
    this.level = 0;
  }

  public int getLivenessGoalInfosSize() {
    return (this.livenessGoalInfos == null) ? 0 : this.livenessGoalInfos.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo> getLivenessGoalInfosIterator() {
    return (this.livenessGoalInfos == null) ? null : this.livenessGoalInfos.iterator();
  }

  public void addToLivenessGoalInfos(com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo elem) {
    if (this.livenessGoalInfos == null) {
      this.livenessGoalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo>();
    }
    this.livenessGoalInfos.add(elem);
  }

  /**
   * 活跃度奖励领取状态
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo> getLivenessGoalInfos() {
    return this.livenessGoalInfos;
  }

  /**
   * 活跃度奖励领取状态
   */
  public GcDailyMissionList setLivenessGoalInfos(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo> livenessGoalInfos) {
    this.livenessGoalInfos = livenessGoalInfos;
    return this;
  }

  public void unsetLivenessGoalInfos() {
    this.livenessGoalInfos = null;
  }

  /** Returns true if field livenessGoalInfos is set (has been assigned a value) and false otherwise */
  public boolean isSetLivenessGoalInfos() {
    return this.livenessGoalInfos != null;
  }

  public void setLivenessGoalInfosIsSet(boolean value) {
    if (!value) {
      this.livenessGoalInfos = null;
    }
  }

  /**
   * 活跃度
   */
  public int getLiveness() {
    return this.liveness;
  }

  /**
   * 活跃度
   */
  public GcDailyMissionList setLiveness(int liveness) {
    this.liveness = liveness;
    setLivenessIsSet(true);
    return this;
  }

  public void unsetLiveness() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LIVENESS_ISSET_ID);
  }

  /** Returns true if field liveness is set (has been assigned a value) and false otherwise */
  public boolean isSetLiveness() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LIVENESS_ISSET_ID);
  }

  public void setLivenessIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LIVENESS_ISSET_ID, value);
  }

  public int getMissionsSize() {
    return (this.missions == null) ? 0 : this.missions.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsMission> getMissionsIterator() {
    return (this.missions == null) ? null : this.missions.iterator();
  }

  public void addToMissions(com.lc.billion.icefire.protocol.structure.PsMission elem) {
    if (this.missions == null) {
      this.missions = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsMission>();
    }
    this.missions.add(elem);
  }

  /**
   * 任务
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsMission> getMissions() {
    return this.missions;
  }

  /**
   * 任务
   */
  public GcDailyMissionList setMissions(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsMission> missions) {
    this.missions = missions;
    return this;
  }

  public void unsetMissions() {
    this.missions = null;
  }

  /** Returns true if field missions is set (has been assigned a value) and false otherwise */
  public boolean isSetMissions() {
    return this.missions != null;
  }

  public void setMissionsIsSet(boolean value) {
    if (!value) {
      this.missions = null;
    }
  }

  /**
   * 下次重置时间
   */
  public long getNextRefreshTime() {
    return this.nextRefreshTime;
  }

  /**
   * 下次重置时间
   */
  public GcDailyMissionList setNextRefreshTime(long nextRefreshTime) {
    this.nextRefreshTime = nextRefreshTime;
    setNextRefreshTimeIsSet(true);
    return this;
  }

  public void unsetNextRefreshTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __NEXTREFRESHTIME_ISSET_ID);
  }

  /** Returns true if field nextRefreshTime is set (has been assigned a value) and false otherwise */
  public boolean isSetNextRefreshTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __NEXTREFRESHTIME_ISSET_ID);
  }

  public void setNextRefreshTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __NEXTREFRESHTIME_ISSET_ID, value);
  }

  /**
   * 今日派发日常任务时主堡
   */
  public int getLevel() {
    return this.level;
  }

  /**
   * 今日派发日常任务时主堡
   */
  public GcDailyMissionList setLevel(int level) {
    this.level = level;
    setLevelIsSet(true);
    return this;
  }

  public void unsetLevel() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LEVEL_ISSET_ID);
  }

  /** Returns true if field level is set (has been assigned a value) and false otherwise */
  public boolean isSetLevel() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LEVEL_ISSET_ID);
  }

  public void setLevelIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LEVEL_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case LIVENESS_GOAL_INFOS:
      if (value == null) {
        unsetLivenessGoalInfos();
      } else {
        setLivenessGoalInfos((java.util.List<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo>)value);
      }
      break;

    case LIVENESS:
      if (value == null) {
        unsetLiveness();
      } else {
        setLiveness((java.lang.Integer)value);
      }
      break;

    case MISSIONS:
      if (value == null) {
        unsetMissions();
      } else {
        setMissions((java.util.List<com.lc.billion.icefire.protocol.structure.PsMission>)value);
      }
      break;

    case NEXT_REFRESH_TIME:
      if (value == null) {
        unsetNextRefreshTime();
      } else {
        setNextRefreshTime((java.lang.Long)value);
      }
      break;

    case LEVEL:
      if (value == null) {
        unsetLevel();
      } else {
        setLevel((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case LIVENESS_GOAL_INFOS:
      return getLivenessGoalInfos();

    case LIVENESS:
      return getLiveness();

    case MISSIONS:
      return getMissions();

    case NEXT_REFRESH_TIME:
      return getNextRefreshTime();

    case LEVEL:
      return getLevel();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case LIVENESS_GOAL_INFOS:
      return isSetLivenessGoalInfos();
    case LIVENESS:
      return isSetLiveness();
    case MISSIONS:
      return isSetMissions();
    case NEXT_REFRESH_TIME:
      return isSetNextRefreshTime();
    case LEVEL:
      return isSetLevel();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcDailyMissionList)
      return this.equals((GcDailyMissionList)that);
    return false;
  }

  public boolean equals(GcDailyMissionList that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_livenessGoalInfos = true && this.isSetLivenessGoalInfos();
    boolean that_present_livenessGoalInfos = true && that.isSetLivenessGoalInfos();
    if (this_present_livenessGoalInfos || that_present_livenessGoalInfos) {
      if (!(this_present_livenessGoalInfos && that_present_livenessGoalInfos))
        return false;
      if (!this.livenessGoalInfos.equals(that.livenessGoalInfos))
        return false;
    }

    boolean this_present_liveness = true;
    boolean that_present_liveness = true;
    if (this_present_liveness || that_present_liveness) {
      if (!(this_present_liveness && that_present_liveness))
        return false;
      if (this.liveness != that.liveness)
        return false;
    }

    boolean this_present_missions = true && this.isSetMissions();
    boolean that_present_missions = true && that.isSetMissions();
    if (this_present_missions || that_present_missions) {
      if (!(this_present_missions && that_present_missions))
        return false;
      if (!this.missions.equals(that.missions))
        return false;
    }

    boolean this_present_nextRefreshTime = true;
    boolean that_present_nextRefreshTime = true;
    if (this_present_nextRefreshTime || that_present_nextRefreshTime) {
      if (!(this_present_nextRefreshTime && that_present_nextRefreshTime))
        return false;
      if (this.nextRefreshTime != that.nextRefreshTime)
        return false;
    }

    boolean this_present_level = true;
    boolean that_present_level = true;
    if (this_present_level || that_present_level) {
      if (!(this_present_level && that_present_level))
        return false;
      if (this.level != that.level)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetLivenessGoalInfos()) ? 131071 : 524287);
    if (isSetLivenessGoalInfos())
      hashCode = hashCode * 8191 + livenessGoalInfos.hashCode();

    hashCode = hashCode * 8191 + liveness;

    hashCode = hashCode * 8191 + ((isSetMissions()) ? 131071 : 524287);
    if (isSetMissions())
      hashCode = hashCode * 8191 + missions.hashCode();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(nextRefreshTime);

    hashCode = hashCode * 8191 + level;

    return hashCode;
  }

  @Override
  public int compareTo(GcDailyMissionList other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetLivenessGoalInfos(), other.isSetLivenessGoalInfos());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLivenessGoalInfos()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.livenessGoalInfos, other.livenessGoalInfos);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetLiveness(), other.isSetLiveness());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLiveness()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.liveness, other.liveness);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMissions(), other.isSetMissions());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMissions()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.missions, other.missions);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetNextRefreshTime(), other.isSetNextRefreshTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNextRefreshTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nextRefreshTime, other.nextRefreshTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetLevel(), other.isSetLevel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLevel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.level, other.level);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcDailyMissionList(");
    boolean first = true;

    sb.append("livenessGoalInfos:");
    if (this.livenessGoalInfos == null) {
      sb.append("null");
    } else {
      sb.append(this.livenessGoalInfos);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("liveness:");
    sb.append(this.liveness);
    first = false;
    if (!first) sb.append(", ");
    sb.append("missions:");
    if (this.missions == null) {
      sb.append("null");
    } else {
      sb.append(this.missions);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("nextRefreshTime:");
    sb.append(this.nextRefreshTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("level:");
    sb.append(this.level);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (livenessGoalInfos == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'livenessGoalInfos' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'liveness' because it's a primitive and you chose the non-beans generator.
    if (missions == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'missions' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'nextRefreshTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'level' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcDailyMissionListStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcDailyMissionListStandardScheme getScheme() {
      return new GcDailyMissionListStandardScheme();
    }
  }

  private static class GcDailyMissionListStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcDailyMissionList> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcDailyMissionList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // LIVENESS_GOAL_INFOS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.livenessGoalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo();
                  _elem1.read(iprot);
                  struct.livenessGoalInfos.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setLivenessGoalInfosIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // LIVENESS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.liveness = iprot.readI32();
              struct.setLivenessIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // MISSIONS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.missions = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsMission>(_list3.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsMission _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = new com.lc.billion.icefire.protocol.structure.PsMission();
                  _elem4.read(iprot);
                  struct.missions.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setMissionsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // NEXT_REFRESH_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.nextRefreshTime = iprot.readI64();
              struct.setNextRefreshTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // LEVEL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.level = iprot.readI32();
              struct.setLevelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetLiveness()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'liveness' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetNextRefreshTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'nextRefreshTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetLevel()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'level' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcDailyMissionList struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.livenessGoalInfos != null) {
        oprot.writeFieldBegin(LIVENESS_GOAL_INFOS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.livenessGoalInfos.size()));
          for (com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo _iter6 : struct.livenessGoalInfos)
          {
            _iter6.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(LIVENESS_FIELD_DESC);
      oprot.writeI32(struct.liveness);
      oprot.writeFieldEnd();
      if (struct.missions != null) {
        oprot.writeFieldBegin(MISSIONS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.missions.size()));
          for (com.lc.billion.icefire.protocol.structure.PsMission _iter7 : struct.missions)
          {
            _iter7.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(NEXT_REFRESH_TIME_FIELD_DESC);
      oprot.writeI64(struct.nextRefreshTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(LEVEL_FIELD_DESC);
      oprot.writeI32(struct.level);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcDailyMissionListTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcDailyMissionListTupleScheme getScheme() {
      return new GcDailyMissionListTupleScheme();
    }
  }

  private static class GcDailyMissionListTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcDailyMissionList> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcDailyMissionList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      {
        oprot.writeI32(struct.livenessGoalInfos.size());
        for (com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo _iter8 : struct.livenessGoalInfos)
        {
          _iter8.write(oprot);
        }
      }
      oprot.writeI32(struct.liveness);
      {
        oprot.writeI32(struct.missions.size());
        for (com.lc.billion.icefire.protocol.structure.PsMission _iter9 : struct.missions)
        {
          _iter9.write(oprot);
        }
      }
      oprot.writeI64(struct.nextRefreshTime);
      oprot.writeI32(struct.level);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcDailyMissionList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      {
        org.apache.thrift.protocol.TList _list10 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.livenessGoalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo>(_list10.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo _elem11;
        for (int _i12 = 0; _i12 < _list10.size; ++_i12)
        {
          _elem11 = new com.lc.billion.icefire.protocol.structure.PsMissionLivenessGoalInfo();
          _elem11.read(iprot);
          struct.livenessGoalInfos.add(_elem11);
        }
      }
      struct.setLivenessGoalInfosIsSet(true);
      struct.liveness = iprot.readI32();
      struct.setLivenessIsSet(true);
      {
        org.apache.thrift.protocol.TList _list13 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.missions = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsMission>(_list13.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsMission _elem14;
        for (int _i15 = 0; _i15 < _list13.size; ++_i15)
        {
          _elem14 = new com.lc.billion.icefire.protocol.structure.PsMission();
          _elem14.read(iprot);
          struct.missions.add(_elem14);
        }
      }
      struct.setMissionsIsSet(true);
      struct.nextRefreshTime = iprot.readI64();
      struct.setNextRefreshTimeIsSet(true);
      struct.level = iprot.readI32();
      struct.setLevelIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

