/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 积分及第一名变化推送
 * @Message(7237)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcGetAllianceBattleScorePush implements org.apache.thrift.TBase<GcGetAllianceBattleScorePush, GcGetAllianceBattleScorePush._Fields>, java.io.Serializable, Cloneable, Comparable<GcGetAllianceBattleScorePush> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcGetAllianceBattleScorePush");

  private static final org.apache.thrift.protocol.TField MY_ALLIANCE_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("myAllianceScore", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RIVALRY_ALLIANCE_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("rivalryAllianceScore", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField FIRST_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("firstName", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField FIRST_HEAD_FIELD_DESC = new org.apache.thrift.protocol.TField("firstHead", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField FIRST_ROLE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("firstRoleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)6);
  private static final org.apache.thrift.protocol.TField RIVALRY_ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rivalryRoleId", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField RIVALRY_FIRST_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("rivalryFirstName", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField RIVALRY_FIRST_HEAD_FIELD_DESC = new org.apache.thrift.protocol.TField("rivalryFirstHead", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField RIVALRY_FIRST_ROLE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("rivalryFirstRoleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)10);
  private static final org.apache.thrift.protocol.TField MY_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("myScore", org.apache.thrift.protocol.TType.I64, (short)11);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcGetAllianceBattleScorePushStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcGetAllianceBattleScorePushTupleSchemeFactory();

  public long myAllianceScore; // optional
  public long rivalryAllianceScore; // optional
  public long roleId; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String firstName; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String firstHead; // optional
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRoleInfo firstRoleInfo; // optional
  public long rivalryRoleId; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String rivalryFirstName; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String rivalryFirstHead; // optional
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRoleInfo rivalryFirstRoleInfo; // optional
  public long myScore; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    MY_ALLIANCE_SCORE((short)1, "myAllianceScore"),
    RIVALRY_ALLIANCE_SCORE((short)2, "rivalryAllianceScore"),
    ROLE_ID((short)3, "roleId"),
    FIRST_NAME((short)4, "firstName"),
    FIRST_HEAD((short)5, "firstHead"),
    FIRST_ROLE_INFO((short)6, "firstRoleInfo"),
    RIVALRY_ROLE_ID((short)7, "rivalryRoleId"),
    RIVALRY_FIRST_NAME((short)8, "rivalryFirstName"),
    RIVALRY_FIRST_HEAD((short)9, "rivalryFirstHead"),
    RIVALRY_FIRST_ROLE_INFO((short)10, "rivalryFirstRoleInfo"),
    MY_SCORE((short)11, "myScore");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // MY_ALLIANCE_SCORE
          return MY_ALLIANCE_SCORE;
        case 2: // RIVALRY_ALLIANCE_SCORE
          return RIVALRY_ALLIANCE_SCORE;
        case 3: // ROLE_ID
          return ROLE_ID;
        case 4: // FIRST_NAME
          return FIRST_NAME;
        case 5: // FIRST_HEAD
          return FIRST_HEAD;
        case 6: // FIRST_ROLE_INFO
          return FIRST_ROLE_INFO;
        case 7: // RIVALRY_ROLE_ID
          return RIVALRY_ROLE_ID;
        case 8: // RIVALRY_FIRST_NAME
          return RIVALRY_FIRST_NAME;
        case 9: // RIVALRY_FIRST_HEAD
          return RIVALRY_FIRST_HEAD;
        case 10: // RIVALRY_FIRST_ROLE_INFO
          return RIVALRY_FIRST_ROLE_INFO;
        case 11: // MY_SCORE
          return MY_SCORE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __MYALLIANCESCORE_ISSET_ID = 0;
  private static final int __RIVALRYALLIANCESCORE_ISSET_ID = 1;
  private static final int __ROLEID_ISSET_ID = 2;
  private static final int __RIVALRYROLEID_ISSET_ID = 3;
  private static final int __MYSCORE_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.MY_ALLIANCE_SCORE,_Fields.RIVALRY_ALLIANCE_SCORE,_Fields.ROLE_ID,_Fields.FIRST_NAME,_Fields.FIRST_HEAD,_Fields.FIRST_ROLE_INFO,_Fields.RIVALRY_ROLE_ID,_Fields.RIVALRY_FIRST_NAME,_Fields.RIVALRY_FIRST_HEAD,_Fields.RIVALRY_FIRST_ROLE_INFO,_Fields.MY_SCORE};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.MY_ALLIANCE_SCORE, new org.apache.thrift.meta_data.FieldMetaData("myAllianceScore", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RIVALRY_ALLIANCE_SCORE, new org.apache.thrift.meta_data.FieldMetaData("rivalryAllianceScore", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("roleId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FIRST_NAME, new org.apache.thrift.meta_data.FieldMetaData("firstName", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.FIRST_HEAD, new org.apache.thrift.meta_data.FieldMetaData("firstHead", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.FIRST_ROLE_INFO, new org.apache.thrift.meta_data.FieldMetaData("firstRoleInfo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsRoleInfo.class)));
    tmpMap.put(_Fields.RIVALRY_ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("rivalryRoleId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RIVALRY_FIRST_NAME, new org.apache.thrift.meta_data.FieldMetaData("rivalryFirstName", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RIVALRY_FIRST_HEAD, new org.apache.thrift.meta_data.FieldMetaData("rivalryFirstHead", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RIVALRY_FIRST_ROLE_INFO, new org.apache.thrift.meta_data.FieldMetaData("rivalryFirstRoleInfo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsRoleInfo.class)));
    tmpMap.put(_Fields.MY_SCORE, new org.apache.thrift.meta_data.FieldMetaData("myScore", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcGetAllianceBattleScorePush.class, metaDataMap);
  }

  public GcGetAllianceBattleScorePush() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcGetAllianceBattleScorePush(GcGetAllianceBattleScorePush other) {
    __isset_bitfield = other.__isset_bitfield;
    this.myAllianceScore = other.myAllianceScore;
    this.rivalryAllianceScore = other.rivalryAllianceScore;
    this.roleId = other.roleId;
    if (other.isSetFirstName()) {
      this.firstName = other.firstName;
    }
    if (other.isSetFirstHead()) {
      this.firstHead = other.firstHead;
    }
    if (other.isSetFirstRoleInfo()) {
      this.firstRoleInfo = new com.lc.billion.icefire.protocol.structure.PsRoleInfo(other.firstRoleInfo);
    }
    this.rivalryRoleId = other.rivalryRoleId;
    if (other.isSetRivalryFirstName()) {
      this.rivalryFirstName = other.rivalryFirstName;
    }
    if (other.isSetRivalryFirstHead()) {
      this.rivalryFirstHead = other.rivalryFirstHead;
    }
    if (other.isSetRivalryFirstRoleInfo()) {
      this.rivalryFirstRoleInfo = new com.lc.billion.icefire.protocol.structure.PsRoleInfo(other.rivalryFirstRoleInfo);
    }
    this.myScore = other.myScore;
  }

  public GcGetAllianceBattleScorePush deepCopy() {
    return new GcGetAllianceBattleScorePush(this);
  }

  @Override
  public void clear() {
    setMyAllianceScoreIsSet(false);
    this.myAllianceScore = 0;
    setRivalryAllianceScoreIsSet(false);
    this.rivalryAllianceScore = 0;
    setRoleIdIsSet(false);
    this.roleId = 0;
    this.firstName = null;
    this.firstHead = null;
    this.firstRoleInfo = null;
    setRivalryRoleIdIsSet(false);
    this.rivalryRoleId = 0;
    this.rivalryFirstName = null;
    this.rivalryFirstHead = null;
    this.rivalryFirstRoleInfo = null;
    setMyScoreIsSet(false);
    this.myScore = 0;
  }

  public long getMyAllianceScore() {
    return this.myAllianceScore;
  }

  public GcGetAllianceBattleScorePush setMyAllianceScore(long myAllianceScore) {
    this.myAllianceScore = myAllianceScore;
    setMyAllianceScoreIsSet(true);
    return this;
  }

  public void unsetMyAllianceScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MYALLIANCESCORE_ISSET_ID);
  }

  /** Returns true if field myAllianceScore is set (has been assigned a value) and false otherwise */
  public boolean isSetMyAllianceScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MYALLIANCESCORE_ISSET_ID);
  }

  public void setMyAllianceScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MYALLIANCESCORE_ISSET_ID, value);
  }

  public long getRivalryAllianceScore() {
    return this.rivalryAllianceScore;
  }

  public GcGetAllianceBattleScorePush setRivalryAllianceScore(long rivalryAllianceScore) {
    this.rivalryAllianceScore = rivalryAllianceScore;
    setRivalryAllianceScoreIsSet(true);
    return this;
  }

  public void unsetRivalryAllianceScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RIVALRYALLIANCESCORE_ISSET_ID);
  }

  /** Returns true if field rivalryAllianceScore is set (has been assigned a value) and false otherwise */
  public boolean isSetRivalryAllianceScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RIVALRYALLIANCESCORE_ISSET_ID);
  }

  public void setRivalryAllianceScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RIVALRYALLIANCESCORE_ISSET_ID, value);
  }

  public long getRoleId() {
    return this.roleId;
  }

  public GcGetAllianceBattleScorePush setRoleId(long roleId) {
    this.roleId = roleId;
    setRoleIdIsSet(true);
    return this;
  }

  public void unsetRoleId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  /** Returns true if field roleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  public void setRoleIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ROLEID_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getFirstName() {
    return this.firstName;
  }

  public GcGetAllianceBattleScorePush setFirstName(@org.apache.thrift.annotation.Nullable java.lang.String firstName) {
    this.firstName = firstName;
    return this;
  }

  public void unsetFirstName() {
    this.firstName = null;
  }

  /** Returns true if field firstName is set (has been assigned a value) and false otherwise */
  public boolean isSetFirstName() {
    return this.firstName != null;
  }

  public void setFirstNameIsSet(boolean value) {
    if (!value) {
      this.firstName = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getFirstHead() {
    return this.firstHead;
  }

  public GcGetAllianceBattleScorePush setFirstHead(@org.apache.thrift.annotation.Nullable java.lang.String firstHead) {
    this.firstHead = firstHead;
    return this;
  }

  public void unsetFirstHead() {
    this.firstHead = null;
  }

  /** Returns true if field firstHead is set (has been assigned a value) and false otherwise */
  public boolean isSetFirstHead() {
    return this.firstHead != null;
  }

  public void setFirstHeadIsSet(boolean value) {
    if (!value) {
      this.firstHead = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.structure.PsRoleInfo getFirstRoleInfo() {
    return this.firstRoleInfo;
  }

  public GcGetAllianceBattleScorePush setFirstRoleInfo(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRoleInfo firstRoleInfo) {
    this.firstRoleInfo = firstRoleInfo;
    return this;
  }

  public void unsetFirstRoleInfo() {
    this.firstRoleInfo = null;
  }

  /** Returns true if field firstRoleInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetFirstRoleInfo() {
    return this.firstRoleInfo != null;
  }

  public void setFirstRoleInfoIsSet(boolean value) {
    if (!value) {
      this.firstRoleInfo = null;
    }
  }

  public long getRivalryRoleId() {
    return this.rivalryRoleId;
  }

  public GcGetAllianceBattleScorePush setRivalryRoleId(long rivalryRoleId) {
    this.rivalryRoleId = rivalryRoleId;
    setRivalryRoleIdIsSet(true);
    return this;
  }

  public void unsetRivalryRoleId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RIVALRYROLEID_ISSET_ID);
  }

  /** Returns true if field rivalryRoleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRivalryRoleId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RIVALRYROLEID_ISSET_ID);
  }

  public void setRivalryRoleIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RIVALRYROLEID_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getRivalryFirstName() {
    return this.rivalryFirstName;
  }

  public GcGetAllianceBattleScorePush setRivalryFirstName(@org.apache.thrift.annotation.Nullable java.lang.String rivalryFirstName) {
    this.rivalryFirstName = rivalryFirstName;
    return this;
  }

  public void unsetRivalryFirstName() {
    this.rivalryFirstName = null;
  }

  /** Returns true if field rivalryFirstName is set (has been assigned a value) and false otherwise */
  public boolean isSetRivalryFirstName() {
    return this.rivalryFirstName != null;
  }

  public void setRivalryFirstNameIsSet(boolean value) {
    if (!value) {
      this.rivalryFirstName = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getRivalryFirstHead() {
    return this.rivalryFirstHead;
  }

  public GcGetAllianceBattleScorePush setRivalryFirstHead(@org.apache.thrift.annotation.Nullable java.lang.String rivalryFirstHead) {
    this.rivalryFirstHead = rivalryFirstHead;
    return this;
  }

  public void unsetRivalryFirstHead() {
    this.rivalryFirstHead = null;
  }

  /** Returns true if field rivalryFirstHead is set (has been assigned a value) and false otherwise */
  public boolean isSetRivalryFirstHead() {
    return this.rivalryFirstHead != null;
  }

  public void setRivalryFirstHeadIsSet(boolean value) {
    if (!value) {
      this.rivalryFirstHead = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.structure.PsRoleInfo getRivalryFirstRoleInfo() {
    return this.rivalryFirstRoleInfo;
  }

  public GcGetAllianceBattleScorePush setRivalryFirstRoleInfo(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRoleInfo rivalryFirstRoleInfo) {
    this.rivalryFirstRoleInfo = rivalryFirstRoleInfo;
    return this;
  }

  public void unsetRivalryFirstRoleInfo() {
    this.rivalryFirstRoleInfo = null;
  }

  /** Returns true if field rivalryFirstRoleInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetRivalryFirstRoleInfo() {
    return this.rivalryFirstRoleInfo != null;
  }

  public void setRivalryFirstRoleInfoIsSet(boolean value) {
    if (!value) {
      this.rivalryFirstRoleInfo = null;
    }
  }

  public long getMyScore() {
    return this.myScore;
  }

  public GcGetAllianceBattleScorePush setMyScore(long myScore) {
    this.myScore = myScore;
    setMyScoreIsSet(true);
    return this;
  }

  public void unsetMyScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MYSCORE_ISSET_ID);
  }

  /** Returns true if field myScore is set (has been assigned a value) and false otherwise */
  public boolean isSetMyScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MYSCORE_ISSET_ID);
  }

  public void setMyScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MYSCORE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case MY_ALLIANCE_SCORE:
      if (value == null) {
        unsetMyAllianceScore();
      } else {
        setMyAllianceScore((java.lang.Long)value);
      }
      break;

    case RIVALRY_ALLIANCE_SCORE:
      if (value == null) {
        unsetRivalryAllianceScore();
      } else {
        setRivalryAllianceScore((java.lang.Long)value);
      }
      break;

    case ROLE_ID:
      if (value == null) {
        unsetRoleId();
      } else {
        setRoleId((java.lang.Long)value);
      }
      break;

    case FIRST_NAME:
      if (value == null) {
        unsetFirstName();
      } else {
        setFirstName((java.lang.String)value);
      }
      break;

    case FIRST_HEAD:
      if (value == null) {
        unsetFirstHead();
      } else {
        setFirstHead((java.lang.String)value);
      }
      break;

    case FIRST_ROLE_INFO:
      if (value == null) {
        unsetFirstRoleInfo();
      } else {
        setFirstRoleInfo((com.lc.billion.icefire.protocol.structure.PsRoleInfo)value);
      }
      break;

    case RIVALRY_ROLE_ID:
      if (value == null) {
        unsetRivalryRoleId();
      } else {
        setRivalryRoleId((java.lang.Long)value);
      }
      break;

    case RIVALRY_FIRST_NAME:
      if (value == null) {
        unsetRivalryFirstName();
      } else {
        setRivalryFirstName((java.lang.String)value);
      }
      break;

    case RIVALRY_FIRST_HEAD:
      if (value == null) {
        unsetRivalryFirstHead();
      } else {
        setRivalryFirstHead((java.lang.String)value);
      }
      break;

    case RIVALRY_FIRST_ROLE_INFO:
      if (value == null) {
        unsetRivalryFirstRoleInfo();
      } else {
        setRivalryFirstRoleInfo((com.lc.billion.icefire.protocol.structure.PsRoleInfo)value);
      }
      break;

    case MY_SCORE:
      if (value == null) {
        unsetMyScore();
      } else {
        setMyScore((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case MY_ALLIANCE_SCORE:
      return getMyAllianceScore();

    case RIVALRY_ALLIANCE_SCORE:
      return getRivalryAllianceScore();

    case ROLE_ID:
      return getRoleId();

    case FIRST_NAME:
      return getFirstName();

    case FIRST_HEAD:
      return getFirstHead();

    case FIRST_ROLE_INFO:
      return getFirstRoleInfo();

    case RIVALRY_ROLE_ID:
      return getRivalryRoleId();

    case RIVALRY_FIRST_NAME:
      return getRivalryFirstName();

    case RIVALRY_FIRST_HEAD:
      return getRivalryFirstHead();

    case RIVALRY_FIRST_ROLE_INFO:
      return getRivalryFirstRoleInfo();

    case MY_SCORE:
      return getMyScore();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case MY_ALLIANCE_SCORE:
      return isSetMyAllianceScore();
    case RIVALRY_ALLIANCE_SCORE:
      return isSetRivalryAllianceScore();
    case ROLE_ID:
      return isSetRoleId();
    case FIRST_NAME:
      return isSetFirstName();
    case FIRST_HEAD:
      return isSetFirstHead();
    case FIRST_ROLE_INFO:
      return isSetFirstRoleInfo();
    case RIVALRY_ROLE_ID:
      return isSetRivalryRoleId();
    case RIVALRY_FIRST_NAME:
      return isSetRivalryFirstName();
    case RIVALRY_FIRST_HEAD:
      return isSetRivalryFirstHead();
    case RIVALRY_FIRST_ROLE_INFO:
      return isSetRivalryFirstRoleInfo();
    case MY_SCORE:
      return isSetMyScore();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcGetAllianceBattleScorePush)
      return this.equals((GcGetAllianceBattleScorePush)that);
    return false;
  }

  public boolean equals(GcGetAllianceBattleScorePush that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_myAllianceScore = true && this.isSetMyAllianceScore();
    boolean that_present_myAllianceScore = true && that.isSetMyAllianceScore();
    if (this_present_myAllianceScore || that_present_myAllianceScore) {
      if (!(this_present_myAllianceScore && that_present_myAllianceScore))
        return false;
      if (this.myAllianceScore != that.myAllianceScore)
        return false;
    }

    boolean this_present_rivalryAllianceScore = true && this.isSetRivalryAllianceScore();
    boolean that_present_rivalryAllianceScore = true && that.isSetRivalryAllianceScore();
    if (this_present_rivalryAllianceScore || that_present_rivalryAllianceScore) {
      if (!(this_present_rivalryAllianceScore && that_present_rivalryAllianceScore))
        return false;
      if (this.rivalryAllianceScore != that.rivalryAllianceScore)
        return false;
    }

    boolean this_present_roleId = true && this.isSetRoleId();
    boolean that_present_roleId = true && that.isSetRoleId();
    if (this_present_roleId || that_present_roleId) {
      if (!(this_present_roleId && that_present_roleId))
        return false;
      if (this.roleId != that.roleId)
        return false;
    }

    boolean this_present_firstName = true && this.isSetFirstName();
    boolean that_present_firstName = true && that.isSetFirstName();
    if (this_present_firstName || that_present_firstName) {
      if (!(this_present_firstName && that_present_firstName))
        return false;
      if (!this.firstName.equals(that.firstName))
        return false;
    }

    boolean this_present_firstHead = true && this.isSetFirstHead();
    boolean that_present_firstHead = true && that.isSetFirstHead();
    if (this_present_firstHead || that_present_firstHead) {
      if (!(this_present_firstHead && that_present_firstHead))
        return false;
      if (!this.firstHead.equals(that.firstHead))
        return false;
    }

    boolean this_present_firstRoleInfo = true && this.isSetFirstRoleInfo();
    boolean that_present_firstRoleInfo = true && that.isSetFirstRoleInfo();
    if (this_present_firstRoleInfo || that_present_firstRoleInfo) {
      if (!(this_present_firstRoleInfo && that_present_firstRoleInfo))
        return false;
      if (!this.firstRoleInfo.equals(that.firstRoleInfo))
        return false;
    }

    boolean this_present_rivalryRoleId = true && this.isSetRivalryRoleId();
    boolean that_present_rivalryRoleId = true && that.isSetRivalryRoleId();
    if (this_present_rivalryRoleId || that_present_rivalryRoleId) {
      if (!(this_present_rivalryRoleId && that_present_rivalryRoleId))
        return false;
      if (this.rivalryRoleId != that.rivalryRoleId)
        return false;
    }

    boolean this_present_rivalryFirstName = true && this.isSetRivalryFirstName();
    boolean that_present_rivalryFirstName = true && that.isSetRivalryFirstName();
    if (this_present_rivalryFirstName || that_present_rivalryFirstName) {
      if (!(this_present_rivalryFirstName && that_present_rivalryFirstName))
        return false;
      if (!this.rivalryFirstName.equals(that.rivalryFirstName))
        return false;
    }

    boolean this_present_rivalryFirstHead = true && this.isSetRivalryFirstHead();
    boolean that_present_rivalryFirstHead = true && that.isSetRivalryFirstHead();
    if (this_present_rivalryFirstHead || that_present_rivalryFirstHead) {
      if (!(this_present_rivalryFirstHead && that_present_rivalryFirstHead))
        return false;
      if (!this.rivalryFirstHead.equals(that.rivalryFirstHead))
        return false;
    }

    boolean this_present_rivalryFirstRoleInfo = true && this.isSetRivalryFirstRoleInfo();
    boolean that_present_rivalryFirstRoleInfo = true && that.isSetRivalryFirstRoleInfo();
    if (this_present_rivalryFirstRoleInfo || that_present_rivalryFirstRoleInfo) {
      if (!(this_present_rivalryFirstRoleInfo && that_present_rivalryFirstRoleInfo))
        return false;
      if (!this.rivalryFirstRoleInfo.equals(that.rivalryFirstRoleInfo))
        return false;
    }

    boolean this_present_myScore = true && this.isSetMyScore();
    boolean that_present_myScore = true && that.isSetMyScore();
    if (this_present_myScore || that_present_myScore) {
      if (!(this_present_myScore && that_present_myScore))
        return false;
      if (this.myScore != that.myScore)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetMyAllianceScore()) ? 131071 : 524287);
    if (isSetMyAllianceScore())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(myAllianceScore);

    hashCode = hashCode * 8191 + ((isSetRivalryAllianceScore()) ? 131071 : 524287);
    if (isSetRivalryAllianceScore())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(rivalryAllianceScore);

    hashCode = hashCode * 8191 + ((isSetRoleId()) ? 131071 : 524287);
    if (isSetRoleId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(roleId);

    hashCode = hashCode * 8191 + ((isSetFirstName()) ? 131071 : 524287);
    if (isSetFirstName())
      hashCode = hashCode * 8191 + firstName.hashCode();

    hashCode = hashCode * 8191 + ((isSetFirstHead()) ? 131071 : 524287);
    if (isSetFirstHead())
      hashCode = hashCode * 8191 + firstHead.hashCode();

    hashCode = hashCode * 8191 + ((isSetFirstRoleInfo()) ? 131071 : 524287);
    if (isSetFirstRoleInfo())
      hashCode = hashCode * 8191 + firstRoleInfo.hashCode();

    hashCode = hashCode * 8191 + ((isSetRivalryRoleId()) ? 131071 : 524287);
    if (isSetRivalryRoleId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(rivalryRoleId);

    hashCode = hashCode * 8191 + ((isSetRivalryFirstName()) ? 131071 : 524287);
    if (isSetRivalryFirstName())
      hashCode = hashCode * 8191 + rivalryFirstName.hashCode();

    hashCode = hashCode * 8191 + ((isSetRivalryFirstHead()) ? 131071 : 524287);
    if (isSetRivalryFirstHead())
      hashCode = hashCode * 8191 + rivalryFirstHead.hashCode();

    hashCode = hashCode * 8191 + ((isSetRivalryFirstRoleInfo()) ? 131071 : 524287);
    if (isSetRivalryFirstRoleInfo())
      hashCode = hashCode * 8191 + rivalryFirstRoleInfo.hashCode();

    hashCode = hashCode * 8191 + ((isSetMyScore()) ? 131071 : 524287);
    if (isSetMyScore())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(myScore);

    return hashCode;
  }

  @Override
  public int compareTo(GcGetAllianceBattleScorePush other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetMyAllianceScore(), other.isSetMyAllianceScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMyAllianceScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.myAllianceScore, other.myAllianceScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRivalryAllianceScore(), other.isSetRivalryAllianceScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRivalryAllianceScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rivalryAllianceScore, other.rivalryAllianceScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRoleId(), other.isSetRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleId, other.roleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetFirstName(), other.isSetFirstName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFirstName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.firstName, other.firstName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetFirstHead(), other.isSetFirstHead());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFirstHead()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.firstHead, other.firstHead);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetFirstRoleInfo(), other.isSetFirstRoleInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFirstRoleInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.firstRoleInfo, other.firstRoleInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRivalryRoleId(), other.isSetRivalryRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRivalryRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rivalryRoleId, other.rivalryRoleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRivalryFirstName(), other.isSetRivalryFirstName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRivalryFirstName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rivalryFirstName, other.rivalryFirstName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRivalryFirstHead(), other.isSetRivalryFirstHead());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRivalryFirstHead()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rivalryFirstHead, other.rivalryFirstHead);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRivalryFirstRoleInfo(), other.isSetRivalryFirstRoleInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRivalryFirstRoleInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rivalryFirstRoleInfo, other.rivalryFirstRoleInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMyScore(), other.isSetMyScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMyScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.myScore, other.myScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcGetAllianceBattleScorePush(");
    boolean first = true;

    if (isSetMyAllianceScore()) {
      sb.append("myAllianceScore:");
      sb.append(this.myAllianceScore);
      first = false;
    }
    if (isSetRivalryAllianceScore()) {
      if (!first) sb.append(", ");
      sb.append("rivalryAllianceScore:");
      sb.append(this.rivalryAllianceScore);
      first = false;
    }
    if (isSetRoleId()) {
      if (!first) sb.append(", ");
      sb.append("roleId:");
      sb.append(this.roleId);
      first = false;
    }
    if (isSetFirstName()) {
      if (!first) sb.append(", ");
      sb.append("firstName:");
      if (this.firstName == null) {
        sb.append("null");
      } else {
        sb.append(this.firstName);
      }
      first = false;
    }
    if (isSetFirstHead()) {
      if (!first) sb.append(", ");
      sb.append("firstHead:");
      if (this.firstHead == null) {
        sb.append("null");
      } else {
        sb.append(this.firstHead);
      }
      first = false;
    }
    if (isSetFirstRoleInfo()) {
      if (!first) sb.append(", ");
      sb.append("firstRoleInfo:");
      if (this.firstRoleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.firstRoleInfo);
      }
      first = false;
    }
    if (isSetRivalryRoleId()) {
      if (!first) sb.append(", ");
      sb.append("rivalryRoleId:");
      sb.append(this.rivalryRoleId);
      first = false;
    }
    if (isSetRivalryFirstName()) {
      if (!first) sb.append(", ");
      sb.append("rivalryFirstName:");
      if (this.rivalryFirstName == null) {
        sb.append("null");
      } else {
        sb.append(this.rivalryFirstName);
      }
      first = false;
    }
    if (isSetRivalryFirstHead()) {
      if (!first) sb.append(", ");
      sb.append("rivalryFirstHead:");
      if (this.rivalryFirstHead == null) {
        sb.append("null");
      } else {
        sb.append(this.rivalryFirstHead);
      }
      first = false;
    }
    if (isSetRivalryFirstRoleInfo()) {
      if (!first) sb.append(", ");
      sb.append("rivalryFirstRoleInfo:");
      if (this.rivalryFirstRoleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.rivalryFirstRoleInfo);
      }
      first = false;
    }
    if (isSetMyScore()) {
      if (!first) sb.append(", ");
      sb.append("myScore:");
      sb.append(this.myScore);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (firstRoleInfo != null) {
      firstRoleInfo.validate();
    }
    if (rivalryFirstRoleInfo != null) {
      rivalryFirstRoleInfo.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcGetAllianceBattleScorePushStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcGetAllianceBattleScorePushStandardScheme getScheme() {
      return new GcGetAllianceBattleScorePushStandardScheme();
    }
  }

  private static class GcGetAllianceBattleScorePushStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcGetAllianceBattleScorePush> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcGetAllianceBattleScorePush struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // MY_ALLIANCE_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.myAllianceScore = iprot.readI64();
              struct.setMyAllianceScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RIVALRY_ALLIANCE_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rivalryAllianceScore = iprot.readI64();
              struct.setRivalryAllianceScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleId = iprot.readI64();
              struct.setRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // FIRST_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.firstName = iprot.readString();
              struct.setFirstNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // FIRST_HEAD
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.firstHead = iprot.readString();
              struct.setFirstHeadIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // FIRST_ROLE_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.firstRoleInfo = new com.lc.billion.icefire.protocol.structure.PsRoleInfo();
              struct.firstRoleInfo.read(iprot);
              struct.setFirstRoleInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // RIVALRY_ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rivalryRoleId = iprot.readI64();
              struct.setRivalryRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // RIVALRY_FIRST_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.rivalryFirstName = iprot.readString();
              struct.setRivalryFirstNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // RIVALRY_FIRST_HEAD
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.rivalryFirstHead = iprot.readString();
              struct.setRivalryFirstHeadIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // RIVALRY_FIRST_ROLE_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.rivalryFirstRoleInfo = new com.lc.billion.icefire.protocol.structure.PsRoleInfo();
              struct.rivalryFirstRoleInfo.read(iprot);
              struct.setRivalryFirstRoleInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // MY_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.myScore = iprot.readI64();
              struct.setMyScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcGetAllianceBattleScorePush struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetMyAllianceScore()) {
        oprot.writeFieldBegin(MY_ALLIANCE_SCORE_FIELD_DESC);
        oprot.writeI64(struct.myAllianceScore);
        oprot.writeFieldEnd();
      }
      if (struct.isSetRivalryAllianceScore()) {
        oprot.writeFieldBegin(RIVALRY_ALLIANCE_SCORE_FIELD_DESC);
        oprot.writeI64(struct.rivalryAllianceScore);
        oprot.writeFieldEnd();
      }
      if (struct.isSetRoleId()) {
        oprot.writeFieldBegin(ROLE_ID_FIELD_DESC);
        oprot.writeI64(struct.roleId);
        oprot.writeFieldEnd();
      }
      if (struct.firstName != null) {
        if (struct.isSetFirstName()) {
          oprot.writeFieldBegin(FIRST_NAME_FIELD_DESC);
          oprot.writeString(struct.firstName);
          oprot.writeFieldEnd();
        }
      }
      if (struct.firstHead != null) {
        if (struct.isSetFirstHead()) {
          oprot.writeFieldBegin(FIRST_HEAD_FIELD_DESC);
          oprot.writeString(struct.firstHead);
          oprot.writeFieldEnd();
        }
      }
      if (struct.firstRoleInfo != null) {
        if (struct.isSetFirstRoleInfo()) {
          oprot.writeFieldBegin(FIRST_ROLE_INFO_FIELD_DESC);
          struct.firstRoleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetRivalryRoleId()) {
        oprot.writeFieldBegin(RIVALRY_ROLE_ID_FIELD_DESC);
        oprot.writeI64(struct.rivalryRoleId);
        oprot.writeFieldEnd();
      }
      if (struct.rivalryFirstName != null) {
        if (struct.isSetRivalryFirstName()) {
          oprot.writeFieldBegin(RIVALRY_FIRST_NAME_FIELD_DESC);
          oprot.writeString(struct.rivalryFirstName);
          oprot.writeFieldEnd();
        }
      }
      if (struct.rivalryFirstHead != null) {
        if (struct.isSetRivalryFirstHead()) {
          oprot.writeFieldBegin(RIVALRY_FIRST_HEAD_FIELD_DESC);
          oprot.writeString(struct.rivalryFirstHead);
          oprot.writeFieldEnd();
        }
      }
      if (struct.rivalryFirstRoleInfo != null) {
        if (struct.isSetRivalryFirstRoleInfo()) {
          oprot.writeFieldBegin(RIVALRY_FIRST_ROLE_INFO_FIELD_DESC);
          struct.rivalryFirstRoleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetMyScore()) {
        oprot.writeFieldBegin(MY_SCORE_FIELD_DESC);
        oprot.writeI64(struct.myScore);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcGetAllianceBattleScorePushTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcGetAllianceBattleScorePushTupleScheme getScheme() {
      return new GcGetAllianceBattleScorePushTupleScheme();
    }
  }

  private static class GcGetAllianceBattleScorePushTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcGetAllianceBattleScorePush> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcGetAllianceBattleScorePush struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetMyAllianceScore()) {
        optionals.set(0);
      }
      if (struct.isSetRivalryAllianceScore()) {
        optionals.set(1);
      }
      if (struct.isSetRoleId()) {
        optionals.set(2);
      }
      if (struct.isSetFirstName()) {
        optionals.set(3);
      }
      if (struct.isSetFirstHead()) {
        optionals.set(4);
      }
      if (struct.isSetFirstRoleInfo()) {
        optionals.set(5);
      }
      if (struct.isSetRivalryRoleId()) {
        optionals.set(6);
      }
      if (struct.isSetRivalryFirstName()) {
        optionals.set(7);
      }
      if (struct.isSetRivalryFirstHead()) {
        optionals.set(8);
      }
      if (struct.isSetRivalryFirstRoleInfo()) {
        optionals.set(9);
      }
      if (struct.isSetMyScore()) {
        optionals.set(10);
      }
      oprot.writeBitSet(optionals, 11);
      if (struct.isSetMyAllianceScore()) {
        oprot.writeI64(struct.myAllianceScore);
      }
      if (struct.isSetRivalryAllianceScore()) {
        oprot.writeI64(struct.rivalryAllianceScore);
      }
      if (struct.isSetRoleId()) {
        oprot.writeI64(struct.roleId);
      }
      if (struct.isSetFirstName()) {
        oprot.writeString(struct.firstName);
      }
      if (struct.isSetFirstHead()) {
        oprot.writeString(struct.firstHead);
      }
      if (struct.isSetFirstRoleInfo()) {
        struct.firstRoleInfo.write(oprot);
      }
      if (struct.isSetRivalryRoleId()) {
        oprot.writeI64(struct.rivalryRoleId);
      }
      if (struct.isSetRivalryFirstName()) {
        oprot.writeString(struct.rivalryFirstName);
      }
      if (struct.isSetRivalryFirstHead()) {
        oprot.writeString(struct.rivalryFirstHead);
      }
      if (struct.isSetRivalryFirstRoleInfo()) {
        struct.rivalryFirstRoleInfo.write(oprot);
      }
      if (struct.isSetMyScore()) {
        oprot.writeI64(struct.myScore);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcGetAllianceBattleScorePush struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(11);
      if (incoming.get(0)) {
        struct.myAllianceScore = iprot.readI64();
        struct.setMyAllianceScoreIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rivalryAllianceScore = iprot.readI64();
        struct.setRivalryAllianceScoreIsSet(true);
      }
      if (incoming.get(2)) {
        struct.roleId = iprot.readI64();
        struct.setRoleIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.firstName = iprot.readString();
        struct.setFirstNameIsSet(true);
      }
      if (incoming.get(4)) {
        struct.firstHead = iprot.readString();
        struct.setFirstHeadIsSet(true);
      }
      if (incoming.get(5)) {
        struct.firstRoleInfo = new com.lc.billion.icefire.protocol.structure.PsRoleInfo();
        struct.firstRoleInfo.read(iprot);
        struct.setFirstRoleInfoIsSet(true);
      }
      if (incoming.get(6)) {
        struct.rivalryRoleId = iprot.readI64();
        struct.setRivalryRoleIdIsSet(true);
      }
      if (incoming.get(7)) {
        struct.rivalryFirstName = iprot.readString();
        struct.setRivalryFirstNameIsSet(true);
      }
      if (incoming.get(8)) {
        struct.rivalryFirstHead = iprot.readString();
        struct.setRivalryFirstHeadIsSet(true);
      }
      if (incoming.get(9)) {
        struct.rivalryFirstRoleInfo = new com.lc.billion.icefire.protocol.structure.PsRoleInfo();
        struct.rivalryFirstRoleInfo.read(iprot);
        struct.setRivalryFirstRoleInfoIsSet(true);
      }
      if (incoming.get(10)) {
        struct.myScore = iprot.readI64();
        struct.setMyScoreIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

