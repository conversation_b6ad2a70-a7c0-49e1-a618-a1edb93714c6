/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 *  返回主公信息
 * @Message(4961)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcGetKingInfo implements org.apache.thrift.TBase<GcGetKingInfo, GcGetKingInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcGetKingInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcGetKingInfo");

  private static final org.apache.thrift.protocol.TField KING_FIELD_DESC = new org.apache.thrift.protocol.TField("king", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField LIKES_FIELD_DESC = new org.apache.thrift.protocol.TField("likes", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField LIKED_FIELD_DESC = new org.apache.thrift.protocol.TField("liked", org.apache.thrift.protocol.TType.BOOL, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcGetKingInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcGetKingInfoTupleSchemeFactory();

  /**
   * 主公信息
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo king; // optional
  /**
   * 大家伙给主公点的赞
   */
  public long likes; // optional
  /**
   * 你是否给主公点过赞
   */
  public boolean liked; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 主公信息
     */
    KING((short)1, "king"),
    /**
     * 大家伙给主公点的赞
     */
    LIKES((short)2, "likes"),
    /**
     * 你是否给主公点过赞
     */
    LIKED((short)3, "liked");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // KING
          return KING;
        case 2: // LIKES
          return LIKES;
        case 3: // LIKED
          return LIKED;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __LIKES_ISSET_ID = 0;
  private static final int __LIKED_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.KING,_Fields.LIKES,_Fields.LIKED};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.KING, new org.apache.thrift.meta_data.FieldMetaData("king", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo.class)));
    tmpMap.put(_Fields.LIKES, new org.apache.thrift.meta_data.FieldMetaData("likes", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.LIKED, new org.apache.thrift.meta_data.FieldMetaData("liked", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcGetKingInfo.class, metaDataMap);
  }

  public GcGetKingInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcGetKingInfo(GcGetKingInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetKing()) {
      this.king = new com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo(other.king);
    }
    this.likes = other.likes;
    this.liked = other.liked;
  }

  public GcGetKingInfo deepCopy() {
    return new GcGetKingInfo(this);
  }

  @Override
  public void clear() {
    this.king = null;
    setLikesIsSet(false);
    this.likes = 0;
    setLikedIsSet(false);
    this.liked = false;
  }

  /**
   * 主公信息
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo getKing() {
    return this.king;
  }

  /**
   * 主公信息
   */
  public GcGetKingInfo setKing(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo king) {
    this.king = king;
    return this;
  }

  public void unsetKing() {
    this.king = null;
  }

  /** Returns true if field king is set (has been assigned a value) and false otherwise */
  public boolean isSetKing() {
    return this.king != null;
  }

  public void setKingIsSet(boolean value) {
    if (!value) {
      this.king = null;
    }
  }

  /**
   * 大家伙给主公点的赞
   */
  public long getLikes() {
    return this.likes;
  }

  /**
   * 大家伙给主公点的赞
   */
  public GcGetKingInfo setLikes(long likes) {
    this.likes = likes;
    setLikesIsSet(true);
    return this;
  }

  public void unsetLikes() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LIKES_ISSET_ID);
  }

  /** Returns true if field likes is set (has been assigned a value) and false otherwise */
  public boolean isSetLikes() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LIKES_ISSET_ID);
  }

  public void setLikesIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LIKES_ISSET_ID, value);
  }

  /**
   * 你是否给主公点过赞
   */
  public boolean isLiked() {
    return this.liked;
  }

  /**
   * 你是否给主公点过赞
   */
  public GcGetKingInfo setLiked(boolean liked) {
    this.liked = liked;
    setLikedIsSet(true);
    return this;
  }

  public void unsetLiked() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LIKED_ISSET_ID);
  }

  /** Returns true if field liked is set (has been assigned a value) and false otherwise */
  public boolean isSetLiked() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LIKED_ISSET_ID);
  }

  public void setLikedIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LIKED_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case KING:
      if (value == null) {
        unsetKing();
      } else {
        setKing((com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo)value);
      }
      break;

    case LIKES:
      if (value == null) {
        unsetLikes();
      } else {
        setLikes((java.lang.Long)value);
      }
      break;

    case LIKED:
      if (value == null) {
        unsetLiked();
      } else {
        setLiked((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case KING:
      return getKing();

    case LIKES:
      return getLikes();

    case LIKED:
      return isLiked();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case KING:
      return isSetKing();
    case LIKES:
      return isSetLikes();
    case LIKED:
      return isSetLiked();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcGetKingInfo)
      return this.equals((GcGetKingInfo)that);
    return false;
  }

  public boolean equals(GcGetKingInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_king = true && this.isSetKing();
    boolean that_present_king = true && that.isSetKing();
    if (this_present_king || that_present_king) {
      if (!(this_present_king && that_present_king))
        return false;
      if (!this.king.equals(that.king))
        return false;
    }

    boolean this_present_likes = true && this.isSetLikes();
    boolean that_present_likes = true && that.isSetLikes();
    if (this_present_likes || that_present_likes) {
      if (!(this_present_likes && that_present_likes))
        return false;
      if (this.likes != that.likes)
        return false;
    }

    boolean this_present_liked = true && this.isSetLiked();
    boolean that_present_liked = true && that.isSetLiked();
    if (this_present_liked || that_present_liked) {
      if (!(this_present_liked && that_present_liked))
        return false;
      if (this.liked != that.liked)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetKing()) ? 131071 : 524287);
    if (isSetKing())
      hashCode = hashCode * 8191 + king.hashCode();

    hashCode = hashCode * 8191 + ((isSetLikes()) ? 131071 : 524287);
    if (isSetLikes())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(likes);

    hashCode = hashCode * 8191 + ((isSetLiked()) ? 131071 : 524287);
    if (isSetLiked())
      hashCode = hashCode * 8191 + ((liked) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(GcGetKingInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetKing(), other.isSetKing());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKing()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.king, other.king);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetLikes(), other.isSetLikes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLikes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.likes, other.likes);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetLiked(), other.isSetLiked());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLiked()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.liked, other.liked);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcGetKingInfo(");
    boolean first = true;

    if (isSetKing()) {
      sb.append("king:");
      if (this.king == null) {
        sb.append("null");
      } else {
        sb.append(this.king);
      }
      first = false;
    }
    if (isSetLikes()) {
      if (!first) sb.append(", ");
      sb.append("likes:");
      sb.append(this.likes);
      first = false;
    }
    if (isSetLiked()) {
      if (!first) sb.append(", ");
      sb.append("liked:");
      sb.append(this.liked);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (king != null) {
      king.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcGetKingInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcGetKingInfoStandardScheme getScheme() {
      return new GcGetKingInfoStandardScheme();
    }
  }

  private static class GcGetKingInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcGetKingInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcGetKingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // KING
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.king = new com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo();
              struct.king.read(iprot);
              struct.setKingIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // LIKES
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.likes = iprot.readI64();
              struct.setLikesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // LIKED
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.liked = iprot.readBool();
              struct.setLikedIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcGetKingInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.king != null) {
        if (struct.isSetKing()) {
          oprot.writeFieldBegin(KING_FIELD_DESC);
          struct.king.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetLikes()) {
        oprot.writeFieldBegin(LIKES_FIELD_DESC);
        oprot.writeI64(struct.likes);
        oprot.writeFieldEnd();
      }
      if (struct.isSetLiked()) {
        oprot.writeFieldBegin(LIKED_FIELD_DESC);
        oprot.writeBool(struct.liked);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcGetKingInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcGetKingInfoTupleScheme getScheme() {
      return new GcGetKingInfoTupleScheme();
    }
  }

  private static class GcGetKingInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcGetKingInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcGetKingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetKing()) {
        optionals.set(0);
      }
      if (struct.isSetLikes()) {
        optionals.set(1);
      }
      if (struct.isSetLiked()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetKing()) {
        struct.king.write(oprot);
      }
      if (struct.isSetLikes()) {
        oprot.writeI64(struct.likes);
      }
      if (struct.isSetLiked()) {
        oprot.writeBool(struct.liked);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcGetKingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.king = new com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo();
        struct.king.read(iprot);
        struct.setKingIsSet(true);
      }
      if (incoming.get(1)) {
        struct.likes = iprot.readI64();
        struct.setLikesIsSet(true);
      }
      if (incoming.get(2)) {
        struct.liked = iprot.readBool();
        struct.setLikedIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

