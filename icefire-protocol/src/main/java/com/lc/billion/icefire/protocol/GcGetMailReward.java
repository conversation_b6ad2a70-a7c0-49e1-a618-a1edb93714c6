/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 领取邮件中的奖励
 * @Message(645)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcGetMailReward implements org.apache.thrift.TBase<GcGetMailReward, GcGetMailReward._Fields>, java.io.Serializable, Cloneable, Comparable<GcGetMailReward> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcGetMailReward");

  private static final org.apache.thrift.protocol.TField CLASSIFY_FIELD_DESC = new org.apache.thrift.protocol.TField("classify", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("ids", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField REWARDS_FIELD_DESC = new org.apache.thrift.protocol.TField("rewards", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField ID_TO_REWARDS_FIELD_DESC = new org.apache.thrift.protocol.TField("idToRewards", org.apache.thrift.protocol.TType.MAP, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcGetMailRewardStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcGetMailRewardTupleSchemeFactory();

  /**
   * 客户端邮件的页签,客户端传什么，后端直接返回
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsEmailClassify
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsEmailClassify classify; // optional
  /**
   * 已领取的邮件gameServerMailId
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> ids; // optional
  /**
   * 已领取的物品
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards; // optional
  /**
   * 分id记录已领取的物品
   */
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>> idToRewards; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 客户端邮件的页签,客户端传什么，后端直接返回
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsEmailClassify
     */
    CLASSIFY((short)1, "classify"),
    /**
     * 已领取的邮件gameServerMailId
     */
    IDS((short)2, "ids"),
    /**
     * 已领取的物品
     */
    REWARDS((short)3, "rewards"),
    /**
     * 分id记录已领取的物品
     */
    ID_TO_REWARDS((short)4, "idToRewards");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CLASSIFY
          return CLASSIFY;
        case 2: // IDS
          return IDS;
        case 3: // REWARDS
          return REWARDS;
        case 4: // ID_TO_REWARDS
          return ID_TO_REWARDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.CLASSIFY,_Fields.IDS,_Fields.REWARDS,_Fields.ID_TO_REWARDS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CLASSIFY, new org.apache.thrift.meta_data.FieldMetaData("classify", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsEmailClassify.class)));
    tmpMap.put(_Fields.IDS, new org.apache.thrift.meta_data.FieldMetaData("ids", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.REWARDS, new org.apache.thrift.meta_data.FieldMetaData("rewards", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class))));
    tmpMap.put(_Fields.ID_TO_REWARDS, new org.apache.thrift.meta_data.FieldMetaData("idToRewards", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
                new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class)))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcGetMailReward.class, metaDataMap);
  }

  public GcGetMailReward() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcGetMailReward(GcGetMailReward other) {
    if (other.isSetClassify()) {
      this.classify = other.classify;
    }
    if (other.isSetIds()) {
      java.util.List<java.lang.String> __this__ids = new java.util.ArrayList<java.lang.String>(other.ids);
      this.ids = __this__ids;
    }
    if (other.isSetRewards()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other.rewards.size());
      for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element : other.rewards) {
        __this__rewards.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element));
      }
      this.rewards = __this__rewards;
    }
    if (other.isSetIdToRewards()) {
      java.util.Map<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>> __this__idToRewards = new java.util.HashMap<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>>(other.idToRewards.size());
      for (java.util.Map.Entry<java.lang.String, java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>> other_element : other.idToRewards.entrySet()) {

        java.lang.String other_element_key = other_element.getKey();
        java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> other_element_value = other_element.getValue();

        java.lang.String __this__idToRewards_copy_key = other_element_key;

        java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__idToRewards_copy_value = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other_element_value.size());
        for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element_value_element : other_element_value) {
          __this__idToRewards_copy_value.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element_value_element));
        }

        __this__idToRewards.put(__this__idToRewards_copy_key, __this__idToRewards_copy_value);
      }
      this.idToRewards = __this__idToRewards;
    }
  }

  public GcGetMailReward deepCopy() {
    return new GcGetMailReward(this);
  }

  @Override
  public void clear() {
    this.classify = null;
    this.ids = null;
    this.rewards = null;
    this.idToRewards = null;
  }

  /**
   * 客户端邮件的页签,客户端传什么，后端直接返回
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsEmailClassify
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsEmailClassify getClassify() {
    return this.classify;
  }

  /**
   * 客户端邮件的页签,客户端传什么，后端直接返回
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsEmailClassify
   */
  public GcGetMailReward setClassify(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsEmailClassify classify) {
    this.classify = classify;
    return this;
  }

  public void unsetClassify() {
    this.classify = null;
  }

  /** Returns true if field classify is set (has been assigned a value) and false otherwise */
  public boolean isSetClassify() {
    return this.classify != null;
  }

  public void setClassifyIsSet(boolean value) {
    if (!value) {
      this.classify = null;
    }
  }

  public int getIdsSize() {
    return (this.ids == null) ? 0 : this.ids.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.String> getIdsIterator() {
    return (this.ids == null) ? null : this.ids.iterator();
  }

  public void addToIds(java.lang.String elem) {
    if (this.ids == null) {
      this.ids = new java.util.ArrayList<java.lang.String>();
    }
    this.ids.add(elem);
  }

  /**
   * 已领取的邮件gameServerMailId
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.String> getIds() {
    return this.ids;
  }

  /**
   * 已领取的邮件gameServerMailId
   */
  public GcGetMailReward setIds(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> ids) {
    this.ids = ids;
    return this;
  }

  public void unsetIds() {
    this.ids = null;
  }

  /** Returns true if field ids is set (has been assigned a value) and false otherwise */
  public boolean isSetIds() {
    return this.ids != null;
  }

  public void setIdsIsSet(boolean value) {
    if (!value) {
      this.ids = null;
    }
  }

  public int getRewardsSize() {
    return (this.rewards == null) ? 0 : this.rewards.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewardsIterator() {
    return (this.rewards == null) ? null : this.rewards.iterator();
  }

  public void addToRewards(com.lc.billion.icefire.protocol.structure.PsSimpleItem elem) {
    if (this.rewards == null) {
      this.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>();
    }
    this.rewards.add(elem);
  }

  /**
   * 已领取的物品
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewards() {
    return this.rewards;
  }

  /**
   * 已领取的物品
   */
  public GcGetMailReward setRewards(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards) {
    this.rewards = rewards;
    return this;
  }

  public void unsetRewards() {
    this.rewards = null;
  }

  /** Returns true if field rewards is set (has been assigned a value) and false otherwise */
  public boolean isSetRewards() {
    return this.rewards != null;
  }

  public void setRewardsIsSet(boolean value) {
    if (!value) {
      this.rewards = null;
    }
  }

  public int getIdToRewardsSize() {
    return (this.idToRewards == null) ? 0 : this.idToRewards.size();
  }

  public void putToIdToRewards(java.lang.String key, java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> val) {
    if (this.idToRewards == null) {
      this.idToRewards = new java.util.HashMap<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>>();
    }
    this.idToRewards.put(key, val);
  }

  /**
   * 分id记录已领取的物品
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>> getIdToRewards() {
    return this.idToRewards;
  }

  /**
   * 分id记录已领取的物品
   */
  public GcGetMailReward setIdToRewards(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>> idToRewards) {
    this.idToRewards = idToRewards;
    return this;
  }

  public void unsetIdToRewards() {
    this.idToRewards = null;
  }

  /** Returns true if field idToRewards is set (has been assigned a value) and false otherwise */
  public boolean isSetIdToRewards() {
    return this.idToRewards != null;
  }

  public void setIdToRewardsIsSet(boolean value) {
    if (!value) {
      this.idToRewards = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case CLASSIFY:
      if (value == null) {
        unsetClassify();
      } else {
        setClassify((com.lc.billion.icefire.protocol.constant.PsEmailClassify)value);
      }
      break;

    case IDS:
      if (value == null) {
        unsetIds();
      } else {
        setIds((java.util.List<java.lang.String>)value);
      }
      break;

    case REWARDS:
      if (value == null) {
        unsetRewards();
      } else {
        setRewards((java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>)value);
      }
      break;

    case ID_TO_REWARDS:
      if (value == null) {
        unsetIdToRewards();
      } else {
        setIdToRewards((java.util.Map<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case CLASSIFY:
      return getClassify();

    case IDS:
      return getIds();

    case REWARDS:
      return getRewards();

    case ID_TO_REWARDS:
      return getIdToRewards();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case CLASSIFY:
      return isSetClassify();
    case IDS:
      return isSetIds();
    case REWARDS:
      return isSetRewards();
    case ID_TO_REWARDS:
      return isSetIdToRewards();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcGetMailReward)
      return this.equals((GcGetMailReward)that);
    return false;
  }

  public boolean equals(GcGetMailReward that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_classify = true && this.isSetClassify();
    boolean that_present_classify = true && that.isSetClassify();
    if (this_present_classify || that_present_classify) {
      if (!(this_present_classify && that_present_classify))
        return false;
      if (!this.classify.equals(that.classify))
        return false;
    }

    boolean this_present_ids = true && this.isSetIds();
    boolean that_present_ids = true && that.isSetIds();
    if (this_present_ids || that_present_ids) {
      if (!(this_present_ids && that_present_ids))
        return false;
      if (!this.ids.equals(that.ids))
        return false;
    }

    boolean this_present_rewards = true && this.isSetRewards();
    boolean that_present_rewards = true && that.isSetRewards();
    if (this_present_rewards || that_present_rewards) {
      if (!(this_present_rewards && that_present_rewards))
        return false;
      if (!this.rewards.equals(that.rewards))
        return false;
    }

    boolean this_present_idToRewards = true && this.isSetIdToRewards();
    boolean that_present_idToRewards = true && that.isSetIdToRewards();
    if (this_present_idToRewards || that_present_idToRewards) {
      if (!(this_present_idToRewards && that_present_idToRewards))
        return false;
      if (!this.idToRewards.equals(that.idToRewards))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetClassify()) ? 131071 : 524287);
    if (isSetClassify())
      hashCode = hashCode * 8191 + classify.getValue();

    hashCode = hashCode * 8191 + ((isSetIds()) ? 131071 : 524287);
    if (isSetIds())
      hashCode = hashCode * 8191 + ids.hashCode();

    hashCode = hashCode * 8191 + ((isSetRewards()) ? 131071 : 524287);
    if (isSetRewards())
      hashCode = hashCode * 8191 + rewards.hashCode();

    hashCode = hashCode * 8191 + ((isSetIdToRewards()) ? 131071 : 524287);
    if (isSetIdToRewards())
      hashCode = hashCode * 8191 + idToRewards.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcGetMailReward other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetClassify(), other.isSetClassify());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetClassify()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.classify, other.classify);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetIds(), other.isSetIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ids, other.ids);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRewards(), other.isSetRewards());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewards()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewards, other.rewards);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetIdToRewards(), other.isSetIdToRewards());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIdToRewards()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.idToRewards, other.idToRewards);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcGetMailReward(");
    boolean first = true;

    if (isSetClassify()) {
      sb.append("classify:");
      if (this.classify == null) {
        sb.append("null");
      } else {
        sb.append(this.classify);
      }
      first = false;
    }
    if (isSetIds()) {
      if (!first) sb.append(", ");
      sb.append("ids:");
      if (this.ids == null) {
        sb.append("null");
      } else {
        sb.append(this.ids);
      }
      first = false;
    }
    if (isSetRewards()) {
      if (!first) sb.append(", ");
      sb.append("rewards:");
      if (this.rewards == null) {
        sb.append("null");
      } else {
        sb.append(this.rewards);
      }
      first = false;
    }
    if (isSetIdToRewards()) {
      if (!first) sb.append(", ");
      sb.append("idToRewards:");
      if (this.idToRewards == null) {
        sb.append("null");
      } else {
        sb.append(this.idToRewards);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcGetMailRewardStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcGetMailRewardStandardScheme getScheme() {
      return new GcGetMailRewardStandardScheme();
    }
  }

  private static class GcGetMailRewardStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcGetMailReward> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcGetMailReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CLASSIFY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.classify = com.lc.billion.icefire.protocol.constant.PsEmailClassify.findByValue(iprot.readI32());
              struct.setClassifyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.ids = new java.util.ArrayList<java.lang.String>(_list0.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = iprot.readString();
                  struct.ids.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REWARDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list3.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                  _elem4.read(iprot);
                  struct.rewards.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setRewardsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ID_TO_REWARDS
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map6 = iprot.readMapBegin();
                struct.idToRewards = new java.util.HashMap<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>>(2*_map6.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _key7;
                @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> _val8;
                for (int _i9 = 0; _i9 < _map6.size; ++_i9)
                {
                  _key7 = iprot.readString();
                  {
                    org.apache.thrift.protocol.TList _list10 = iprot.readListBegin();
                    _val8 = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list10.size);
                    @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem11;
                    for (int _i12 = 0; _i12 < _list10.size; ++_i12)
                    {
                      _elem11 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                      _elem11.read(iprot);
                      _val8.add(_elem11);
                    }
                    iprot.readListEnd();
                  }
                  struct.idToRewards.put(_key7, _val8);
                }
                iprot.readMapEnd();
              }
              struct.setIdToRewardsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcGetMailReward struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.classify != null) {
        if (struct.isSetClassify()) {
          oprot.writeFieldBegin(CLASSIFY_FIELD_DESC);
          oprot.writeI32(struct.classify.getValue());
          oprot.writeFieldEnd();
        }
      }
      if (struct.ids != null) {
        if (struct.isSetIds()) {
          oprot.writeFieldBegin(IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.ids.size()));
            for (java.lang.String _iter13 : struct.ids)
            {
              oprot.writeString(_iter13);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.rewards != null) {
        if (struct.isSetRewards()) {
          oprot.writeFieldBegin(REWARDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.rewards.size()));
            for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter14 : struct.rewards)
            {
              _iter14.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.idToRewards != null) {
        if (struct.isSetIdToRewards()) {
          oprot.writeFieldBegin(ID_TO_REWARDS_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.LIST, struct.idToRewards.size()));
            for (java.util.Map.Entry<java.lang.String, java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>> _iter15 : struct.idToRewards.entrySet())
            {
              oprot.writeString(_iter15.getKey());
              {
                oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, _iter15.getValue().size()));
                for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter16 : _iter15.getValue())
                {
                  _iter16.write(oprot);
                }
                oprot.writeListEnd();
              }
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcGetMailRewardTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcGetMailRewardTupleScheme getScheme() {
      return new GcGetMailRewardTupleScheme();
    }
  }

  private static class GcGetMailRewardTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcGetMailReward> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcGetMailReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetClassify()) {
        optionals.set(0);
      }
      if (struct.isSetIds()) {
        optionals.set(1);
      }
      if (struct.isSetRewards()) {
        optionals.set(2);
      }
      if (struct.isSetIdToRewards()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetClassify()) {
        oprot.writeI32(struct.classify.getValue());
      }
      if (struct.isSetIds()) {
        {
          oprot.writeI32(struct.ids.size());
          for (java.lang.String _iter17 : struct.ids)
          {
            oprot.writeString(_iter17);
          }
        }
      }
      if (struct.isSetRewards()) {
        {
          oprot.writeI32(struct.rewards.size());
          for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter18 : struct.rewards)
          {
            _iter18.write(oprot);
          }
        }
      }
      if (struct.isSetIdToRewards()) {
        {
          oprot.writeI32(struct.idToRewards.size());
          for (java.util.Map.Entry<java.lang.String, java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>> _iter19 : struct.idToRewards.entrySet())
          {
            oprot.writeString(_iter19.getKey());
            {
              oprot.writeI32(_iter19.getValue().size());
              for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter20 : _iter19.getValue())
              {
                _iter20.write(oprot);
              }
            }
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcGetMailReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.classify = com.lc.billion.icefire.protocol.constant.PsEmailClassify.findByValue(iprot.readI32());
        struct.setClassifyIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list21 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRING);
          struct.ids = new java.util.ArrayList<java.lang.String>(_list21.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _elem22;
          for (int _i23 = 0; _i23 < _list21.size; ++_i23)
          {
            _elem22 = iprot.readString();
            struct.ids.add(_elem22);
          }
        }
        struct.setIdsIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list24 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list24.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem25;
          for (int _i26 = 0; _i26 < _list24.size; ++_i26)
          {
            _elem25 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
            _elem25.read(iprot);
            struct.rewards.add(_elem25);
          }
        }
        struct.setRewardsIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TMap _map27 = iprot.readMapBegin(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.LIST); 
          struct.idToRewards = new java.util.HashMap<java.lang.String,java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>>(2*_map27.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _key28;
          @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> _val29;
          for (int _i30 = 0; _i30 < _map27.size; ++_i30)
          {
            _key28 = iprot.readString();
            {
              org.apache.thrift.protocol.TList _list31 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
              _val29 = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list31.size);
              @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem32;
              for (int _i33 = 0; _i33 < _list31.size; ++_i33)
              {
                _elem32 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                _elem32.read(iprot);
                _val29.add(_elem32);
              }
            }
            struct.idToRewards.put(_key28, _val29);
          }
        }
        struct.setIdToRewardsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

