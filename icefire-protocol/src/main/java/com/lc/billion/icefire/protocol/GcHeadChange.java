/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 头像 更换头像(系统头像)
 * @Message(1407)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcHeadChange implements org.apache.thrift.TBase<GcHeadChange, GcHeadChange._Fields>, java.io.Serializable, Cloneable, Comparable<GcHeadChange> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcHeadChange");

  private static final org.apache.thrift.protocol.TField NEW_HEAD_FIELD_DESC = new org.apache.thrift.protocol.TField("newHead", org.apache.thrift.protocol.TType.STRING, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcHeadChangeStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcHeadChangeTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.lang.String newHead; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    NEW_HEAD((short)1, "newHead");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // NEW_HEAD
          return NEW_HEAD;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.NEW_HEAD, new org.apache.thrift.meta_data.FieldMetaData("newHead", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcHeadChange.class, metaDataMap);
  }

  public GcHeadChange() {
  }

  public GcHeadChange(
    java.lang.String newHead)
  {
    this();
    this.newHead = newHead;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcHeadChange(GcHeadChange other) {
    if (other.isSetNewHead()) {
      this.newHead = other.newHead;
    }
  }

  public GcHeadChange deepCopy() {
    return new GcHeadChange(this);
  }

  @Override
  public void clear() {
    this.newHead = null;
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getNewHead() {
    return this.newHead;
  }

  public GcHeadChange setNewHead(@org.apache.thrift.annotation.Nullable java.lang.String newHead) {
    this.newHead = newHead;
    return this;
  }

  public void unsetNewHead() {
    this.newHead = null;
  }

  /** Returns true if field newHead is set (has been assigned a value) and false otherwise */
  public boolean isSetNewHead() {
    return this.newHead != null;
  }

  public void setNewHeadIsSet(boolean value) {
    if (!value) {
      this.newHead = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case NEW_HEAD:
      if (value == null) {
        unsetNewHead();
      } else {
        setNewHead((java.lang.String)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case NEW_HEAD:
      return getNewHead();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case NEW_HEAD:
      return isSetNewHead();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcHeadChange)
      return this.equals((GcHeadChange)that);
    return false;
  }

  public boolean equals(GcHeadChange that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_newHead = true && this.isSetNewHead();
    boolean that_present_newHead = true && that.isSetNewHead();
    if (this_present_newHead || that_present_newHead) {
      if (!(this_present_newHead && that_present_newHead))
        return false;
      if (!this.newHead.equals(that.newHead))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetNewHead()) ? 131071 : 524287);
    if (isSetNewHead())
      hashCode = hashCode * 8191 + newHead.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcHeadChange other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetNewHead(), other.isSetNewHead());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNewHead()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.newHead, other.newHead);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcHeadChange(");
    boolean first = true;

    sb.append("newHead:");
    if (this.newHead == null) {
      sb.append("null");
    } else {
      sb.append(this.newHead);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (newHead == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'newHead' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcHeadChangeStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcHeadChangeStandardScheme getScheme() {
      return new GcHeadChangeStandardScheme();
    }
  }

  private static class GcHeadChangeStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcHeadChange> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcHeadChange struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // NEW_HEAD
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.newHead = iprot.readString();
              struct.setNewHeadIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcHeadChange struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.newHead != null) {
        oprot.writeFieldBegin(NEW_HEAD_FIELD_DESC);
        oprot.writeString(struct.newHead);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcHeadChangeTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcHeadChangeTupleScheme getScheme() {
      return new GcHeadChangeTupleScheme();
    }
  }

  private static class GcHeadChangeTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcHeadChange> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcHeadChange struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.newHead);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcHeadChange struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.newHead = iprot.readString();
      struct.setNewHeadIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

