/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 头像 请求上传头像Token
 * @Message(1401)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcHeadUploadToken implements org.apache.thrift.TBase<GcHeadUploadToken, GcHeadUploadToken._Fields>, java.io.Serializable, Cloneable, Comparable<GcHeadUploadToken> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcHeadUploadToken");

  private static final org.apache.thrift.protocol.TField TOKEN_FIELD_DESC = new org.apache.thrift.protocol.TField("token", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField ERROR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("errorCode", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField BAN_END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("banEndTime", org.apache.thrift.protocol.TType.I64, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcHeadUploadTokenStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcHeadUploadTokenTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsHeadTokenInfo token; // optional
  /**
   * 0: 成功
   * 1: 上传成功24小时内不能上传
   * 2: 被举报生效后24内不能上传
   * 3: 上传头像尚未解锁
   * 4: 上传条件不满足
   * 5: 被GM封禁
   */
  public int errorCode; // required
  /**
   * GM封禁，举报结束时间戳
   */
  public long banEndTime; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    TOKEN((short)1, "token"),
    /**
     * 0: 成功
     * 1: 上传成功24小时内不能上传
     * 2: 被举报生效后24内不能上传
     * 3: 上传头像尚未解锁
     * 4: 上传条件不满足
     * 5: 被GM封禁
     */
    ERROR_CODE((short)2, "errorCode"),
    /**
     * GM封禁，举报结束时间戳
     */
    BAN_END_TIME((short)3, "banEndTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TOKEN
          return TOKEN;
        case 2: // ERROR_CODE
          return ERROR_CODE;
        case 3: // BAN_END_TIME
          return BAN_END_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ERRORCODE_ISSET_ID = 0;
  private static final int __BANENDTIME_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.TOKEN,_Fields.BAN_END_TIME};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TOKEN, new org.apache.thrift.meta_data.FieldMetaData("token", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsHeadTokenInfo.class)));
    tmpMap.put(_Fields.ERROR_CODE, new org.apache.thrift.meta_data.FieldMetaData("errorCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.BAN_END_TIME, new org.apache.thrift.meta_data.FieldMetaData("banEndTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcHeadUploadToken.class, metaDataMap);
  }

  public GcHeadUploadToken() {
  }

  public GcHeadUploadToken(
    int errorCode)
  {
    this();
    this.errorCode = errorCode;
    setErrorCodeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcHeadUploadToken(GcHeadUploadToken other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetToken()) {
      this.token = new com.lc.billion.icefire.protocol.structure.PsHeadTokenInfo(other.token);
    }
    this.errorCode = other.errorCode;
    this.banEndTime = other.banEndTime;
  }

  public GcHeadUploadToken deepCopy() {
    return new GcHeadUploadToken(this);
  }

  @Override
  public void clear() {
    this.token = null;
    setErrorCodeIsSet(false);
    this.errorCode = 0;
    setBanEndTimeIsSet(false);
    this.banEndTime = 0;
  }

  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.structure.PsHeadTokenInfo getToken() {
    return this.token;
  }

  public GcHeadUploadToken setToken(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsHeadTokenInfo token) {
    this.token = token;
    return this;
  }

  public void unsetToken() {
    this.token = null;
  }

  /** Returns true if field token is set (has been assigned a value) and false otherwise */
  public boolean isSetToken() {
    return this.token != null;
  }

  public void setTokenIsSet(boolean value) {
    if (!value) {
      this.token = null;
    }
  }

  /**
   * 0: 成功
   * 1: 上传成功24小时内不能上传
   * 2: 被举报生效后24内不能上传
   * 3: 上传头像尚未解锁
   * 4: 上传条件不满足
   * 5: 被GM封禁
   */
  public int getErrorCode() {
    return this.errorCode;
  }

  /**
   * 0: 成功
   * 1: 上传成功24小时内不能上传
   * 2: 被举报生效后24内不能上传
   * 3: 上传头像尚未解锁
   * 4: 上传条件不满足
   * 5: 被GM封禁
   */
  public GcHeadUploadToken setErrorCode(int errorCode) {
    this.errorCode = errorCode;
    setErrorCodeIsSet(true);
    return this;
  }

  public void unsetErrorCode() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ERRORCODE_ISSET_ID);
  }

  /** Returns true if field errorCode is set (has been assigned a value) and false otherwise */
  public boolean isSetErrorCode() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ERRORCODE_ISSET_ID);
  }

  public void setErrorCodeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ERRORCODE_ISSET_ID, value);
  }

  /**
   * GM封禁，举报结束时间戳
   */
  public long getBanEndTime() {
    return this.banEndTime;
  }

  /**
   * GM封禁，举报结束时间戳
   */
  public GcHeadUploadToken setBanEndTime(long banEndTime) {
    this.banEndTime = banEndTime;
    setBanEndTimeIsSet(true);
    return this;
  }

  public void unsetBanEndTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BANENDTIME_ISSET_ID);
  }

  /** Returns true if field banEndTime is set (has been assigned a value) and false otherwise */
  public boolean isSetBanEndTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BANENDTIME_ISSET_ID);
  }

  public void setBanEndTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BANENDTIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case TOKEN:
      if (value == null) {
        unsetToken();
      } else {
        setToken((com.lc.billion.icefire.protocol.structure.PsHeadTokenInfo)value);
      }
      break;

    case ERROR_CODE:
      if (value == null) {
        unsetErrorCode();
      } else {
        setErrorCode((java.lang.Integer)value);
      }
      break;

    case BAN_END_TIME:
      if (value == null) {
        unsetBanEndTime();
      } else {
        setBanEndTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case TOKEN:
      return getToken();

    case ERROR_CODE:
      return getErrorCode();

    case BAN_END_TIME:
      return getBanEndTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case TOKEN:
      return isSetToken();
    case ERROR_CODE:
      return isSetErrorCode();
    case BAN_END_TIME:
      return isSetBanEndTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcHeadUploadToken)
      return this.equals((GcHeadUploadToken)that);
    return false;
  }

  public boolean equals(GcHeadUploadToken that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_token = true && this.isSetToken();
    boolean that_present_token = true && that.isSetToken();
    if (this_present_token || that_present_token) {
      if (!(this_present_token && that_present_token))
        return false;
      if (!this.token.equals(that.token))
        return false;
    }

    boolean this_present_errorCode = true;
    boolean that_present_errorCode = true;
    if (this_present_errorCode || that_present_errorCode) {
      if (!(this_present_errorCode && that_present_errorCode))
        return false;
      if (this.errorCode != that.errorCode)
        return false;
    }

    boolean this_present_banEndTime = true && this.isSetBanEndTime();
    boolean that_present_banEndTime = true && that.isSetBanEndTime();
    if (this_present_banEndTime || that_present_banEndTime) {
      if (!(this_present_banEndTime && that_present_banEndTime))
        return false;
      if (this.banEndTime != that.banEndTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetToken()) ? 131071 : 524287);
    if (isSetToken())
      hashCode = hashCode * 8191 + token.hashCode();

    hashCode = hashCode * 8191 + errorCode;

    hashCode = hashCode * 8191 + ((isSetBanEndTime()) ? 131071 : 524287);
    if (isSetBanEndTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(banEndTime);

    return hashCode;
  }

  @Override
  public int compareTo(GcHeadUploadToken other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetToken(), other.isSetToken());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetToken()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.token, other.token);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetErrorCode(), other.isSetErrorCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetErrorCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.errorCode, other.errorCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBanEndTime(), other.isSetBanEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBanEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.banEndTime, other.banEndTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcHeadUploadToken(");
    boolean first = true;

    if (isSetToken()) {
      sb.append("token:");
      if (this.token == null) {
        sb.append("null");
      } else {
        sb.append(this.token);
      }
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("errorCode:");
    sb.append(this.errorCode);
    first = false;
    if (isSetBanEndTime()) {
      if (!first) sb.append(", ");
      sb.append("banEndTime:");
      sb.append(this.banEndTime);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'errorCode' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
    if (token != null) {
      token.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcHeadUploadTokenStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcHeadUploadTokenStandardScheme getScheme() {
      return new GcHeadUploadTokenStandardScheme();
    }
  }

  private static class GcHeadUploadTokenStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcHeadUploadToken> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcHeadUploadToken struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TOKEN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.token = new com.lc.billion.icefire.protocol.structure.PsHeadTokenInfo();
              struct.token.read(iprot);
              struct.setTokenIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ERROR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.errorCode = iprot.readI32();
              struct.setErrorCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BAN_END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.banEndTime = iprot.readI64();
              struct.setBanEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetErrorCode()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'errorCode' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcHeadUploadToken struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.token != null) {
        if (struct.isSetToken()) {
          oprot.writeFieldBegin(TOKEN_FIELD_DESC);
          struct.token.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldBegin(ERROR_CODE_FIELD_DESC);
      oprot.writeI32(struct.errorCode);
      oprot.writeFieldEnd();
      if (struct.isSetBanEndTime()) {
        oprot.writeFieldBegin(BAN_END_TIME_FIELD_DESC);
        oprot.writeI64(struct.banEndTime);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcHeadUploadTokenTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcHeadUploadTokenTupleScheme getScheme() {
      return new GcHeadUploadTokenTupleScheme();
    }
  }

  private static class GcHeadUploadTokenTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcHeadUploadToken> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcHeadUploadToken struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.errorCode);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetToken()) {
        optionals.set(0);
      }
      if (struct.isSetBanEndTime()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetToken()) {
        struct.token.write(oprot);
      }
      if (struct.isSetBanEndTime()) {
        oprot.writeI64(struct.banEndTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcHeadUploadToken struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.errorCode = iprot.readI32();
      struct.setErrorCodeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.token = new com.lc.billion.icefire.protocol.structure.PsHeadTokenInfo();
        struct.token.read(iprot);
        struct.setTokenIsSet(true);
      }
      if (incoming.get(1)) {
        struct.banEndTime = iprot.readI64();
        struct.setBanEndTimeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

