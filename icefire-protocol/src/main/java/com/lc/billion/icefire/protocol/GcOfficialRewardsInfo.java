/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 论功行赏信息
 * @Message(4767)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcOfficialRewardsInfo implements org.apache.thrift.TBase<GcOfficialRewardsInfo, GcOfficialRewardsInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcOfficialRewardsInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcOfficialRewardsInfo");

  private static final org.apache.thrift.protocol.TField REWARDS_REMAIN_TIMES_FIELD_DESC = new org.apache.thrift.protocol.TField("rewardsRemainTimes", org.apache.thrift.protocol.TType.MAP, (short)1);
  private static final org.apache.thrift.protocol.TField SEASON_FIELD_DESC = new org.apache.thrift.protocol.TField("season", org.apache.thrift.protocol.TType.I32, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcOfficialRewardsInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcOfficialRewardsInfoTupleSchemeFactory();

  /**
   * 奖励对应的剩余领取次数*
   */
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.Integer> rewardsRemainTimes; // optional
  /**
   * 是否是k服*
   */
  public int season; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 奖励对应的剩余领取次数*
     */
    REWARDS_REMAIN_TIMES((short)1, "rewardsRemainTimes"),
    /**
     * 是否是k服*
     */
    SEASON((short)2, "season");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // REWARDS_REMAIN_TIMES
          return REWARDS_REMAIN_TIMES;
        case 2: // SEASON
          return SEASON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SEASON_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.REWARDS_REMAIN_TIMES,_Fields.SEASON};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.REWARDS_REMAIN_TIMES, new org.apache.thrift.meta_data.FieldMetaData("rewardsRemainTimes", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    tmpMap.put(_Fields.SEASON, new org.apache.thrift.meta_data.FieldMetaData("season", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcOfficialRewardsInfo.class, metaDataMap);
  }

  public GcOfficialRewardsInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcOfficialRewardsInfo(GcOfficialRewardsInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetRewardsRemainTimes()) {
      java.util.Map<java.lang.String,java.lang.Integer> __this__rewardsRemainTimes = new java.util.HashMap<java.lang.String,java.lang.Integer>(other.rewardsRemainTimes);
      this.rewardsRemainTimes = __this__rewardsRemainTimes;
    }
    this.season = other.season;
  }

  public GcOfficialRewardsInfo deepCopy() {
    return new GcOfficialRewardsInfo(this);
  }

  @Override
  public void clear() {
    this.rewardsRemainTimes = null;
    setSeasonIsSet(false);
    this.season = 0;
  }

  public int getRewardsRemainTimesSize() {
    return (this.rewardsRemainTimes == null) ? 0 : this.rewardsRemainTimes.size();
  }

  public void putToRewardsRemainTimes(java.lang.String key, int val) {
    if (this.rewardsRemainTimes == null) {
      this.rewardsRemainTimes = new java.util.HashMap<java.lang.String,java.lang.Integer>();
    }
    this.rewardsRemainTimes.put(key, val);
  }

  /**
   * 奖励对应的剩余领取次数*
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.String,java.lang.Integer> getRewardsRemainTimes() {
    return this.rewardsRemainTimes;
  }

  /**
   * 奖励对应的剩余领取次数*
   */
  public GcOfficialRewardsInfo setRewardsRemainTimes(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.Integer> rewardsRemainTimes) {
    this.rewardsRemainTimes = rewardsRemainTimes;
    return this;
  }

  public void unsetRewardsRemainTimes() {
    this.rewardsRemainTimes = null;
  }

  /** Returns true if field rewardsRemainTimes is set (has been assigned a value) and false otherwise */
  public boolean isSetRewardsRemainTimes() {
    return this.rewardsRemainTimes != null;
  }

  public void setRewardsRemainTimesIsSet(boolean value) {
    if (!value) {
      this.rewardsRemainTimes = null;
    }
  }

  /**
   * 是否是k服*
   */
  public int getSeason() {
    return this.season;
  }

  /**
   * 是否是k服*
   */
  public GcOfficialRewardsInfo setSeason(int season) {
    this.season = season;
    setSeasonIsSet(true);
    return this;
  }

  public void unsetSeason() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SEASON_ISSET_ID);
  }

  /** Returns true if field season is set (has been assigned a value) and false otherwise */
  public boolean isSetSeason() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SEASON_ISSET_ID);
  }

  public void setSeasonIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SEASON_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case REWARDS_REMAIN_TIMES:
      if (value == null) {
        unsetRewardsRemainTimes();
      } else {
        setRewardsRemainTimes((java.util.Map<java.lang.String,java.lang.Integer>)value);
      }
      break;

    case SEASON:
      if (value == null) {
        unsetSeason();
      } else {
        setSeason((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case REWARDS_REMAIN_TIMES:
      return getRewardsRemainTimes();

    case SEASON:
      return getSeason();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case REWARDS_REMAIN_TIMES:
      return isSetRewardsRemainTimes();
    case SEASON:
      return isSetSeason();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcOfficialRewardsInfo)
      return this.equals((GcOfficialRewardsInfo)that);
    return false;
  }

  public boolean equals(GcOfficialRewardsInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_rewardsRemainTimes = true && this.isSetRewardsRemainTimes();
    boolean that_present_rewardsRemainTimes = true && that.isSetRewardsRemainTimes();
    if (this_present_rewardsRemainTimes || that_present_rewardsRemainTimes) {
      if (!(this_present_rewardsRemainTimes && that_present_rewardsRemainTimes))
        return false;
      if (!this.rewardsRemainTimes.equals(that.rewardsRemainTimes))
        return false;
    }

    boolean this_present_season = true && this.isSetSeason();
    boolean that_present_season = true && that.isSetSeason();
    if (this_present_season || that_present_season) {
      if (!(this_present_season && that_present_season))
        return false;
      if (this.season != that.season)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetRewardsRemainTimes()) ? 131071 : 524287);
    if (isSetRewardsRemainTimes())
      hashCode = hashCode * 8191 + rewardsRemainTimes.hashCode();

    hashCode = hashCode * 8191 + ((isSetSeason()) ? 131071 : 524287);
    if (isSetSeason())
      hashCode = hashCode * 8191 + season;

    return hashCode;
  }

  @Override
  public int compareTo(GcOfficialRewardsInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRewardsRemainTimes(), other.isSetRewardsRemainTimes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewardsRemainTimes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewardsRemainTimes, other.rewardsRemainTimes);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSeason(), other.isSetSeason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.season, other.season);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcOfficialRewardsInfo(");
    boolean first = true;

    if (isSetRewardsRemainTimes()) {
      sb.append("rewardsRemainTimes:");
      if (this.rewardsRemainTimes == null) {
        sb.append("null");
      } else {
        sb.append(this.rewardsRemainTimes);
      }
      first = false;
    }
    if (isSetSeason()) {
      if (!first) sb.append(", ");
      sb.append("season:");
      sb.append(this.season);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcOfficialRewardsInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcOfficialRewardsInfoStandardScheme getScheme() {
      return new GcOfficialRewardsInfoStandardScheme();
    }
  }

  private static class GcOfficialRewardsInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcOfficialRewardsInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcOfficialRewardsInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // REWARDS_REMAIN_TIMES
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map0 = iprot.readMapBegin();
                struct.rewardsRemainTimes = new java.util.HashMap<java.lang.String,java.lang.Integer>(2*_map0.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _key1;
                int _val2;
                for (int _i3 = 0; _i3 < _map0.size; ++_i3)
                {
                  _key1 = iprot.readString();
                  _val2 = iprot.readI32();
                  struct.rewardsRemainTimes.put(_key1, _val2);
                }
                iprot.readMapEnd();
              }
              struct.setRewardsRemainTimesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SEASON
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.season = iprot.readI32();
              struct.setSeasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcOfficialRewardsInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.rewardsRemainTimes != null) {
        if (struct.isSetRewardsRemainTimes()) {
          oprot.writeFieldBegin(REWARDS_REMAIN_TIMES_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32, struct.rewardsRemainTimes.size()));
            for (java.util.Map.Entry<java.lang.String, java.lang.Integer> _iter4 : struct.rewardsRemainTimes.entrySet())
            {
              oprot.writeString(_iter4.getKey());
              oprot.writeI32(_iter4.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetSeason()) {
        oprot.writeFieldBegin(SEASON_FIELD_DESC);
        oprot.writeI32(struct.season);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcOfficialRewardsInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcOfficialRewardsInfoTupleScheme getScheme() {
      return new GcOfficialRewardsInfoTupleScheme();
    }
  }

  private static class GcOfficialRewardsInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcOfficialRewardsInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcOfficialRewardsInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRewardsRemainTimes()) {
        optionals.set(0);
      }
      if (struct.isSetSeason()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRewardsRemainTimes()) {
        {
          oprot.writeI32(struct.rewardsRemainTimes.size());
          for (java.util.Map.Entry<java.lang.String, java.lang.Integer> _iter5 : struct.rewardsRemainTimes.entrySet())
          {
            oprot.writeString(_iter5.getKey());
            oprot.writeI32(_iter5.getValue());
          }
        }
      }
      if (struct.isSetSeason()) {
        oprot.writeI32(struct.season);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcOfficialRewardsInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map6 = iprot.readMapBegin(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32); 
          struct.rewardsRemainTimes = new java.util.HashMap<java.lang.String,java.lang.Integer>(2*_map6.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _key7;
          int _val8;
          for (int _i9 = 0; _i9 < _map6.size; ++_i9)
          {
            _key7 = iprot.readString();
            _val8 = iprot.readI32();
            struct.rewardsRemainTimes.put(_key7, _val8);
          }
        }
        struct.setRewardsRemainTimesIsSet(true);
      }
      if (incoming.get(1)) {
        struct.season = iprot.readI32();
        struct.setSeasonIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

