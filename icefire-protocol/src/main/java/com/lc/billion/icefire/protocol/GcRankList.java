/**
 * Autogenerated by <PERSON><PERSON><PERSON>mpiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 获取排行榜
 * @Message(1752)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcRankList implements org.apache.thrift.TBase<GcRankList, GcRankList._Fields>, java.io.Serializable, Cloneable, Comparable<GcRankList> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcRankList");

  private static final org.apache.thrift.protocol.TField TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("type", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField MEMBERS_FIELD_DESC = new org.apache.thrift.protocol.TField("members", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField OWNER_RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("ownerRank", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField OWNER_VALUE_FIELD_DESC = new org.apache.thrift.protocol.TField("ownerValue", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField OWNER_PS_RANK_VARIATION_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("ownerPsRankVariationType", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField PAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("page", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField HERO_SIMPLE_INFOS_FIELD_DESC = new org.apache.thrift.protocol.TField("heroSimpleInfos", org.apache.thrift.protocol.TType.LIST, (short)8);
  private static final org.apache.thrift.protocol.TField MAX_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("maxCount", org.apache.thrift.protocol.TType.I32, (short)9);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.STRING, (short)10);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcRankListStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcRankListTupleSchemeFactory();

  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRankType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsRankType type; // required
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsRankMember> members; // optional
  /**
   * 我的排名
   */
  public int ownerRank; // optional
  public long ownerValue; // optional
  /**
   * 排名变化
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRankVariationType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsRankVariationType ownerPsRankVariationType; // optional
  /**
   * 页号
   */
  public int page; // optional
  /**
   * 其他参数内容
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String params; // optional
  /**
   * 多英雄信息列表
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo> heroSimpleInfos; // optional
  public int maxCount; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String ext; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsRankType
     */
    TYPE((short)1, "type"),
    MEMBERS((short)2, "members"),
    /**
     * 我的排名
     */
    OWNER_RANK((short)3, "ownerRank"),
    OWNER_VALUE((short)4, "ownerValue"),
    /**
     * 排名变化
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsRankVariationType
     */
    OWNER_PS_RANK_VARIATION_TYPE((short)5, "ownerPsRankVariationType"),
    /**
     * 页号
     */
    PAGE((short)6, "page"),
    /**
     * 其他参数内容
     */
    PARAMS((short)7, "params"),
    /**
     * 多英雄信息列表
     */
    HERO_SIMPLE_INFOS((short)8, "heroSimpleInfos"),
    MAX_COUNT((short)9, "maxCount"),
    EXT((short)10, "ext");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TYPE
          return TYPE;
        case 2: // MEMBERS
          return MEMBERS;
        case 3: // OWNER_RANK
          return OWNER_RANK;
        case 4: // OWNER_VALUE
          return OWNER_VALUE;
        case 5: // OWNER_PS_RANK_VARIATION_TYPE
          return OWNER_PS_RANK_VARIATION_TYPE;
        case 6: // PAGE
          return PAGE;
        case 7: // PARAMS
          return PARAMS;
        case 8: // HERO_SIMPLE_INFOS
          return HERO_SIMPLE_INFOS;
        case 9: // MAX_COUNT
          return MAX_COUNT;
        case 10: // EXT
          return EXT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __OWNERRANK_ISSET_ID = 0;
  private static final int __OWNERVALUE_ISSET_ID = 1;
  private static final int __PAGE_ISSET_ID = 2;
  private static final int __MAXCOUNT_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.MEMBERS,_Fields.OWNER_RANK,_Fields.OWNER_VALUE,_Fields.OWNER_PS_RANK_VARIATION_TYPE,_Fields.PAGE,_Fields.PARAMS,_Fields.HERO_SIMPLE_INFOS,_Fields.MAX_COUNT,_Fields.EXT};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TYPE, new org.apache.thrift.meta_data.FieldMetaData("type", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsRankType.class)));
    tmpMap.put(_Fields.MEMBERS, new org.apache.thrift.meta_data.FieldMetaData("members", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsRankMember.class))));
    tmpMap.put(_Fields.OWNER_RANK, new org.apache.thrift.meta_data.FieldMetaData("ownerRank", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.OWNER_VALUE, new org.apache.thrift.meta_data.FieldMetaData("ownerValue", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.OWNER_PS_RANK_VARIATION_TYPE, new org.apache.thrift.meta_data.FieldMetaData("ownerPsRankVariationType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsRankVariationType.class)));
    tmpMap.put(_Fields.PAGE, new org.apache.thrift.meta_data.FieldMetaData("page", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.HERO_SIMPLE_INFOS, new org.apache.thrift.meta_data.FieldMetaData("heroSimpleInfos", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo.class))));
    tmpMap.put(_Fields.MAX_COUNT, new org.apache.thrift.meta_data.FieldMetaData("maxCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcRankList.class, metaDataMap);
  }

  public GcRankList() {
  }

  public GcRankList(
    com.lc.billion.icefire.protocol.constant.PsRankType type)
  {
    this();
    this.type = type;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcRankList(GcRankList other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetType()) {
      this.type = other.type;
    }
    if (other.isSetMembers()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsRankMember> __this__members = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRankMember>(other.members.size());
      for (com.lc.billion.icefire.protocol.structure.PsRankMember other_element : other.members) {
        __this__members.add(new com.lc.billion.icefire.protocol.structure.PsRankMember(other_element));
      }
      this.members = __this__members;
    }
    this.ownerRank = other.ownerRank;
    this.ownerValue = other.ownerValue;
    if (other.isSetOwnerPsRankVariationType()) {
      this.ownerPsRankVariationType = other.ownerPsRankVariationType;
    }
    this.page = other.page;
    if (other.isSetParams()) {
      this.params = other.params;
    }
    if (other.isSetHeroSimpleInfos()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo> __this__heroSimpleInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo>(other.heroSimpleInfos.size());
      for (com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo other_element : other.heroSimpleInfos) {
        __this__heroSimpleInfos.add(new com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo(other_element));
      }
      this.heroSimpleInfos = __this__heroSimpleInfos;
    }
    this.maxCount = other.maxCount;
    if (other.isSetExt()) {
      this.ext = other.ext;
    }
  }

  public GcRankList deepCopy() {
    return new GcRankList(this);
  }

  @Override
  public void clear() {
    this.type = null;
    this.members = null;
    setOwnerRankIsSet(false);
    this.ownerRank = 0;
    setOwnerValueIsSet(false);
    this.ownerValue = 0;
    this.ownerPsRankVariationType = null;
    setPageIsSet(false);
    this.page = 0;
    this.params = null;
    this.heroSimpleInfos = null;
    setMaxCountIsSet(false);
    this.maxCount = 0;
    this.ext = null;
  }

  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRankType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsRankType getType() {
    return this.type;
  }

  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRankType
   */
  public GcRankList setType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsRankType type) {
    this.type = type;
    return this;
  }

  public void unsetType() {
    this.type = null;
  }

  /** Returns true if field type is set (has been assigned a value) and false otherwise */
  public boolean isSetType() {
    return this.type != null;
  }

  public void setTypeIsSet(boolean value) {
    if (!value) {
      this.type = null;
    }
  }

  public int getMembersSize() {
    return (this.members == null) ? 0 : this.members.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsRankMember> getMembersIterator() {
    return (this.members == null) ? null : this.members.iterator();
  }

  public void addToMembers(com.lc.billion.icefire.protocol.structure.PsRankMember elem) {
    if (this.members == null) {
      this.members = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRankMember>();
    }
    this.members.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsRankMember> getMembers() {
    return this.members;
  }

  public GcRankList setMembers(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsRankMember> members) {
    this.members = members;
    return this;
  }

  public void unsetMembers() {
    this.members = null;
  }

  /** Returns true if field members is set (has been assigned a value) and false otherwise */
  public boolean isSetMembers() {
    return this.members != null;
  }

  public void setMembersIsSet(boolean value) {
    if (!value) {
      this.members = null;
    }
  }

  /**
   * 我的排名
   */
  public int getOwnerRank() {
    return this.ownerRank;
  }

  /**
   * 我的排名
   */
  public GcRankList setOwnerRank(int ownerRank) {
    this.ownerRank = ownerRank;
    setOwnerRankIsSet(true);
    return this;
  }

  public void unsetOwnerRank() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __OWNERRANK_ISSET_ID);
  }

  /** Returns true if field ownerRank is set (has been assigned a value) and false otherwise */
  public boolean isSetOwnerRank() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __OWNERRANK_ISSET_ID);
  }

  public void setOwnerRankIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __OWNERRANK_ISSET_ID, value);
  }

  public long getOwnerValue() {
    return this.ownerValue;
  }

  public GcRankList setOwnerValue(long ownerValue) {
    this.ownerValue = ownerValue;
    setOwnerValueIsSet(true);
    return this;
  }

  public void unsetOwnerValue() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __OWNERVALUE_ISSET_ID);
  }

  /** Returns true if field ownerValue is set (has been assigned a value) and false otherwise */
  public boolean isSetOwnerValue() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __OWNERVALUE_ISSET_ID);
  }

  public void setOwnerValueIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __OWNERVALUE_ISSET_ID, value);
  }

  /**
   * 排名变化
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRankVariationType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsRankVariationType getOwnerPsRankVariationType() {
    return this.ownerPsRankVariationType;
  }

  /**
   * 排名变化
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsRankVariationType
   */
  public GcRankList setOwnerPsRankVariationType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsRankVariationType ownerPsRankVariationType) {
    this.ownerPsRankVariationType = ownerPsRankVariationType;
    return this;
  }

  public void unsetOwnerPsRankVariationType() {
    this.ownerPsRankVariationType = null;
  }

  /** Returns true if field ownerPsRankVariationType is set (has been assigned a value) and false otherwise */
  public boolean isSetOwnerPsRankVariationType() {
    return this.ownerPsRankVariationType != null;
  }

  public void setOwnerPsRankVariationTypeIsSet(boolean value) {
    if (!value) {
      this.ownerPsRankVariationType = null;
    }
  }

  /**
   * 页号
   */
  public int getPage() {
    return this.page;
  }

  /**
   * 页号
   */
  public GcRankList setPage(int page) {
    this.page = page;
    setPageIsSet(true);
    return this;
  }

  public void unsetPage() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PAGE_ISSET_ID);
  }

  /** Returns true if field page is set (has been assigned a value) and false otherwise */
  public boolean isSetPage() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PAGE_ISSET_ID);
  }

  public void setPageIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PAGE_ISSET_ID, value);
  }

  /**
   * 其他参数内容
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getParams() {
    return this.params;
  }

  /**
   * 其他参数内容
   */
  public GcRankList setParams(@org.apache.thrift.annotation.Nullable java.lang.String params) {
    this.params = params;
    return this;
  }

  public void unsetParams() {
    this.params = null;
  }

  /** Returns true if field params is set (has been assigned a value) and false otherwise */
  public boolean isSetParams() {
    return this.params != null;
  }

  public void setParamsIsSet(boolean value) {
    if (!value) {
      this.params = null;
    }
  }

  public int getHeroSimpleInfosSize() {
    return (this.heroSimpleInfos == null) ? 0 : this.heroSimpleInfos.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo> getHeroSimpleInfosIterator() {
    return (this.heroSimpleInfos == null) ? null : this.heroSimpleInfos.iterator();
  }

  public void addToHeroSimpleInfos(com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo elem) {
    if (this.heroSimpleInfos == null) {
      this.heroSimpleInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo>();
    }
    this.heroSimpleInfos.add(elem);
  }

  /**
   * 多英雄信息列表
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo> getHeroSimpleInfos() {
    return this.heroSimpleInfos;
  }

  /**
   * 多英雄信息列表
   */
  public GcRankList setHeroSimpleInfos(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo> heroSimpleInfos) {
    this.heroSimpleInfos = heroSimpleInfos;
    return this;
  }

  public void unsetHeroSimpleInfos() {
    this.heroSimpleInfos = null;
  }

  /** Returns true if field heroSimpleInfos is set (has been assigned a value) and false otherwise */
  public boolean isSetHeroSimpleInfos() {
    return this.heroSimpleInfos != null;
  }

  public void setHeroSimpleInfosIsSet(boolean value) {
    if (!value) {
      this.heroSimpleInfos = null;
    }
  }

  public int getMaxCount() {
    return this.maxCount;
  }

  public GcRankList setMaxCount(int maxCount) {
    this.maxCount = maxCount;
    setMaxCountIsSet(true);
    return this;
  }

  public void unsetMaxCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MAXCOUNT_ISSET_ID);
  }

  /** Returns true if field maxCount is set (has been assigned a value) and false otherwise */
  public boolean isSetMaxCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MAXCOUNT_ISSET_ID);
  }

  public void setMaxCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MAXCOUNT_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getExt() {
    return this.ext;
  }

  public GcRankList setExt(@org.apache.thrift.annotation.Nullable java.lang.String ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case TYPE:
      if (value == null) {
        unsetType();
      } else {
        setType((com.lc.billion.icefire.protocol.constant.PsRankType)value);
      }
      break;

    case MEMBERS:
      if (value == null) {
        unsetMembers();
      } else {
        setMembers((java.util.List<com.lc.billion.icefire.protocol.structure.PsRankMember>)value);
      }
      break;

    case OWNER_RANK:
      if (value == null) {
        unsetOwnerRank();
      } else {
        setOwnerRank((java.lang.Integer)value);
      }
      break;

    case OWNER_VALUE:
      if (value == null) {
        unsetOwnerValue();
      } else {
        setOwnerValue((java.lang.Long)value);
      }
      break;

    case OWNER_PS_RANK_VARIATION_TYPE:
      if (value == null) {
        unsetOwnerPsRankVariationType();
      } else {
        setOwnerPsRankVariationType((com.lc.billion.icefire.protocol.constant.PsRankVariationType)value);
      }
      break;

    case PAGE:
      if (value == null) {
        unsetPage();
      } else {
        setPage((java.lang.Integer)value);
      }
      break;

    case PARAMS:
      if (value == null) {
        unsetParams();
      } else {
        setParams((java.lang.String)value);
      }
      break;

    case HERO_SIMPLE_INFOS:
      if (value == null) {
        unsetHeroSimpleInfos();
      } else {
        setHeroSimpleInfos((java.util.List<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo>)value);
      }
      break;

    case MAX_COUNT:
      if (value == null) {
        unsetMaxCount();
      } else {
        setMaxCount((java.lang.Integer)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((java.lang.String)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case TYPE:
      return getType();

    case MEMBERS:
      return getMembers();

    case OWNER_RANK:
      return getOwnerRank();

    case OWNER_VALUE:
      return getOwnerValue();

    case OWNER_PS_RANK_VARIATION_TYPE:
      return getOwnerPsRankVariationType();

    case PAGE:
      return getPage();

    case PARAMS:
      return getParams();

    case HERO_SIMPLE_INFOS:
      return getHeroSimpleInfos();

    case MAX_COUNT:
      return getMaxCount();

    case EXT:
      return getExt();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case TYPE:
      return isSetType();
    case MEMBERS:
      return isSetMembers();
    case OWNER_RANK:
      return isSetOwnerRank();
    case OWNER_VALUE:
      return isSetOwnerValue();
    case OWNER_PS_RANK_VARIATION_TYPE:
      return isSetOwnerPsRankVariationType();
    case PAGE:
      return isSetPage();
    case PARAMS:
      return isSetParams();
    case HERO_SIMPLE_INFOS:
      return isSetHeroSimpleInfos();
    case MAX_COUNT:
      return isSetMaxCount();
    case EXT:
      return isSetExt();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcRankList)
      return this.equals((GcRankList)that);
    return false;
  }

  public boolean equals(GcRankList that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_type = true && this.isSetType();
    boolean that_present_type = true && that.isSetType();
    if (this_present_type || that_present_type) {
      if (!(this_present_type && that_present_type))
        return false;
      if (!this.type.equals(that.type))
        return false;
    }

    boolean this_present_members = true && this.isSetMembers();
    boolean that_present_members = true && that.isSetMembers();
    if (this_present_members || that_present_members) {
      if (!(this_present_members && that_present_members))
        return false;
      if (!this.members.equals(that.members))
        return false;
    }

    boolean this_present_ownerRank = true && this.isSetOwnerRank();
    boolean that_present_ownerRank = true && that.isSetOwnerRank();
    if (this_present_ownerRank || that_present_ownerRank) {
      if (!(this_present_ownerRank && that_present_ownerRank))
        return false;
      if (this.ownerRank != that.ownerRank)
        return false;
    }

    boolean this_present_ownerValue = true && this.isSetOwnerValue();
    boolean that_present_ownerValue = true && that.isSetOwnerValue();
    if (this_present_ownerValue || that_present_ownerValue) {
      if (!(this_present_ownerValue && that_present_ownerValue))
        return false;
      if (this.ownerValue != that.ownerValue)
        return false;
    }

    boolean this_present_ownerPsRankVariationType = true && this.isSetOwnerPsRankVariationType();
    boolean that_present_ownerPsRankVariationType = true && that.isSetOwnerPsRankVariationType();
    if (this_present_ownerPsRankVariationType || that_present_ownerPsRankVariationType) {
      if (!(this_present_ownerPsRankVariationType && that_present_ownerPsRankVariationType))
        return false;
      if (!this.ownerPsRankVariationType.equals(that.ownerPsRankVariationType))
        return false;
    }

    boolean this_present_page = true && this.isSetPage();
    boolean that_present_page = true && that.isSetPage();
    if (this_present_page || that_present_page) {
      if (!(this_present_page && that_present_page))
        return false;
      if (this.page != that.page)
        return false;
    }

    boolean this_present_params = true && this.isSetParams();
    boolean that_present_params = true && that.isSetParams();
    if (this_present_params || that_present_params) {
      if (!(this_present_params && that_present_params))
        return false;
      if (!this.params.equals(that.params))
        return false;
    }

    boolean this_present_heroSimpleInfos = true && this.isSetHeroSimpleInfos();
    boolean that_present_heroSimpleInfos = true && that.isSetHeroSimpleInfos();
    if (this_present_heroSimpleInfos || that_present_heroSimpleInfos) {
      if (!(this_present_heroSimpleInfos && that_present_heroSimpleInfos))
        return false;
      if (!this.heroSimpleInfos.equals(that.heroSimpleInfos))
        return false;
    }

    boolean this_present_maxCount = true && this.isSetMaxCount();
    boolean that_present_maxCount = true && that.isSetMaxCount();
    if (this_present_maxCount || that_present_maxCount) {
      if (!(this_present_maxCount && that_present_maxCount))
        return false;
      if (this.maxCount != that.maxCount)
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetType()) ? 131071 : 524287);
    if (isSetType())
      hashCode = hashCode * 8191 + type.getValue();

    hashCode = hashCode * 8191 + ((isSetMembers()) ? 131071 : 524287);
    if (isSetMembers())
      hashCode = hashCode * 8191 + members.hashCode();

    hashCode = hashCode * 8191 + ((isSetOwnerRank()) ? 131071 : 524287);
    if (isSetOwnerRank())
      hashCode = hashCode * 8191 + ownerRank;

    hashCode = hashCode * 8191 + ((isSetOwnerValue()) ? 131071 : 524287);
    if (isSetOwnerValue())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(ownerValue);

    hashCode = hashCode * 8191 + ((isSetOwnerPsRankVariationType()) ? 131071 : 524287);
    if (isSetOwnerPsRankVariationType())
      hashCode = hashCode * 8191 + ownerPsRankVariationType.getValue();

    hashCode = hashCode * 8191 + ((isSetPage()) ? 131071 : 524287);
    if (isSetPage())
      hashCode = hashCode * 8191 + page;

    hashCode = hashCode * 8191 + ((isSetParams()) ? 131071 : 524287);
    if (isSetParams())
      hashCode = hashCode * 8191 + params.hashCode();

    hashCode = hashCode * 8191 + ((isSetHeroSimpleInfos()) ? 131071 : 524287);
    if (isSetHeroSimpleInfos())
      hashCode = hashCode * 8191 + heroSimpleInfos.hashCode();

    hashCode = hashCode * 8191 + ((isSetMaxCount()) ? 131071 : 524287);
    if (isSetMaxCount())
      hashCode = hashCode * 8191 + maxCount;

    hashCode = hashCode * 8191 + ((isSetExt()) ? 131071 : 524287);
    if (isSetExt())
      hashCode = hashCode * 8191 + ext.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcRankList other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetType(), other.isSetType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.type, other.type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMembers(), other.isSetMembers());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMembers()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.members, other.members);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetOwnerRank(), other.isSetOwnerRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOwnerRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ownerRank, other.ownerRank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetOwnerValue(), other.isSetOwnerValue());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOwnerValue()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ownerValue, other.ownerValue);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetOwnerPsRankVariationType(), other.isSetOwnerPsRankVariationType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOwnerPsRankVariationType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ownerPsRankVariationType, other.ownerPsRankVariationType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPage(), other.isSetPage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.page, other.page);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetParams(), other.isSetParams());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetParams()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHeroSimpleInfos(), other.isSetHeroSimpleInfos());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeroSimpleInfos()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.heroSimpleInfos, other.heroSimpleInfos);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMaxCount(), other.isSetMaxCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMaxCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.maxCount, other.maxCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetExt(), other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcRankList(");
    boolean first = true;

    sb.append("type:");
    if (this.type == null) {
      sb.append("null");
    } else {
      sb.append(this.type);
    }
    first = false;
    if (isSetMembers()) {
      if (!first) sb.append(", ");
      sb.append("members:");
      if (this.members == null) {
        sb.append("null");
      } else {
        sb.append(this.members);
      }
      first = false;
    }
    if (isSetOwnerRank()) {
      if (!first) sb.append(", ");
      sb.append("ownerRank:");
      sb.append(this.ownerRank);
      first = false;
    }
    if (isSetOwnerValue()) {
      if (!first) sb.append(", ");
      sb.append("ownerValue:");
      sb.append(this.ownerValue);
      first = false;
    }
    if (isSetOwnerPsRankVariationType()) {
      if (!first) sb.append(", ");
      sb.append("ownerPsRankVariationType:");
      if (this.ownerPsRankVariationType == null) {
        sb.append("null");
      } else {
        sb.append(this.ownerPsRankVariationType);
      }
      first = false;
    }
    if (isSetPage()) {
      if (!first) sb.append(", ");
      sb.append("page:");
      sb.append(this.page);
      first = false;
    }
    if (isSetParams()) {
      if (!first) sb.append(", ");
      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
    }
    if (isSetHeroSimpleInfos()) {
      if (!first) sb.append(", ");
      sb.append("heroSimpleInfos:");
      if (this.heroSimpleInfos == null) {
        sb.append("null");
      } else {
        sb.append(this.heroSimpleInfos);
      }
      first = false;
    }
    if (isSetMaxCount()) {
      if (!first) sb.append(", ");
      sb.append("maxCount:");
      sb.append(this.maxCount);
      first = false;
    }
    if (isSetExt()) {
      if (!first) sb.append(", ");
      sb.append("ext:");
      if (this.ext == null) {
        sb.append("null");
      } else {
        sb.append(this.ext);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (type == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'type' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcRankListStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRankListStandardScheme getScheme() {
      return new GcRankListStandardScheme();
    }
  }

  private static class GcRankListStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcRankList> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcRankList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.type = com.lc.billion.icefire.protocol.constant.PsRankType.findByValue(iprot.readI32());
              struct.setTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MEMBERS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.members = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRankMember>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRankMember _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsRankMember();
                  _elem1.read(iprot);
                  struct.members.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setMembersIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // OWNER_RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.ownerRank = iprot.readI32();
              struct.setOwnerRankIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // OWNER_VALUE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ownerValue = iprot.readI64();
              struct.setOwnerValueIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // OWNER_PS_RANK_VARIATION_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.ownerPsRankVariationType = com.lc.billion.icefire.protocol.constant.PsRankVariationType.findByValue(iprot.readI32());
              struct.setOwnerPsRankVariationTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // PAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.page = iprot.readI32();
              struct.setPageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // PARAMS
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.params = iprot.readString();
              struct.setParamsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // HERO_SIMPLE_INFOS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.heroSimpleInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo>(_list3.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = new com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo();
                  _elem4.read(iprot);
                  struct.heroSimpleInfos.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setHeroSimpleInfosIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // MAX_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.maxCount = iprot.readI32();
              struct.setMaxCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ext = iprot.readString();
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcRankList struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.type != null) {
        oprot.writeFieldBegin(TYPE_FIELD_DESC);
        oprot.writeI32(struct.type.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.members != null) {
        if (struct.isSetMembers()) {
          oprot.writeFieldBegin(MEMBERS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.members.size()));
            for (com.lc.billion.icefire.protocol.structure.PsRankMember _iter6 : struct.members)
            {
              _iter6.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetOwnerRank()) {
        oprot.writeFieldBegin(OWNER_RANK_FIELD_DESC);
        oprot.writeI32(struct.ownerRank);
        oprot.writeFieldEnd();
      }
      if (struct.isSetOwnerValue()) {
        oprot.writeFieldBegin(OWNER_VALUE_FIELD_DESC);
        oprot.writeI64(struct.ownerValue);
        oprot.writeFieldEnd();
      }
      if (struct.ownerPsRankVariationType != null) {
        if (struct.isSetOwnerPsRankVariationType()) {
          oprot.writeFieldBegin(OWNER_PS_RANK_VARIATION_TYPE_FIELD_DESC);
          oprot.writeI32(struct.ownerPsRankVariationType.getValue());
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetPage()) {
        oprot.writeFieldBegin(PAGE_FIELD_DESC);
        oprot.writeI32(struct.page);
        oprot.writeFieldEnd();
      }
      if (struct.params != null) {
        if (struct.isSetParams()) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          oprot.writeString(struct.params);
          oprot.writeFieldEnd();
        }
      }
      if (struct.heroSimpleInfos != null) {
        if (struct.isSetHeroSimpleInfos()) {
          oprot.writeFieldBegin(HERO_SIMPLE_INFOS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.heroSimpleInfos.size()));
            for (com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo _iter7 : struct.heroSimpleInfos)
            {
              _iter7.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetMaxCount()) {
        oprot.writeFieldBegin(MAX_COUNT_FIELD_DESC);
        oprot.writeI32(struct.maxCount);
        oprot.writeFieldEnd();
      }
      if (struct.ext != null) {
        if (struct.isSetExt()) {
          oprot.writeFieldBegin(EXT_FIELD_DESC);
          oprot.writeString(struct.ext);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcRankListTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRankListTupleScheme getScheme() {
      return new GcRankListTupleScheme();
    }
  }

  private static class GcRankListTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcRankList> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcRankList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.type.getValue());
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetMembers()) {
        optionals.set(0);
      }
      if (struct.isSetOwnerRank()) {
        optionals.set(1);
      }
      if (struct.isSetOwnerValue()) {
        optionals.set(2);
      }
      if (struct.isSetOwnerPsRankVariationType()) {
        optionals.set(3);
      }
      if (struct.isSetPage()) {
        optionals.set(4);
      }
      if (struct.isSetParams()) {
        optionals.set(5);
      }
      if (struct.isSetHeroSimpleInfos()) {
        optionals.set(6);
      }
      if (struct.isSetMaxCount()) {
        optionals.set(7);
      }
      if (struct.isSetExt()) {
        optionals.set(8);
      }
      oprot.writeBitSet(optionals, 9);
      if (struct.isSetMembers()) {
        {
          oprot.writeI32(struct.members.size());
          for (com.lc.billion.icefire.protocol.structure.PsRankMember _iter8 : struct.members)
          {
            _iter8.write(oprot);
          }
        }
      }
      if (struct.isSetOwnerRank()) {
        oprot.writeI32(struct.ownerRank);
      }
      if (struct.isSetOwnerValue()) {
        oprot.writeI64(struct.ownerValue);
      }
      if (struct.isSetOwnerPsRankVariationType()) {
        oprot.writeI32(struct.ownerPsRankVariationType.getValue());
      }
      if (struct.isSetPage()) {
        oprot.writeI32(struct.page);
      }
      if (struct.isSetParams()) {
        oprot.writeString(struct.params);
      }
      if (struct.isSetHeroSimpleInfos()) {
        {
          oprot.writeI32(struct.heroSimpleInfos.size());
          for (com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo _iter9 : struct.heroSimpleInfos)
          {
            _iter9.write(oprot);
          }
        }
      }
      if (struct.isSetMaxCount()) {
        oprot.writeI32(struct.maxCount);
      }
      if (struct.isSetExt()) {
        oprot.writeString(struct.ext);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcRankList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.type = com.lc.billion.icefire.protocol.constant.PsRankType.findByValue(iprot.readI32());
      struct.setTypeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(9);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list10 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.members = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRankMember>(_list10.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRankMember _elem11;
          for (int _i12 = 0; _i12 < _list10.size; ++_i12)
          {
            _elem11 = new com.lc.billion.icefire.protocol.structure.PsRankMember();
            _elem11.read(iprot);
            struct.members.add(_elem11);
          }
        }
        struct.setMembersIsSet(true);
      }
      if (incoming.get(1)) {
        struct.ownerRank = iprot.readI32();
        struct.setOwnerRankIsSet(true);
      }
      if (incoming.get(2)) {
        struct.ownerValue = iprot.readI64();
        struct.setOwnerValueIsSet(true);
      }
      if (incoming.get(3)) {
        struct.ownerPsRankVariationType = com.lc.billion.icefire.protocol.constant.PsRankVariationType.findByValue(iprot.readI32());
        struct.setOwnerPsRankVariationTypeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.page = iprot.readI32();
        struct.setPageIsSet(true);
      }
      if (incoming.get(5)) {
        struct.params = iprot.readString();
        struct.setParamsIsSet(true);
      }
      if (incoming.get(6)) {
        {
          org.apache.thrift.protocol.TList _list13 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.heroSimpleInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo>(_list13.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo _elem14;
          for (int _i15 = 0; _i15 < _list13.size; ++_i15)
          {
            _elem14 = new com.lc.billion.icefire.protocol.structure.PsHeroSimpleInfo();
            _elem14.read(iprot);
            struct.heroSimpleInfos.add(_elem14);
          }
        }
        struct.setHeroSimpleInfosIsSet(true);
      }
      if (incoming.get(7)) {
        struct.maxCount = iprot.readI32();
        struct.setMaxCountIsSet(true);
      }
      if (incoming.get(8)) {
        struct.ext = iprot.readString();
        struct.setExtIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

