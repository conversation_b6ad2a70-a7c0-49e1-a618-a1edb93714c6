/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 州府历史记录返回
 * @Message(6931)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcRegionCapitalBelongHistory implements org.apache.thrift.TBase<GcRegionCapitalBelongHistory, GcRegionCapitalBelongHistory._Fields>, java.io.Serializable, Cloneable, Comparable<GcRegionCapitalBelongHistory> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcRegionCapitalBelongHistory");

  private static final org.apache.thrift.protocol.TField BELONG_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("belongList", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcRegionCapitalBelongHistoryStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcRegionCapitalBelongHistoryTupleSchemeFactory();

  /**
   * 占领记录
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong> belongList; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 占领记录
     */
    BELONG_LIST((short)2, "belongList");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 2: // BELONG_LIST
          return BELONG_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.BELONG_LIST};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BELONG_LIST, new org.apache.thrift.meta_data.FieldMetaData("belongList", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcRegionCapitalBelongHistory.class, metaDataMap);
  }

  public GcRegionCapitalBelongHistory() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcRegionCapitalBelongHistory(GcRegionCapitalBelongHistory other) {
    if (other.isSetBelongList()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong> __this__belongList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong>(other.belongList.size());
      for (com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong other_element : other.belongList) {
        __this__belongList.add(new com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong(other_element));
      }
      this.belongList = __this__belongList;
    }
  }

  public GcRegionCapitalBelongHistory deepCopy() {
    return new GcRegionCapitalBelongHistory(this);
  }

  @Override
  public void clear() {
    this.belongList = null;
  }

  public int getBelongListSize() {
    return (this.belongList == null) ? 0 : this.belongList.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong> getBelongListIterator() {
    return (this.belongList == null) ? null : this.belongList.iterator();
  }

  public void addToBelongList(com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong elem) {
    if (this.belongList == null) {
      this.belongList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong>();
    }
    this.belongList.add(elem);
  }

  /**
   * 占领记录
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong> getBelongList() {
    return this.belongList;
  }

  /**
   * 占领记录
   */
  public GcRegionCapitalBelongHistory setBelongList(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong> belongList) {
    this.belongList = belongList;
    return this;
  }

  public void unsetBelongList() {
    this.belongList = null;
  }

  /** Returns true if field belongList is set (has been assigned a value) and false otherwise */
  public boolean isSetBelongList() {
    return this.belongList != null;
  }

  public void setBelongListIsSet(boolean value) {
    if (!value) {
      this.belongList = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case BELONG_LIST:
      if (value == null) {
        unsetBelongList();
      } else {
        setBelongList((java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case BELONG_LIST:
      return getBelongList();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case BELONG_LIST:
      return isSetBelongList();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcRegionCapitalBelongHistory)
      return this.equals((GcRegionCapitalBelongHistory)that);
    return false;
  }

  public boolean equals(GcRegionCapitalBelongHistory that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_belongList = true && this.isSetBelongList();
    boolean that_present_belongList = true && that.isSetBelongList();
    if (this_present_belongList || that_present_belongList) {
      if (!(this_present_belongList && that_present_belongList))
        return false;
      if (!this.belongList.equals(that.belongList))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetBelongList()) ? 131071 : 524287);
    if (isSetBelongList())
      hashCode = hashCode * 8191 + belongList.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcRegionCapitalBelongHistory other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetBelongList(), other.isSetBelongList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBelongList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.belongList, other.belongList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcRegionCapitalBelongHistory(");
    boolean first = true;

    if (isSetBelongList()) {
      sb.append("belongList:");
      if (this.belongList == null) {
        sb.append("null");
      } else {
        sb.append(this.belongList);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcRegionCapitalBelongHistoryStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRegionCapitalBelongHistoryStandardScheme getScheme() {
      return new GcRegionCapitalBelongHistoryStandardScheme();
    }
  }

  private static class GcRegionCapitalBelongHistoryStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcRegionCapitalBelongHistory> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcRegionCapitalBelongHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 2: // BELONG_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.belongList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong();
                  _elem1.read(iprot);
                  struct.belongList.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setBelongListIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcRegionCapitalBelongHistory struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.belongList != null) {
        if (struct.isSetBelongList()) {
          oprot.writeFieldBegin(BELONG_LIST_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.belongList.size()));
            for (com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong _iter3 : struct.belongList)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcRegionCapitalBelongHistoryTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRegionCapitalBelongHistoryTupleScheme getScheme() {
      return new GcRegionCapitalBelongHistoryTupleScheme();
    }
  }

  private static class GcRegionCapitalBelongHistoryTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcRegionCapitalBelongHistory> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcRegionCapitalBelongHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetBelongList()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetBelongList()) {
        {
          oprot.writeI32(struct.belongList.size());
          for (com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong _iter4 : struct.belongList)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcRegionCapitalBelongHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.belongList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsRegionCapitalBelong();
            _elem6.read(iprot);
            struct.belongList.add(_elem6);
          }
        }
        struct.setBelongListIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

