/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 下发KVK安全区信息
 * @Message(7426)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcSeasonWarmUpSafeRegionPush implements org.apache.thrift.TBase<GcSeasonWarmUpSafeRegionPush, GcSeasonWarmUpSafeRegionPush._Fields>, java.io.Serializable, Cloneable, Comparable<GcSeasonWarmUpSafeRegionPush> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcSeasonWarmUpSafeRegionPush");

  private static final org.apache.thrift.protocol.TField REGION_TO_SERVER_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("regionToServerMap", org.apache.thrift.protocol.TType.MAP, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcSeasonWarmUpSafeRegionPushStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcSeasonWarmUpSafeRegionPushTupleSchemeFactory();

  /**
   * key 是区域id value 是原服ID
   */
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.Integer,java.lang.Integer> regionToServerMap; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * key 是区域id value 是原服ID
     */
    REGION_TO_SERVER_MAP((short)1, "regionToServerMap");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // REGION_TO_SERVER_MAP
          return REGION_TO_SERVER_MAP;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.REGION_TO_SERVER_MAP};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.REGION_TO_SERVER_MAP, new org.apache.thrift.meta_data.FieldMetaData("regionToServerMap", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcSeasonWarmUpSafeRegionPush.class, metaDataMap);
  }

  public GcSeasonWarmUpSafeRegionPush() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcSeasonWarmUpSafeRegionPush(GcSeasonWarmUpSafeRegionPush other) {
    if (other.isSetRegionToServerMap()) {
      java.util.Map<java.lang.Integer,java.lang.Integer> __this__regionToServerMap = new java.util.HashMap<java.lang.Integer,java.lang.Integer>(other.regionToServerMap);
      this.regionToServerMap = __this__regionToServerMap;
    }
  }

  public GcSeasonWarmUpSafeRegionPush deepCopy() {
    return new GcSeasonWarmUpSafeRegionPush(this);
  }

  @Override
  public void clear() {
    this.regionToServerMap = null;
  }

  public int getRegionToServerMapSize() {
    return (this.regionToServerMap == null) ? 0 : this.regionToServerMap.size();
  }

  public void putToRegionToServerMap(int key, int val) {
    if (this.regionToServerMap == null) {
      this.regionToServerMap = new java.util.HashMap<java.lang.Integer,java.lang.Integer>();
    }
    this.regionToServerMap.put(key, val);
  }

  /**
   * key 是区域id value 是原服ID
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.Integer,java.lang.Integer> getRegionToServerMap() {
    return this.regionToServerMap;
  }

  /**
   * key 是区域id value 是原服ID
   */
  public GcSeasonWarmUpSafeRegionPush setRegionToServerMap(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.Integer,java.lang.Integer> regionToServerMap) {
    this.regionToServerMap = regionToServerMap;
    return this;
  }

  public void unsetRegionToServerMap() {
    this.regionToServerMap = null;
  }

  /** Returns true if field regionToServerMap is set (has been assigned a value) and false otherwise */
  public boolean isSetRegionToServerMap() {
    return this.regionToServerMap != null;
  }

  public void setRegionToServerMapIsSet(boolean value) {
    if (!value) {
      this.regionToServerMap = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case REGION_TO_SERVER_MAP:
      if (value == null) {
        unsetRegionToServerMap();
      } else {
        setRegionToServerMap((java.util.Map<java.lang.Integer,java.lang.Integer>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case REGION_TO_SERVER_MAP:
      return getRegionToServerMap();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case REGION_TO_SERVER_MAP:
      return isSetRegionToServerMap();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcSeasonWarmUpSafeRegionPush)
      return this.equals((GcSeasonWarmUpSafeRegionPush)that);
    return false;
  }

  public boolean equals(GcSeasonWarmUpSafeRegionPush that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_regionToServerMap = true && this.isSetRegionToServerMap();
    boolean that_present_regionToServerMap = true && that.isSetRegionToServerMap();
    if (this_present_regionToServerMap || that_present_regionToServerMap) {
      if (!(this_present_regionToServerMap && that_present_regionToServerMap))
        return false;
      if (!this.regionToServerMap.equals(that.regionToServerMap))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetRegionToServerMap()) ? 131071 : 524287);
    if (isSetRegionToServerMap())
      hashCode = hashCode * 8191 + regionToServerMap.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcSeasonWarmUpSafeRegionPush other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRegionToServerMap(), other.isSetRegionToServerMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRegionToServerMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.regionToServerMap, other.regionToServerMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcSeasonWarmUpSafeRegionPush(");
    boolean first = true;

    if (isSetRegionToServerMap()) {
      sb.append("regionToServerMap:");
      if (this.regionToServerMap == null) {
        sb.append("null");
      } else {
        sb.append(this.regionToServerMap);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcSeasonWarmUpSafeRegionPushStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcSeasonWarmUpSafeRegionPushStandardScheme getScheme() {
      return new GcSeasonWarmUpSafeRegionPushStandardScheme();
    }
  }

  private static class GcSeasonWarmUpSafeRegionPushStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcSeasonWarmUpSafeRegionPush> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcSeasonWarmUpSafeRegionPush struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // REGION_TO_SERVER_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map48 = iprot.readMapBegin();
                struct.regionToServerMap = new java.util.HashMap<java.lang.Integer,java.lang.Integer>(2*_map48.size);
                int _key49;
                int _val50;
                for (int _i51 = 0; _i51 < _map48.size; ++_i51)
                {
                  _key49 = iprot.readI32();
                  _val50 = iprot.readI32();
                  struct.regionToServerMap.put(_key49, _val50);
                }
                iprot.readMapEnd();
              }
              struct.setRegionToServerMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcSeasonWarmUpSafeRegionPush struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.regionToServerMap != null) {
        if (struct.isSetRegionToServerMap()) {
          oprot.writeFieldBegin(REGION_TO_SERVER_MAP_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.I32, struct.regionToServerMap.size()));
            for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> _iter52 : struct.regionToServerMap.entrySet())
            {
              oprot.writeI32(_iter52.getKey());
              oprot.writeI32(_iter52.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcSeasonWarmUpSafeRegionPushTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcSeasonWarmUpSafeRegionPushTupleScheme getScheme() {
      return new GcSeasonWarmUpSafeRegionPushTupleScheme();
    }
  }

  private static class GcSeasonWarmUpSafeRegionPushTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcSeasonWarmUpSafeRegionPush> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcSeasonWarmUpSafeRegionPush struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRegionToServerMap()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetRegionToServerMap()) {
        {
          oprot.writeI32(struct.regionToServerMap.size());
          for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> _iter53 : struct.regionToServerMap.entrySet())
          {
            oprot.writeI32(_iter53.getKey());
            oprot.writeI32(_iter53.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcSeasonWarmUpSafeRegionPush struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map54 = iprot.readMapBegin(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.I32); 
          struct.regionToServerMap = new java.util.HashMap<java.lang.Integer,java.lang.Integer>(2*_map54.size);
          int _key55;
          int _val56;
          for (int _i57 = 0; _i57 < _map54.size; ++_i57)
          {
            _key55 = iprot.readI32();
            _val56 = iprot.readI32();
            struct.regionToServerMap.put(_key55, _val56);
          }
        }
        struct.setRegionToServerMapIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

