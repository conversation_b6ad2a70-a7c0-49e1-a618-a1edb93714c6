/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 请求TVT活动个人战绩返回
 * @Message(7154)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcTvtBattleRecord implements org.apache.thrift.TBase<GcTvtBattleRecord, GcTvtBattleRecord._Fields>, java.io.Serializable, Cloneable, Comparable<GcTvtBattleRecord> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcTvtBattleRecord");

  private static final org.apache.thrift.protocol.TField TVT_POWER_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("tvtPowerScore", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RECORDS_FIELD_DESC = new org.apache.thrift.protocol.TField("records", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcTvtBattleRecordStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcTvtBattleRecordTupleSchemeFactory();

  /**
   * 当前实力积分
   */
  public long tvtPowerScore; // optional
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord> records; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 当前实力积分
     */
    TVT_POWER_SCORE((short)1, "tvtPowerScore"),
    RECORDS((short)2, "records");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TVT_POWER_SCORE
          return TVT_POWER_SCORE;
        case 2: // RECORDS
          return RECORDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __TVTPOWERSCORE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.TVT_POWER_SCORE,_Fields.RECORDS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TVT_POWER_SCORE, new org.apache.thrift.meta_data.FieldMetaData("tvtPowerScore", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RECORDS, new org.apache.thrift.meta_data.FieldMetaData("records", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcTvtBattleRecord.class, metaDataMap);
  }

  public GcTvtBattleRecord() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcTvtBattleRecord(GcTvtBattleRecord other) {
    __isset_bitfield = other.__isset_bitfield;
    this.tvtPowerScore = other.tvtPowerScore;
    if (other.isSetRecords()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord> __this__records = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord>(other.records.size());
      for (com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord other_element : other.records) {
        __this__records.add(new com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord(other_element));
      }
      this.records = __this__records;
    }
  }

  public GcTvtBattleRecord deepCopy() {
    return new GcTvtBattleRecord(this);
  }

  @Override
  public void clear() {
    setTvtPowerScoreIsSet(false);
    this.tvtPowerScore = 0;
    this.records = null;
  }

  /**
   * 当前实力积分
   */
  public long getTvtPowerScore() {
    return this.tvtPowerScore;
  }

  /**
   * 当前实力积分
   */
  public GcTvtBattleRecord setTvtPowerScore(long tvtPowerScore) {
    this.tvtPowerScore = tvtPowerScore;
    setTvtPowerScoreIsSet(true);
    return this;
  }

  public void unsetTvtPowerScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TVTPOWERSCORE_ISSET_ID);
  }

  /** Returns true if field tvtPowerScore is set (has been assigned a value) and false otherwise */
  public boolean isSetTvtPowerScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TVTPOWERSCORE_ISSET_ID);
  }

  public void setTvtPowerScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TVTPOWERSCORE_ISSET_ID, value);
  }

  public int getRecordsSize() {
    return (this.records == null) ? 0 : this.records.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord> getRecordsIterator() {
    return (this.records == null) ? null : this.records.iterator();
  }

  public void addToRecords(com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord elem) {
    if (this.records == null) {
      this.records = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord>();
    }
    this.records.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord> getRecords() {
    return this.records;
  }

  public GcTvtBattleRecord setRecords(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord> records) {
    this.records = records;
    return this;
  }

  public void unsetRecords() {
    this.records = null;
  }

  /** Returns true if field records is set (has been assigned a value) and false otherwise */
  public boolean isSetRecords() {
    return this.records != null;
  }

  public void setRecordsIsSet(boolean value) {
    if (!value) {
      this.records = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case TVT_POWER_SCORE:
      if (value == null) {
        unsetTvtPowerScore();
      } else {
        setTvtPowerScore((java.lang.Long)value);
      }
      break;

    case RECORDS:
      if (value == null) {
        unsetRecords();
      } else {
        setRecords((java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case TVT_POWER_SCORE:
      return getTvtPowerScore();

    case RECORDS:
      return getRecords();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case TVT_POWER_SCORE:
      return isSetTvtPowerScore();
    case RECORDS:
      return isSetRecords();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcTvtBattleRecord)
      return this.equals((GcTvtBattleRecord)that);
    return false;
  }

  public boolean equals(GcTvtBattleRecord that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_tvtPowerScore = true && this.isSetTvtPowerScore();
    boolean that_present_tvtPowerScore = true && that.isSetTvtPowerScore();
    if (this_present_tvtPowerScore || that_present_tvtPowerScore) {
      if (!(this_present_tvtPowerScore && that_present_tvtPowerScore))
        return false;
      if (this.tvtPowerScore != that.tvtPowerScore)
        return false;
    }

    boolean this_present_records = true && this.isSetRecords();
    boolean that_present_records = true && that.isSetRecords();
    if (this_present_records || that_present_records) {
      if (!(this_present_records && that_present_records))
        return false;
      if (!this.records.equals(that.records))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetTvtPowerScore()) ? 131071 : 524287);
    if (isSetTvtPowerScore())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(tvtPowerScore);

    hashCode = hashCode * 8191 + ((isSetRecords()) ? 131071 : 524287);
    if (isSetRecords())
      hashCode = hashCode * 8191 + records.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcTvtBattleRecord other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetTvtPowerScore(), other.isSetTvtPowerScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTvtPowerScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tvtPowerScore, other.tvtPowerScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRecords(), other.isSetRecords());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRecords()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.records, other.records);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcTvtBattleRecord(");
    boolean first = true;

    if (isSetTvtPowerScore()) {
      sb.append("tvtPowerScore:");
      sb.append(this.tvtPowerScore);
      first = false;
    }
    if (isSetRecords()) {
      if (!first) sb.append(", ");
      sb.append("records:");
      if (this.records == null) {
        sb.append("null");
      } else {
        sb.append(this.records);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcTvtBattleRecordStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcTvtBattleRecordStandardScheme getScheme() {
      return new GcTvtBattleRecordStandardScheme();
    }
  }

  private static class GcTvtBattleRecordStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcTvtBattleRecord> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcTvtBattleRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TVT_POWER_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.tvtPowerScore = iprot.readI64();
              struct.setTvtPowerScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RECORDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.records = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord();
                  _elem1.read(iprot);
                  struct.records.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRecordsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcTvtBattleRecord struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetTvtPowerScore()) {
        oprot.writeFieldBegin(TVT_POWER_SCORE_FIELD_DESC);
        oprot.writeI64(struct.tvtPowerScore);
        oprot.writeFieldEnd();
      }
      if (struct.records != null) {
        if (struct.isSetRecords()) {
          oprot.writeFieldBegin(RECORDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.records.size()));
            for (com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord _iter3 : struct.records)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcTvtBattleRecordTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcTvtBattleRecordTupleScheme getScheme() {
      return new GcTvtBattleRecordTupleScheme();
    }
  }

  private static class GcTvtBattleRecordTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcTvtBattleRecord> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcTvtBattleRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetTvtPowerScore()) {
        optionals.set(0);
      }
      if (struct.isSetRecords()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetTvtPowerScore()) {
        oprot.writeI64(struct.tvtPowerScore);
      }
      if (struct.isSetRecords()) {
        {
          oprot.writeI32(struct.records.size());
          for (com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord _iter4 : struct.records)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcTvtBattleRecord struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.tvtPowerScore = iprot.readI64();
        struct.setTvtPowerScoreIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.records = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsTvtRoleBattleRecord();
            _elem6.read(iprot);
            struct.records.add(_elem6);
          }
        }
        struct.setRecordsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

