/**
 * Autogenerated by <PERSON>hrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 更新州府简要信息给当前服玩家
 * @Message(6910)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcUpdateRegionCapitalSimpleInfo implements org.apache.thrift.TBase<GcUpdateRegionCapitalSimpleInfo, GcUpdateRegionCapitalSimpleInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcUpdateRegionCapitalSimpleInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcUpdateRegionCapitalSimpleInfo");

  private static final org.apache.thrift.protocol.TField SIMPLE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("simpleInfo", org.apache.thrift.protocol.TType.LIST, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcUpdateRegionCapitalSimpleInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcUpdateRegionCapitalSimpleInfoTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple> simpleInfo; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SIMPLE_INFO((short)1, "simpleInfo");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SIMPLE_INFO
          return SIMPLE_INFO;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.SIMPLE_INFO};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SIMPLE_INFO, new org.apache.thrift.meta_data.FieldMetaData("simpleInfo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcUpdateRegionCapitalSimpleInfo.class, metaDataMap);
  }

  public GcUpdateRegionCapitalSimpleInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcUpdateRegionCapitalSimpleInfo(GcUpdateRegionCapitalSimpleInfo other) {
    if (other.isSetSimpleInfo()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple> __this__simpleInfo = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple>(other.simpleInfo.size());
      for (com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple other_element : other.simpleInfo) {
        __this__simpleInfo.add(new com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple(other_element));
      }
      this.simpleInfo = __this__simpleInfo;
    }
  }

  public GcUpdateRegionCapitalSimpleInfo deepCopy() {
    return new GcUpdateRegionCapitalSimpleInfo(this);
  }

  @Override
  public void clear() {
    this.simpleInfo = null;
  }

  public int getSimpleInfoSize() {
    return (this.simpleInfo == null) ? 0 : this.simpleInfo.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple> getSimpleInfoIterator() {
    return (this.simpleInfo == null) ? null : this.simpleInfo.iterator();
  }

  public void addToSimpleInfo(com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple elem) {
    if (this.simpleInfo == null) {
      this.simpleInfo = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple>();
    }
    this.simpleInfo.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple> getSimpleInfo() {
    return this.simpleInfo;
  }

  public GcUpdateRegionCapitalSimpleInfo setSimpleInfo(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple> simpleInfo) {
    this.simpleInfo = simpleInfo;
    return this;
  }

  public void unsetSimpleInfo() {
    this.simpleInfo = null;
  }

  /** Returns true if field simpleInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetSimpleInfo() {
    return this.simpleInfo != null;
  }

  public void setSimpleInfoIsSet(boolean value) {
    if (!value) {
      this.simpleInfo = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case SIMPLE_INFO:
      if (value == null) {
        unsetSimpleInfo();
      } else {
        setSimpleInfo((java.util.List<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case SIMPLE_INFO:
      return getSimpleInfo();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case SIMPLE_INFO:
      return isSetSimpleInfo();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcUpdateRegionCapitalSimpleInfo)
      return this.equals((GcUpdateRegionCapitalSimpleInfo)that);
    return false;
  }

  public boolean equals(GcUpdateRegionCapitalSimpleInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_simpleInfo = true && this.isSetSimpleInfo();
    boolean that_present_simpleInfo = true && that.isSetSimpleInfo();
    if (this_present_simpleInfo || that_present_simpleInfo) {
      if (!(this_present_simpleInfo && that_present_simpleInfo))
        return false;
      if (!this.simpleInfo.equals(that.simpleInfo))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetSimpleInfo()) ? 131071 : 524287);
    if (isSetSimpleInfo())
      hashCode = hashCode * 8191 + simpleInfo.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcUpdateRegionCapitalSimpleInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetSimpleInfo(), other.isSetSimpleInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSimpleInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.simpleInfo, other.simpleInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcUpdateRegionCapitalSimpleInfo(");
    boolean first = true;

    if (isSetSimpleInfo()) {
      sb.append("simpleInfo:");
      if (this.simpleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.simpleInfo);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcUpdateRegionCapitalSimpleInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcUpdateRegionCapitalSimpleInfoStandardScheme getScheme() {
      return new GcUpdateRegionCapitalSimpleInfoStandardScheme();
    }
  }

  private static class GcUpdateRegionCapitalSimpleInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcUpdateRegionCapitalSimpleInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcUpdateRegionCapitalSimpleInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SIMPLE_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.simpleInfo = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple();
                  _elem1.read(iprot);
                  struct.simpleInfo.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setSimpleInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcUpdateRegionCapitalSimpleInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.simpleInfo != null) {
        if (struct.isSetSimpleInfo()) {
          oprot.writeFieldBegin(SIMPLE_INFO_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.simpleInfo.size()));
            for (com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple _iter3 : struct.simpleInfo)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcUpdateRegionCapitalSimpleInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcUpdateRegionCapitalSimpleInfoTupleScheme getScheme() {
      return new GcUpdateRegionCapitalSimpleInfoTupleScheme();
    }
  }

  private static class GcUpdateRegionCapitalSimpleInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcUpdateRegionCapitalSimpleInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcUpdateRegionCapitalSimpleInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetSimpleInfo()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetSimpleInfo()) {
        {
          oprot.writeI32(struct.simpleInfo.size());
          for (com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple _iter4 : struct.simpleInfo)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcUpdateRegionCapitalSimpleInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.simpleInfo = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsRegionCapitalSimple();
            _elem6.read(iprot);
            struct.simpleInfo.add(_elem6);
          }
        }
        struct.setSimpleInfoIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

