/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 小人死亡上报
 * @Message(7259)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GceopleDie implements org.apache.thrift.TBase<GceopleDie, GceopleDie._Fields>, java.io.Serializable, Cloneable, Comparable<GceopleDie> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GceopleDie");

  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField DIE_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("dieIds", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GceopleDieStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GceopleDieTupleSchemeFactory();

  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsErrorCode code; // required
  /**
   * 上报死亡成功的id
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.Integer> dieIds; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
     */
    CODE((short)1, "code"),
    /**
     * 上报死亡成功的id
     */
    DIE_IDS((short)2, "dieIds");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CODE
          return CODE;
        case 2: // DIE_IDS
          return DIE_IDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.DIE_IDS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsErrorCode.class)));
    tmpMap.put(_Fields.DIE_IDS, new org.apache.thrift.meta_data.FieldMetaData("dieIds", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GceopleDie.class, metaDataMap);
  }

  public GceopleDie() {
  }

  public GceopleDie(
    com.lc.billion.icefire.protocol.constant.PsErrorCode code)
  {
    this();
    this.code = code;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GceopleDie(GceopleDie other) {
    if (other.isSetCode()) {
      this.code = other.code;
    }
    if (other.isSetDieIds()) {
      java.util.List<java.lang.Integer> __this__dieIds = new java.util.ArrayList<java.lang.Integer>(other.dieIds);
      this.dieIds = __this__dieIds;
    }
  }

  public GceopleDie deepCopy() {
    return new GceopleDie(this);
  }

  @Override
  public void clear() {
    this.code = null;
    this.dieIds = null;
  }

  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsErrorCode getCode() {
    return this.code;
  }

  /**
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  public GceopleDie setCode(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsErrorCode code) {
    this.code = code;
    return this;
  }

  public void unsetCode() {
    this.code = null;
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return this.code != null;
  }

  public void setCodeIsSet(boolean value) {
    if (!value) {
      this.code = null;
    }
  }

  public int getDieIdsSize() {
    return (this.dieIds == null) ? 0 : this.dieIds.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.Integer> getDieIdsIterator() {
    return (this.dieIds == null) ? null : this.dieIds.iterator();
  }

  public void addToDieIds(int elem) {
    if (this.dieIds == null) {
      this.dieIds = new java.util.ArrayList<java.lang.Integer>();
    }
    this.dieIds.add(elem);
  }

  /**
   * 上报死亡成功的id
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.Integer> getDieIds() {
    return this.dieIds;
  }

  /**
   * 上报死亡成功的id
   */
  public GceopleDie setDieIds(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.Integer> dieIds) {
    this.dieIds = dieIds;
    return this;
  }

  public void unsetDieIds() {
    this.dieIds = null;
  }

  /** Returns true if field dieIds is set (has been assigned a value) and false otherwise */
  public boolean isSetDieIds() {
    return this.dieIds != null;
  }

  public void setDieIdsIsSet(boolean value) {
    if (!value) {
      this.dieIds = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((com.lc.billion.icefire.protocol.constant.PsErrorCode)value);
      }
      break;

    case DIE_IDS:
      if (value == null) {
        unsetDieIds();
      } else {
        setDieIds((java.util.List<java.lang.Integer>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case CODE:
      return getCode();

    case DIE_IDS:
      return getDieIds();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case CODE:
      return isSetCode();
    case DIE_IDS:
      return isSetDieIds();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GceopleDie)
      return this.equals((GceopleDie)that);
    return false;
  }

  public boolean equals(GceopleDie that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_code = true && this.isSetCode();
    boolean that_present_code = true && that.isSetCode();
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (!this.code.equals(that.code))
        return false;
    }

    boolean this_present_dieIds = true && this.isSetDieIds();
    boolean that_present_dieIds = true && that.isSetDieIds();
    if (this_present_dieIds || that_present_dieIds) {
      if (!(this_present_dieIds && that_present_dieIds))
        return false;
      if (!this.dieIds.equals(that.dieIds))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetCode()) ? 131071 : 524287);
    if (isSetCode())
      hashCode = hashCode * 8191 + code.getValue();

    hashCode = hashCode * 8191 + ((isSetDieIds()) ? 131071 : 524287);
    if (isSetDieIds())
      hashCode = hashCode * 8191 + dieIds.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GceopleDie other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetCode(), other.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, other.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetDieIds(), other.isSetDieIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDieIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dieIds, other.dieIds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GceopleDie(");
    boolean first = true;

    sb.append("code:");
    if (this.code == null) {
      sb.append("null");
    } else {
      sb.append(this.code);
    }
    first = false;
    if (isSetDieIds()) {
      if (!first) sb.append(", ");
      sb.append("dieIds:");
      if (this.dieIds == null) {
        sb.append("null");
      } else {
        sb.append(this.dieIds);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (code == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'code' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GceopleDieStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GceopleDieStandardScheme getScheme() {
      return new GceopleDieStandardScheme();
    }
  }

  private static class GceopleDieStandardScheme extends org.apache.thrift.scheme.StandardScheme<GceopleDie> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GceopleDie struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.code = com.lc.billion.icefire.protocol.constant.PsErrorCode.findByValue(iprot.readI32());
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // DIE_IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.dieIds = new java.util.ArrayList<java.lang.Integer>(_list0.size);
                int _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = iprot.readI32();
                  struct.dieIds.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setDieIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GceopleDie struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.code != null) {
        oprot.writeFieldBegin(CODE_FIELD_DESC);
        oprot.writeI32(struct.code.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.dieIds != null) {
        if (struct.isSetDieIds()) {
          oprot.writeFieldBegin(DIE_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, struct.dieIds.size()));
            for (int _iter3 : struct.dieIds)
            {
              oprot.writeI32(_iter3);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GceopleDieTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GceopleDieTupleScheme getScheme() {
      return new GceopleDieTupleScheme();
    }
  }

  private static class GceopleDieTupleScheme extends org.apache.thrift.scheme.TupleScheme<GceopleDie> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GceopleDie struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.code.getValue());
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetDieIds()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetDieIds()) {
        {
          oprot.writeI32(struct.dieIds.size());
          for (int _iter4 : struct.dieIds)
          {
            oprot.writeI32(_iter4);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GceopleDie struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.code = com.lc.billion.icefire.protocol.constant.PsErrorCode.findByValue(iprot.readI32());
      struct.setCodeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.I32);
          struct.dieIds = new java.util.ArrayList<java.lang.Integer>(_list5.size);
          int _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = iprot.readI32();
            struct.dieIds.add(_elem6);
          }
        }
        struct.setDieIdsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

