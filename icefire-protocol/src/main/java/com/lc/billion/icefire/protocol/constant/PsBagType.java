/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


/**
 * 背包类型
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsBagType implements org.apache.thrift.TEnum {
  /**
   * 主背包
   */
  MAIN(1);

  private final int value;

  private PsBagType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsBagType findByValue(int value) { 
    switch (value) {
      case 1:
        return MAIN;
      default:
        return null;
    }
  }
}
