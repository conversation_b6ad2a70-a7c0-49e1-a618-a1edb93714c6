/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 军队进度信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsArmyProgress implements org.apache.thrift.TBase<PsArmyProgress, PsArmyProgress._Fields>, java.io.Serializable, Cloneable, Comparable<PsArmyProgress> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsArmyProgress");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField CITY_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("cityId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField OWNER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("ownerId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("startTime", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField UPDATE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("updateTime", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField REMAIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("remainTime", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField ORIGINAL_TOTAL_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("originalTotalTime", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("type", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField WORK_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("workType", org.apache.thrift.protocol.TType.I32, (short)9);
  private static final org.apache.thrift.protocol.TField START_X_FIELD_DESC = new org.apache.thrift.protocol.TField("startX", org.apache.thrift.protocol.TType.I32, (short)10);
  private static final org.apache.thrift.protocol.TField START_Y_FIELD_DESC = new org.apache.thrift.protocol.TField("startY", org.apache.thrift.protocol.TType.I32, (short)11);
  private static final org.apache.thrift.protocol.TField END_X_FIELD_DESC = new org.apache.thrift.protocol.TField("endX", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField END_Y_FIELD_DESC = new org.apache.thrift.protocol.TField("endY", org.apache.thrift.protocol.TType.I32, (short)13);
  private static final org.apache.thrift.protocol.TField TARGET_X_FIELD_DESC = new org.apache.thrift.protocol.TField("targetX", org.apache.thrift.protocol.TType.I32, (short)14);
  private static final org.apache.thrift.protocol.TField TARGET_Y_FIELD_DESC = new org.apache.thrift.protocol.TField("targetY", org.apache.thrift.protocol.TType.I32, (short)15);
  private static final org.apache.thrift.protocol.TField HEAD_FIELD_DESC = new org.apache.thrift.protocol.TField("head", org.apache.thrift.protocol.TType.STRING, (short)16);
  private static final org.apache.thrift.protocol.TField CURR_EXPLOIT_AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("currExploitAmount", org.apache.thrift.protocol.TType.I64, (short)17);
  private static final org.apache.thrift.protocol.TField MAX_EXPLOIT_AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("maxExploitAmount", org.apache.thrift.protocol.TType.I64, (short)18);
  private static final org.apache.thrift.protocol.TField CURR_EXPLOIT_SPEED_FIELD_DESC = new org.apache.thrift.protocol.TField("currExploitSpeed", org.apache.thrift.protocol.TType.DOUBLE, (short)19);
  private static final org.apache.thrift.protocol.TField SPEED_RATIO_FIELD_DESC = new org.apache.thrift.protocol.TField("speedRatio", org.apache.thrift.protocol.TType.DOUBLE, (short)21);
  private static final org.apache.thrift.protocol.TField PARENT_ARMYOWNER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("parentArmyownerId", org.apache.thrift.protocol.TType.I64, (short)22);
  private static final org.apache.thrift.protocol.TField RES_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("resMetaId", org.apache.thrift.protocol.TType.I32, (short)27);
  private static final org.apache.thrift.protocol.TField TARGET_NODE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("targetNodeType", org.apache.thrift.protocol.TType.I32, (short)28);
  private static final org.apache.thrift.protocol.TField EXPLORE_HOUSE_GROUP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("exploreHouseGroupId", org.apache.thrift.protocol.TType.STRING, (short)29);
  private static final org.apache.thrift.protocol.TField TARGET_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("targetName", org.apache.thrift.protocol.TType.STRING, (short)30);
  private static final org.apache.thrift.protocol.TField RALLY_ARMY_WORK_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("rallyArmyWorkType", org.apache.thrift.protocol.TType.I32, (short)31);
  private static final org.apache.thrift.protocol.TField SOLDIERS_FIELD_DESC = new org.apache.thrift.protocol.TField("soldiers", org.apache.thrift.protocol.TType.LIST, (short)32);
  private static final org.apache.thrift.protocol.TField HEROS_FIELD_DESC = new org.apache.thrift.protocol.TField("heros", org.apache.thrift.protocol.TType.LIST, (short)33);
  private static final org.apache.thrift.protocol.TField START_POINT_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("startPointSize", org.apache.thrift.protocol.TType.STRUCT, (short)34);
  private static final org.apache.thrift.protocol.TField END_POINT_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("endPointSize", org.apache.thrift.protocol.TType.STRUCT, (short)35);
  private static final org.apache.thrift.protocol.TField ROLE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("roleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)36);
  private static final org.apache.thrift.protocol.TField IS_RALLY_HIDE_HERO_FIELD_DESC = new org.apache.thrift.protocol.TField("isRallyHideHero", org.apache.thrift.protocol.TType.BOOL, (short)37);
  private static final org.apache.thrift.protocol.TField CURRENCY_FIELD_DESC = new org.apache.thrift.protocol.TField("currency", org.apache.thrift.protocol.TType.I32, (short)38);
  private static final org.apache.thrift.protocol.TField BUILDING_FIELD_DESC = new org.apache.thrift.protocol.TField("building", org.apache.thrift.protocol.TType.BOOL, (short)39);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsArmyProgressStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsArmyProgressTupleSchemeFactory();

  /**
   * id
   */
  public long id; // required
  /**
   * 出发城市ID
   */
  public long cityId; // required
  /**
   * 部队所属OwnerID
   */
  public long ownerId; // required
  /**
   * 工作开始时间
   */
  public long startTime; // required
  /**
   * 更新时间(绝对时间)，毫秒
   */
  public long updateTime; // required
  /**
   * 工作剩余时间，毫秒
   */
  public long remainTime; // required
  /**
   * 初始总工作时间，毫秒
   */
  public long originalTotalTime; // required
  /**
   * 军队类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsArmyType type; // required
  /**
   * 工作类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyWorkType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsArmyWorkType workType; // required
  /**
   * 起始点x坐标
   */
  public int startX; // required
  /**
   * 起始点y坐标
   */
  public int startY; // required
  /**
   * 终点x坐标， 地格坐标
   */
  public int endX; // optional
  /**
   * 终点y坐标， 地格坐标
   */
  public int endY; // optional
  /**
   * 集结最终目的地
   */
  public int targetX; // optional
  public int targetY; // optional
  /**
   * 头像
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String head; // optional
  /**
   * 当前采集量
   */
  public long currExploitAmount; // optional
  /**
   * 最大采集量
   */
  public long maxExploitAmount; // optional
  /**
   * 当前采集速度(大地图上资源采集速度 每秒)
   */
  public double currExploitSpeed; // optional
  /**
   * 加速倍率 初始值为1
   */
  public double speedRatio; // optional
  /**
   * 隶属军队Id
   */
  public long parentArmyownerId; // optional
  public int resMetaId; // optional
  /**
   * 行军的最终目标点类型，（如果参与集结行军，目标点类型是集结行军的目标点类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsMapNodeType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsMapNodeType targetNodeType; // optional
  /**
   * 探索者营地groupId*
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String exploreHouseGroupId; // optional
  /**
   * 目标点名字、可能是多语言key，（驻防需要显示在哪里驻防）
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String targetName; // optional
  /**
   * 如果是参与集结行军，集结发起行军的 行军状态 workType *
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyWorkType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsArmyWorkType rallyArmyWorkType; // optional
  /**
   * 携带的士兵
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<PsSoldierInfo> soldiers; // optional
  /**
   * 携带的英雄
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> heros; // optional
  /**
   * 起始点在地图上占格子的大小
   */
  public @org.apache.thrift.annotation.Nullable PsPointSize startPointSize; // optional
  /**
   * 终点在地图上占格子的大小
   */
  public @org.apache.thrift.annotation.Nullable PsPointSize endPointSize; // optional
  /**
   * 玩家信息 *
   */
  public @org.apache.thrift.annotation.Nullable PsRoleInfo roleInfo; // optional
  /**
   * 是否隐藏车头信息
   */
  public boolean isRallyHideHero; // optional
  /**
   * 联盟资源中心的货币类型 *
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsCurrency
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsCurrency currency; // optional
  /**
   * 是否在修建中 *
   */
  public boolean building; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * id
     */
    ID((short)1, "id"),
    /**
     * 出发城市ID
     */
    CITY_ID((short)2, "cityId"),
    /**
     * 部队所属OwnerID
     */
    OWNER_ID((short)3, "ownerId"),
    /**
     * 工作开始时间
     */
    START_TIME((short)4, "startTime"),
    /**
     * 更新时间(绝对时间)，毫秒
     */
    UPDATE_TIME((short)5, "updateTime"),
    /**
     * 工作剩余时间，毫秒
     */
    REMAIN_TIME((short)6, "remainTime"),
    /**
     * 初始总工作时间，毫秒
     */
    ORIGINAL_TOTAL_TIME((short)7, "originalTotalTime"),
    /**
     * 军队类型
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsArmyType
     */
    TYPE((short)8, "type"),
    /**
     * 工作类型
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsArmyWorkType
     */
    WORK_TYPE((short)9, "workType"),
    /**
     * 起始点x坐标
     */
    START_X((short)10, "startX"),
    /**
     * 起始点y坐标
     */
    START_Y((short)11, "startY"),
    /**
     * 终点x坐标， 地格坐标
     */
    END_X((short)12, "endX"),
    /**
     * 终点y坐标， 地格坐标
     */
    END_Y((short)13, "endY"),
    /**
     * 集结最终目的地
     */
    TARGET_X((short)14, "targetX"),
    TARGET_Y((short)15, "targetY"),
    /**
     * 头像
     */
    HEAD((short)16, "head"),
    /**
     * 当前采集量
     */
    CURR_EXPLOIT_AMOUNT((short)17, "currExploitAmount"),
    /**
     * 最大采集量
     */
    MAX_EXPLOIT_AMOUNT((short)18, "maxExploitAmount"),
    /**
     * 当前采集速度(大地图上资源采集速度 每秒)
     */
    CURR_EXPLOIT_SPEED((short)19, "currExploitSpeed"),
    /**
     * 加速倍率 初始值为1
     */
    SPEED_RATIO((short)21, "speedRatio"),
    /**
     * 隶属军队Id
     */
    PARENT_ARMYOWNER_ID((short)22, "parentArmyownerId"),
    RES_META_ID((short)27, "resMetaId"),
    /**
     * 行军的最终目标点类型，（如果参与集结行军，目标点类型是集结行军的目标点类型
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsMapNodeType
     */
    TARGET_NODE_TYPE((short)28, "targetNodeType"),
    /**
     * 探索者营地groupId*
     */
    EXPLORE_HOUSE_GROUP_ID((short)29, "exploreHouseGroupId"),
    /**
     * 目标点名字、可能是多语言key，（驻防需要显示在哪里驻防）
     */
    TARGET_NAME((short)30, "targetName"),
    /**
     * 如果是参与集结行军，集结发起行军的 行军状态 workType *
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsArmyWorkType
     */
    RALLY_ARMY_WORK_TYPE((short)31, "rallyArmyWorkType"),
    /**
     * 携带的士兵
     */
    SOLDIERS((short)32, "soldiers"),
    /**
     * 携带的英雄
     */
    HEROS((short)33, "heros"),
    /**
     * 起始点在地图上占格子的大小
     */
    START_POINT_SIZE((short)34, "startPointSize"),
    /**
     * 终点在地图上占格子的大小
     */
    END_POINT_SIZE((short)35, "endPointSize"),
    /**
     * 玩家信息 *
     */
    ROLE_INFO((short)36, "roleInfo"),
    /**
     * 是否隐藏车头信息
     */
    IS_RALLY_HIDE_HERO((short)37, "isRallyHideHero"),
    /**
     * 联盟资源中心的货币类型 *
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsCurrency
     */
    CURRENCY((short)38, "currency"),
    /**
     * 是否在修建中 *
     */
    BUILDING((short)39, "building");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // CITY_ID
          return CITY_ID;
        case 3: // OWNER_ID
          return OWNER_ID;
        case 4: // START_TIME
          return START_TIME;
        case 5: // UPDATE_TIME
          return UPDATE_TIME;
        case 6: // REMAIN_TIME
          return REMAIN_TIME;
        case 7: // ORIGINAL_TOTAL_TIME
          return ORIGINAL_TOTAL_TIME;
        case 8: // TYPE
          return TYPE;
        case 9: // WORK_TYPE
          return WORK_TYPE;
        case 10: // START_X
          return START_X;
        case 11: // START_Y
          return START_Y;
        case 12: // END_X
          return END_X;
        case 13: // END_Y
          return END_Y;
        case 14: // TARGET_X
          return TARGET_X;
        case 15: // TARGET_Y
          return TARGET_Y;
        case 16: // HEAD
          return HEAD;
        case 17: // CURR_EXPLOIT_AMOUNT
          return CURR_EXPLOIT_AMOUNT;
        case 18: // MAX_EXPLOIT_AMOUNT
          return MAX_EXPLOIT_AMOUNT;
        case 19: // CURR_EXPLOIT_SPEED
          return CURR_EXPLOIT_SPEED;
        case 21: // SPEED_RATIO
          return SPEED_RATIO;
        case 22: // PARENT_ARMYOWNER_ID
          return PARENT_ARMYOWNER_ID;
        case 27: // RES_META_ID
          return RES_META_ID;
        case 28: // TARGET_NODE_TYPE
          return TARGET_NODE_TYPE;
        case 29: // EXPLORE_HOUSE_GROUP_ID
          return EXPLORE_HOUSE_GROUP_ID;
        case 30: // TARGET_NAME
          return TARGET_NAME;
        case 31: // RALLY_ARMY_WORK_TYPE
          return RALLY_ARMY_WORK_TYPE;
        case 32: // SOLDIERS
          return SOLDIERS;
        case 33: // HEROS
          return HEROS;
        case 34: // START_POINT_SIZE
          return START_POINT_SIZE;
        case 35: // END_POINT_SIZE
          return END_POINT_SIZE;
        case 36: // ROLE_INFO
          return ROLE_INFO;
        case 37: // IS_RALLY_HIDE_HERO
          return IS_RALLY_HIDE_HERO;
        case 38: // CURRENCY
          return CURRENCY;
        case 39: // BUILDING
          return BUILDING;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ID_ISSET_ID = 0;
  private static final int __CITYID_ISSET_ID = 1;
  private static final int __OWNERID_ISSET_ID = 2;
  private static final int __STARTTIME_ISSET_ID = 3;
  private static final int __UPDATETIME_ISSET_ID = 4;
  private static final int __REMAINTIME_ISSET_ID = 5;
  private static final int __ORIGINALTOTALTIME_ISSET_ID = 6;
  private static final int __STARTX_ISSET_ID = 7;
  private static final int __STARTY_ISSET_ID = 8;
  private static final int __ENDX_ISSET_ID = 9;
  private static final int __ENDY_ISSET_ID = 10;
  private static final int __TARGETX_ISSET_ID = 11;
  private static final int __TARGETY_ISSET_ID = 12;
  private static final int __CURREXPLOITAMOUNT_ISSET_ID = 13;
  private static final int __MAXEXPLOITAMOUNT_ISSET_ID = 14;
  private static final int __CURREXPLOITSPEED_ISSET_ID = 15;
  private static final int __SPEEDRATIO_ISSET_ID = 16;
  private static final int __PARENTARMYOWNERID_ISSET_ID = 17;
  private static final int __RESMETAID_ISSET_ID = 18;
  private static final int __ISRALLYHIDEHERO_ISSET_ID = 19;
  private static final int __BUILDING_ISSET_ID = 20;
  private int __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.END_X,_Fields.END_Y,_Fields.TARGET_X,_Fields.TARGET_Y,_Fields.HEAD,_Fields.CURR_EXPLOIT_AMOUNT,_Fields.MAX_EXPLOIT_AMOUNT,_Fields.CURR_EXPLOIT_SPEED,_Fields.SPEED_RATIO,_Fields.PARENT_ARMYOWNER_ID,_Fields.RES_META_ID,_Fields.TARGET_NODE_TYPE,_Fields.EXPLORE_HOUSE_GROUP_ID,_Fields.TARGET_NAME,_Fields.RALLY_ARMY_WORK_TYPE,_Fields.SOLDIERS,_Fields.HEROS,_Fields.START_POINT_SIZE,_Fields.END_POINT_SIZE,_Fields.ROLE_INFO,_Fields.IS_RALLY_HIDE_HERO,_Fields.CURRENCY,_Fields.BUILDING};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CITY_ID, new org.apache.thrift.meta_data.FieldMetaData("cityId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.OWNER_ID, new org.apache.thrift.meta_data.FieldMetaData("ownerId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.START_TIME, new org.apache.thrift.meta_data.FieldMetaData("startTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.UPDATE_TIME, new org.apache.thrift.meta_data.FieldMetaData("updateTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.REMAIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("remainTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ORIGINAL_TOTAL_TIME, new org.apache.thrift.meta_data.FieldMetaData("originalTotalTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TYPE, new org.apache.thrift.meta_data.FieldMetaData("type", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsArmyType.class)));
    tmpMap.put(_Fields.WORK_TYPE, new org.apache.thrift.meta_data.FieldMetaData("workType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsArmyWorkType.class)));
    tmpMap.put(_Fields.START_X, new org.apache.thrift.meta_data.FieldMetaData("startX", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.START_Y, new org.apache.thrift.meta_data.FieldMetaData("startY", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.END_X, new org.apache.thrift.meta_data.FieldMetaData("endX", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.END_Y, new org.apache.thrift.meta_data.FieldMetaData("endY", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TARGET_X, new org.apache.thrift.meta_data.FieldMetaData("targetX", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TARGET_Y, new org.apache.thrift.meta_data.FieldMetaData("targetY", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.HEAD, new org.apache.thrift.meta_data.FieldMetaData("head", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CURR_EXPLOIT_AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("currExploitAmount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MAX_EXPLOIT_AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("maxExploitAmount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CURR_EXPLOIT_SPEED, new org.apache.thrift.meta_data.FieldMetaData("currExploitSpeed", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.SPEED_RATIO, new org.apache.thrift.meta_data.FieldMetaData("speedRatio", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.PARENT_ARMYOWNER_ID, new org.apache.thrift.meta_data.FieldMetaData("parentArmyownerId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RES_META_ID, new org.apache.thrift.meta_data.FieldMetaData("resMetaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TARGET_NODE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("targetNodeType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsMapNodeType.class)));
    tmpMap.put(_Fields.EXPLORE_HOUSE_GROUP_ID, new org.apache.thrift.meta_data.FieldMetaData("exploreHouseGroupId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TARGET_NAME, new org.apache.thrift.meta_data.FieldMetaData("targetName", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RALLY_ARMY_WORK_TYPE, new org.apache.thrift.meta_data.FieldMetaData("rallyArmyWorkType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsArmyWorkType.class)));
    tmpMap.put(_Fields.SOLDIERS, new org.apache.thrift.meta_data.FieldMetaData("soldiers", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsSoldierInfo.class))));
    tmpMap.put(_Fields.HEROS, new org.apache.thrift.meta_data.FieldMetaData("heros", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.START_POINT_SIZE, new org.apache.thrift.meta_data.FieldMetaData("startPointSize", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "PsPointSize")));
    tmpMap.put(_Fields.END_POINT_SIZE, new org.apache.thrift.meta_data.FieldMetaData("endPointSize", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "PsPointSize")));
    tmpMap.put(_Fields.ROLE_INFO, new org.apache.thrift.meta_data.FieldMetaData("roleInfo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "PsRoleInfo")));
    tmpMap.put(_Fields.IS_RALLY_HIDE_HERO, new org.apache.thrift.meta_data.FieldMetaData("isRallyHideHero", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.CURRENCY, new org.apache.thrift.meta_data.FieldMetaData("currency", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsCurrency.class)));
    tmpMap.put(_Fields.BUILDING, new org.apache.thrift.meta_data.FieldMetaData("building", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsArmyProgress.class, metaDataMap);
  }

  public PsArmyProgress() {
  }

  public PsArmyProgress(
    long id,
    long cityId,
    long ownerId,
    long startTime,
    long updateTime,
    long remainTime,
    long originalTotalTime,
    com.lc.billion.icefire.protocol.constant.PsArmyType type,
    com.lc.billion.icefire.protocol.constant.PsArmyWorkType workType,
    int startX,
    int startY)
  {
    this();
    this.id = id;
    setIdIsSet(true);
    this.cityId = cityId;
    setCityIdIsSet(true);
    this.ownerId = ownerId;
    setOwnerIdIsSet(true);
    this.startTime = startTime;
    setStartTimeIsSet(true);
    this.updateTime = updateTime;
    setUpdateTimeIsSet(true);
    this.remainTime = remainTime;
    setRemainTimeIsSet(true);
    this.originalTotalTime = originalTotalTime;
    setOriginalTotalTimeIsSet(true);
    this.type = type;
    this.workType = workType;
    this.startX = startX;
    setStartXIsSet(true);
    this.startY = startY;
    setStartYIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsArmyProgress(PsArmyProgress other) {
    __isset_bitfield = other.__isset_bitfield;
    this.id = other.id;
    this.cityId = other.cityId;
    this.ownerId = other.ownerId;
    this.startTime = other.startTime;
    this.updateTime = other.updateTime;
    this.remainTime = other.remainTime;
    this.originalTotalTime = other.originalTotalTime;
    if (other.isSetType()) {
      this.type = other.type;
    }
    if (other.isSetWorkType()) {
      this.workType = other.workType;
    }
    this.startX = other.startX;
    this.startY = other.startY;
    this.endX = other.endX;
    this.endY = other.endY;
    this.targetX = other.targetX;
    this.targetY = other.targetY;
    if (other.isSetHead()) {
      this.head = other.head;
    }
    this.currExploitAmount = other.currExploitAmount;
    this.maxExploitAmount = other.maxExploitAmount;
    this.currExploitSpeed = other.currExploitSpeed;
    this.speedRatio = other.speedRatio;
    this.parentArmyownerId = other.parentArmyownerId;
    this.resMetaId = other.resMetaId;
    if (other.isSetTargetNodeType()) {
      this.targetNodeType = other.targetNodeType;
    }
    if (other.isSetExploreHouseGroupId()) {
      this.exploreHouseGroupId = other.exploreHouseGroupId;
    }
    if (other.isSetTargetName()) {
      this.targetName = other.targetName;
    }
    if (other.isSetRallyArmyWorkType()) {
      this.rallyArmyWorkType = other.rallyArmyWorkType;
    }
    if (other.isSetSoldiers()) {
      java.util.List<PsSoldierInfo> __this__soldiers = new java.util.ArrayList<PsSoldierInfo>(other.soldiers.size());
      for (PsSoldierInfo other_element : other.soldiers) {
        __this__soldiers.add(new PsSoldierInfo(other_element));
      }
      this.soldiers = __this__soldiers;
    }
    if (other.isSetHeros()) {
      java.util.List<java.lang.String> __this__heros = new java.util.ArrayList<java.lang.String>(other.heros);
      this.heros = __this__heros;
    }
    if (other.isSetStartPointSize()) {
      this.startPointSize = new PsPointSize(other.startPointSize);
    }
    if (other.isSetEndPointSize()) {
      this.endPointSize = new PsPointSize(other.endPointSize);
    }
    if (other.isSetRoleInfo()) {
      this.roleInfo = new PsRoleInfo(other.roleInfo);
    }
    this.isRallyHideHero = other.isRallyHideHero;
    if (other.isSetCurrency()) {
      this.currency = other.currency;
    }
    this.building = other.building;
  }

  public PsArmyProgress deepCopy() {
    return new PsArmyProgress(this);
  }

  @Override
  public void clear() {
    setIdIsSet(false);
    this.id = 0;
    setCityIdIsSet(false);
    this.cityId = 0;
    setOwnerIdIsSet(false);
    this.ownerId = 0;
    setStartTimeIsSet(false);
    this.startTime = 0;
    setUpdateTimeIsSet(false);
    this.updateTime = 0;
    setRemainTimeIsSet(false);
    this.remainTime = 0;
    setOriginalTotalTimeIsSet(false);
    this.originalTotalTime = 0;
    this.type = null;
    this.workType = null;
    setStartXIsSet(false);
    this.startX = 0;
    setStartYIsSet(false);
    this.startY = 0;
    setEndXIsSet(false);
    this.endX = 0;
    setEndYIsSet(false);
    this.endY = 0;
    setTargetXIsSet(false);
    this.targetX = 0;
    setTargetYIsSet(false);
    this.targetY = 0;
    this.head = null;
    setCurrExploitAmountIsSet(false);
    this.currExploitAmount = 0;
    setMaxExploitAmountIsSet(false);
    this.maxExploitAmount = 0;
    setCurrExploitSpeedIsSet(false);
    this.currExploitSpeed = 0.0;
    setSpeedRatioIsSet(false);
    this.speedRatio = 0.0;
    setParentArmyownerIdIsSet(false);
    this.parentArmyownerId = 0;
    setResMetaIdIsSet(false);
    this.resMetaId = 0;
    this.targetNodeType = null;
    this.exploreHouseGroupId = null;
    this.targetName = null;
    this.rallyArmyWorkType = null;
    this.soldiers = null;
    this.heros = null;
    this.startPointSize = null;
    this.endPointSize = null;
    this.roleInfo = null;
    setIsRallyHideHeroIsSet(false);
    this.isRallyHideHero = false;
    this.currency = null;
    setBuildingIsSet(false);
    this.building = false;
  }

  /**
   * id
   */
  public long getId() {
    return this.id;
  }

  /**
   * id
   */
  public PsArmyProgress setId(long id) {
    this.id = id;
    setIdIsSet(true);
    return this;
  }

  public void unsetId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
  }

  public void setIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
  }

  /**
   * 出发城市ID
   */
  public long getCityId() {
    return this.cityId;
  }

  /**
   * 出发城市ID
   */
  public PsArmyProgress setCityId(long cityId) {
    this.cityId = cityId;
    setCityIdIsSet(true);
    return this;
  }

  public void unsetCityId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CITYID_ISSET_ID);
  }

  /** Returns true if field cityId is set (has been assigned a value) and false otherwise */
  public boolean isSetCityId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CITYID_ISSET_ID);
  }

  public void setCityIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CITYID_ISSET_ID, value);
  }

  /**
   * 部队所属OwnerID
   */
  public long getOwnerId() {
    return this.ownerId;
  }

  /**
   * 部队所属OwnerID
   */
  public PsArmyProgress setOwnerId(long ownerId) {
    this.ownerId = ownerId;
    setOwnerIdIsSet(true);
    return this;
  }

  public void unsetOwnerId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __OWNERID_ISSET_ID);
  }

  /** Returns true if field ownerId is set (has been assigned a value) and false otherwise */
  public boolean isSetOwnerId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __OWNERID_ISSET_ID);
  }

  public void setOwnerIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __OWNERID_ISSET_ID, value);
  }

  /**
   * 工作开始时间
   */
  public long getStartTime() {
    return this.startTime;
  }

  /**
   * 工作开始时间
   */
  public PsArmyProgress setStartTime(long startTime) {
    this.startTime = startTime;
    setStartTimeIsSet(true);
    return this;
  }

  public void unsetStartTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  /** Returns true if field startTime is set (has been assigned a value) and false otherwise */
  public boolean isSetStartTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  public void setStartTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTTIME_ISSET_ID, value);
  }

  /**
   * 更新时间(绝对时间)，毫秒
   */
  public long getUpdateTime() {
    return this.updateTime;
  }

  /**
   * 更新时间(绝对时间)，毫秒
   */
  public PsArmyProgress setUpdateTime(long updateTime) {
    this.updateTime = updateTime;
    setUpdateTimeIsSet(true);
    return this;
  }

  public void unsetUpdateTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __UPDATETIME_ISSET_ID);
  }

  /** Returns true if field updateTime is set (has been assigned a value) and false otherwise */
  public boolean isSetUpdateTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __UPDATETIME_ISSET_ID);
  }

  public void setUpdateTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __UPDATETIME_ISSET_ID, value);
  }

  /**
   * 工作剩余时间，毫秒
   */
  public long getRemainTime() {
    return this.remainTime;
  }

  /**
   * 工作剩余时间，毫秒
   */
  public PsArmyProgress setRemainTime(long remainTime) {
    this.remainTime = remainTime;
    setRemainTimeIsSet(true);
    return this;
  }

  public void unsetRemainTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __REMAINTIME_ISSET_ID);
  }

  /** Returns true if field remainTime is set (has been assigned a value) and false otherwise */
  public boolean isSetRemainTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __REMAINTIME_ISSET_ID);
  }

  public void setRemainTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __REMAINTIME_ISSET_ID, value);
  }

  /**
   * 初始总工作时间，毫秒
   */
  public long getOriginalTotalTime() {
    return this.originalTotalTime;
  }

  /**
   * 初始总工作时间，毫秒
   */
  public PsArmyProgress setOriginalTotalTime(long originalTotalTime) {
    this.originalTotalTime = originalTotalTime;
    setOriginalTotalTimeIsSet(true);
    return this;
  }

  public void unsetOriginalTotalTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ORIGINALTOTALTIME_ISSET_ID);
  }

  /** Returns true if field originalTotalTime is set (has been assigned a value) and false otherwise */
  public boolean isSetOriginalTotalTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ORIGINALTOTALTIME_ISSET_ID);
  }

  public void setOriginalTotalTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ORIGINALTOTALTIME_ISSET_ID, value);
  }

  /**
   * 军队类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsArmyType getType() {
    return this.type;
  }

  /**
   * 军队类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyType
   */
  public PsArmyProgress setType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsArmyType type) {
    this.type = type;
    return this;
  }

  public void unsetType() {
    this.type = null;
  }

  /** Returns true if field type is set (has been assigned a value) and false otherwise */
  public boolean isSetType() {
    return this.type != null;
  }

  public void setTypeIsSet(boolean value) {
    if (!value) {
      this.type = null;
    }
  }

  /**
   * 工作类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyWorkType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsArmyWorkType getWorkType() {
    return this.workType;
  }

  /**
   * 工作类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyWorkType
   */
  public PsArmyProgress setWorkType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsArmyWorkType workType) {
    this.workType = workType;
    return this;
  }

  public void unsetWorkType() {
    this.workType = null;
  }

  /** Returns true if field workType is set (has been assigned a value) and false otherwise */
  public boolean isSetWorkType() {
    return this.workType != null;
  }

  public void setWorkTypeIsSet(boolean value) {
    if (!value) {
      this.workType = null;
    }
  }

  /**
   * 起始点x坐标
   */
  public int getStartX() {
    return this.startX;
  }

  /**
   * 起始点x坐标
   */
  public PsArmyProgress setStartX(int startX) {
    this.startX = startX;
    setStartXIsSet(true);
    return this;
  }

  public void unsetStartX() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTX_ISSET_ID);
  }

  /** Returns true if field startX is set (has been assigned a value) and false otherwise */
  public boolean isSetStartX() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTX_ISSET_ID);
  }

  public void setStartXIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTX_ISSET_ID, value);
  }

  /**
   * 起始点y坐标
   */
  public int getStartY() {
    return this.startY;
  }

  /**
   * 起始点y坐标
   */
  public PsArmyProgress setStartY(int startY) {
    this.startY = startY;
    setStartYIsSet(true);
    return this;
  }

  public void unsetStartY() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTY_ISSET_ID);
  }

  /** Returns true if field startY is set (has been assigned a value) and false otherwise */
  public boolean isSetStartY() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTY_ISSET_ID);
  }

  public void setStartYIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTY_ISSET_ID, value);
  }

  /**
   * 终点x坐标， 地格坐标
   */
  public int getEndX() {
    return this.endX;
  }

  /**
   * 终点x坐标， 地格坐标
   */
  public PsArmyProgress setEndX(int endX) {
    this.endX = endX;
    setEndXIsSet(true);
    return this;
  }

  public void unsetEndX() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ENDX_ISSET_ID);
  }

  /** Returns true if field endX is set (has been assigned a value) and false otherwise */
  public boolean isSetEndX() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ENDX_ISSET_ID);
  }

  public void setEndXIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ENDX_ISSET_ID, value);
  }

  /**
   * 终点y坐标， 地格坐标
   */
  public int getEndY() {
    return this.endY;
  }

  /**
   * 终点y坐标， 地格坐标
   */
  public PsArmyProgress setEndY(int endY) {
    this.endY = endY;
    setEndYIsSet(true);
    return this;
  }

  public void unsetEndY() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ENDY_ISSET_ID);
  }

  /** Returns true if field endY is set (has been assigned a value) and false otherwise */
  public boolean isSetEndY() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ENDY_ISSET_ID);
  }

  public void setEndYIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ENDY_ISSET_ID, value);
  }

  /**
   * 集结最终目的地
   */
  public int getTargetX() {
    return this.targetX;
  }

  /**
   * 集结最终目的地
   */
  public PsArmyProgress setTargetX(int targetX) {
    this.targetX = targetX;
    setTargetXIsSet(true);
    return this;
  }

  public void unsetTargetX() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TARGETX_ISSET_ID);
  }

  /** Returns true if field targetX is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetX() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TARGETX_ISSET_ID);
  }

  public void setTargetXIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TARGETX_ISSET_ID, value);
  }

  public int getTargetY() {
    return this.targetY;
  }

  public PsArmyProgress setTargetY(int targetY) {
    this.targetY = targetY;
    setTargetYIsSet(true);
    return this;
  }

  public void unsetTargetY() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TARGETY_ISSET_ID);
  }

  /** Returns true if field targetY is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetY() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TARGETY_ISSET_ID);
  }

  public void setTargetYIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TARGETY_ISSET_ID, value);
  }

  /**
   * 头像
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getHead() {
    return this.head;
  }

  /**
   * 头像
   */
  public PsArmyProgress setHead(@org.apache.thrift.annotation.Nullable java.lang.String head) {
    this.head = head;
    return this;
  }

  public void unsetHead() {
    this.head = null;
  }

  /** Returns true if field head is set (has been assigned a value) and false otherwise */
  public boolean isSetHead() {
    return this.head != null;
  }

  public void setHeadIsSet(boolean value) {
    if (!value) {
      this.head = null;
    }
  }

  /**
   * 当前采集量
   */
  public long getCurrExploitAmount() {
    return this.currExploitAmount;
  }

  /**
   * 当前采集量
   */
  public PsArmyProgress setCurrExploitAmount(long currExploitAmount) {
    this.currExploitAmount = currExploitAmount;
    setCurrExploitAmountIsSet(true);
    return this;
  }

  public void unsetCurrExploitAmount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CURREXPLOITAMOUNT_ISSET_ID);
  }

  /** Returns true if field currExploitAmount is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrExploitAmount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CURREXPLOITAMOUNT_ISSET_ID);
  }

  public void setCurrExploitAmountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CURREXPLOITAMOUNT_ISSET_ID, value);
  }

  /**
   * 最大采集量
   */
  public long getMaxExploitAmount() {
    return this.maxExploitAmount;
  }

  /**
   * 最大采集量
   */
  public PsArmyProgress setMaxExploitAmount(long maxExploitAmount) {
    this.maxExploitAmount = maxExploitAmount;
    setMaxExploitAmountIsSet(true);
    return this;
  }

  public void unsetMaxExploitAmount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MAXEXPLOITAMOUNT_ISSET_ID);
  }

  /** Returns true if field maxExploitAmount is set (has been assigned a value) and false otherwise */
  public boolean isSetMaxExploitAmount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MAXEXPLOITAMOUNT_ISSET_ID);
  }

  public void setMaxExploitAmountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MAXEXPLOITAMOUNT_ISSET_ID, value);
  }

  /**
   * 当前采集速度(大地图上资源采集速度 每秒)
   */
  public double getCurrExploitSpeed() {
    return this.currExploitSpeed;
  }

  /**
   * 当前采集速度(大地图上资源采集速度 每秒)
   */
  public PsArmyProgress setCurrExploitSpeed(double currExploitSpeed) {
    this.currExploitSpeed = currExploitSpeed;
    setCurrExploitSpeedIsSet(true);
    return this;
  }

  public void unsetCurrExploitSpeed() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CURREXPLOITSPEED_ISSET_ID);
  }

  /** Returns true if field currExploitSpeed is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrExploitSpeed() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CURREXPLOITSPEED_ISSET_ID);
  }

  public void setCurrExploitSpeedIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CURREXPLOITSPEED_ISSET_ID, value);
  }

  /**
   * 加速倍率 初始值为1
   */
  public double getSpeedRatio() {
    return this.speedRatio;
  }

  /**
   * 加速倍率 初始值为1
   */
  public PsArmyProgress setSpeedRatio(double speedRatio) {
    this.speedRatio = speedRatio;
    setSpeedRatioIsSet(true);
    return this;
  }

  public void unsetSpeedRatio() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SPEEDRATIO_ISSET_ID);
  }

  /** Returns true if field speedRatio is set (has been assigned a value) and false otherwise */
  public boolean isSetSpeedRatio() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SPEEDRATIO_ISSET_ID);
  }

  public void setSpeedRatioIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SPEEDRATIO_ISSET_ID, value);
  }

  /**
   * 隶属军队Id
   */
  public long getParentArmyownerId() {
    return this.parentArmyownerId;
  }

  /**
   * 隶属军队Id
   */
  public PsArmyProgress setParentArmyownerId(long parentArmyownerId) {
    this.parentArmyownerId = parentArmyownerId;
    setParentArmyownerIdIsSet(true);
    return this;
  }

  public void unsetParentArmyownerId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PARENTARMYOWNERID_ISSET_ID);
  }

  /** Returns true if field parentArmyownerId is set (has been assigned a value) and false otherwise */
  public boolean isSetParentArmyownerId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PARENTARMYOWNERID_ISSET_ID);
  }

  public void setParentArmyownerIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PARENTARMYOWNERID_ISSET_ID, value);
  }

  public int getResMetaId() {
    return this.resMetaId;
  }

  public PsArmyProgress setResMetaId(int resMetaId) {
    this.resMetaId = resMetaId;
    setResMetaIdIsSet(true);
    return this;
  }

  public void unsetResMetaId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RESMETAID_ISSET_ID);
  }

  /** Returns true if field resMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetResMetaId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RESMETAID_ISSET_ID);
  }

  public void setResMetaIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RESMETAID_ISSET_ID, value);
  }

  /**
   * 行军的最终目标点类型，（如果参与集结行军，目标点类型是集结行军的目标点类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsMapNodeType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsMapNodeType getTargetNodeType() {
    return this.targetNodeType;
  }

  /**
   * 行军的最终目标点类型，（如果参与集结行军，目标点类型是集结行军的目标点类型
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsMapNodeType
   */
  public PsArmyProgress setTargetNodeType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsMapNodeType targetNodeType) {
    this.targetNodeType = targetNodeType;
    return this;
  }

  public void unsetTargetNodeType() {
    this.targetNodeType = null;
  }

  /** Returns true if field targetNodeType is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetNodeType() {
    return this.targetNodeType != null;
  }

  public void setTargetNodeTypeIsSet(boolean value) {
    if (!value) {
      this.targetNodeType = null;
    }
  }

  /**
   * 探索者营地groupId*
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getExploreHouseGroupId() {
    return this.exploreHouseGroupId;
  }

  /**
   * 探索者营地groupId*
   */
  public PsArmyProgress setExploreHouseGroupId(@org.apache.thrift.annotation.Nullable java.lang.String exploreHouseGroupId) {
    this.exploreHouseGroupId = exploreHouseGroupId;
    return this;
  }

  public void unsetExploreHouseGroupId() {
    this.exploreHouseGroupId = null;
  }

  /** Returns true if field exploreHouseGroupId is set (has been assigned a value) and false otherwise */
  public boolean isSetExploreHouseGroupId() {
    return this.exploreHouseGroupId != null;
  }

  public void setExploreHouseGroupIdIsSet(boolean value) {
    if (!value) {
      this.exploreHouseGroupId = null;
    }
  }

  /**
   * 目标点名字、可能是多语言key，（驻防需要显示在哪里驻防）
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getTargetName() {
    return this.targetName;
  }

  /**
   * 目标点名字、可能是多语言key，（驻防需要显示在哪里驻防）
   */
  public PsArmyProgress setTargetName(@org.apache.thrift.annotation.Nullable java.lang.String targetName) {
    this.targetName = targetName;
    return this;
  }

  public void unsetTargetName() {
    this.targetName = null;
  }

  /** Returns true if field targetName is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetName() {
    return this.targetName != null;
  }

  public void setTargetNameIsSet(boolean value) {
    if (!value) {
      this.targetName = null;
    }
  }

  /**
   * 如果是参与集结行军，集结发起行军的 行军状态 workType *
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyWorkType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsArmyWorkType getRallyArmyWorkType() {
    return this.rallyArmyWorkType;
  }

  /**
   * 如果是参与集结行军，集结发起行军的 行军状态 workType *
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsArmyWorkType
   */
  public PsArmyProgress setRallyArmyWorkType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsArmyWorkType rallyArmyWorkType) {
    this.rallyArmyWorkType = rallyArmyWorkType;
    return this;
  }

  public void unsetRallyArmyWorkType() {
    this.rallyArmyWorkType = null;
  }

  /** Returns true if field rallyArmyWorkType is set (has been assigned a value) and false otherwise */
  public boolean isSetRallyArmyWorkType() {
    return this.rallyArmyWorkType != null;
  }

  public void setRallyArmyWorkTypeIsSet(boolean value) {
    if (!value) {
      this.rallyArmyWorkType = null;
    }
  }

  public int getSoldiersSize() {
    return (this.soldiers == null) ? 0 : this.soldiers.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<PsSoldierInfo> getSoldiersIterator() {
    return (this.soldiers == null) ? null : this.soldiers.iterator();
  }

  public void addToSoldiers(PsSoldierInfo elem) {
    if (this.soldiers == null) {
      this.soldiers = new java.util.ArrayList<PsSoldierInfo>();
    }
    this.soldiers.add(elem);
  }

  /**
   * 携带的士兵
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<PsSoldierInfo> getSoldiers() {
    return this.soldiers;
  }

  /**
   * 携带的士兵
   */
  public PsArmyProgress setSoldiers(@org.apache.thrift.annotation.Nullable java.util.List<PsSoldierInfo> soldiers) {
    this.soldiers = soldiers;
    return this;
  }

  public void unsetSoldiers() {
    this.soldiers = null;
  }

  /** Returns true if field soldiers is set (has been assigned a value) and false otherwise */
  public boolean isSetSoldiers() {
    return this.soldiers != null;
  }

  public void setSoldiersIsSet(boolean value) {
    if (!value) {
      this.soldiers = null;
    }
  }

  public int getHerosSize() {
    return (this.heros == null) ? 0 : this.heros.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.String> getHerosIterator() {
    return (this.heros == null) ? null : this.heros.iterator();
  }

  public void addToHeros(java.lang.String elem) {
    if (this.heros == null) {
      this.heros = new java.util.ArrayList<java.lang.String>();
    }
    this.heros.add(elem);
  }

  /**
   * 携带的英雄
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.String> getHeros() {
    return this.heros;
  }

  /**
   * 携带的英雄
   */
  public PsArmyProgress setHeros(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> heros) {
    this.heros = heros;
    return this;
  }

  public void unsetHeros() {
    this.heros = null;
  }

  /** Returns true if field heros is set (has been assigned a value) and false otherwise */
  public boolean isSetHeros() {
    return this.heros != null;
  }

  public void setHerosIsSet(boolean value) {
    if (!value) {
      this.heros = null;
    }
  }

  /**
   * 起始点在地图上占格子的大小
   */
  @org.apache.thrift.annotation.Nullable
  public PsPointSize getStartPointSize() {
    return this.startPointSize;
  }

  /**
   * 起始点在地图上占格子的大小
   */
  public PsArmyProgress setStartPointSize(@org.apache.thrift.annotation.Nullable PsPointSize startPointSize) {
    this.startPointSize = startPointSize;
    return this;
  }

  public void unsetStartPointSize() {
    this.startPointSize = null;
  }

  /** Returns true if field startPointSize is set (has been assigned a value) and false otherwise */
  public boolean isSetStartPointSize() {
    return this.startPointSize != null;
  }

  public void setStartPointSizeIsSet(boolean value) {
    if (!value) {
      this.startPointSize = null;
    }
  }

  /**
   * 终点在地图上占格子的大小
   */
  @org.apache.thrift.annotation.Nullable
  public PsPointSize getEndPointSize() {
    return this.endPointSize;
  }

  /**
   * 终点在地图上占格子的大小
   */
  public PsArmyProgress setEndPointSize(@org.apache.thrift.annotation.Nullable PsPointSize endPointSize) {
    this.endPointSize = endPointSize;
    return this;
  }

  public void unsetEndPointSize() {
    this.endPointSize = null;
  }

  /** Returns true if field endPointSize is set (has been assigned a value) and false otherwise */
  public boolean isSetEndPointSize() {
    return this.endPointSize != null;
  }

  public void setEndPointSizeIsSet(boolean value) {
    if (!value) {
      this.endPointSize = null;
    }
  }

  /**
   * 玩家信息 *
   */
  @org.apache.thrift.annotation.Nullable
  public PsRoleInfo getRoleInfo() {
    return this.roleInfo;
  }

  /**
   * 玩家信息 *
   */
  public PsArmyProgress setRoleInfo(@org.apache.thrift.annotation.Nullable PsRoleInfo roleInfo) {
    this.roleInfo = roleInfo;
    return this;
  }

  public void unsetRoleInfo() {
    this.roleInfo = null;
  }

  /** Returns true if field roleInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleInfo() {
    return this.roleInfo != null;
  }

  public void setRoleInfoIsSet(boolean value) {
    if (!value) {
      this.roleInfo = null;
    }
  }

  /**
   * 是否隐藏车头信息
   */
  public boolean isIsRallyHideHero() {
    return this.isRallyHideHero;
  }

  /**
   * 是否隐藏车头信息
   */
  public PsArmyProgress setIsRallyHideHero(boolean isRallyHideHero) {
    this.isRallyHideHero = isRallyHideHero;
    setIsRallyHideHeroIsSet(true);
    return this;
  }

  public void unsetIsRallyHideHero() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ISRALLYHIDEHERO_ISSET_ID);
  }

  /** Returns true if field isRallyHideHero is set (has been assigned a value) and false otherwise */
  public boolean isSetIsRallyHideHero() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ISRALLYHIDEHERO_ISSET_ID);
  }

  public void setIsRallyHideHeroIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ISRALLYHIDEHERO_ISSET_ID, value);
  }

  /**
   * 联盟资源中心的货币类型 *
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsCurrency
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsCurrency getCurrency() {
    return this.currency;
  }

  /**
   * 联盟资源中心的货币类型 *
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsCurrency
   */
  public PsArmyProgress setCurrency(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsCurrency currency) {
    this.currency = currency;
    return this;
  }

  public void unsetCurrency() {
    this.currency = null;
  }

  /** Returns true if field currency is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrency() {
    return this.currency != null;
  }

  public void setCurrencyIsSet(boolean value) {
    if (!value) {
      this.currency = null;
    }
  }

  /**
   * 是否在修建中 *
   */
  public boolean isBuilding() {
    return this.building;
  }

  /**
   * 是否在修建中 *
   */
  public PsArmyProgress setBuilding(boolean building) {
    this.building = building;
    setBuildingIsSet(true);
    return this;
  }

  public void unsetBuilding() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BUILDING_ISSET_ID);
  }

  /** Returns true if field building is set (has been assigned a value) and false otherwise */
  public boolean isSetBuilding() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BUILDING_ISSET_ID);
  }

  public void setBuildingIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BUILDING_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((java.lang.Long)value);
      }
      break;

    case CITY_ID:
      if (value == null) {
        unsetCityId();
      } else {
        setCityId((java.lang.Long)value);
      }
      break;

    case OWNER_ID:
      if (value == null) {
        unsetOwnerId();
      } else {
        setOwnerId((java.lang.Long)value);
      }
      break;

    case START_TIME:
      if (value == null) {
        unsetStartTime();
      } else {
        setStartTime((java.lang.Long)value);
      }
      break;

    case UPDATE_TIME:
      if (value == null) {
        unsetUpdateTime();
      } else {
        setUpdateTime((java.lang.Long)value);
      }
      break;

    case REMAIN_TIME:
      if (value == null) {
        unsetRemainTime();
      } else {
        setRemainTime((java.lang.Long)value);
      }
      break;

    case ORIGINAL_TOTAL_TIME:
      if (value == null) {
        unsetOriginalTotalTime();
      } else {
        setOriginalTotalTime((java.lang.Long)value);
      }
      break;

    case TYPE:
      if (value == null) {
        unsetType();
      } else {
        setType((com.lc.billion.icefire.protocol.constant.PsArmyType)value);
      }
      break;

    case WORK_TYPE:
      if (value == null) {
        unsetWorkType();
      } else {
        setWorkType((com.lc.billion.icefire.protocol.constant.PsArmyWorkType)value);
      }
      break;

    case START_X:
      if (value == null) {
        unsetStartX();
      } else {
        setStartX((java.lang.Integer)value);
      }
      break;

    case START_Y:
      if (value == null) {
        unsetStartY();
      } else {
        setStartY((java.lang.Integer)value);
      }
      break;

    case END_X:
      if (value == null) {
        unsetEndX();
      } else {
        setEndX((java.lang.Integer)value);
      }
      break;

    case END_Y:
      if (value == null) {
        unsetEndY();
      } else {
        setEndY((java.lang.Integer)value);
      }
      break;

    case TARGET_X:
      if (value == null) {
        unsetTargetX();
      } else {
        setTargetX((java.lang.Integer)value);
      }
      break;

    case TARGET_Y:
      if (value == null) {
        unsetTargetY();
      } else {
        setTargetY((java.lang.Integer)value);
      }
      break;

    case HEAD:
      if (value == null) {
        unsetHead();
      } else {
        setHead((java.lang.String)value);
      }
      break;

    case CURR_EXPLOIT_AMOUNT:
      if (value == null) {
        unsetCurrExploitAmount();
      } else {
        setCurrExploitAmount((java.lang.Long)value);
      }
      break;

    case MAX_EXPLOIT_AMOUNT:
      if (value == null) {
        unsetMaxExploitAmount();
      } else {
        setMaxExploitAmount((java.lang.Long)value);
      }
      break;

    case CURR_EXPLOIT_SPEED:
      if (value == null) {
        unsetCurrExploitSpeed();
      } else {
        setCurrExploitSpeed((java.lang.Double)value);
      }
      break;

    case SPEED_RATIO:
      if (value == null) {
        unsetSpeedRatio();
      } else {
        setSpeedRatio((java.lang.Double)value);
      }
      break;

    case PARENT_ARMYOWNER_ID:
      if (value == null) {
        unsetParentArmyownerId();
      } else {
        setParentArmyownerId((java.lang.Long)value);
      }
      break;

    case RES_META_ID:
      if (value == null) {
        unsetResMetaId();
      } else {
        setResMetaId((java.lang.Integer)value);
      }
      break;

    case TARGET_NODE_TYPE:
      if (value == null) {
        unsetTargetNodeType();
      } else {
        setTargetNodeType((com.lc.billion.icefire.protocol.constant.PsMapNodeType)value);
      }
      break;

    case EXPLORE_HOUSE_GROUP_ID:
      if (value == null) {
        unsetExploreHouseGroupId();
      } else {
        setExploreHouseGroupId((java.lang.String)value);
      }
      break;

    case TARGET_NAME:
      if (value == null) {
        unsetTargetName();
      } else {
        setTargetName((java.lang.String)value);
      }
      break;

    case RALLY_ARMY_WORK_TYPE:
      if (value == null) {
        unsetRallyArmyWorkType();
      } else {
        setRallyArmyWorkType((com.lc.billion.icefire.protocol.constant.PsArmyWorkType)value);
      }
      break;

    case SOLDIERS:
      if (value == null) {
        unsetSoldiers();
      } else {
        setSoldiers((java.util.List<PsSoldierInfo>)value);
      }
      break;

    case HEROS:
      if (value == null) {
        unsetHeros();
      } else {
        setHeros((java.util.List<java.lang.String>)value);
      }
      break;

    case START_POINT_SIZE:
      if (value == null) {
        unsetStartPointSize();
      } else {
        setStartPointSize((PsPointSize)value);
      }
      break;

    case END_POINT_SIZE:
      if (value == null) {
        unsetEndPointSize();
      } else {
        setEndPointSize((PsPointSize)value);
      }
      break;

    case ROLE_INFO:
      if (value == null) {
        unsetRoleInfo();
      } else {
        setRoleInfo((PsRoleInfo)value);
      }
      break;

    case IS_RALLY_HIDE_HERO:
      if (value == null) {
        unsetIsRallyHideHero();
      } else {
        setIsRallyHideHero((java.lang.Boolean)value);
      }
      break;

    case CURRENCY:
      if (value == null) {
        unsetCurrency();
      } else {
        setCurrency((com.lc.billion.icefire.protocol.constant.PsCurrency)value);
      }
      break;

    case BUILDING:
      if (value == null) {
        unsetBuilding();
      } else {
        setBuilding((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case CITY_ID:
      return getCityId();

    case OWNER_ID:
      return getOwnerId();

    case START_TIME:
      return getStartTime();

    case UPDATE_TIME:
      return getUpdateTime();

    case REMAIN_TIME:
      return getRemainTime();

    case ORIGINAL_TOTAL_TIME:
      return getOriginalTotalTime();

    case TYPE:
      return getType();

    case WORK_TYPE:
      return getWorkType();

    case START_X:
      return getStartX();

    case START_Y:
      return getStartY();

    case END_X:
      return getEndX();

    case END_Y:
      return getEndY();

    case TARGET_X:
      return getTargetX();

    case TARGET_Y:
      return getTargetY();

    case HEAD:
      return getHead();

    case CURR_EXPLOIT_AMOUNT:
      return getCurrExploitAmount();

    case MAX_EXPLOIT_AMOUNT:
      return getMaxExploitAmount();

    case CURR_EXPLOIT_SPEED:
      return getCurrExploitSpeed();

    case SPEED_RATIO:
      return getSpeedRatio();

    case PARENT_ARMYOWNER_ID:
      return getParentArmyownerId();

    case RES_META_ID:
      return getResMetaId();

    case TARGET_NODE_TYPE:
      return getTargetNodeType();

    case EXPLORE_HOUSE_GROUP_ID:
      return getExploreHouseGroupId();

    case TARGET_NAME:
      return getTargetName();

    case RALLY_ARMY_WORK_TYPE:
      return getRallyArmyWorkType();

    case SOLDIERS:
      return getSoldiers();

    case HEROS:
      return getHeros();

    case START_POINT_SIZE:
      return getStartPointSize();

    case END_POINT_SIZE:
      return getEndPointSize();

    case ROLE_INFO:
      return getRoleInfo();

    case IS_RALLY_HIDE_HERO:
      return isIsRallyHideHero();

    case CURRENCY:
      return getCurrency();

    case BUILDING:
      return isBuilding();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case CITY_ID:
      return isSetCityId();
    case OWNER_ID:
      return isSetOwnerId();
    case START_TIME:
      return isSetStartTime();
    case UPDATE_TIME:
      return isSetUpdateTime();
    case REMAIN_TIME:
      return isSetRemainTime();
    case ORIGINAL_TOTAL_TIME:
      return isSetOriginalTotalTime();
    case TYPE:
      return isSetType();
    case WORK_TYPE:
      return isSetWorkType();
    case START_X:
      return isSetStartX();
    case START_Y:
      return isSetStartY();
    case END_X:
      return isSetEndX();
    case END_Y:
      return isSetEndY();
    case TARGET_X:
      return isSetTargetX();
    case TARGET_Y:
      return isSetTargetY();
    case HEAD:
      return isSetHead();
    case CURR_EXPLOIT_AMOUNT:
      return isSetCurrExploitAmount();
    case MAX_EXPLOIT_AMOUNT:
      return isSetMaxExploitAmount();
    case CURR_EXPLOIT_SPEED:
      return isSetCurrExploitSpeed();
    case SPEED_RATIO:
      return isSetSpeedRatio();
    case PARENT_ARMYOWNER_ID:
      return isSetParentArmyownerId();
    case RES_META_ID:
      return isSetResMetaId();
    case TARGET_NODE_TYPE:
      return isSetTargetNodeType();
    case EXPLORE_HOUSE_GROUP_ID:
      return isSetExploreHouseGroupId();
    case TARGET_NAME:
      return isSetTargetName();
    case RALLY_ARMY_WORK_TYPE:
      return isSetRallyArmyWorkType();
    case SOLDIERS:
      return isSetSoldiers();
    case HEROS:
      return isSetHeros();
    case START_POINT_SIZE:
      return isSetStartPointSize();
    case END_POINT_SIZE:
      return isSetEndPointSize();
    case ROLE_INFO:
      return isSetRoleInfo();
    case IS_RALLY_HIDE_HERO:
      return isSetIsRallyHideHero();
    case CURRENCY:
      return isSetCurrency();
    case BUILDING:
      return isSetBuilding();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsArmyProgress)
      return this.equals((PsArmyProgress)that);
    return false;
  }

  public boolean equals(PsArmyProgress that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_id = true;
    boolean that_present_id = true;
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (this.id != that.id)
        return false;
    }

    boolean this_present_cityId = true;
    boolean that_present_cityId = true;
    if (this_present_cityId || that_present_cityId) {
      if (!(this_present_cityId && that_present_cityId))
        return false;
      if (this.cityId != that.cityId)
        return false;
    }

    boolean this_present_ownerId = true;
    boolean that_present_ownerId = true;
    if (this_present_ownerId || that_present_ownerId) {
      if (!(this_present_ownerId && that_present_ownerId))
        return false;
      if (this.ownerId != that.ownerId)
        return false;
    }

    boolean this_present_startTime = true;
    boolean that_present_startTime = true;
    if (this_present_startTime || that_present_startTime) {
      if (!(this_present_startTime && that_present_startTime))
        return false;
      if (this.startTime != that.startTime)
        return false;
    }

    boolean this_present_updateTime = true;
    boolean that_present_updateTime = true;
    if (this_present_updateTime || that_present_updateTime) {
      if (!(this_present_updateTime && that_present_updateTime))
        return false;
      if (this.updateTime != that.updateTime)
        return false;
    }

    boolean this_present_remainTime = true;
    boolean that_present_remainTime = true;
    if (this_present_remainTime || that_present_remainTime) {
      if (!(this_present_remainTime && that_present_remainTime))
        return false;
      if (this.remainTime != that.remainTime)
        return false;
    }

    boolean this_present_originalTotalTime = true;
    boolean that_present_originalTotalTime = true;
    if (this_present_originalTotalTime || that_present_originalTotalTime) {
      if (!(this_present_originalTotalTime && that_present_originalTotalTime))
        return false;
      if (this.originalTotalTime != that.originalTotalTime)
        return false;
    }

    boolean this_present_type = true && this.isSetType();
    boolean that_present_type = true && that.isSetType();
    if (this_present_type || that_present_type) {
      if (!(this_present_type && that_present_type))
        return false;
      if (!this.type.equals(that.type))
        return false;
    }

    boolean this_present_workType = true && this.isSetWorkType();
    boolean that_present_workType = true && that.isSetWorkType();
    if (this_present_workType || that_present_workType) {
      if (!(this_present_workType && that_present_workType))
        return false;
      if (!this.workType.equals(that.workType))
        return false;
    }

    boolean this_present_startX = true;
    boolean that_present_startX = true;
    if (this_present_startX || that_present_startX) {
      if (!(this_present_startX && that_present_startX))
        return false;
      if (this.startX != that.startX)
        return false;
    }

    boolean this_present_startY = true;
    boolean that_present_startY = true;
    if (this_present_startY || that_present_startY) {
      if (!(this_present_startY && that_present_startY))
        return false;
      if (this.startY != that.startY)
        return false;
    }

    boolean this_present_endX = true && this.isSetEndX();
    boolean that_present_endX = true && that.isSetEndX();
    if (this_present_endX || that_present_endX) {
      if (!(this_present_endX && that_present_endX))
        return false;
      if (this.endX != that.endX)
        return false;
    }

    boolean this_present_endY = true && this.isSetEndY();
    boolean that_present_endY = true && that.isSetEndY();
    if (this_present_endY || that_present_endY) {
      if (!(this_present_endY && that_present_endY))
        return false;
      if (this.endY != that.endY)
        return false;
    }

    boolean this_present_targetX = true && this.isSetTargetX();
    boolean that_present_targetX = true && that.isSetTargetX();
    if (this_present_targetX || that_present_targetX) {
      if (!(this_present_targetX && that_present_targetX))
        return false;
      if (this.targetX != that.targetX)
        return false;
    }

    boolean this_present_targetY = true && this.isSetTargetY();
    boolean that_present_targetY = true && that.isSetTargetY();
    if (this_present_targetY || that_present_targetY) {
      if (!(this_present_targetY && that_present_targetY))
        return false;
      if (this.targetY != that.targetY)
        return false;
    }

    boolean this_present_head = true && this.isSetHead();
    boolean that_present_head = true && that.isSetHead();
    if (this_present_head || that_present_head) {
      if (!(this_present_head && that_present_head))
        return false;
      if (!this.head.equals(that.head))
        return false;
    }

    boolean this_present_currExploitAmount = true && this.isSetCurrExploitAmount();
    boolean that_present_currExploitAmount = true && that.isSetCurrExploitAmount();
    if (this_present_currExploitAmount || that_present_currExploitAmount) {
      if (!(this_present_currExploitAmount && that_present_currExploitAmount))
        return false;
      if (this.currExploitAmount != that.currExploitAmount)
        return false;
    }

    boolean this_present_maxExploitAmount = true && this.isSetMaxExploitAmount();
    boolean that_present_maxExploitAmount = true && that.isSetMaxExploitAmount();
    if (this_present_maxExploitAmount || that_present_maxExploitAmount) {
      if (!(this_present_maxExploitAmount && that_present_maxExploitAmount))
        return false;
      if (this.maxExploitAmount != that.maxExploitAmount)
        return false;
    }

    boolean this_present_currExploitSpeed = true && this.isSetCurrExploitSpeed();
    boolean that_present_currExploitSpeed = true && that.isSetCurrExploitSpeed();
    if (this_present_currExploitSpeed || that_present_currExploitSpeed) {
      if (!(this_present_currExploitSpeed && that_present_currExploitSpeed))
        return false;
      if (this.currExploitSpeed != that.currExploitSpeed)
        return false;
    }

    boolean this_present_speedRatio = true && this.isSetSpeedRatio();
    boolean that_present_speedRatio = true && that.isSetSpeedRatio();
    if (this_present_speedRatio || that_present_speedRatio) {
      if (!(this_present_speedRatio && that_present_speedRatio))
        return false;
      if (this.speedRatio != that.speedRatio)
        return false;
    }

    boolean this_present_parentArmyownerId = true && this.isSetParentArmyownerId();
    boolean that_present_parentArmyownerId = true && that.isSetParentArmyownerId();
    if (this_present_parentArmyownerId || that_present_parentArmyownerId) {
      if (!(this_present_parentArmyownerId && that_present_parentArmyownerId))
        return false;
      if (this.parentArmyownerId != that.parentArmyownerId)
        return false;
    }

    boolean this_present_resMetaId = true && this.isSetResMetaId();
    boolean that_present_resMetaId = true && that.isSetResMetaId();
    if (this_present_resMetaId || that_present_resMetaId) {
      if (!(this_present_resMetaId && that_present_resMetaId))
        return false;
      if (this.resMetaId != that.resMetaId)
        return false;
    }

    boolean this_present_targetNodeType = true && this.isSetTargetNodeType();
    boolean that_present_targetNodeType = true && that.isSetTargetNodeType();
    if (this_present_targetNodeType || that_present_targetNodeType) {
      if (!(this_present_targetNodeType && that_present_targetNodeType))
        return false;
      if (!this.targetNodeType.equals(that.targetNodeType))
        return false;
    }

    boolean this_present_exploreHouseGroupId = true && this.isSetExploreHouseGroupId();
    boolean that_present_exploreHouseGroupId = true && that.isSetExploreHouseGroupId();
    if (this_present_exploreHouseGroupId || that_present_exploreHouseGroupId) {
      if (!(this_present_exploreHouseGroupId && that_present_exploreHouseGroupId))
        return false;
      if (!this.exploreHouseGroupId.equals(that.exploreHouseGroupId))
        return false;
    }

    boolean this_present_targetName = true && this.isSetTargetName();
    boolean that_present_targetName = true && that.isSetTargetName();
    if (this_present_targetName || that_present_targetName) {
      if (!(this_present_targetName && that_present_targetName))
        return false;
      if (!this.targetName.equals(that.targetName))
        return false;
    }

    boolean this_present_rallyArmyWorkType = true && this.isSetRallyArmyWorkType();
    boolean that_present_rallyArmyWorkType = true && that.isSetRallyArmyWorkType();
    if (this_present_rallyArmyWorkType || that_present_rallyArmyWorkType) {
      if (!(this_present_rallyArmyWorkType && that_present_rallyArmyWorkType))
        return false;
      if (!this.rallyArmyWorkType.equals(that.rallyArmyWorkType))
        return false;
    }

    boolean this_present_soldiers = true && this.isSetSoldiers();
    boolean that_present_soldiers = true && that.isSetSoldiers();
    if (this_present_soldiers || that_present_soldiers) {
      if (!(this_present_soldiers && that_present_soldiers))
        return false;
      if (!this.soldiers.equals(that.soldiers))
        return false;
    }

    boolean this_present_heros = true && this.isSetHeros();
    boolean that_present_heros = true && that.isSetHeros();
    if (this_present_heros || that_present_heros) {
      if (!(this_present_heros && that_present_heros))
        return false;
      if (!this.heros.equals(that.heros))
        return false;
    }

    boolean this_present_startPointSize = true && this.isSetStartPointSize();
    boolean that_present_startPointSize = true && that.isSetStartPointSize();
    if (this_present_startPointSize || that_present_startPointSize) {
      if (!(this_present_startPointSize && that_present_startPointSize))
        return false;
      if (!this.startPointSize.equals(that.startPointSize))
        return false;
    }

    boolean this_present_endPointSize = true && this.isSetEndPointSize();
    boolean that_present_endPointSize = true && that.isSetEndPointSize();
    if (this_present_endPointSize || that_present_endPointSize) {
      if (!(this_present_endPointSize && that_present_endPointSize))
        return false;
      if (!this.endPointSize.equals(that.endPointSize))
        return false;
    }

    boolean this_present_roleInfo = true && this.isSetRoleInfo();
    boolean that_present_roleInfo = true && that.isSetRoleInfo();
    if (this_present_roleInfo || that_present_roleInfo) {
      if (!(this_present_roleInfo && that_present_roleInfo))
        return false;
      if (!this.roleInfo.equals(that.roleInfo))
        return false;
    }

    boolean this_present_isRallyHideHero = true && this.isSetIsRallyHideHero();
    boolean that_present_isRallyHideHero = true && that.isSetIsRallyHideHero();
    if (this_present_isRallyHideHero || that_present_isRallyHideHero) {
      if (!(this_present_isRallyHideHero && that_present_isRallyHideHero))
        return false;
      if (this.isRallyHideHero != that.isRallyHideHero)
        return false;
    }

    boolean this_present_currency = true && this.isSetCurrency();
    boolean that_present_currency = true && that.isSetCurrency();
    if (this_present_currency || that_present_currency) {
      if (!(this_present_currency && that_present_currency))
        return false;
      if (!this.currency.equals(that.currency))
        return false;
    }

    boolean this_present_building = true && this.isSetBuilding();
    boolean that_present_building = true && that.isSetBuilding();
    if (this_present_building || that_present_building) {
      if (!(this_present_building && that_present_building))
        return false;
      if (this.building != that.building)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(id);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(cityId);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(ownerId);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(startTime);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(updateTime);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(remainTime);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(originalTotalTime);

    hashCode = hashCode * 8191 + ((isSetType()) ? 131071 : 524287);
    if (isSetType())
      hashCode = hashCode * 8191 + type.getValue();

    hashCode = hashCode * 8191 + ((isSetWorkType()) ? 131071 : 524287);
    if (isSetWorkType())
      hashCode = hashCode * 8191 + workType.getValue();

    hashCode = hashCode * 8191 + startX;

    hashCode = hashCode * 8191 + startY;

    hashCode = hashCode * 8191 + ((isSetEndX()) ? 131071 : 524287);
    if (isSetEndX())
      hashCode = hashCode * 8191 + endX;

    hashCode = hashCode * 8191 + ((isSetEndY()) ? 131071 : 524287);
    if (isSetEndY())
      hashCode = hashCode * 8191 + endY;

    hashCode = hashCode * 8191 + ((isSetTargetX()) ? 131071 : 524287);
    if (isSetTargetX())
      hashCode = hashCode * 8191 + targetX;

    hashCode = hashCode * 8191 + ((isSetTargetY()) ? 131071 : 524287);
    if (isSetTargetY())
      hashCode = hashCode * 8191 + targetY;

    hashCode = hashCode * 8191 + ((isSetHead()) ? 131071 : 524287);
    if (isSetHead())
      hashCode = hashCode * 8191 + head.hashCode();

    hashCode = hashCode * 8191 + ((isSetCurrExploitAmount()) ? 131071 : 524287);
    if (isSetCurrExploitAmount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(currExploitAmount);

    hashCode = hashCode * 8191 + ((isSetMaxExploitAmount()) ? 131071 : 524287);
    if (isSetMaxExploitAmount())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(maxExploitAmount);

    hashCode = hashCode * 8191 + ((isSetCurrExploitSpeed()) ? 131071 : 524287);
    if (isSetCurrExploitSpeed())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(currExploitSpeed);

    hashCode = hashCode * 8191 + ((isSetSpeedRatio()) ? 131071 : 524287);
    if (isSetSpeedRatio())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(speedRatio);

    hashCode = hashCode * 8191 + ((isSetParentArmyownerId()) ? 131071 : 524287);
    if (isSetParentArmyownerId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(parentArmyownerId);

    hashCode = hashCode * 8191 + ((isSetResMetaId()) ? 131071 : 524287);
    if (isSetResMetaId())
      hashCode = hashCode * 8191 + resMetaId;

    hashCode = hashCode * 8191 + ((isSetTargetNodeType()) ? 131071 : 524287);
    if (isSetTargetNodeType())
      hashCode = hashCode * 8191 + targetNodeType.getValue();

    hashCode = hashCode * 8191 + ((isSetExploreHouseGroupId()) ? 131071 : 524287);
    if (isSetExploreHouseGroupId())
      hashCode = hashCode * 8191 + exploreHouseGroupId.hashCode();

    hashCode = hashCode * 8191 + ((isSetTargetName()) ? 131071 : 524287);
    if (isSetTargetName())
      hashCode = hashCode * 8191 + targetName.hashCode();

    hashCode = hashCode * 8191 + ((isSetRallyArmyWorkType()) ? 131071 : 524287);
    if (isSetRallyArmyWorkType())
      hashCode = hashCode * 8191 + rallyArmyWorkType.getValue();

    hashCode = hashCode * 8191 + ((isSetSoldiers()) ? 131071 : 524287);
    if (isSetSoldiers())
      hashCode = hashCode * 8191 + soldiers.hashCode();

    hashCode = hashCode * 8191 + ((isSetHeros()) ? 131071 : 524287);
    if (isSetHeros())
      hashCode = hashCode * 8191 + heros.hashCode();

    hashCode = hashCode * 8191 + ((isSetStartPointSize()) ? 131071 : 524287);
    if (isSetStartPointSize())
      hashCode = hashCode * 8191 + startPointSize.hashCode();

    hashCode = hashCode * 8191 + ((isSetEndPointSize()) ? 131071 : 524287);
    if (isSetEndPointSize())
      hashCode = hashCode * 8191 + endPointSize.hashCode();

    hashCode = hashCode * 8191 + ((isSetRoleInfo()) ? 131071 : 524287);
    if (isSetRoleInfo())
      hashCode = hashCode * 8191 + roleInfo.hashCode();

    hashCode = hashCode * 8191 + ((isSetIsRallyHideHero()) ? 131071 : 524287);
    if (isSetIsRallyHideHero())
      hashCode = hashCode * 8191 + ((isRallyHideHero) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetCurrency()) ? 131071 : 524287);
    if (isSetCurrency())
      hashCode = hashCode * 8191 + currency.getValue();

    hashCode = hashCode * 8191 + ((isSetBuilding()) ? 131071 : 524287);
    if (isSetBuilding())
      hashCode = hashCode * 8191 + ((building) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(PsArmyProgress other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetId(), other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCityId(), other.isSetCityId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCityId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cityId, other.cityId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetOwnerId(), other.isSetOwnerId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOwnerId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ownerId, other.ownerId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartTime(), other.isSetStartTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startTime, other.startTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetUpdateTime(), other.isSetUpdateTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUpdateTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.updateTime, other.updateTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRemainTime(), other.isSetRemainTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRemainTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.remainTime, other.remainTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetOriginalTotalTime(), other.isSetOriginalTotalTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOriginalTotalTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.originalTotalTime, other.originalTotalTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetType(), other.isSetType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.type, other.type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetWorkType(), other.isSetWorkType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWorkType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.workType, other.workType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartX(), other.isSetStartX());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartX()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startX, other.startX);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartY(), other.isSetStartY());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartY()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startY, other.startY);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndX(), other.isSetEndX());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndX()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endX, other.endX);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndY(), other.isSetEndY());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndY()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endY, other.endY);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetX(), other.isSetTargetX());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetX()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetX, other.targetX);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetY(), other.isSetTargetY());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetY()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetY, other.targetY);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHead(), other.isSetHead());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHead()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.head, other.head);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCurrExploitAmount(), other.isSetCurrExploitAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrExploitAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currExploitAmount, other.currExploitAmount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMaxExploitAmount(), other.isSetMaxExploitAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMaxExploitAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.maxExploitAmount, other.maxExploitAmount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCurrExploitSpeed(), other.isSetCurrExploitSpeed());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrExploitSpeed()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currExploitSpeed, other.currExploitSpeed);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSpeedRatio(), other.isSetSpeedRatio());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSpeedRatio()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.speedRatio, other.speedRatio);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetParentArmyownerId(), other.isSetParentArmyownerId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetParentArmyownerId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.parentArmyownerId, other.parentArmyownerId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetResMetaId(), other.isSetResMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.resMetaId, other.resMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetNodeType(), other.isSetTargetNodeType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetNodeType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetNodeType, other.targetNodeType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetExploreHouseGroupId(), other.isSetExploreHouseGroupId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExploreHouseGroupId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.exploreHouseGroupId, other.exploreHouseGroupId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetName(), other.isSetTargetName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetName, other.targetName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRallyArmyWorkType(), other.isSetRallyArmyWorkType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRallyArmyWorkType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rallyArmyWorkType, other.rallyArmyWorkType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSoldiers(), other.isSetSoldiers());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSoldiers()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.soldiers, other.soldiers);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHeros(), other.isSetHeros());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeros()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.heros, other.heros);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartPointSize(), other.isSetStartPointSize());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartPointSize()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startPointSize, other.startPointSize);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndPointSize(), other.isSetEndPointSize());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndPointSize()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endPointSize, other.endPointSize);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRoleInfo(), other.isSetRoleInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleInfo, other.roleInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetIsRallyHideHero(), other.isSetIsRallyHideHero());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsRallyHideHero()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isRallyHideHero, other.isRallyHideHero);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCurrency(), other.isSetCurrency());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrency()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currency, other.currency);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBuilding(), other.isSetBuilding());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBuilding()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.building, other.building);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsArmyProgress(");
    boolean first = true;

    sb.append("id:");
    sb.append(this.id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("cityId:");
    sb.append(this.cityId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ownerId:");
    sb.append(this.ownerId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("startTime:");
    sb.append(this.startTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("updateTime:");
    sb.append(this.updateTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("remainTime:");
    sb.append(this.remainTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("originalTotalTime:");
    sb.append(this.originalTotalTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("type:");
    if (this.type == null) {
      sb.append("null");
    } else {
      sb.append(this.type);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("workType:");
    if (this.workType == null) {
      sb.append("null");
    } else {
      sb.append(this.workType);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("startX:");
    sb.append(this.startX);
    first = false;
    if (!first) sb.append(", ");
    sb.append("startY:");
    sb.append(this.startY);
    first = false;
    if (isSetEndX()) {
      if (!first) sb.append(", ");
      sb.append("endX:");
      sb.append(this.endX);
      first = false;
    }
    if (isSetEndY()) {
      if (!first) sb.append(", ");
      sb.append("endY:");
      sb.append(this.endY);
      first = false;
    }
    if (isSetTargetX()) {
      if (!first) sb.append(", ");
      sb.append("targetX:");
      sb.append(this.targetX);
      first = false;
    }
    if (isSetTargetY()) {
      if (!first) sb.append(", ");
      sb.append("targetY:");
      sb.append(this.targetY);
      first = false;
    }
    if (isSetHead()) {
      if (!first) sb.append(", ");
      sb.append("head:");
      if (this.head == null) {
        sb.append("null");
      } else {
        sb.append(this.head);
      }
      first = false;
    }
    if (isSetCurrExploitAmount()) {
      if (!first) sb.append(", ");
      sb.append("currExploitAmount:");
      sb.append(this.currExploitAmount);
      first = false;
    }
    if (isSetMaxExploitAmount()) {
      if (!first) sb.append(", ");
      sb.append("maxExploitAmount:");
      sb.append(this.maxExploitAmount);
      first = false;
    }
    if (isSetCurrExploitSpeed()) {
      if (!first) sb.append(", ");
      sb.append("currExploitSpeed:");
      sb.append(this.currExploitSpeed);
      first = false;
    }
    if (isSetSpeedRatio()) {
      if (!first) sb.append(", ");
      sb.append("speedRatio:");
      sb.append(this.speedRatio);
      first = false;
    }
    if (isSetParentArmyownerId()) {
      if (!first) sb.append(", ");
      sb.append("parentArmyownerId:");
      sb.append(this.parentArmyownerId);
      first = false;
    }
    if (isSetResMetaId()) {
      if (!first) sb.append(", ");
      sb.append("resMetaId:");
      sb.append(this.resMetaId);
      first = false;
    }
    if (isSetTargetNodeType()) {
      if (!first) sb.append(", ");
      sb.append("targetNodeType:");
      if (this.targetNodeType == null) {
        sb.append("null");
      } else {
        sb.append(this.targetNodeType);
      }
      first = false;
    }
    if (isSetExploreHouseGroupId()) {
      if (!first) sb.append(", ");
      sb.append("exploreHouseGroupId:");
      if (this.exploreHouseGroupId == null) {
        sb.append("null");
      } else {
        sb.append(this.exploreHouseGroupId);
      }
      first = false;
    }
    if (isSetTargetName()) {
      if (!first) sb.append(", ");
      sb.append("targetName:");
      if (this.targetName == null) {
        sb.append("null");
      } else {
        sb.append(this.targetName);
      }
      first = false;
    }
    if (isSetRallyArmyWorkType()) {
      if (!first) sb.append(", ");
      sb.append("rallyArmyWorkType:");
      if (this.rallyArmyWorkType == null) {
        sb.append("null");
      } else {
        sb.append(this.rallyArmyWorkType);
      }
      first = false;
    }
    if (isSetSoldiers()) {
      if (!first) sb.append(", ");
      sb.append("soldiers:");
      if (this.soldiers == null) {
        sb.append("null");
      } else {
        sb.append(this.soldiers);
      }
      first = false;
    }
    if (isSetHeros()) {
      if (!first) sb.append(", ");
      sb.append("heros:");
      if (this.heros == null) {
        sb.append("null");
      } else {
        sb.append(this.heros);
      }
      first = false;
    }
    if (isSetStartPointSize()) {
      if (!first) sb.append(", ");
      sb.append("startPointSize:");
      if (this.startPointSize == null) {
        sb.append("null");
      } else {
        sb.append(this.startPointSize);
      }
      first = false;
    }
    if (isSetEndPointSize()) {
      if (!first) sb.append(", ");
      sb.append("endPointSize:");
      if (this.endPointSize == null) {
        sb.append("null");
      } else {
        sb.append(this.endPointSize);
      }
      first = false;
    }
    if (isSetRoleInfo()) {
      if (!first) sb.append(", ");
      sb.append("roleInfo:");
      if (this.roleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.roleInfo);
      }
      first = false;
    }
    if (isSetIsRallyHideHero()) {
      if (!first) sb.append(", ");
      sb.append("isRallyHideHero:");
      sb.append(this.isRallyHideHero);
      first = false;
    }
    if (isSetCurrency()) {
      if (!first) sb.append(", ");
      sb.append("currency:");
      if (this.currency == null) {
        sb.append("null");
      } else {
        sb.append(this.currency);
      }
      first = false;
    }
    if (isSetBuilding()) {
      if (!first) sb.append(", ");
      sb.append("building:");
      sb.append(this.building);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'id' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'cityId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'ownerId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'startTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'updateTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'remainTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'originalTotalTime' because it's a primitive and you chose the non-beans generator.
    if (type == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'type' was not present! Struct: " + toString());
    }
    if (workType == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'workType' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'startX' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'startY' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsArmyProgressStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsArmyProgressStandardScheme getScheme() {
      return new PsArmyProgressStandardScheme();
    }
  }

  private static class PsArmyProgressStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsArmyProgress> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsArmyProgress struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.id = iprot.readI64();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // CITY_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.cityId = iprot.readI64();
              struct.setCityIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // OWNER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ownerId = iprot.readI64();
              struct.setOwnerIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // START_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.startTime = iprot.readI64();
              struct.setStartTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // UPDATE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.updateTime = iprot.readI64();
              struct.setUpdateTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // REMAIN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.remainTime = iprot.readI64();
              struct.setRemainTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // ORIGINAL_TOTAL_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.originalTotalTime = iprot.readI64();
              struct.setOriginalTotalTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.type = com.lc.billion.icefire.protocol.constant.PsArmyType.findByValue(iprot.readI32());
              struct.setTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // WORK_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.workType = com.lc.billion.icefire.protocol.constant.PsArmyWorkType.findByValue(iprot.readI32());
              struct.setWorkTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // START_X
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.startX = iprot.readI32();
              struct.setStartXIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // START_Y
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.startY = iprot.readI32();
              struct.setStartYIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // END_X
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.endX = iprot.readI32();
              struct.setEndXIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // END_Y
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.endY = iprot.readI32();
              struct.setEndYIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // TARGET_X
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.targetX = iprot.readI32();
              struct.setTargetXIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // TARGET_Y
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.targetY = iprot.readI32();
              struct.setTargetYIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // HEAD
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.head = iprot.readString();
              struct.setHeadIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // CURR_EXPLOIT_AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.currExploitAmount = iprot.readI64();
              struct.setCurrExploitAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 18: // MAX_EXPLOIT_AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.maxExploitAmount = iprot.readI64();
              struct.setMaxExploitAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 19: // CURR_EXPLOIT_SPEED
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.currExploitSpeed = iprot.readDouble();
              struct.setCurrExploitSpeedIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 21: // SPEED_RATIO
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.speedRatio = iprot.readDouble();
              struct.setSpeedRatioIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 22: // PARENT_ARMYOWNER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.parentArmyownerId = iprot.readI64();
              struct.setParentArmyownerIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 27: // RES_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.resMetaId = iprot.readI32();
              struct.setResMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 28: // TARGET_NODE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.targetNodeType = com.lc.billion.icefire.protocol.constant.PsMapNodeType.findByValue(iprot.readI32());
              struct.setTargetNodeTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 29: // EXPLORE_HOUSE_GROUP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.exploreHouseGroupId = iprot.readString();
              struct.setExploreHouseGroupIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 30: // TARGET_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.targetName = iprot.readString();
              struct.setTargetNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 31: // RALLY_ARMY_WORK_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rallyArmyWorkType = com.lc.billion.icefire.protocol.constant.PsArmyWorkType.findByValue(iprot.readI32());
              struct.setRallyArmyWorkTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 32: // SOLDIERS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.soldiers = new java.util.ArrayList<PsSoldierInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable PsSoldierInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new PsSoldierInfo();
                  _elem1.read(iprot);
                  struct.soldiers.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setSoldiersIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 33: // HEROS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.heros = new java.util.ArrayList<java.lang.String>(_list3.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = iprot.readString();
                  struct.heros.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setHerosIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 34: // START_POINT_SIZE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.startPointSize = new PsPointSize();
              struct.startPointSize.read(iprot);
              struct.setStartPointSizeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 35: // END_POINT_SIZE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.endPointSize = new PsPointSize();
              struct.endPointSize.read(iprot);
              struct.setEndPointSizeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 36: // ROLE_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.roleInfo = new PsRoleInfo();
              struct.roleInfo.read(iprot);
              struct.setRoleInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 37: // IS_RALLY_HIDE_HERO
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isRallyHideHero = iprot.readBool();
              struct.setIsRallyHideHeroIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 38: // CURRENCY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.currency = com.lc.billion.icefire.protocol.constant.PsCurrency.findByValue(iprot.readI32());
              struct.setCurrencyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 39: // BUILDING
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.building = iprot.readBool();
              struct.setBuildingIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'id' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCityId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'cityId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetOwnerId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ownerId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetStartTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'startTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetUpdateTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'updateTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetRemainTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'remainTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetOriginalTotalTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'originalTotalTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetStartX()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'startX' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetStartY()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'startY' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsArmyProgress struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ID_FIELD_DESC);
      oprot.writeI64(struct.id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CITY_ID_FIELD_DESC);
      oprot.writeI64(struct.cityId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(OWNER_ID_FIELD_DESC);
      oprot.writeI64(struct.ownerId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(START_TIME_FIELD_DESC);
      oprot.writeI64(struct.startTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(UPDATE_TIME_FIELD_DESC);
      oprot.writeI64(struct.updateTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(REMAIN_TIME_FIELD_DESC);
      oprot.writeI64(struct.remainTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ORIGINAL_TOTAL_TIME_FIELD_DESC);
      oprot.writeI64(struct.originalTotalTime);
      oprot.writeFieldEnd();
      if (struct.type != null) {
        oprot.writeFieldBegin(TYPE_FIELD_DESC);
        oprot.writeI32(struct.type.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.workType != null) {
        oprot.writeFieldBegin(WORK_TYPE_FIELD_DESC);
        oprot.writeI32(struct.workType.getValue());
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(START_X_FIELD_DESC);
      oprot.writeI32(struct.startX);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(START_Y_FIELD_DESC);
      oprot.writeI32(struct.startY);
      oprot.writeFieldEnd();
      if (struct.isSetEndX()) {
        oprot.writeFieldBegin(END_X_FIELD_DESC);
        oprot.writeI32(struct.endX);
        oprot.writeFieldEnd();
      }
      if (struct.isSetEndY()) {
        oprot.writeFieldBegin(END_Y_FIELD_DESC);
        oprot.writeI32(struct.endY);
        oprot.writeFieldEnd();
      }
      if (struct.isSetTargetX()) {
        oprot.writeFieldBegin(TARGET_X_FIELD_DESC);
        oprot.writeI32(struct.targetX);
        oprot.writeFieldEnd();
      }
      if (struct.isSetTargetY()) {
        oprot.writeFieldBegin(TARGET_Y_FIELD_DESC);
        oprot.writeI32(struct.targetY);
        oprot.writeFieldEnd();
      }
      if (struct.head != null) {
        if (struct.isSetHead()) {
          oprot.writeFieldBegin(HEAD_FIELD_DESC);
          oprot.writeString(struct.head);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetCurrExploitAmount()) {
        oprot.writeFieldBegin(CURR_EXPLOIT_AMOUNT_FIELD_DESC);
        oprot.writeI64(struct.currExploitAmount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetMaxExploitAmount()) {
        oprot.writeFieldBegin(MAX_EXPLOIT_AMOUNT_FIELD_DESC);
        oprot.writeI64(struct.maxExploitAmount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetCurrExploitSpeed()) {
        oprot.writeFieldBegin(CURR_EXPLOIT_SPEED_FIELD_DESC);
        oprot.writeDouble(struct.currExploitSpeed);
        oprot.writeFieldEnd();
      }
      if (struct.isSetSpeedRatio()) {
        oprot.writeFieldBegin(SPEED_RATIO_FIELD_DESC);
        oprot.writeDouble(struct.speedRatio);
        oprot.writeFieldEnd();
      }
      if (struct.isSetParentArmyownerId()) {
        oprot.writeFieldBegin(PARENT_ARMYOWNER_ID_FIELD_DESC);
        oprot.writeI64(struct.parentArmyownerId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetResMetaId()) {
        oprot.writeFieldBegin(RES_META_ID_FIELD_DESC);
        oprot.writeI32(struct.resMetaId);
        oprot.writeFieldEnd();
      }
      if (struct.targetNodeType != null) {
        if (struct.isSetTargetNodeType()) {
          oprot.writeFieldBegin(TARGET_NODE_TYPE_FIELD_DESC);
          oprot.writeI32(struct.targetNodeType.getValue());
          oprot.writeFieldEnd();
        }
      }
      if (struct.exploreHouseGroupId != null) {
        if (struct.isSetExploreHouseGroupId()) {
          oprot.writeFieldBegin(EXPLORE_HOUSE_GROUP_ID_FIELD_DESC);
          oprot.writeString(struct.exploreHouseGroupId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.targetName != null) {
        if (struct.isSetTargetName()) {
          oprot.writeFieldBegin(TARGET_NAME_FIELD_DESC);
          oprot.writeString(struct.targetName);
          oprot.writeFieldEnd();
        }
      }
      if (struct.rallyArmyWorkType != null) {
        if (struct.isSetRallyArmyWorkType()) {
          oprot.writeFieldBegin(RALLY_ARMY_WORK_TYPE_FIELD_DESC);
          oprot.writeI32(struct.rallyArmyWorkType.getValue());
          oprot.writeFieldEnd();
        }
      }
      if (struct.soldiers != null) {
        if (struct.isSetSoldiers()) {
          oprot.writeFieldBegin(SOLDIERS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.soldiers.size()));
            for (PsSoldierInfo _iter6 : struct.soldiers)
            {
              _iter6.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.heros != null) {
        if (struct.isSetHeros()) {
          oprot.writeFieldBegin(HEROS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.heros.size()));
            for (java.lang.String _iter7 : struct.heros)
            {
              oprot.writeString(_iter7);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.startPointSize != null) {
        if (struct.isSetStartPointSize()) {
          oprot.writeFieldBegin(START_POINT_SIZE_FIELD_DESC);
          struct.startPointSize.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.endPointSize != null) {
        if (struct.isSetEndPointSize()) {
          oprot.writeFieldBegin(END_POINT_SIZE_FIELD_DESC);
          struct.endPointSize.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.roleInfo != null) {
        if (struct.isSetRoleInfo()) {
          oprot.writeFieldBegin(ROLE_INFO_FIELD_DESC);
          struct.roleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetIsRallyHideHero()) {
        oprot.writeFieldBegin(IS_RALLY_HIDE_HERO_FIELD_DESC);
        oprot.writeBool(struct.isRallyHideHero);
        oprot.writeFieldEnd();
      }
      if (struct.currency != null) {
        if (struct.isSetCurrency()) {
          oprot.writeFieldBegin(CURRENCY_FIELD_DESC);
          oprot.writeI32(struct.currency.getValue());
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetBuilding()) {
        oprot.writeFieldBegin(BUILDING_FIELD_DESC);
        oprot.writeBool(struct.building);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsArmyProgressTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsArmyProgressTupleScheme getScheme() {
      return new PsArmyProgressTupleScheme();
    }
  }

  private static class PsArmyProgressTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsArmyProgress> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsArmyProgress struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.id);
      oprot.writeI64(struct.cityId);
      oprot.writeI64(struct.ownerId);
      oprot.writeI64(struct.startTime);
      oprot.writeI64(struct.updateTime);
      oprot.writeI64(struct.remainTime);
      oprot.writeI64(struct.originalTotalTime);
      oprot.writeI32(struct.type.getValue());
      oprot.writeI32(struct.workType.getValue());
      oprot.writeI32(struct.startX);
      oprot.writeI32(struct.startY);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetEndX()) {
        optionals.set(0);
      }
      if (struct.isSetEndY()) {
        optionals.set(1);
      }
      if (struct.isSetTargetX()) {
        optionals.set(2);
      }
      if (struct.isSetTargetY()) {
        optionals.set(3);
      }
      if (struct.isSetHead()) {
        optionals.set(4);
      }
      if (struct.isSetCurrExploitAmount()) {
        optionals.set(5);
      }
      if (struct.isSetMaxExploitAmount()) {
        optionals.set(6);
      }
      if (struct.isSetCurrExploitSpeed()) {
        optionals.set(7);
      }
      if (struct.isSetSpeedRatio()) {
        optionals.set(8);
      }
      if (struct.isSetParentArmyownerId()) {
        optionals.set(9);
      }
      if (struct.isSetResMetaId()) {
        optionals.set(10);
      }
      if (struct.isSetTargetNodeType()) {
        optionals.set(11);
      }
      if (struct.isSetExploreHouseGroupId()) {
        optionals.set(12);
      }
      if (struct.isSetTargetName()) {
        optionals.set(13);
      }
      if (struct.isSetRallyArmyWorkType()) {
        optionals.set(14);
      }
      if (struct.isSetSoldiers()) {
        optionals.set(15);
      }
      if (struct.isSetHeros()) {
        optionals.set(16);
      }
      if (struct.isSetStartPointSize()) {
        optionals.set(17);
      }
      if (struct.isSetEndPointSize()) {
        optionals.set(18);
      }
      if (struct.isSetRoleInfo()) {
        optionals.set(19);
      }
      if (struct.isSetIsRallyHideHero()) {
        optionals.set(20);
      }
      if (struct.isSetCurrency()) {
        optionals.set(21);
      }
      if (struct.isSetBuilding()) {
        optionals.set(22);
      }
      oprot.writeBitSet(optionals, 23);
      if (struct.isSetEndX()) {
        oprot.writeI32(struct.endX);
      }
      if (struct.isSetEndY()) {
        oprot.writeI32(struct.endY);
      }
      if (struct.isSetTargetX()) {
        oprot.writeI32(struct.targetX);
      }
      if (struct.isSetTargetY()) {
        oprot.writeI32(struct.targetY);
      }
      if (struct.isSetHead()) {
        oprot.writeString(struct.head);
      }
      if (struct.isSetCurrExploitAmount()) {
        oprot.writeI64(struct.currExploitAmount);
      }
      if (struct.isSetMaxExploitAmount()) {
        oprot.writeI64(struct.maxExploitAmount);
      }
      if (struct.isSetCurrExploitSpeed()) {
        oprot.writeDouble(struct.currExploitSpeed);
      }
      if (struct.isSetSpeedRatio()) {
        oprot.writeDouble(struct.speedRatio);
      }
      if (struct.isSetParentArmyownerId()) {
        oprot.writeI64(struct.parentArmyownerId);
      }
      if (struct.isSetResMetaId()) {
        oprot.writeI32(struct.resMetaId);
      }
      if (struct.isSetTargetNodeType()) {
        oprot.writeI32(struct.targetNodeType.getValue());
      }
      if (struct.isSetExploreHouseGroupId()) {
        oprot.writeString(struct.exploreHouseGroupId);
      }
      if (struct.isSetTargetName()) {
        oprot.writeString(struct.targetName);
      }
      if (struct.isSetRallyArmyWorkType()) {
        oprot.writeI32(struct.rallyArmyWorkType.getValue());
      }
      if (struct.isSetSoldiers()) {
        {
          oprot.writeI32(struct.soldiers.size());
          for (PsSoldierInfo _iter8 : struct.soldiers)
          {
            _iter8.write(oprot);
          }
        }
      }
      if (struct.isSetHeros()) {
        {
          oprot.writeI32(struct.heros.size());
          for (java.lang.String _iter9 : struct.heros)
          {
            oprot.writeString(_iter9);
          }
        }
      }
      if (struct.isSetStartPointSize()) {
        struct.startPointSize.write(oprot);
      }
      if (struct.isSetEndPointSize()) {
        struct.endPointSize.write(oprot);
      }
      if (struct.isSetRoleInfo()) {
        struct.roleInfo.write(oprot);
      }
      if (struct.isSetIsRallyHideHero()) {
        oprot.writeBool(struct.isRallyHideHero);
      }
      if (struct.isSetCurrency()) {
        oprot.writeI32(struct.currency.getValue());
      }
      if (struct.isSetBuilding()) {
        oprot.writeBool(struct.building);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsArmyProgress struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.id = iprot.readI64();
      struct.setIdIsSet(true);
      struct.cityId = iprot.readI64();
      struct.setCityIdIsSet(true);
      struct.ownerId = iprot.readI64();
      struct.setOwnerIdIsSet(true);
      struct.startTime = iprot.readI64();
      struct.setStartTimeIsSet(true);
      struct.updateTime = iprot.readI64();
      struct.setUpdateTimeIsSet(true);
      struct.remainTime = iprot.readI64();
      struct.setRemainTimeIsSet(true);
      struct.originalTotalTime = iprot.readI64();
      struct.setOriginalTotalTimeIsSet(true);
      struct.type = com.lc.billion.icefire.protocol.constant.PsArmyType.findByValue(iprot.readI32());
      struct.setTypeIsSet(true);
      struct.workType = com.lc.billion.icefire.protocol.constant.PsArmyWorkType.findByValue(iprot.readI32());
      struct.setWorkTypeIsSet(true);
      struct.startX = iprot.readI32();
      struct.setStartXIsSet(true);
      struct.startY = iprot.readI32();
      struct.setStartYIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(23);
      if (incoming.get(0)) {
        struct.endX = iprot.readI32();
        struct.setEndXIsSet(true);
      }
      if (incoming.get(1)) {
        struct.endY = iprot.readI32();
        struct.setEndYIsSet(true);
      }
      if (incoming.get(2)) {
        struct.targetX = iprot.readI32();
        struct.setTargetXIsSet(true);
      }
      if (incoming.get(3)) {
        struct.targetY = iprot.readI32();
        struct.setTargetYIsSet(true);
      }
      if (incoming.get(4)) {
        struct.head = iprot.readString();
        struct.setHeadIsSet(true);
      }
      if (incoming.get(5)) {
        struct.currExploitAmount = iprot.readI64();
        struct.setCurrExploitAmountIsSet(true);
      }
      if (incoming.get(6)) {
        struct.maxExploitAmount = iprot.readI64();
        struct.setMaxExploitAmountIsSet(true);
      }
      if (incoming.get(7)) {
        struct.currExploitSpeed = iprot.readDouble();
        struct.setCurrExploitSpeedIsSet(true);
      }
      if (incoming.get(8)) {
        struct.speedRatio = iprot.readDouble();
        struct.setSpeedRatioIsSet(true);
      }
      if (incoming.get(9)) {
        struct.parentArmyownerId = iprot.readI64();
        struct.setParentArmyownerIdIsSet(true);
      }
      if (incoming.get(10)) {
        struct.resMetaId = iprot.readI32();
        struct.setResMetaIdIsSet(true);
      }
      if (incoming.get(11)) {
        struct.targetNodeType = com.lc.billion.icefire.protocol.constant.PsMapNodeType.findByValue(iprot.readI32());
        struct.setTargetNodeTypeIsSet(true);
      }
      if (incoming.get(12)) {
        struct.exploreHouseGroupId = iprot.readString();
        struct.setExploreHouseGroupIdIsSet(true);
      }
      if (incoming.get(13)) {
        struct.targetName = iprot.readString();
        struct.setTargetNameIsSet(true);
      }
      if (incoming.get(14)) {
        struct.rallyArmyWorkType = com.lc.billion.icefire.protocol.constant.PsArmyWorkType.findByValue(iprot.readI32());
        struct.setRallyArmyWorkTypeIsSet(true);
      }
      if (incoming.get(15)) {
        {
          org.apache.thrift.protocol.TList _list10 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.soldiers = new java.util.ArrayList<PsSoldierInfo>(_list10.size);
          @org.apache.thrift.annotation.Nullable PsSoldierInfo _elem11;
          for (int _i12 = 0; _i12 < _list10.size; ++_i12)
          {
            _elem11 = new PsSoldierInfo();
            _elem11.read(iprot);
            struct.soldiers.add(_elem11);
          }
        }
        struct.setSoldiersIsSet(true);
      }
      if (incoming.get(16)) {
        {
          org.apache.thrift.protocol.TList _list13 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRING);
          struct.heros = new java.util.ArrayList<java.lang.String>(_list13.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _elem14;
          for (int _i15 = 0; _i15 < _list13.size; ++_i15)
          {
            _elem14 = iprot.readString();
            struct.heros.add(_elem14);
          }
        }
        struct.setHerosIsSet(true);
      }
      if (incoming.get(17)) {
        struct.startPointSize = new PsPointSize();
        struct.startPointSize.read(iprot);
        struct.setStartPointSizeIsSet(true);
      }
      if (incoming.get(18)) {
        struct.endPointSize = new PsPointSize();
        struct.endPointSize.read(iprot);
        struct.setEndPointSizeIsSet(true);
      }
      if (incoming.get(19)) {
        struct.roleInfo = new PsRoleInfo();
        struct.roleInfo.read(iprot);
        struct.setRoleInfoIsSet(true);
      }
      if (incoming.get(20)) {
        struct.isRallyHideHero = iprot.readBool();
        struct.setIsRallyHideHeroIsSet(true);
      }
      if (incoming.get(21)) {
        struct.currency = com.lc.billion.icefire.protocol.constant.PsCurrency.findByValue(iprot.readI32());
        struct.setCurrencyIsSet(true);
      }
      if (incoming.get(22)) {
        struct.building = iprot.readBool();
        struct.setBuildingIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

