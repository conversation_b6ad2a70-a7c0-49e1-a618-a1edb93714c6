/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 新手累充奖励信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsContinuousRechargeRewardInfo implements org.apache.thrift.TBase<PsContinuousRechargeRewardInfo, PsContinuousRechargeRewardInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsContinuousRechargeRewardInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsContinuousRechargeRewardInfo");

  private static final org.apache.thrift.protocol.TField META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("metaId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.BYTE, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsContinuousRechargeRewardInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsContinuousRechargeRewardInfoTupleSchemeFactory();

  /**
   * reward id *
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String metaId; // optional
  /**
   * 奖励状态,0代表不能领取，1代表可以领取，2代表已领取 *
   */
  public byte status; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * reward id *
     */
    META_ID((short)1, "metaId"),
    /**
     * 奖励状态,0代表不能领取，1代表可以领取，2代表已领取 *
     */
    STATUS((short)2, "status");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // META_ID
          return META_ID;
        case 2: // STATUS
          return STATUS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __STATUS_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.META_ID,_Fields.STATUS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.META_ID, new org.apache.thrift.meta_data.FieldMetaData("metaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BYTE)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsContinuousRechargeRewardInfo.class, metaDataMap);
  }

  public PsContinuousRechargeRewardInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsContinuousRechargeRewardInfo(PsContinuousRechargeRewardInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetMetaId()) {
      this.metaId = other.metaId;
    }
    this.status = other.status;
  }

  public PsContinuousRechargeRewardInfo deepCopy() {
    return new PsContinuousRechargeRewardInfo(this);
  }

  @Override
  public void clear() {
    this.metaId = null;
    setStatusIsSet(false);
    this.status = 0;
  }

  /**
   * reward id *
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getMetaId() {
    return this.metaId;
  }

  /**
   * reward id *
   */
  public PsContinuousRechargeRewardInfo setMetaId(@org.apache.thrift.annotation.Nullable java.lang.String metaId) {
    this.metaId = metaId;
    return this;
  }

  public void unsetMetaId() {
    this.metaId = null;
  }

  /** Returns true if field metaId is set (has been assigned a value) and false otherwise */
  public boolean isSetMetaId() {
    return this.metaId != null;
  }

  public void setMetaIdIsSet(boolean value) {
    if (!value) {
      this.metaId = null;
    }
  }

  /**
   * 奖励状态,0代表不能领取，1代表可以领取，2代表已领取 *
   */
  public byte getStatus() {
    return this.status;
  }

  /**
   * 奖励状态,0代表不能领取，1代表可以领取，2代表已领取 *
   */
  public PsContinuousRechargeRewardInfo setStatus(byte status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case META_ID:
      if (value == null) {
        unsetMetaId();
      } else {
        setMetaId((java.lang.String)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((java.lang.Byte)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case META_ID:
      return getMetaId();

    case STATUS:
      return getStatus();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case META_ID:
      return isSetMetaId();
    case STATUS:
      return isSetStatus();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsContinuousRechargeRewardInfo)
      return this.equals((PsContinuousRechargeRewardInfo)that);
    return false;
  }

  public boolean equals(PsContinuousRechargeRewardInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_metaId = true && this.isSetMetaId();
    boolean that_present_metaId = true && that.isSetMetaId();
    if (this_present_metaId || that_present_metaId) {
      if (!(this_present_metaId && that_present_metaId))
        return false;
      if (!this.metaId.equals(that.metaId))
        return false;
    }

    boolean this_present_status = true && this.isSetStatus();
    boolean that_present_status = true && that.isSetStatus();
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetMetaId()) ? 131071 : 524287);
    if (isSetMetaId())
      hashCode = hashCode * 8191 + metaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetStatus()) ? 131071 : 524287);
    if (isSetStatus())
      hashCode = hashCode * 8191 + (int) (status);

    return hashCode;
  }

  @Override
  public int compareTo(PsContinuousRechargeRewardInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetMetaId(), other.isSetMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.metaId, other.metaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStatus(), other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsContinuousRechargeRewardInfo(");
    boolean first = true;

    if (isSetMetaId()) {
      sb.append("metaId:");
      if (this.metaId == null) {
        sb.append("null");
      } else {
        sb.append(this.metaId);
      }
      first = false;
    }
    if (isSetStatus()) {
      if (!first) sb.append(", ");
      sb.append("status:");
      sb.append(this.status);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsContinuousRechargeRewardInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsContinuousRechargeRewardInfoStandardScheme getScheme() {
      return new PsContinuousRechargeRewardInfoStandardScheme();
    }
  }

  private static class PsContinuousRechargeRewardInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsContinuousRechargeRewardInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsContinuousRechargeRewardInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.metaId = iprot.readString();
              struct.setMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.BYTE) {
              struct.status = iprot.readByte();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsContinuousRechargeRewardInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.metaId != null) {
        if (struct.isSetMetaId()) {
          oprot.writeFieldBegin(META_ID_FIELD_DESC);
          oprot.writeString(struct.metaId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetStatus()) {
        oprot.writeFieldBegin(STATUS_FIELD_DESC);
        oprot.writeByte(struct.status);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsContinuousRechargeRewardInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsContinuousRechargeRewardInfoTupleScheme getScheme() {
      return new PsContinuousRechargeRewardInfoTupleScheme();
    }
  }

  private static class PsContinuousRechargeRewardInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsContinuousRechargeRewardInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsContinuousRechargeRewardInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetMetaId()) {
        optionals.set(0);
      }
      if (struct.isSetStatus()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetMetaId()) {
        oprot.writeString(struct.metaId);
      }
      if (struct.isSetStatus()) {
        oprot.writeByte(struct.status);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsContinuousRechargeRewardInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.metaId = iprot.readString();
        struct.setMetaIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.status = iprot.readByte();
        struct.setStatusIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

