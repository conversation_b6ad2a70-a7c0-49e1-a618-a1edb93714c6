/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * GVG联盟结算信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsGVGAllianceResultInfo implements org.apache.thrift.TBase<PsGVGAllianceResultInfo, PsGVGAllianceResultInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsGVGAllianceResultInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsGVGAllianceResultInfo");

  private static final org.apache.thrift.protocol.TField ALLIANCE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField SERVER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("serverId", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField ALLIANCE_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceName", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField ALLIANCE_ALIAS_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceAlias", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField ALLIANCE_FLAG_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceFlag", org.apache.thrift.protocol.TType.STRUCT, (short)5);
  private static final org.apache.thrift.protocol.TField RESULT_FIELD_DESC = new org.apache.thrift.protocol.TField("result", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField MEMBER_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("memberCount", org.apache.thrift.protocol.TType.I32, (short)7);
  private static final org.apache.thrift.protocol.TField TOTAL_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("totalScore", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField WU_CHAO_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("wuChaoScore", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField GATHER_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("gatherScore", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField GUAN_DU_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("guanDuTime", org.apache.thrift.protocol.TType.I64, (short)11);
  private static final org.apache.thrift.protocol.TField KILL_ENEMY_TOTAL_FIELD_DESC = new org.apache.thrift.protocol.TField("killEnemyTotal", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField BUILDING_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("buildingScore", org.apache.thrift.protocol.TType.I64, (short)13);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsGVGAllianceResultInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsGVGAllianceResultInfoTupleSchemeFactory();

  /**
   * allianceId
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String allianceId; // required
  public int serverId; // required
  public @org.apache.thrift.annotation.Nullable java.lang.String allianceName; // required
  public @org.apache.thrift.annotation.Nullable java.lang.String allianceAlias; // required
  public @org.apache.thrift.annotation.Nullable PsAllianceFlagInfo allianceFlag; // required
  /**
   * 是否胜利 0:胜利，1失败
   */
  public int result; // required
  /**
   * 联盟人数
   */
  public int memberCount; // required
  /**
   * 总积分
   */
  public long totalScore; // required
  /**
   * 乌巢
   */
  public long wuChaoScore; // required
  /**
   * 辎重车
   */
  public long gatherScore; // required
  /**
   * 官渡时长
   */
  public long guanDuTime; // required
  /**
   * 击败士兵
   */
  public int killEnemyTotal; // required
  /**
   * 占领建筑
   */
  public long buildingScore; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * allianceId
     */
    ALLIANCE_ID((short)1, "allianceId"),
    SERVER_ID((short)2, "serverId"),
    ALLIANCE_NAME((short)3, "allianceName"),
    ALLIANCE_ALIAS((short)4, "allianceAlias"),
    ALLIANCE_FLAG((short)5, "allianceFlag"),
    /**
     * 是否胜利 0:胜利，1失败
     */
    RESULT((short)6, "result"),
    /**
     * 联盟人数
     */
    MEMBER_COUNT((short)7, "memberCount"),
    /**
     * 总积分
     */
    TOTAL_SCORE((short)8, "totalScore"),
    /**
     * 乌巢
     */
    WU_CHAO_SCORE((short)9, "wuChaoScore"),
    /**
     * 辎重车
     */
    GATHER_SCORE((short)10, "gatherScore"),
    /**
     * 官渡时长
     */
    GUAN_DU_TIME((short)11, "guanDuTime"),
    /**
     * 击败士兵
     */
    KILL_ENEMY_TOTAL((short)12, "killEnemyTotal"),
    /**
     * 占领建筑
     */
    BUILDING_SCORE((short)13, "buildingScore");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ALLIANCE_ID
          return ALLIANCE_ID;
        case 2: // SERVER_ID
          return SERVER_ID;
        case 3: // ALLIANCE_NAME
          return ALLIANCE_NAME;
        case 4: // ALLIANCE_ALIAS
          return ALLIANCE_ALIAS;
        case 5: // ALLIANCE_FLAG
          return ALLIANCE_FLAG;
        case 6: // RESULT
          return RESULT;
        case 7: // MEMBER_COUNT
          return MEMBER_COUNT;
        case 8: // TOTAL_SCORE
          return TOTAL_SCORE;
        case 9: // WU_CHAO_SCORE
          return WU_CHAO_SCORE;
        case 10: // GATHER_SCORE
          return GATHER_SCORE;
        case 11: // GUAN_DU_TIME
          return GUAN_DU_TIME;
        case 12: // KILL_ENEMY_TOTAL
          return KILL_ENEMY_TOTAL;
        case 13: // BUILDING_SCORE
          return BUILDING_SCORE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SERVERID_ISSET_ID = 0;
  private static final int __RESULT_ISSET_ID = 1;
  private static final int __MEMBERCOUNT_ISSET_ID = 2;
  private static final int __TOTALSCORE_ISSET_ID = 3;
  private static final int __WUCHAOSCORE_ISSET_ID = 4;
  private static final int __GATHERSCORE_ISSET_ID = 5;
  private static final int __GUANDUTIME_ISSET_ID = 6;
  private static final int __KILLENEMYTOTAL_ISSET_ID = 7;
  private static final int __BUILDINGSCORE_ISSET_ID = 8;
  private short __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ALLIANCE_ID, new org.apache.thrift.meta_data.FieldMetaData("allianceId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SERVER_ID, new org.apache.thrift.meta_data.FieldMetaData("serverId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ALLIANCE_NAME, new org.apache.thrift.meta_data.FieldMetaData("allianceName", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ALLIANCE_ALIAS, new org.apache.thrift.meta_data.FieldMetaData("allianceAlias", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ALLIANCE_FLAG, new org.apache.thrift.meta_data.FieldMetaData("allianceFlag", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsAllianceFlagInfo.class)));
    tmpMap.put(_Fields.RESULT, new org.apache.thrift.meta_data.FieldMetaData("result", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MEMBER_COUNT, new org.apache.thrift.meta_data.FieldMetaData("memberCount", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TOTAL_SCORE, new org.apache.thrift.meta_data.FieldMetaData("totalScore", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.WU_CHAO_SCORE, new org.apache.thrift.meta_data.FieldMetaData("wuChaoScore", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.GATHER_SCORE, new org.apache.thrift.meta_data.FieldMetaData("gatherScore", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.GUAN_DU_TIME, new org.apache.thrift.meta_data.FieldMetaData("guanDuTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.KILL_ENEMY_TOTAL, new org.apache.thrift.meta_data.FieldMetaData("killEnemyTotal", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.BUILDING_SCORE, new org.apache.thrift.meta_data.FieldMetaData("buildingScore", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsGVGAllianceResultInfo.class, metaDataMap);
  }

  public PsGVGAllianceResultInfo() {
  }

  public PsGVGAllianceResultInfo(
    java.lang.String allianceId,
    int serverId,
    java.lang.String allianceName,
    java.lang.String allianceAlias,
    PsAllianceFlagInfo allianceFlag,
    int result,
    int memberCount,
    long totalScore,
    long wuChaoScore,
    long gatherScore,
    long guanDuTime,
    int killEnemyTotal,
    long buildingScore)
  {
    this();
    this.allianceId = allianceId;
    this.serverId = serverId;
    setServerIdIsSet(true);
    this.allianceName = allianceName;
    this.allianceAlias = allianceAlias;
    this.allianceFlag = allianceFlag;
    this.result = result;
    setResultIsSet(true);
    this.memberCount = memberCount;
    setMemberCountIsSet(true);
    this.totalScore = totalScore;
    setTotalScoreIsSet(true);
    this.wuChaoScore = wuChaoScore;
    setWuChaoScoreIsSet(true);
    this.gatherScore = gatherScore;
    setGatherScoreIsSet(true);
    this.guanDuTime = guanDuTime;
    setGuanDuTimeIsSet(true);
    this.killEnemyTotal = killEnemyTotal;
    setKillEnemyTotalIsSet(true);
    this.buildingScore = buildingScore;
    setBuildingScoreIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsGVGAllianceResultInfo(PsGVGAllianceResultInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetAllianceId()) {
      this.allianceId = other.allianceId;
    }
    this.serverId = other.serverId;
    if (other.isSetAllianceName()) {
      this.allianceName = other.allianceName;
    }
    if (other.isSetAllianceAlias()) {
      this.allianceAlias = other.allianceAlias;
    }
    if (other.isSetAllianceFlag()) {
      this.allianceFlag = new PsAllianceFlagInfo(other.allianceFlag);
    }
    this.result = other.result;
    this.memberCount = other.memberCount;
    this.totalScore = other.totalScore;
    this.wuChaoScore = other.wuChaoScore;
    this.gatherScore = other.gatherScore;
    this.guanDuTime = other.guanDuTime;
    this.killEnemyTotal = other.killEnemyTotal;
    this.buildingScore = other.buildingScore;
  }

  public PsGVGAllianceResultInfo deepCopy() {
    return new PsGVGAllianceResultInfo(this);
  }

  @Override
  public void clear() {
    this.allianceId = null;
    setServerIdIsSet(false);
    this.serverId = 0;
    this.allianceName = null;
    this.allianceAlias = null;
    this.allianceFlag = null;
    setResultIsSet(false);
    this.result = 0;
    setMemberCountIsSet(false);
    this.memberCount = 0;
    setTotalScoreIsSet(false);
    this.totalScore = 0;
    setWuChaoScoreIsSet(false);
    this.wuChaoScore = 0;
    setGatherScoreIsSet(false);
    this.gatherScore = 0;
    setGuanDuTimeIsSet(false);
    this.guanDuTime = 0;
    setKillEnemyTotalIsSet(false);
    this.killEnemyTotal = 0;
    setBuildingScoreIsSet(false);
    this.buildingScore = 0;
  }

  /**
   * allianceId
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getAllianceId() {
    return this.allianceId;
  }

  /**
   * allianceId
   */
  public PsGVGAllianceResultInfo setAllianceId(@org.apache.thrift.annotation.Nullable java.lang.String allianceId) {
    this.allianceId = allianceId;
    return this;
  }

  public void unsetAllianceId() {
    this.allianceId = null;
  }

  /** Returns true if field allianceId is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceId() {
    return this.allianceId != null;
  }

  public void setAllianceIdIsSet(boolean value) {
    if (!value) {
      this.allianceId = null;
    }
  }

  public int getServerId() {
    return this.serverId;
  }

  public PsGVGAllianceResultInfo setServerId(int serverId) {
    this.serverId = serverId;
    setServerIdIsSet(true);
    return this;
  }

  public void unsetServerId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SERVERID_ISSET_ID);
  }

  /** Returns true if field serverId is set (has been assigned a value) and false otherwise */
  public boolean isSetServerId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SERVERID_ISSET_ID);
  }

  public void setServerIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SERVERID_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getAllianceName() {
    return this.allianceName;
  }

  public PsGVGAllianceResultInfo setAllianceName(@org.apache.thrift.annotation.Nullable java.lang.String allianceName) {
    this.allianceName = allianceName;
    return this;
  }

  public void unsetAllianceName() {
    this.allianceName = null;
  }

  /** Returns true if field allianceName is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceName() {
    return this.allianceName != null;
  }

  public void setAllianceNameIsSet(boolean value) {
    if (!value) {
      this.allianceName = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getAllianceAlias() {
    return this.allianceAlias;
  }

  public PsGVGAllianceResultInfo setAllianceAlias(@org.apache.thrift.annotation.Nullable java.lang.String allianceAlias) {
    this.allianceAlias = allianceAlias;
    return this;
  }

  public void unsetAllianceAlias() {
    this.allianceAlias = null;
  }

  /** Returns true if field allianceAlias is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceAlias() {
    return this.allianceAlias != null;
  }

  public void setAllianceAliasIsSet(boolean value) {
    if (!value) {
      this.allianceAlias = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public PsAllianceFlagInfo getAllianceFlag() {
    return this.allianceFlag;
  }

  public PsGVGAllianceResultInfo setAllianceFlag(@org.apache.thrift.annotation.Nullable PsAllianceFlagInfo allianceFlag) {
    this.allianceFlag = allianceFlag;
    return this;
  }

  public void unsetAllianceFlag() {
    this.allianceFlag = null;
  }

  /** Returns true if field allianceFlag is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceFlag() {
    return this.allianceFlag != null;
  }

  public void setAllianceFlagIsSet(boolean value) {
    if (!value) {
      this.allianceFlag = null;
    }
  }

  /**
   * 是否胜利 0:胜利，1失败
   */
  public int getResult() {
    return this.result;
  }

  /**
   * 是否胜利 0:胜利，1失败
   */
  public PsGVGAllianceResultInfo setResult(int result) {
    this.result = result;
    setResultIsSet(true);
    return this;
  }

  public void unsetResult() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  /** Returns true if field result is set (has been assigned a value) and false otherwise */
  public boolean isSetResult() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  public void setResultIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RESULT_ISSET_ID, value);
  }

  /**
   * 联盟人数
   */
  public int getMemberCount() {
    return this.memberCount;
  }

  /**
   * 联盟人数
   */
  public PsGVGAllianceResultInfo setMemberCount(int memberCount) {
    this.memberCount = memberCount;
    setMemberCountIsSet(true);
    return this;
  }

  public void unsetMemberCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MEMBERCOUNT_ISSET_ID);
  }

  /** Returns true if field memberCount is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MEMBERCOUNT_ISSET_ID);
  }

  public void setMemberCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MEMBERCOUNT_ISSET_ID, value);
  }

  /**
   * 总积分
   */
  public long getTotalScore() {
    return this.totalScore;
  }

  /**
   * 总积分
   */
  public PsGVGAllianceResultInfo setTotalScore(long totalScore) {
    this.totalScore = totalScore;
    setTotalScoreIsSet(true);
    return this;
  }

  public void unsetTotalScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TOTALSCORE_ISSET_ID);
  }

  /** Returns true if field totalScore is set (has been assigned a value) and false otherwise */
  public boolean isSetTotalScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TOTALSCORE_ISSET_ID);
  }

  public void setTotalScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TOTALSCORE_ISSET_ID, value);
  }

  /**
   * 乌巢
   */
  public long getWuChaoScore() {
    return this.wuChaoScore;
  }

  /**
   * 乌巢
   */
  public PsGVGAllianceResultInfo setWuChaoScore(long wuChaoScore) {
    this.wuChaoScore = wuChaoScore;
    setWuChaoScoreIsSet(true);
    return this;
  }

  public void unsetWuChaoScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __WUCHAOSCORE_ISSET_ID);
  }

  /** Returns true if field wuChaoScore is set (has been assigned a value) and false otherwise */
  public boolean isSetWuChaoScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __WUCHAOSCORE_ISSET_ID);
  }

  public void setWuChaoScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __WUCHAOSCORE_ISSET_ID, value);
  }

  /**
   * 辎重车
   */
  public long getGatherScore() {
    return this.gatherScore;
  }

  /**
   * 辎重车
   */
  public PsGVGAllianceResultInfo setGatherScore(long gatherScore) {
    this.gatherScore = gatherScore;
    setGatherScoreIsSet(true);
    return this;
  }

  public void unsetGatherScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __GATHERSCORE_ISSET_ID);
  }

  /** Returns true if field gatherScore is set (has been assigned a value) and false otherwise */
  public boolean isSetGatherScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __GATHERSCORE_ISSET_ID);
  }

  public void setGatherScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __GATHERSCORE_ISSET_ID, value);
  }

  /**
   * 官渡时长
   */
  public long getGuanDuTime() {
    return this.guanDuTime;
  }

  /**
   * 官渡时长
   */
  public PsGVGAllianceResultInfo setGuanDuTime(long guanDuTime) {
    this.guanDuTime = guanDuTime;
    setGuanDuTimeIsSet(true);
    return this;
  }

  public void unsetGuanDuTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __GUANDUTIME_ISSET_ID);
  }

  /** Returns true if field guanDuTime is set (has been assigned a value) and false otherwise */
  public boolean isSetGuanDuTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __GUANDUTIME_ISSET_ID);
  }

  public void setGuanDuTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __GUANDUTIME_ISSET_ID, value);
  }

  /**
   * 击败士兵
   */
  public int getKillEnemyTotal() {
    return this.killEnemyTotal;
  }

  /**
   * 击败士兵
   */
  public PsGVGAllianceResultInfo setKillEnemyTotal(int killEnemyTotal) {
    this.killEnemyTotal = killEnemyTotal;
    setKillEnemyTotalIsSet(true);
    return this;
  }

  public void unsetKillEnemyTotal() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __KILLENEMYTOTAL_ISSET_ID);
  }

  /** Returns true if field killEnemyTotal is set (has been assigned a value) and false otherwise */
  public boolean isSetKillEnemyTotal() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __KILLENEMYTOTAL_ISSET_ID);
  }

  public void setKillEnemyTotalIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __KILLENEMYTOTAL_ISSET_ID, value);
  }

  /**
   * 占领建筑
   */
  public long getBuildingScore() {
    return this.buildingScore;
  }

  /**
   * 占领建筑
   */
  public PsGVGAllianceResultInfo setBuildingScore(long buildingScore) {
    this.buildingScore = buildingScore;
    setBuildingScoreIsSet(true);
    return this;
  }

  public void unsetBuildingScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BUILDINGSCORE_ISSET_ID);
  }

  /** Returns true if field buildingScore is set (has been assigned a value) and false otherwise */
  public boolean isSetBuildingScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BUILDINGSCORE_ISSET_ID);
  }

  public void setBuildingScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BUILDINGSCORE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ALLIANCE_ID:
      if (value == null) {
        unsetAllianceId();
      } else {
        setAllianceId((java.lang.String)value);
      }
      break;

    case SERVER_ID:
      if (value == null) {
        unsetServerId();
      } else {
        setServerId((java.lang.Integer)value);
      }
      break;

    case ALLIANCE_NAME:
      if (value == null) {
        unsetAllianceName();
      } else {
        setAllianceName((java.lang.String)value);
      }
      break;

    case ALLIANCE_ALIAS:
      if (value == null) {
        unsetAllianceAlias();
      } else {
        setAllianceAlias((java.lang.String)value);
      }
      break;

    case ALLIANCE_FLAG:
      if (value == null) {
        unsetAllianceFlag();
      } else {
        setAllianceFlag((PsAllianceFlagInfo)value);
      }
      break;

    case RESULT:
      if (value == null) {
        unsetResult();
      } else {
        setResult((java.lang.Integer)value);
      }
      break;

    case MEMBER_COUNT:
      if (value == null) {
        unsetMemberCount();
      } else {
        setMemberCount((java.lang.Integer)value);
      }
      break;

    case TOTAL_SCORE:
      if (value == null) {
        unsetTotalScore();
      } else {
        setTotalScore((java.lang.Long)value);
      }
      break;

    case WU_CHAO_SCORE:
      if (value == null) {
        unsetWuChaoScore();
      } else {
        setWuChaoScore((java.lang.Long)value);
      }
      break;

    case GATHER_SCORE:
      if (value == null) {
        unsetGatherScore();
      } else {
        setGatherScore((java.lang.Long)value);
      }
      break;

    case GUAN_DU_TIME:
      if (value == null) {
        unsetGuanDuTime();
      } else {
        setGuanDuTime((java.lang.Long)value);
      }
      break;

    case KILL_ENEMY_TOTAL:
      if (value == null) {
        unsetKillEnemyTotal();
      } else {
        setKillEnemyTotal((java.lang.Integer)value);
      }
      break;

    case BUILDING_SCORE:
      if (value == null) {
        unsetBuildingScore();
      } else {
        setBuildingScore((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ALLIANCE_ID:
      return getAllianceId();

    case SERVER_ID:
      return getServerId();

    case ALLIANCE_NAME:
      return getAllianceName();

    case ALLIANCE_ALIAS:
      return getAllianceAlias();

    case ALLIANCE_FLAG:
      return getAllianceFlag();

    case RESULT:
      return getResult();

    case MEMBER_COUNT:
      return getMemberCount();

    case TOTAL_SCORE:
      return getTotalScore();

    case WU_CHAO_SCORE:
      return getWuChaoScore();

    case GATHER_SCORE:
      return getGatherScore();

    case GUAN_DU_TIME:
      return getGuanDuTime();

    case KILL_ENEMY_TOTAL:
      return getKillEnemyTotal();

    case BUILDING_SCORE:
      return getBuildingScore();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ALLIANCE_ID:
      return isSetAllianceId();
    case SERVER_ID:
      return isSetServerId();
    case ALLIANCE_NAME:
      return isSetAllianceName();
    case ALLIANCE_ALIAS:
      return isSetAllianceAlias();
    case ALLIANCE_FLAG:
      return isSetAllianceFlag();
    case RESULT:
      return isSetResult();
    case MEMBER_COUNT:
      return isSetMemberCount();
    case TOTAL_SCORE:
      return isSetTotalScore();
    case WU_CHAO_SCORE:
      return isSetWuChaoScore();
    case GATHER_SCORE:
      return isSetGatherScore();
    case GUAN_DU_TIME:
      return isSetGuanDuTime();
    case KILL_ENEMY_TOTAL:
      return isSetKillEnemyTotal();
    case BUILDING_SCORE:
      return isSetBuildingScore();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsGVGAllianceResultInfo)
      return this.equals((PsGVGAllianceResultInfo)that);
    return false;
  }

  public boolean equals(PsGVGAllianceResultInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_allianceId = true && this.isSetAllianceId();
    boolean that_present_allianceId = true && that.isSetAllianceId();
    if (this_present_allianceId || that_present_allianceId) {
      if (!(this_present_allianceId && that_present_allianceId))
        return false;
      if (!this.allianceId.equals(that.allianceId))
        return false;
    }

    boolean this_present_serverId = true;
    boolean that_present_serverId = true;
    if (this_present_serverId || that_present_serverId) {
      if (!(this_present_serverId && that_present_serverId))
        return false;
      if (this.serverId != that.serverId)
        return false;
    }

    boolean this_present_allianceName = true && this.isSetAllianceName();
    boolean that_present_allianceName = true && that.isSetAllianceName();
    if (this_present_allianceName || that_present_allianceName) {
      if (!(this_present_allianceName && that_present_allianceName))
        return false;
      if (!this.allianceName.equals(that.allianceName))
        return false;
    }

    boolean this_present_allianceAlias = true && this.isSetAllianceAlias();
    boolean that_present_allianceAlias = true && that.isSetAllianceAlias();
    if (this_present_allianceAlias || that_present_allianceAlias) {
      if (!(this_present_allianceAlias && that_present_allianceAlias))
        return false;
      if (!this.allianceAlias.equals(that.allianceAlias))
        return false;
    }

    boolean this_present_allianceFlag = true && this.isSetAllianceFlag();
    boolean that_present_allianceFlag = true && that.isSetAllianceFlag();
    if (this_present_allianceFlag || that_present_allianceFlag) {
      if (!(this_present_allianceFlag && that_present_allianceFlag))
        return false;
      if (!this.allianceFlag.equals(that.allianceFlag))
        return false;
    }

    boolean this_present_result = true;
    boolean that_present_result = true;
    if (this_present_result || that_present_result) {
      if (!(this_present_result && that_present_result))
        return false;
      if (this.result != that.result)
        return false;
    }

    boolean this_present_memberCount = true;
    boolean that_present_memberCount = true;
    if (this_present_memberCount || that_present_memberCount) {
      if (!(this_present_memberCount && that_present_memberCount))
        return false;
      if (this.memberCount != that.memberCount)
        return false;
    }

    boolean this_present_totalScore = true;
    boolean that_present_totalScore = true;
    if (this_present_totalScore || that_present_totalScore) {
      if (!(this_present_totalScore && that_present_totalScore))
        return false;
      if (this.totalScore != that.totalScore)
        return false;
    }

    boolean this_present_wuChaoScore = true;
    boolean that_present_wuChaoScore = true;
    if (this_present_wuChaoScore || that_present_wuChaoScore) {
      if (!(this_present_wuChaoScore && that_present_wuChaoScore))
        return false;
      if (this.wuChaoScore != that.wuChaoScore)
        return false;
    }

    boolean this_present_gatherScore = true;
    boolean that_present_gatherScore = true;
    if (this_present_gatherScore || that_present_gatherScore) {
      if (!(this_present_gatherScore && that_present_gatherScore))
        return false;
      if (this.gatherScore != that.gatherScore)
        return false;
    }

    boolean this_present_guanDuTime = true;
    boolean that_present_guanDuTime = true;
    if (this_present_guanDuTime || that_present_guanDuTime) {
      if (!(this_present_guanDuTime && that_present_guanDuTime))
        return false;
      if (this.guanDuTime != that.guanDuTime)
        return false;
    }

    boolean this_present_killEnemyTotal = true;
    boolean that_present_killEnemyTotal = true;
    if (this_present_killEnemyTotal || that_present_killEnemyTotal) {
      if (!(this_present_killEnemyTotal && that_present_killEnemyTotal))
        return false;
      if (this.killEnemyTotal != that.killEnemyTotal)
        return false;
    }

    boolean this_present_buildingScore = true;
    boolean that_present_buildingScore = true;
    if (this_present_buildingScore || that_present_buildingScore) {
      if (!(this_present_buildingScore && that_present_buildingScore))
        return false;
      if (this.buildingScore != that.buildingScore)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetAllianceId()) ? 131071 : 524287);
    if (isSetAllianceId())
      hashCode = hashCode * 8191 + allianceId.hashCode();

    hashCode = hashCode * 8191 + serverId;

    hashCode = hashCode * 8191 + ((isSetAllianceName()) ? 131071 : 524287);
    if (isSetAllianceName())
      hashCode = hashCode * 8191 + allianceName.hashCode();

    hashCode = hashCode * 8191 + ((isSetAllianceAlias()) ? 131071 : 524287);
    if (isSetAllianceAlias())
      hashCode = hashCode * 8191 + allianceAlias.hashCode();

    hashCode = hashCode * 8191 + ((isSetAllianceFlag()) ? 131071 : 524287);
    if (isSetAllianceFlag())
      hashCode = hashCode * 8191 + allianceFlag.hashCode();

    hashCode = hashCode * 8191 + result;

    hashCode = hashCode * 8191 + memberCount;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(totalScore);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(wuChaoScore);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(gatherScore);

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(guanDuTime);

    hashCode = hashCode * 8191 + killEnemyTotal;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(buildingScore);

    return hashCode;
  }

  @Override
  public int compareTo(PsGVGAllianceResultInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetAllianceId(), other.isSetAllianceId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceId, other.allianceId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetServerId(), other.isSetServerId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetServerId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.serverId, other.serverId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAllianceName(), other.isSetAllianceName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceName, other.allianceName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAllianceAlias(), other.isSetAllianceAlias());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceAlias()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceAlias, other.allianceAlias);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAllianceFlag(), other.isSetAllianceFlag());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceFlag()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceFlag, other.allianceFlag);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetResult(), other.isSetResult());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResult()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.result, other.result);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMemberCount(), other.isSetMemberCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberCount, other.memberCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTotalScore(), other.isSetTotalScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTotalScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.totalScore, other.totalScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetWuChaoScore(), other.isSetWuChaoScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWuChaoScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.wuChaoScore, other.wuChaoScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetGatherScore(), other.isSetGatherScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGatherScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.gatherScore, other.gatherScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetGuanDuTime(), other.isSetGuanDuTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGuanDuTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.guanDuTime, other.guanDuTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetKillEnemyTotal(), other.isSetKillEnemyTotal());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKillEnemyTotal()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.killEnemyTotal, other.killEnemyTotal);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBuildingScore(), other.isSetBuildingScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBuildingScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.buildingScore, other.buildingScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsGVGAllianceResultInfo(");
    boolean first = true;

    sb.append("allianceId:");
    if (this.allianceId == null) {
      sb.append("null");
    } else {
      sb.append(this.allianceId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("serverId:");
    sb.append(this.serverId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("allianceName:");
    if (this.allianceName == null) {
      sb.append("null");
    } else {
      sb.append(this.allianceName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("allianceAlias:");
    if (this.allianceAlias == null) {
      sb.append("null");
    } else {
      sb.append(this.allianceAlias);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("allianceFlag:");
    if (this.allianceFlag == null) {
      sb.append("null");
    } else {
      sb.append(this.allianceFlag);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("result:");
    sb.append(this.result);
    first = false;
    if (!first) sb.append(", ");
    sb.append("memberCount:");
    sb.append(this.memberCount);
    first = false;
    if (!first) sb.append(", ");
    sb.append("totalScore:");
    sb.append(this.totalScore);
    first = false;
    if (!first) sb.append(", ");
    sb.append("wuChaoScore:");
    sb.append(this.wuChaoScore);
    first = false;
    if (!first) sb.append(", ");
    sb.append("gatherScore:");
    sb.append(this.gatherScore);
    first = false;
    if (!first) sb.append(", ");
    sb.append("guanDuTime:");
    sb.append(this.guanDuTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("killEnemyTotal:");
    sb.append(this.killEnemyTotal);
    first = false;
    if (!first) sb.append(", ");
    sb.append("buildingScore:");
    sb.append(this.buildingScore);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (allianceId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'allianceId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'serverId' because it's a primitive and you chose the non-beans generator.
    if (allianceName == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'allianceName' was not present! Struct: " + toString());
    }
    if (allianceAlias == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'allianceAlias' was not present! Struct: " + toString());
    }
    if (allianceFlag == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'allianceFlag' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'result' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'memberCount' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'totalScore' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'wuChaoScore' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'gatherScore' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'guanDuTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'killEnemyTotal' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'buildingScore' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
    if (allianceFlag != null) {
      allianceFlag.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsGVGAllianceResultInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsGVGAllianceResultInfoStandardScheme getScheme() {
      return new PsGVGAllianceResultInfoStandardScheme();
    }
  }

  private static class PsGVGAllianceResultInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsGVGAllianceResultInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsGVGAllianceResultInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ALLIANCE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.allianceId = iprot.readString();
              struct.setAllianceIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SERVER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.serverId = iprot.readI32();
              struct.setServerIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ALLIANCE_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.allianceName = iprot.readString();
              struct.setAllianceNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ALLIANCE_ALIAS
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.allianceAlias = iprot.readString();
              struct.setAllianceAliasIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ALLIANCE_FLAG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.allianceFlag = new PsAllianceFlagInfo();
              struct.allianceFlag.read(iprot);
              struct.setAllianceFlagIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // RESULT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.result = iprot.readI32();
              struct.setResultIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // MEMBER_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.memberCount = iprot.readI32();
              struct.setMemberCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // TOTAL_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.totalScore = iprot.readI64();
              struct.setTotalScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // WU_CHAO_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.wuChaoScore = iprot.readI64();
              struct.setWuChaoScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // GATHER_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.gatherScore = iprot.readI64();
              struct.setGatherScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // GUAN_DU_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.guanDuTime = iprot.readI64();
              struct.setGuanDuTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // KILL_ENEMY_TOTAL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.killEnemyTotal = iprot.readI32();
              struct.setKillEnemyTotalIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // BUILDING_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.buildingScore = iprot.readI64();
              struct.setBuildingScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetServerId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'serverId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetResult()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'result' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetMemberCount()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'memberCount' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTotalScore()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'totalScore' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetWuChaoScore()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'wuChaoScore' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetGatherScore()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'gatherScore' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetGuanDuTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'guanDuTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetKillEnemyTotal()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'killEnemyTotal' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetBuildingScore()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'buildingScore' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsGVGAllianceResultInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.allianceId != null) {
        oprot.writeFieldBegin(ALLIANCE_ID_FIELD_DESC);
        oprot.writeString(struct.allianceId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SERVER_ID_FIELD_DESC);
      oprot.writeI32(struct.serverId);
      oprot.writeFieldEnd();
      if (struct.allianceName != null) {
        oprot.writeFieldBegin(ALLIANCE_NAME_FIELD_DESC);
        oprot.writeString(struct.allianceName);
        oprot.writeFieldEnd();
      }
      if (struct.allianceAlias != null) {
        oprot.writeFieldBegin(ALLIANCE_ALIAS_FIELD_DESC);
        oprot.writeString(struct.allianceAlias);
        oprot.writeFieldEnd();
      }
      if (struct.allianceFlag != null) {
        oprot.writeFieldBegin(ALLIANCE_FLAG_FIELD_DESC);
        struct.allianceFlag.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(RESULT_FIELD_DESC);
      oprot.writeI32(struct.result);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(MEMBER_COUNT_FIELD_DESC);
      oprot.writeI32(struct.memberCount);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TOTAL_SCORE_FIELD_DESC);
      oprot.writeI64(struct.totalScore);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(WU_CHAO_SCORE_FIELD_DESC);
      oprot.writeI64(struct.wuChaoScore);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(GATHER_SCORE_FIELD_DESC);
      oprot.writeI64(struct.gatherScore);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(GUAN_DU_TIME_FIELD_DESC);
      oprot.writeI64(struct.guanDuTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(KILL_ENEMY_TOTAL_FIELD_DESC);
      oprot.writeI32(struct.killEnemyTotal);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(BUILDING_SCORE_FIELD_DESC);
      oprot.writeI64(struct.buildingScore);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsGVGAllianceResultInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsGVGAllianceResultInfoTupleScheme getScheme() {
      return new PsGVGAllianceResultInfoTupleScheme();
    }
  }

  private static class PsGVGAllianceResultInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsGVGAllianceResultInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsGVGAllianceResultInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.allianceId);
      oprot.writeI32(struct.serverId);
      oprot.writeString(struct.allianceName);
      oprot.writeString(struct.allianceAlias);
      struct.allianceFlag.write(oprot);
      oprot.writeI32(struct.result);
      oprot.writeI32(struct.memberCount);
      oprot.writeI64(struct.totalScore);
      oprot.writeI64(struct.wuChaoScore);
      oprot.writeI64(struct.gatherScore);
      oprot.writeI64(struct.guanDuTime);
      oprot.writeI32(struct.killEnemyTotal);
      oprot.writeI64(struct.buildingScore);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsGVGAllianceResultInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.allianceId = iprot.readString();
      struct.setAllianceIdIsSet(true);
      struct.serverId = iprot.readI32();
      struct.setServerIdIsSet(true);
      struct.allianceName = iprot.readString();
      struct.setAllianceNameIsSet(true);
      struct.allianceAlias = iprot.readString();
      struct.setAllianceAliasIsSet(true);
      struct.allianceFlag = new PsAllianceFlagInfo();
      struct.allianceFlag.read(iprot);
      struct.setAllianceFlagIsSet(true);
      struct.result = iprot.readI32();
      struct.setResultIsSet(true);
      struct.memberCount = iprot.readI32();
      struct.setMemberCountIsSet(true);
      struct.totalScore = iprot.readI64();
      struct.setTotalScoreIsSet(true);
      struct.wuChaoScore = iprot.readI64();
      struct.setWuChaoScoreIsSet(true);
      struct.gatherScore = iprot.readI64();
      struct.setGatherScoreIsSet(true);
      struct.guanDuTime = iprot.readI64();
      struct.setGuanDuTimeIsSet(true);
      struct.killEnemyTotal = iprot.readI32();
      struct.setKillEnemyTotalIsSet(true);
      struct.buildingScore = iprot.readI64();
      struct.setBuildingScoreIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

