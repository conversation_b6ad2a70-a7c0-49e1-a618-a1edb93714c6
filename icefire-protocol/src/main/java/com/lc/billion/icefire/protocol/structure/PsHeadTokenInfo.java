/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsHeadTokenInfo implements org.apache.thrift.TBase<PsHeadTokenInfo, PsHeadTokenInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsHeadTokenInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsHeadTokenInfo");

  private static final org.apache.thrift.protocol.TField ACCESS_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("accessId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField POLICY_FIELD_DESC = new org.apache.thrift.protocol.TField("policy", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField SIGNATURE_FIELD_DESC = new org.apache.thrift.protocol.TField("signature", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField HOST_FIELD_DESC = new org.apache.thrift.protocol.TField("host", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("key", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField VERSION_FIELD_DESC = new org.apache.thrift.protocol.TField("version", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField HEAD_FIELD_DESC = new org.apache.thrift.protocol.TField("head", org.apache.thrift.protocol.TType.STRING, (short)7);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsHeadTokenInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsHeadTokenInfoTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.lang.String accessId; // required
  /**
   * 上传限制条件
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String policy; // required
  /**
   * 签名
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String signature; // required
  /**
   * 上传host
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String host; // required
  /**
   * 上传文件名
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String key; // required
  /**
   * 头像版本号
   */
  public int version; // required
  /**
   * 头像名
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String head; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACCESS_ID((short)1, "accessId"),
    /**
     * 上传限制条件
     */
    POLICY((short)2, "policy"),
    /**
     * 签名
     */
    SIGNATURE((short)3, "signature"),
    /**
     * 上传host
     */
    HOST((short)4, "host"),
    /**
     * 上传文件名
     */
    KEY((short)5, "key"),
    /**
     * 头像版本号
     */
    VERSION((short)6, "version"),
    /**
     * 头像名
     */
    HEAD((short)7, "head");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACCESS_ID
          return ACCESS_ID;
        case 2: // POLICY
          return POLICY;
        case 3: // SIGNATURE
          return SIGNATURE;
        case 4: // HOST
          return HOST;
        case 5: // KEY
          return KEY;
        case 6: // VERSION
          return VERSION;
        case 7: // HEAD
          return HEAD;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __VERSION_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACCESS_ID, new org.apache.thrift.meta_data.FieldMetaData("accessId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.POLICY, new org.apache.thrift.meta_data.FieldMetaData("policy", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SIGNATURE, new org.apache.thrift.meta_data.FieldMetaData("signature", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.HOST, new org.apache.thrift.meta_data.FieldMetaData("host", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.KEY, new org.apache.thrift.meta_data.FieldMetaData("key", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.VERSION, new org.apache.thrift.meta_data.FieldMetaData("version", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.HEAD, new org.apache.thrift.meta_data.FieldMetaData("head", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsHeadTokenInfo.class, metaDataMap);
  }

  public PsHeadTokenInfo() {
  }

  public PsHeadTokenInfo(
    java.lang.String accessId,
    java.lang.String policy,
    java.lang.String signature,
    java.lang.String host,
    java.lang.String key,
    int version,
    java.lang.String head)
  {
    this();
    this.accessId = accessId;
    this.policy = policy;
    this.signature = signature;
    this.host = host;
    this.key = key;
    this.version = version;
    setVersionIsSet(true);
    this.head = head;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsHeadTokenInfo(PsHeadTokenInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetAccessId()) {
      this.accessId = other.accessId;
    }
    if (other.isSetPolicy()) {
      this.policy = other.policy;
    }
    if (other.isSetSignature()) {
      this.signature = other.signature;
    }
    if (other.isSetHost()) {
      this.host = other.host;
    }
    if (other.isSetKey()) {
      this.key = other.key;
    }
    this.version = other.version;
    if (other.isSetHead()) {
      this.head = other.head;
    }
  }

  public PsHeadTokenInfo deepCopy() {
    return new PsHeadTokenInfo(this);
  }

  @Override
  public void clear() {
    this.accessId = null;
    this.policy = null;
    this.signature = null;
    this.host = null;
    this.key = null;
    setVersionIsSet(false);
    this.version = 0;
    this.head = null;
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getAccessId() {
    return this.accessId;
  }

  public PsHeadTokenInfo setAccessId(@org.apache.thrift.annotation.Nullable java.lang.String accessId) {
    this.accessId = accessId;
    return this;
  }

  public void unsetAccessId() {
    this.accessId = null;
  }

  /** Returns true if field accessId is set (has been assigned a value) and false otherwise */
  public boolean isSetAccessId() {
    return this.accessId != null;
  }

  public void setAccessIdIsSet(boolean value) {
    if (!value) {
      this.accessId = null;
    }
  }

  /**
   * 上传限制条件
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getPolicy() {
    return this.policy;
  }

  /**
   * 上传限制条件
   */
  public PsHeadTokenInfo setPolicy(@org.apache.thrift.annotation.Nullable java.lang.String policy) {
    this.policy = policy;
    return this;
  }

  public void unsetPolicy() {
    this.policy = null;
  }

  /** Returns true if field policy is set (has been assigned a value) and false otherwise */
  public boolean isSetPolicy() {
    return this.policy != null;
  }

  public void setPolicyIsSet(boolean value) {
    if (!value) {
      this.policy = null;
    }
  }

  /**
   * 签名
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getSignature() {
    return this.signature;
  }

  /**
   * 签名
   */
  public PsHeadTokenInfo setSignature(@org.apache.thrift.annotation.Nullable java.lang.String signature) {
    this.signature = signature;
    return this;
  }

  public void unsetSignature() {
    this.signature = null;
  }

  /** Returns true if field signature is set (has been assigned a value) and false otherwise */
  public boolean isSetSignature() {
    return this.signature != null;
  }

  public void setSignatureIsSet(boolean value) {
    if (!value) {
      this.signature = null;
    }
  }

  /**
   * 上传host
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getHost() {
    return this.host;
  }

  /**
   * 上传host
   */
  public PsHeadTokenInfo setHost(@org.apache.thrift.annotation.Nullable java.lang.String host) {
    this.host = host;
    return this;
  }

  public void unsetHost() {
    this.host = null;
  }

  /** Returns true if field host is set (has been assigned a value) and false otherwise */
  public boolean isSetHost() {
    return this.host != null;
  }

  public void setHostIsSet(boolean value) {
    if (!value) {
      this.host = null;
    }
  }

  /**
   * 上传文件名
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getKey() {
    return this.key;
  }

  /**
   * 上传文件名
   */
  public PsHeadTokenInfo setKey(@org.apache.thrift.annotation.Nullable java.lang.String key) {
    this.key = key;
    return this;
  }

  public void unsetKey() {
    this.key = null;
  }

  /** Returns true if field key is set (has been assigned a value) and false otherwise */
  public boolean isSetKey() {
    return this.key != null;
  }

  public void setKeyIsSet(boolean value) {
    if (!value) {
      this.key = null;
    }
  }

  /**
   * 头像版本号
   */
  public int getVersion() {
    return this.version;
  }

  /**
   * 头像版本号
   */
  public PsHeadTokenInfo setVersion(int version) {
    this.version = version;
    setVersionIsSet(true);
    return this;
  }

  public void unsetVersion() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __VERSION_ISSET_ID);
  }

  /** Returns true if field version is set (has been assigned a value) and false otherwise */
  public boolean isSetVersion() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __VERSION_ISSET_ID);
  }

  public void setVersionIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __VERSION_ISSET_ID, value);
  }

  /**
   * 头像名
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getHead() {
    return this.head;
  }

  /**
   * 头像名
   */
  public PsHeadTokenInfo setHead(@org.apache.thrift.annotation.Nullable java.lang.String head) {
    this.head = head;
    return this;
  }

  public void unsetHead() {
    this.head = null;
  }

  /** Returns true if field head is set (has been assigned a value) and false otherwise */
  public boolean isSetHead() {
    return this.head != null;
  }

  public void setHeadIsSet(boolean value) {
    if (!value) {
      this.head = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ACCESS_ID:
      if (value == null) {
        unsetAccessId();
      } else {
        setAccessId((java.lang.String)value);
      }
      break;

    case POLICY:
      if (value == null) {
        unsetPolicy();
      } else {
        setPolicy((java.lang.String)value);
      }
      break;

    case SIGNATURE:
      if (value == null) {
        unsetSignature();
      } else {
        setSignature((java.lang.String)value);
      }
      break;

    case HOST:
      if (value == null) {
        unsetHost();
      } else {
        setHost((java.lang.String)value);
      }
      break;

    case KEY:
      if (value == null) {
        unsetKey();
      } else {
        setKey((java.lang.String)value);
      }
      break;

    case VERSION:
      if (value == null) {
        unsetVersion();
      } else {
        setVersion((java.lang.Integer)value);
      }
      break;

    case HEAD:
      if (value == null) {
        unsetHead();
      } else {
        setHead((java.lang.String)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ACCESS_ID:
      return getAccessId();

    case POLICY:
      return getPolicy();

    case SIGNATURE:
      return getSignature();

    case HOST:
      return getHost();

    case KEY:
      return getKey();

    case VERSION:
      return getVersion();

    case HEAD:
      return getHead();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ACCESS_ID:
      return isSetAccessId();
    case POLICY:
      return isSetPolicy();
    case SIGNATURE:
      return isSetSignature();
    case HOST:
      return isSetHost();
    case KEY:
      return isSetKey();
    case VERSION:
      return isSetVersion();
    case HEAD:
      return isSetHead();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsHeadTokenInfo)
      return this.equals((PsHeadTokenInfo)that);
    return false;
  }

  public boolean equals(PsHeadTokenInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_accessId = true && this.isSetAccessId();
    boolean that_present_accessId = true && that.isSetAccessId();
    if (this_present_accessId || that_present_accessId) {
      if (!(this_present_accessId && that_present_accessId))
        return false;
      if (!this.accessId.equals(that.accessId))
        return false;
    }

    boolean this_present_policy = true && this.isSetPolicy();
    boolean that_present_policy = true && that.isSetPolicy();
    if (this_present_policy || that_present_policy) {
      if (!(this_present_policy && that_present_policy))
        return false;
      if (!this.policy.equals(that.policy))
        return false;
    }

    boolean this_present_signature = true && this.isSetSignature();
    boolean that_present_signature = true && that.isSetSignature();
    if (this_present_signature || that_present_signature) {
      if (!(this_present_signature && that_present_signature))
        return false;
      if (!this.signature.equals(that.signature))
        return false;
    }

    boolean this_present_host = true && this.isSetHost();
    boolean that_present_host = true && that.isSetHost();
    if (this_present_host || that_present_host) {
      if (!(this_present_host && that_present_host))
        return false;
      if (!this.host.equals(that.host))
        return false;
    }

    boolean this_present_key = true && this.isSetKey();
    boolean that_present_key = true && that.isSetKey();
    if (this_present_key || that_present_key) {
      if (!(this_present_key && that_present_key))
        return false;
      if (!this.key.equals(that.key))
        return false;
    }

    boolean this_present_version = true;
    boolean that_present_version = true;
    if (this_present_version || that_present_version) {
      if (!(this_present_version && that_present_version))
        return false;
      if (this.version != that.version)
        return false;
    }

    boolean this_present_head = true && this.isSetHead();
    boolean that_present_head = true && that.isSetHead();
    if (this_present_head || that_present_head) {
      if (!(this_present_head && that_present_head))
        return false;
      if (!this.head.equals(that.head))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetAccessId()) ? 131071 : 524287);
    if (isSetAccessId())
      hashCode = hashCode * 8191 + accessId.hashCode();

    hashCode = hashCode * 8191 + ((isSetPolicy()) ? 131071 : 524287);
    if (isSetPolicy())
      hashCode = hashCode * 8191 + policy.hashCode();

    hashCode = hashCode * 8191 + ((isSetSignature()) ? 131071 : 524287);
    if (isSetSignature())
      hashCode = hashCode * 8191 + signature.hashCode();

    hashCode = hashCode * 8191 + ((isSetHost()) ? 131071 : 524287);
    if (isSetHost())
      hashCode = hashCode * 8191 + host.hashCode();

    hashCode = hashCode * 8191 + ((isSetKey()) ? 131071 : 524287);
    if (isSetKey())
      hashCode = hashCode * 8191 + key.hashCode();

    hashCode = hashCode * 8191 + version;

    hashCode = hashCode * 8191 + ((isSetHead()) ? 131071 : 524287);
    if (isSetHead())
      hashCode = hashCode * 8191 + head.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsHeadTokenInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetAccessId(), other.isSetAccessId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAccessId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.accessId, other.accessId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPolicy(), other.isSetPolicy());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPolicy()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.policy, other.policy);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSignature(), other.isSetSignature());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSignature()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.signature, other.signature);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHost(), other.isSetHost());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHost()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.host, other.host);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetKey(), other.isSetKey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.key, other.key);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetVersion(), other.isSetVersion());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetVersion()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.version, other.version);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHead(), other.isSetHead());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHead()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.head, other.head);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsHeadTokenInfo(");
    boolean first = true;

    sb.append("accessId:");
    if (this.accessId == null) {
      sb.append("null");
    } else {
      sb.append(this.accessId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("policy:");
    if (this.policy == null) {
      sb.append("null");
    } else {
      sb.append(this.policy);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("signature:");
    if (this.signature == null) {
      sb.append("null");
    } else {
      sb.append(this.signature);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("host:");
    if (this.host == null) {
      sb.append("null");
    } else {
      sb.append(this.host);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("key:");
    if (this.key == null) {
      sb.append("null");
    } else {
      sb.append(this.key);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("version:");
    sb.append(this.version);
    first = false;
    if (!first) sb.append(", ");
    sb.append("head:");
    if (this.head == null) {
      sb.append("null");
    } else {
      sb.append(this.head);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (accessId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'accessId' was not present! Struct: " + toString());
    }
    if (policy == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'policy' was not present! Struct: " + toString());
    }
    if (signature == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'signature' was not present! Struct: " + toString());
    }
    if (host == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'host' was not present! Struct: " + toString());
    }
    if (key == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'key' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'version' because it's a primitive and you chose the non-beans generator.
    if (head == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'head' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsHeadTokenInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsHeadTokenInfoStandardScheme getScheme() {
      return new PsHeadTokenInfoStandardScheme();
    }
  }

  private static class PsHeadTokenInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsHeadTokenInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsHeadTokenInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACCESS_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.accessId = iprot.readString();
              struct.setAccessIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // POLICY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.policy = iprot.readString();
              struct.setPolicyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SIGNATURE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.signature = iprot.readString();
              struct.setSignatureIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // HOST
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.host = iprot.readString();
              struct.setHostIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // KEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.key = iprot.readString();
              struct.setKeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // VERSION
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.version = iprot.readI32();
              struct.setVersionIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // HEAD
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.head = iprot.readString();
              struct.setHeadIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetVersion()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'version' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsHeadTokenInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.accessId != null) {
        oprot.writeFieldBegin(ACCESS_ID_FIELD_DESC);
        oprot.writeString(struct.accessId);
        oprot.writeFieldEnd();
      }
      if (struct.policy != null) {
        oprot.writeFieldBegin(POLICY_FIELD_DESC);
        oprot.writeString(struct.policy);
        oprot.writeFieldEnd();
      }
      if (struct.signature != null) {
        oprot.writeFieldBegin(SIGNATURE_FIELD_DESC);
        oprot.writeString(struct.signature);
        oprot.writeFieldEnd();
      }
      if (struct.host != null) {
        oprot.writeFieldBegin(HOST_FIELD_DESC);
        oprot.writeString(struct.host);
        oprot.writeFieldEnd();
      }
      if (struct.key != null) {
        oprot.writeFieldBegin(KEY_FIELD_DESC);
        oprot.writeString(struct.key);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(VERSION_FIELD_DESC);
      oprot.writeI32(struct.version);
      oprot.writeFieldEnd();
      if (struct.head != null) {
        oprot.writeFieldBegin(HEAD_FIELD_DESC);
        oprot.writeString(struct.head);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsHeadTokenInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsHeadTokenInfoTupleScheme getScheme() {
      return new PsHeadTokenInfoTupleScheme();
    }
  }

  private static class PsHeadTokenInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsHeadTokenInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsHeadTokenInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.accessId);
      oprot.writeString(struct.policy);
      oprot.writeString(struct.signature);
      oprot.writeString(struct.host);
      oprot.writeString(struct.key);
      oprot.writeI32(struct.version);
      oprot.writeString(struct.head);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsHeadTokenInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.accessId = iprot.readString();
      struct.setAccessIdIsSet(true);
      struct.policy = iprot.readString();
      struct.setPolicyIsSet(true);
      struct.signature = iprot.readString();
      struct.setSignatureIsSet(true);
      struct.host = iprot.readString();
      struct.setHostIsSet(true);
      struct.key = iprot.readString();
      struct.setKeyIsSet(true);
      struct.version = iprot.readI32();
      struct.setVersionIsSet(true);
      struct.head = iprot.readString();
      struct.setHeadIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

