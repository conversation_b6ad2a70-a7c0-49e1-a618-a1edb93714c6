/**
 * Autogenerated by <PERSON><PERSON><PERSON>mpiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 新最强领主每日信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsNewStrongestLordDayInfo implements org.apache.thrift.TBase<PsNewStrongestLordDayInfo, PsNewStrongestLordDayInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsNewStrongestLordDayInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsNewStrongestLordDayInfo");

  private static final org.apache.thrift.protocol.TField SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("score", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("rank", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField CUR_DAY_FIELD_DESC = new org.apache.thrift.protocol.TField("curDay", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("startTime", org.apache.thrift.protocol.TType.I64, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsNewStrongestLordDayInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsNewStrongestLordDayInfoTupleSchemeFactory();

  /**
   * 每日积分
   */
  public long score; // optional
  /**
   * 结束时间
   */
  public long endTime; // optional
  /**
   * 每日排名
   */
  public int rank; // optional
  /**
   * 当前天数
   */
  public int curDay; // optional
  /**
   * 开始时间
   */
  public long startTime; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 每日积分
     */
    SCORE((short)1, "score"),
    /**
     * 结束时间
     */
    END_TIME((short)2, "endTime"),
    /**
     * 每日排名
     */
    RANK((short)3, "rank"),
    /**
     * 当前天数
     */
    CUR_DAY((short)4, "curDay"),
    /**
     * 开始时间
     */
    START_TIME((short)5, "startTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SCORE
          return SCORE;
        case 2: // END_TIME
          return END_TIME;
        case 3: // RANK
          return RANK;
        case 4: // CUR_DAY
          return CUR_DAY;
        case 5: // START_TIME
          return START_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SCORE_ISSET_ID = 0;
  private static final int __ENDTIME_ISSET_ID = 1;
  private static final int __RANK_ISSET_ID = 2;
  private static final int __CURDAY_ISSET_ID = 3;
  private static final int __STARTTIME_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.SCORE,_Fields.END_TIME,_Fields.RANK,_Fields.CUR_DAY,_Fields.START_TIME};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SCORE, new org.apache.thrift.meta_data.FieldMetaData("score", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANK, new org.apache.thrift.meta_data.FieldMetaData("rank", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CUR_DAY, new org.apache.thrift.meta_data.FieldMetaData("curDay", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.START_TIME, new org.apache.thrift.meta_data.FieldMetaData("startTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsNewStrongestLordDayInfo.class, metaDataMap);
  }

  public PsNewStrongestLordDayInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsNewStrongestLordDayInfo(PsNewStrongestLordDayInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.score = other.score;
    this.endTime = other.endTime;
    this.rank = other.rank;
    this.curDay = other.curDay;
    this.startTime = other.startTime;
  }

  public PsNewStrongestLordDayInfo deepCopy() {
    return new PsNewStrongestLordDayInfo(this);
  }

  @Override
  public void clear() {
    setScoreIsSet(false);
    this.score = 0;
    setEndTimeIsSet(false);
    this.endTime = 0;
    setRankIsSet(false);
    this.rank = 0;
    setCurDayIsSet(false);
    this.curDay = 0;
    setStartTimeIsSet(false);
    this.startTime = 0;
  }

  /**
   * 每日积分
   */
  public long getScore() {
    return this.score;
  }

  /**
   * 每日积分
   */
  public PsNewStrongestLordDayInfo setScore(long score) {
    this.score = score;
    setScoreIsSet(true);
    return this;
  }

  public void unsetScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  /** Returns true if field score is set (has been assigned a value) and false otherwise */
  public boolean isSetScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  public void setScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SCORE_ISSET_ID, value);
  }

  /**
   * 结束时间
   */
  public long getEndTime() {
    return this.endTime;
  }

  /**
   * 结束时间
   */
  public PsNewStrongestLordDayInfo setEndTime(long endTime) {
    this.endTime = endTime;
    setEndTimeIsSet(true);
    return this;
  }

  public void unsetEndTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
  public boolean isSetEndTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  public void setEndTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
  }

  /**
   * 每日排名
   */
  public int getRank() {
    return this.rank;
  }

  /**
   * 每日排名
   */
  public PsNewStrongestLordDayInfo setRank(int rank) {
    this.rank = rank;
    setRankIsSet(true);
    return this;
  }

  public void unsetRank() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __RANK_ISSET_ID);
  }

  /** Returns true if field rank is set (has been assigned a value) and false otherwise */
  public boolean isSetRank() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __RANK_ISSET_ID);
  }

  public void setRankIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __RANK_ISSET_ID, value);
  }

  /**
   * 当前天数
   */
  public int getCurDay() {
    return this.curDay;
  }

  /**
   * 当前天数
   */
  public PsNewStrongestLordDayInfo setCurDay(int curDay) {
    this.curDay = curDay;
    setCurDayIsSet(true);
    return this;
  }

  public void unsetCurDay() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CURDAY_ISSET_ID);
  }

  /** Returns true if field curDay is set (has been assigned a value) and false otherwise */
  public boolean isSetCurDay() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CURDAY_ISSET_ID);
  }

  public void setCurDayIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CURDAY_ISSET_ID, value);
  }

  /**
   * 开始时间
   */
  public long getStartTime() {
    return this.startTime;
  }

  /**
   * 开始时间
   */
  public PsNewStrongestLordDayInfo setStartTime(long startTime) {
    this.startTime = startTime;
    setStartTimeIsSet(true);
    return this;
  }

  public void unsetStartTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  /** Returns true if field startTime is set (has been assigned a value) and false otherwise */
  public boolean isSetStartTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  public void setStartTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTTIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case SCORE:
      if (value == null) {
        unsetScore();
      } else {
        setScore((java.lang.Long)value);
      }
      break;

    case END_TIME:
      if (value == null) {
        unsetEndTime();
      } else {
        setEndTime((java.lang.Long)value);
      }
      break;

    case RANK:
      if (value == null) {
        unsetRank();
      } else {
        setRank((java.lang.Integer)value);
      }
      break;

    case CUR_DAY:
      if (value == null) {
        unsetCurDay();
      } else {
        setCurDay((java.lang.Integer)value);
      }
      break;

    case START_TIME:
      if (value == null) {
        unsetStartTime();
      } else {
        setStartTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case SCORE:
      return getScore();

    case END_TIME:
      return getEndTime();

    case RANK:
      return getRank();

    case CUR_DAY:
      return getCurDay();

    case START_TIME:
      return getStartTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case SCORE:
      return isSetScore();
    case END_TIME:
      return isSetEndTime();
    case RANK:
      return isSetRank();
    case CUR_DAY:
      return isSetCurDay();
    case START_TIME:
      return isSetStartTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsNewStrongestLordDayInfo)
      return this.equals((PsNewStrongestLordDayInfo)that);
    return false;
  }

  public boolean equals(PsNewStrongestLordDayInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_score = true && this.isSetScore();
    boolean that_present_score = true && that.isSetScore();
    if (this_present_score || that_present_score) {
      if (!(this_present_score && that_present_score))
        return false;
      if (this.score != that.score)
        return false;
    }

    boolean this_present_endTime = true && this.isSetEndTime();
    boolean that_present_endTime = true && that.isSetEndTime();
    if (this_present_endTime || that_present_endTime) {
      if (!(this_present_endTime && that_present_endTime))
        return false;
      if (this.endTime != that.endTime)
        return false;
    }

    boolean this_present_rank = true && this.isSetRank();
    boolean that_present_rank = true && that.isSetRank();
    if (this_present_rank || that_present_rank) {
      if (!(this_present_rank && that_present_rank))
        return false;
      if (this.rank != that.rank)
        return false;
    }

    boolean this_present_curDay = true && this.isSetCurDay();
    boolean that_present_curDay = true && that.isSetCurDay();
    if (this_present_curDay || that_present_curDay) {
      if (!(this_present_curDay && that_present_curDay))
        return false;
      if (this.curDay != that.curDay)
        return false;
    }

    boolean this_present_startTime = true && this.isSetStartTime();
    boolean that_present_startTime = true && that.isSetStartTime();
    if (this_present_startTime || that_present_startTime) {
      if (!(this_present_startTime && that_present_startTime))
        return false;
      if (this.startTime != that.startTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetScore()) ? 131071 : 524287);
    if (isSetScore())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(score);

    hashCode = hashCode * 8191 + ((isSetEndTime()) ? 131071 : 524287);
    if (isSetEndTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(endTime);

    hashCode = hashCode * 8191 + ((isSetRank()) ? 131071 : 524287);
    if (isSetRank())
      hashCode = hashCode * 8191 + rank;

    hashCode = hashCode * 8191 + ((isSetCurDay()) ? 131071 : 524287);
    if (isSetCurDay())
      hashCode = hashCode * 8191 + curDay;

    hashCode = hashCode * 8191 + ((isSetStartTime()) ? 131071 : 524287);
    if (isSetStartTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(startTime);

    return hashCode;
  }

  @Override
  public int compareTo(PsNewStrongestLordDayInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetScore(), other.isSetScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.score, other.score);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndTime(), other.isSetEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRank(), other.isSetRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rank, other.rank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCurDay(), other.isSetCurDay());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurDay()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.curDay, other.curDay);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartTime(), other.isSetStartTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startTime, other.startTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsNewStrongestLordDayInfo(");
    boolean first = true;

    if (isSetScore()) {
      sb.append("score:");
      sb.append(this.score);
      first = false;
    }
    if (isSetEndTime()) {
      if (!first) sb.append(", ");
      sb.append("endTime:");
      sb.append(this.endTime);
      first = false;
    }
    if (isSetRank()) {
      if (!first) sb.append(", ");
      sb.append("rank:");
      sb.append(this.rank);
      first = false;
    }
    if (isSetCurDay()) {
      if (!first) sb.append(", ");
      sb.append("curDay:");
      sb.append(this.curDay);
      first = false;
    }
    if (isSetStartTime()) {
      if (!first) sb.append(", ");
      sb.append("startTime:");
      sb.append(this.startTime);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsNewStrongestLordDayInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsNewStrongestLordDayInfoStandardScheme getScheme() {
      return new PsNewStrongestLordDayInfoStandardScheme();
    }
  }

  private static class PsNewStrongestLordDayInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsNewStrongestLordDayInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsNewStrongestLordDayInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.score = iprot.readI64();
              struct.setScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.endTime = iprot.readI64();
              struct.setEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rank = iprot.readI32();
              struct.setRankIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CUR_DAY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.curDay = iprot.readI32();
              struct.setCurDayIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // START_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.startTime = iprot.readI64();
              struct.setStartTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsNewStrongestLordDayInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetScore()) {
        oprot.writeFieldBegin(SCORE_FIELD_DESC);
        oprot.writeI64(struct.score);
        oprot.writeFieldEnd();
      }
      if (struct.isSetEndTime()) {
        oprot.writeFieldBegin(END_TIME_FIELD_DESC);
        oprot.writeI64(struct.endTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetRank()) {
        oprot.writeFieldBegin(RANK_FIELD_DESC);
        oprot.writeI32(struct.rank);
        oprot.writeFieldEnd();
      }
      if (struct.isSetCurDay()) {
        oprot.writeFieldBegin(CUR_DAY_FIELD_DESC);
        oprot.writeI32(struct.curDay);
        oprot.writeFieldEnd();
      }
      if (struct.isSetStartTime()) {
        oprot.writeFieldBegin(START_TIME_FIELD_DESC);
        oprot.writeI64(struct.startTime);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsNewStrongestLordDayInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsNewStrongestLordDayInfoTupleScheme getScheme() {
      return new PsNewStrongestLordDayInfoTupleScheme();
    }
  }

  private static class PsNewStrongestLordDayInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsNewStrongestLordDayInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsNewStrongestLordDayInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetScore()) {
        optionals.set(0);
      }
      if (struct.isSetEndTime()) {
        optionals.set(1);
      }
      if (struct.isSetRank()) {
        optionals.set(2);
      }
      if (struct.isSetCurDay()) {
        optionals.set(3);
      }
      if (struct.isSetStartTime()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetScore()) {
        oprot.writeI64(struct.score);
      }
      if (struct.isSetEndTime()) {
        oprot.writeI64(struct.endTime);
      }
      if (struct.isSetRank()) {
        oprot.writeI32(struct.rank);
      }
      if (struct.isSetCurDay()) {
        oprot.writeI32(struct.curDay);
      }
      if (struct.isSetStartTime()) {
        oprot.writeI64(struct.startTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsNewStrongestLordDayInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.score = iprot.readI64();
        struct.setScoreIsSet(true);
      }
      if (incoming.get(1)) {
        struct.endTime = iprot.readI64();
        struct.setEndTimeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.rank = iprot.readI32();
        struct.setRankIsSet(true);
      }
      if (incoming.get(3)) {
        struct.curDay = iprot.readI32();
        struct.setCurDayIsSet(true);
      }
      if (incoming.get(4)) {
        struct.startTime = iprot.readI64();
        struct.setStartTimeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

