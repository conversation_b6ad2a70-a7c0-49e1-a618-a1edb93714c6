/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 抽卡结果
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsPubTreasureResult implements org.apache.thrift.TBase<PsPubTreasureResult, PsPubTreasureResult._Fields>, java.io.Serializable, Cloneable, Comparable<PsPubTreasureResult> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsPubTreasureResult");

  private static final org.apache.thrift.protocol.TField TIMES_FIELD_DESC = new org.apache.thrift.protocol.TField("times", org.apache.thrift.protocol.TType.I16, (short)1);
  private static final org.apache.thrift.protocol.TField ITEM_REWARDS_FIELD_DESC = new org.apache.thrift.protocol.TField("itemRewards", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField TRANSFORM_HERO_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("transformHeroIds", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField TRANSFORM_TUNED_CAR_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("transformTunedCarIds", org.apache.thrift.protocol.TType.LIST, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsPubTreasureResultStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsPubTreasureResultTupleSchemeFactory();

  /**
   * 第几次抽卡 *
   */
  public short times; // optional
  /**
   * 宝箱开出的原始道具
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<PsSimpleItem> itemRewards; // optional
  /**
   * 获得英雄，但是转化成道具的英雄列表*
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> transformHeroIds; // optional
  /**
   * 获得改装车，但是转化成道具的改装车列表*
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> transformTunedCarIds; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 第几次抽卡 *
     */
    TIMES((short)1, "times"),
    /**
     * 宝箱开出的原始道具
     */
    ITEM_REWARDS((short)2, "itemRewards"),
    /**
     * 获得英雄，但是转化成道具的英雄列表*
     */
    TRANSFORM_HERO_IDS((short)3, "transformHeroIds"),
    /**
     * 获得改装车，但是转化成道具的改装车列表*
     */
    TRANSFORM_TUNED_CAR_IDS((short)4, "transformTunedCarIds");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TIMES
          return TIMES;
        case 2: // ITEM_REWARDS
          return ITEM_REWARDS;
        case 3: // TRANSFORM_HERO_IDS
          return TRANSFORM_HERO_IDS;
        case 4: // TRANSFORM_TUNED_CAR_IDS
          return TRANSFORM_TUNED_CAR_IDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __TIMES_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.TIMES,_Fields.ITEM_REWARDS,_Fields.TRANSFORM_HERO_IDS,_Fields.TRANSFORM_TUNED_CAR_IDS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TIMES, new org.apache.thrift.meta_data.FieldMetaData("times", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I16)));
    tmpMap.put(_Fields.ITEM_REWARDS, new org.apache.thrift.meta_data.FieldMetaData("itemRewards", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsSimpleItem.class))));
    tmpMap.put(_Fields.TRANSFORM_HERO_IDS, new org.apache.thrift.meta_data.FieldMetaData("transformHeroIds", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.TRANSFORM_TUNED_CAR_IDS, new org.apache.thrift.meta_data.FieldMetaData("transformTunedCarIds", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsPubTreasureResult.class, metaDataMap);
  }

  public PsPubTreasureResult() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsPubTreasureResult(PsPubTreasureResult other) {
    __isset_bitfield = other.__isset_bitfield;
    this.times = other.times;
    if (other.isSetItemRewards()) {
      java.util.List<PsSimpleItem> __this__itemRewards = new java.util.ArrayList<PsSimpleItem>(other.itemRewards.size());
      for (PsSimpleItem other_element : other.itemRewards) {
        __this__itemRewards.add(new PsSimpleItem(other_element));
      }
      this.itemRewards = __this__itemRewards;
    }
    if (other.isSetTransformHeroIds()) {
      java.util.List<java.lang.String> __this__transformHeroIds = new java.util.ArrayList<java.lang.String>(other.transformHeroIds);
      this.transformHeroIds = __this__transformHeroIds;
    }
    if (other.isSetTransformTunedCarIds()) {
      java.util.List<java.lang.String> __this__transformTunedCarIds = new java.util.ArrayList<java.lang.String>(other.transformTunedCarIds);
      this.transformTunedCarIds = __this__transformTunedCarIds;
    }
  }

  public PsPubTreasureResult deepCopy() {
    return new PsPubTreasureResult(this);
  }

  @Override
  public void clear() {
    setTimesIsSet(false);
    this.times = 0;
    this.itemRewards = null;
    this.transformHeroIds = null;
    this.transformTunedCarIds = null;
  }

  /**
   * 第几次抽卡 *
   */
  public short getTimes() {
    return this.times;
  }

  /**
   * 第几次抽卡 *
   */
  public PsPubTreasureResult setTimes(short times) {
    this.times = times;
    setTimesIsSet(true);
    return this;
  }

  public void unsetTimes() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TIMES_ISSET_ID);
  }

  /** Returns true if field times is set (has been assigned a value) and false otherwise */
  public boolean isSetTimes() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TIMES_ISSET_ID);
  }

  public void setTimesIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TIMES_ISSET_ID, value);
  }

  public int getItemRewardsSize() {
    return (this.itemRewards == null) ? 0 : this.itemRewards.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<PsSimpleItem> getItemRewardsIterator() {
    return (this.itemRewards == null) ? null : this.itemRewards.iterator();
  }

  public void addToItemRewards(PsSimpleItem elem) {
    if (this.itemRewards == null) {
      this.itemRewards = new java.util.ArrayList<PsSimpleItem>();
    }
    this.itemRewards.add(elem);
  }

  /**
   * 宝箱开出的原始道具
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<PsSimpleItem> getItemRewards() {
    return this.itemRewards;
  }

  /**
   * 宝箱开出的原始道具
   */
  public PsPubTreasureResult setItemRewards(@org.apache.thrift.annotation.Nullable java.util.List<PsSimpleItem> itemRewards) {
    this.itemRewards = itemRewards;
    return this;
  }

  public void unsetItemRewards() {
    this.itemRewards = null;
  }

  /** Returns true if field itemRewards is set (has been assigned a value) and false otherwise */
  public boolean isSetItemRewards() {
    return this.itemRewards != null;
  }

  public void setItemRewardsIsSet(boolean value) {
    if (!value) {
      this.itemRewards = null;
    }
  }

  public int getTransformHeroIdsSize() {
    return (this.transformHeroIds == null) ? 0 : this.transformHeroIds.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.String> getTransformHeroIdsIterator() {
    return (this.transformHeroIds == null) ? null : this.transformHeroIds.iterator();
  }

  public void addToTransformHeroIds(java.lang.String elem) {
    if (this.transformHeroIds == null) {
      this.transformHeroIds = new java.util.ArrayList<java.lang.String>();
    }
    this.transformHeroIds.add(elem);
  }

  /**
   * 获得英雄，但是转化成道具的英雄列表*
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.String> getTransformHeroIds() {
    return this.transformHeroIds;
  }

  /**
   * 获得英雄，但是转化成道具的英雄列表*
   */
  public PsPubTreasureResult setTransformHeroIds(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> transformHeroIds) {
    this.transformHeroIds = transformHeroIds;
    return this;
  }

  public void unsetTransformHeroIds() {
    this.transformHeroIds = null;
  }

  /** Returns true if field transformHeroIds is set (has been assigned a value) and false otherwise */
  public boolean isSetTransformHeroIds() {
    return this.transformHeroIds != null;
  }

  public void setTransformHeroIdsIsSet(boolean value) {
    if (!value) {
      this.transformHeroIds = null;
    }
  }

  public int getTransformTunedCarIdsSize() {
    return (this.transformTunedCarIds == null) ? 0 : this.transformTunedCarIds.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<java.lang.String> getTransformTunedCarIdsIterator() {
    return (this.transformTunedCarIds == null) ? null : this.transformTunedCarIds.iterator();
  }

  public void addToTransformTunedCarIds(java.lang.String elem) {
    if (this.transformTunedCarIds == null) {
      this.transformTunedCarIds = new java.util.ArrayList<java.lang.String>();
    }
    this.transformTunedCarIds.add(elem);
  }

  /**
   * 获得改装车，但是转化成道具的改装车列表*
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<java.lang.String> getTransformTunedCarIds() {
    return this.transformTunedCarIds;
  }

  /**
   * 获得改装车，但是转化成道具的改装车列表*
   */
  public PsPubTreasureResult setTransformTunedCarIds(@org.apache.thrift.annotation.Nullable java.util.List<java.lang.String> transformTunedCarIds) {
    this.transformTunedCarIds = transformTunedCarIds;
    return this;
  }

  public void unsetTransformTunedCarIds() {
    this.transformTunedCarIds = null;
  }

  /** Returns true if field transformTunedCarIds is set (has been assigned a value) and false otherwise */
  public boolean isSetTransformTunedCarIds() {
    return this.transformTunedCarIds != null;
  }

  public void setTransformTunedCarIdsIsSet(boolean value) {
    if (!value) {
      this.transformTunedCarIds = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case TIMES:
      if (value == null) {
        unsetTimes();
      } else {
        setTimes((java.lang.Short)value);
      }
      break;

    case ITEM_REWARDS:
      if (value == null) {
        unsetItemRewards();
      } else {
        setItemRewards((java.util.List<PsSimpleItem>)value);
      }
      break;

    case TRANSFORM_HERO_IDS:
      if (value == null) {
        unsetTransformHeroIds();
      } else {
        setTransformHeroIds((java.util.List<java.lang.String>)value);
      }
      break;

    case TRANSFORM_TUNED_CAR_IDS:
      if (value == null) {
        unsetTransformTunedCarIds();
      } else {
        setTransformTunedCarIds((java.util.List<java.lang.String>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case TIMES:
      return getTimes();

    case ITEM_REWARDS:
      return getItemRewards();

    case TRANSFORM_HERO_IDS:
      return getTransformHeroIds();

    case TRANSFORM_TUNED_CAR_IDS:
      return getTransformTunedCarIds();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case TIMES:
      return isSetTimes();
    case ITEM_REWARDS:
      return isSetItemRewards();
    case TRANSFORM_HERO_IDS:
      return isSetTransformHeroIds();
    case TRANSFORM_TUNED_CAR_IDS:
      return isSetTransformTunedCarIds();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsPubTreasureResult)
      return this.equals((PsPubTreasureResult)that);
    return false;
  }

  public boolean equals(PsPubTreasureResult that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_times = true && this.isSetTimes();
    boolean that_present_times = true && that.isSetTimes();
    if (this_present_times || that_present_times) {
      if (!(this_present_times && that_present_times))
        return false;
      if (this.times != that.times)
        return false;
    }

    boolean this_present_itemRewards = true && this.isSetItemRewards();
    boolean that_present_itemRewards = true && that.isSetItemRewards();
    if (this_present_itemRewards || that_present_itemRewards) {
      if (!(this_present_itemRewards && that_present_itemRewards))
        return false;
      if (!this.itemRewards.equals(that.itemRewards))
        return false;
    }

    boolean this_present_transformHeroIds = true && this.isSetTransformHeroIds();
    boolean that_present_transformHeroIds = true && that.isSetTransformHeroIds();
    if (this_present_transformHeroIds || that_present_transformHeroIds) {
      if (!(this_present_transformHeroIds && that_present_transformHeroIds))
        return false;
      if (!this.transformHeroIds.equals(that.transformHeroIds))
        return false;
    }

    boolean this_present_transformTunedCarIds = true && this.isSetTransformTunedCarIds();
    boolean that_present_transformTunedCarIds = true && that.isSetTransformTunedCarIds();
    if (this_present_transformTunedCarIds || that_present_transformTunedCarIds) {
      if (!(this_present_transformTunedCarIds && that_present_transformTunedCarIds))
        return false;
      if (!this.transformTunedCarIds.equals(that.transformTunedCarIds))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetTimes()) ? 131071 : 524287);
    if (isSetTimes())
      hashCode = hashCode * 8191 + times;

    hashCode = hashCode * 8191 + ((isSetItemRewards()) ? 131071 : 524287);
    if (isSetItemRewards())
      hashCode = hashCode * 8191 + itemRewards.hashCode();

    hashCode = hashCode * 8191 + ((isSetTransformHeroIds()) ? 131071 : 524287);
    if (isSetTransformHeroIds())
      hashCode = hashCode * 8191 + transformHeroIds.hashCode();

    hashCode = hashCode * 8191 + ((isSetTransformTunedCarIds()) ? 131071 : 524287);
    if (isSetTransformTunedCarIds())
      hashCode = hashCode * 8191 + transformTunedCarIds.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsPubTreasureResult other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetTimes(), other.isSetTimes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.times, other.times);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetItemRewards(), other.isSetItemRewards());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemRewards()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemRewards, other.itemRewards);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTransformHeroIds(), other.isSetTransformHeroIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTransformHeroIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.transformHeroIds, other.transformHeroIds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTransformTunedCarIds(), other.isSetTransformTunedCarIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTransformTunedCarIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.transformTunedCarIds, other.transformTunedCarIds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsPubTreasureResult(");
    boolean first = true;

    if (isSetTimes()) {
      sb.append("times:");
      sb.append(this.times);
      first = false;
    }
    if (isSetItemRewards()) {
      if (!first) sb.append(", ");
      sb.append("itemRewards:");
      if (this.itemRewards == null) {
        sb.append("null");
      } else {
        sb.append(this.itemRewards);
      }
      first = false;
    }
    if (isSetTransformHeroIds()) {
      if (!first) sb.append(", ");
      sb.append("transformHeroIds:");
      if (this.transformHeroIds == null) {
        sb.append("null");
      } else {
        sb.append(this.transformHeroIds);
      }
      first = false;
    }
    if (isSetTransformTunedCarIds()) {
      if (!first) sb.append(", ");
      sb.append("transformTunedCarIds:");
      if (this.transformTunedCarIds == null) {
        sb.append("null");
      } else {
        sb.append(this.transformTunedCarIds);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsPubTreasureResultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsPubTreasureResultStandardScheme getScheme() {
      return new PsPubTreasureResultStandardScheme();
    }
  }

  private static class PsPubTreasureResultStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsPubTreasureResult> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsPubTreasureResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TIMES
            if (schemeField.type == org.apache.thrift.protocol.TType.I16) {
              struct.times = iprot.readI16();
              struct.setTimesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ITEM_REWARDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.itemRewards = new java.util.ArrayList<PsSimpleItem>(_list0.size);
                @org.apache.thrift.annotation.Nullable PsSimpleItem _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new PsSimpleItem();
                  _elem1.read(iprot);
                  struct.itemRewards.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setItemRewardsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TRANSFORM_HERO_IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.transformHeroIds = new java.util.ArrayList<java.lang.String>(_list3.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = iprot.readString();
                  struct.transformHeroIds.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setTransformHeroIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TRANSFORM_TUNED_CAR_IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list6 = iprot.readListBegin();
                struct.transformTunedCarIds = new java.util.ArrayList<java.lang.String>(_list6.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _elem7;
                for (int _i8 = 0; _i8 < _list6.size; ++_i8)
                {
                  _elem7 = iprot.readString();
                  struct.transformTunedCarIds.add(_elem7);
                }
                iprot.readListEnd();
              }
              struct.setTransformTunedCarIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsPubTreasureResult struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetTimes()) {
        oprot.writeFieldBegin(TIMES_FIELD_DESC);
        oprot.writeI16(struct.times);
        oprot.writeFieldEnd();
      }
      if (struct.itemRewards != null) {
        if (struct.isSetItemRewards()) {
          oprot.writeFieldBegin(ITEM_REWARDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.itemRewards.size()));
            for (PsSimpleItem _iter9 : struct.itemRewards)
            {
              _iter9.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.transformHeroIds != null) {
        if (struct.isSetTransformHeroIds()) {
          oprot.writeFieldBegin(TRANSFORM_HERO_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.transformHeroIds.size()));
            for (java.lang.String _iter10 : struct.transformHeroIds)
            {
              oprot.writeString(_iter10);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.transformTunedCarIds != null) {
        if (struct.isSetTransformTunedCarIds()) {
          oprot.writeFieldBegin(TRANSFORM_TUNED_CAR_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.transformTunedCarIds.size()));
            for (java.lang.String _iter11 : struct.transformTunedCarIds)
            {
              oprot.writeString(_iter11);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsPubTreasureResultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsPubTreasureResultTupleScheme getScheme() {
      return new PsPubTreasureResultTupleScheme();
    }
  }

  private static class PsPubTreasureResultTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsPubTreasureResult> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsPubTreasureResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetTimes()) {
        optionals.set(0);
      }
      if (struct.isSetItemRewards()) {
        optionals.set(1);
      }
      if (struct.isSetTransformHeroIds()) {
        optionals.set(2);
      }
      if (struct.isSetTransformTunedCarIds()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetTimes()) {
        oprot.writeI16(struct.times);
      }
      if (struct.isSetItemRewards()) {
        {
          oprot.writeI32(struct.itemRewards.size());
          for (PsSimpleItem _iter12 : struct.itemRewards)
          {
            _iter12.write(oprot);
          }
        }
      }
      if (struct.isSetTransformHeroIds()) {
        {
          oprot.writeI32(struct.transformHeroIds.size());
          for (java.lang.String _iter13 : struct.transformHeroIds)
          {
            oprot.writeString(_iter13);
          }
        }
      }
      if (struct.isSetTransformTunedCarIds()) {
        {
          oprot.writeI32(struct.transformTunedCarIds.size());
          for (java.lang.String _iter14 : struct.transformTunedCarIds)
          {
            oprot.writeString(_iter14);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsPubTreasureResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.times = iprot.readI16();
        struct.setTimesIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list15 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.itemRewards = new java.util.ArrayList<PsSimpleItem>(_list15.size);
          @org.apache.thrift.annotation.Nullable PsSimpleItem _elem16;
          for (int _i17 = 0; _i17 < _list15.size; ++_i17)
          {
            _elem16 = new PsSimpleItem();
            _elem16.read(iprot);
            struct.itemRewards.add(_elem16);
          }
        }
        struct.setItemRewardsIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list18 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRING);
          struct.transformHeroIds = new java.util.ArrayList<java.lang.String>(_list18.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _elem19;
          for (int _i20 = 0; _i20 < _list18.size; ++_i20)
          {
            _elem19 = iprot.readString();
            struct.transformHeroIds.add(_elem19);
          }
        }
        struct.setTransformHeroIdsIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list21 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRING);
          struct.transformTunedCarIds = new java.util.ArrayList<java.lang.String>(_list21.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _elem22;
          for (int _i23 = 0; _i23 < _list21.size; ++_i23)
          {
            _elem22 = iprot.readString();
            struct.transformTunedCarIds.add(_elem22);
          }
        }
        struct.setTransformTunedCarIdsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

