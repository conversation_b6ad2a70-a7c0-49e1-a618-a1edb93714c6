/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 热点数据
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsRegionCapitalHotConflict implements org.apache.thrift.TBase<PsRegionCapitalHotConflict, PsRegionCapitalHotConflict._Fields>, java.io.Serializable, Cloneable, Comparable<PsRegionCapitalHotConflict> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsRegionCapitalHotConflict");

  private static final org.apache.thrift.protocol.TField META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("metaId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField INVOLVED_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("involvedCount", org.apache.thrift.protocol.TType.MAP, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsRegionCapitalHotConflictStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsRegionCapitalHotConflictTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.lang.String metaId; // required
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.Integer> involvedCount; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    META_ID((short)1, "metaId"),
    INVOLVED_COUNT((short)2, "involvedCount");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // META_ID
          return META_ID;
        case 2: // INVOLVED_COUNT
          return INVOLVED_COUNT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.INVOLVED_COUNT};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.META_ID, new org.apache.thrift.meta_data.FieldMetaData("metaId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.INVOLVED_COUNT, new org.apache.thrift.meta_data.FieldMetaData("involvedCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsRegionCapitalHotConflict.class, metaDataMap);
  }

  public PsRegionCapitalHotConflict() {
  }

  public PsRegionCapitalHotConflict(
    java.lang.String metaId)
  {
    this();
    this.metaId = metaId;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsRegionCapitalHotConflict(PsRegionCapitalHotConflict other) {
    if (other.isSetMetaId()) {
      this.metaId = other.metaId;
    }
    if (other.isSetInvolvedCount()) {
      java.util.Map<java.lang.String,java.lang.Integer> __this__involvedCount = new java.util.HashMap<java.lang.String,java.lang.Integer>(other.involvedCount);
      this.involvedCount = __this__involvedCount;
    }
  }

  public PsRegionCapitalHotConflict deepCopy() {
    return new PsRegionCapitalHotConflict(this);
  }

  @Override
  public void clear() {
    this.metaId = null;
    this.involvedCount = null;
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getMetaId() {
    return this.metaId;
  }

  public PsRegionCapitalHotConflict setMetaId(@org.apache.thrift.annotation.Nullable java.lang.String metaId) {
    this.metaId = metaId;
    return this;
  }

  public void unsetMetaId() {
    this.metaId = null;
  }

  /** Returns true if field metaId is set (has been assigned a value) and false otherwise */
  public boolean isSetMetaId() {
    return this.metaId != null;
  }

  public void setMetaIdIsSet(boolean value) {
    if (!value) {
      this.metaId = null;
    }
  }

  public int getInvolvedCountSize() {
    return (this.involvedCount == null) ? 0 : this.involvedCount.size();
  }

  public void putToInvolvedCount(java.lang.String key, int val) {
    if (this.involvedCount == null) {
      this.involvedCount = new java.util.HashMap<java.lang.String,java.lang.Integer>();
    }
    this.involvedCount.put(key, val);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.String,java.lang.Integer> getInvolvedCount() {
    return this.involvedCount;
  }

  public PsRegionCapitalHotConflict setInvolvedCount(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.Integer> involvedCount) {
    this.involvedCount = involvedCount;
    return this;
  }

  public void unsetInvolvedCount() {
    this.involvedCount = null;
  }

  /** Returns true if field involvedCount is set (has been assigned a value) and false otherwise */
  public boolean isSetInvolvedCount() {
    return this.involvedCount != null;
  }

  public void setInvolvedCountIsSet(boolean value) {
    if (!value) {
      this.involvedCount = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case META_ID:
      if (value == null) {
        unsetMetaId();
      } else {
        setMetaId((java.lang.String)value);
      }
      break;

    case INVOLVED_COUNT:
      if (value == null) {
        unsetInvolvedCount();
      } else {
        setInvolvedCount((java.util.Map<java.lang.String,java.lang.Integer>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case META_ID:
      return getMetaId();

    case INVOLVED_COUNT:
      return getInvolvedCount();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case META_ID:
      return isSetMetaId();
    case INVOLVED_COUNT:
      return isSetInvolvedCount();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsRegionCapitalHotConflict)
      return this.equals((PsRegionCapitalHotConflict)that);
    return false;
  }

  public boolean equals(PsRegionCapitalHotConflict that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_metaId = true && this.isSetMetaId();
    boolean that_present_metaId = true && that.isSetMetaId();
    if (this_present_metaId || that_present_metaId) {
      if (!(this_present_metaId && that_present_metaId))
        return false;
      if (!this.metaId.equals(that.metaId))
        return false;
    }

    boolean this_present_involvedCount = true && this.isSetInvolvedCount();
    boolean that_present_involvedCount = true && that.isSetInvolvedCount();
    if (this_present_involvedCount || that_present_involvedCount) {
      if (!(this_present_involvedCount && that_present_involvedCount))
        return false;
      if (!this.involvedCount.equals(that.involvedCount))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetMetaId()) ? 131071 : 524287);
    if (isSetMetaId())
      hashCode = hashCode * 8191 + metaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetInvolvedCount()) ? 131071 : 524287);
    if (isSetInvolvedCount())
      hashCode = hashCode * 8191 + involvedCount.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsRegionCapitalHotConflict other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetMetaId(), other.isSetMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.metaId, other.metaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetInvolvedCount(), other.isSetInvolvedCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetInvolvedCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.involvedCount, other.involvedCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsRegionCapitalHotConflict(");
    boolean first = true;

    sb.append("metaId:");
    if (this.metaId == null) {
      sb.append("null");
    } else {
      sb.append(this.metaId);
    }
    first = false;
    if (isSetInvolvedCount()) {
      if (!first) sb.append(", ");
      sb.append("involvedCount:");
      if (this.involvedCount == null) {
        sb.append("null");
      } else {
        sb.append(this.involvedCount);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (metaId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'metaId' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsRegionCapitalHotConflictStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsRegionCapitalHotConflictStandardScheme getScheme() {
      return new PsRegionCapitalHotConflictStandardScheme();
    }
  }

  private static class PsRegionCapitalHotConflictStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsRegionCapitalHotConflict> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsRegionCapitalHotConflict struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.metaId = iprot.readString();
              struct.setMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // INVOLVED_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map0 = iprot.readMapBegin();
                struct.involvedCount = new java.util.HashMap<java.lang.String,java.lang.Integer>(2*_map0.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _key1;
                int _val2;
                for (int _i3 = 0; _i3 < _map0.size; ++_i3)
                {
                  _key1 = iprot.readString();
                  _val2 = iprot.readI32();
                  struct.involvedCount.put(_key1, _val2);
                }
                iprot.readMapEnd();
              }
              struct.setInvolvedCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsRegionCapitalHotConflict struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.metaId != null) {
        oprot.writeFieldBegin(META_ID_FIELD_DESC);
        oprot.writeString(struct.metaId);
        oprot.writeFieldEnd();
      }
      if (struct.involvedCount != null) {
        if (struct.isSetInvolvedCount()) {
          oprot.writeFieldBegin(INVOLVED_COUNT_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32, struct.involvedCount.size()));
            for (java.util.Map.Entry<java.lang.String, java.lang.Integer> _iter4 : struct.involvedCount.entrySet())
            {
              oprot.writeString(_iter4.getKey());
              oprot.writeI32(_iter4.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsRegionCapitalHotConflictTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsRegionCapitalHotConflictTupleScheme getScheme() {
      return new PsRegionCapitalHotConflictTupleScheme();
    }
  }

  private static class PsRegionCapitalHotConflictTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsRegionCapitalHotConflict> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsRegionCapitalHotConflict struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.metaId);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetInvolvedCount()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetInvolvedCount()) {
        {
          oprot.writeI32(struct.involvedCount.size());
          for (java.util.Map.Entry<java.lang.String, java.lang.Integer> _iter5 : struct.involvedCount.entrySet())
          {
            oprot.writeString(_iter5.getKey());
            oprot.writeI32(_iter5.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsRegionCapitalHotConflict struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.metaId = iprot.readString();
      struct.setMetaIdIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map6 = iprot.readMapBegin(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32); 
          struct.involvedCount = new java.util.HashMap<java.lang.String,java.lang.Integer>(2*_map6.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _key7;
          int _val8;
          for (int _i9 = 0; _i9 < _map6.size; ++_i9)
          {
            _key7 = iprot.readString();
            _val8 = iprot.readI32();
            struct.involvedCount.put(_key7, _val8);
          }
        }
        struct.setInvolvedCountIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

