/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * GVG 战场建筑内队伍信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsStrongHoldArmyInfo implements org.apache.thrift.TBase<PsStrongHoldArmyInfo, PsStrongHoldArmyInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsStrongHoldArmyInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsStrongHoldArmyInfo");

  private static final org.apache.thrift.protocol.TField ARMY_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("armyId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField DETAIL_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("detailInfo", org.apache.thrift.protocol.TType.STRUCT, (short)5);
  private static final org.apache.thrift.protocol.TField START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("startTime", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField TOTAL_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("totalTime", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField ARRIVED_FIELD_DESC = new org.apache.thrift.protocol.TField("arrived", org.apache.thrift.protocol.TType.BOOL, (short)8);
  private static final org.apache.thrift.protocol.TField ARRIVE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("arriveTime", org.apache.thrift.protocol.TType.I64, (short)11);
  private static final org.apache.thrift.protocol.TField SORT_ARRIVE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("sortArriveTime", org.apache.thrift.protocol.TType.I64, (short)12);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsStrongHoldArmyInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsStrongHoldArmyInfoTupleSchemeFactory();

  /**
   * 部队id
   */
  public long armyId; // required
  /**
   * 部队详情 *
   */
  public @org.apache.thrift.annotation.Nullable PsStationArmyUnit detailInfo; // required
  /**
   * 行军开始时间 *
   */
  public long startTime; // optional
  /**
   * 行军总时间 *
   */
  public long totalTime; // optional
  /**
   * 是否到达 *
   */
  public boolean arrived; // optional
  /**
   * 行军到达时间 *
   */
  public long arriveTime; // optional
  /**
   * 出战顺序排序用 *
   */
  public long sortArriveTime; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 部队id
     */
    ARMY_ID((short)1, "armyId"),
    /**
     * 部队详情 *
     */
    DETAIL_INFO((short)5, "detailInfo"),
    /**
     * 行军开始时间 *
     */
    START_TIME((short)6, "startTime"),
    /**
     * 行军总时间 *
     */
    TOTAL_TIME((short)7, "totalTime"),
    /**
     * 是否到达 *
     */
    ARRIVED((short)8, "arrived"),
    /**
     * 行军到达时间 *
     */
    ARRIVE_TIME((short)11, "arriveTime"),
    /**
     * 出战顺序排序用 *
     */
    SORT_ARRIVE_TIME((short)12, "sortArriveTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ARMY_ID
          return ARMY_ID;
        case 5: // DETAIL_INFO
          return DETAIL_INFO;
        case 6: // START_TIME
          return START_TIME;
        case 7: // TOTAL_TIME
          return TOTAL_TIME;
        case 8: // ARRIVED
          return ARRIVED;
        case 11: // ARRIVE_TIME
          return ARRIVE_TIME;
        case 12: // SORT_ARRIVE_TIME
          return SORT_ARRIVE_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ARMYID_ISSET_ID = 0;
  private static final int __STARTTIME_ISSET_ID = 1;
  private static final int __TOTALTIME_ISSET_ID = 2;
  private static final int __ARRIVED_ISSET_ID = 3;
  private static final int __ARRIVETIME_ISSET_ID = 4;
  private static final int __SORTARRIVETIME_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.START_TIME,_Fields.TOTAL_TIME,_Fields.ARRIVED,_Fields.ARRIVE_TIME,_Fields.SORT_ARRIVE_TIME};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ARMY_ID, new org.apache.thrift.meta_data.FieldMetaData("armyId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DETAIL_INFO, new org.apache.thrift.meta_data.FieldMetaData("detailInfo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsStationArmyUnit.class)));
    tmpMap.put(_Fields.START_TIME, new org.apache.thrift.meta_data.FieldMetaData("startTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TOTAL_TIME, new org.apache.thrift.meta_data.FieldMetaData("totalTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ARRIVED, new org.apache.thrift.meta_data.FieldMetaData("arrived", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.ARRIVE_TIME, new org.apache.thrift.meta_data.FieldMetaData("arriveTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SORT_ARRIVE_TIME, new org.apache.thrift.meta_data.FieldMetaData("sortArriveTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsStrongHoldArmyInfo.class, metaDataMap);
  }

  public PsStrongHoldArmyInfo() {
  }

  public PsStrongHoldArmyInfo(
    long armyId,
    PsStationArmyUnit detailInfo)
  {
    this();
    this.armyId = armyId;
    setArmyIdIsSet(true);
    this.detailInfo = detailInfo;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsStrongHoldArmyInfo(PsStrongHoldArmyInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.armyId = other.armyId;
    if (other.isSetDetailInfo()) {
      this.detailInfo = new PsStationArmyUnit(other.detailInfo);
    }
    this.startTime = other.startTime;
    this.totalTime = other.totalTime;
    this.arrived = other.arrived;
    this.arriveTime = other.arriveTime;
    this.sortArriveTime = other.sortArriveTime;
  }

  public PsStrongHoldArmyInfo deepCopy() {
    return new PsStrongHoldArmyInfo(this);
  }

  @Override
  public void clear() {
    setArmyIdIsSet(false);
    this.armyId = 0;
    this.detailInfo = null;
    setStartTimeIsSet(false);
    this.startTime = 0;
    setTotalTimeIsSet(false);
    this.totalTime = 0;
    setArrivedIsSet(false);
    this.arrived = false;
    setArriveTimeIsSet(false);
    this.arriveTime = 0;
    setSortArriveTimeIsSet(false);
    this.sortArriveTime = 0;
  }

  /**
   * 部队id
   */
  public long getArmyId() {
    return this.armyId;
  }

  /**
   * 部队id
   */
  public PsStrongHoldArmyInfo setArmyId(long armyId) {
    this.armyId = armyId;
    setArmyIdIsSet(true);
    return this;
  }

  public void unsetArmyId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ARMYID_ISSET_ID);
  }

  /** Returns true if field armyId is set (has been assigned a value) and false otherwise */
  public boolean isSetArmyId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ARMYID_ISSET_ID);
  }

  public void setArmyIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ARMYID_ISSET_ID, value);
  }

  /**
   * 部队详情 *
   */
  @org.apache.thrift.annotation.Nullable
  public PsStationArmyUnit getDetailInfo() {
    return this.detailInfo;
  }

  /**
   * 部队详情 *
   */
  public PsStrongHoldArmyInfo setDetailInfo(@org.apache.thrift.annotation.Nullable PsStationArmyUnit detailInfo) {
    this.detailInfo = detailInfo;
    return this;
  }

  public void unsetDetailInfo() {
    this.detailInfo = null;
  }

  /** Returns true if field detailInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetDetailInfo() {
    return this.detailInfo != null;
  }

  public void setDetailInfoIsSet(boolean value) {
    if (!value) {
      this.detailInfo = null;
    }
  }

  /**
   * 行军开始时间 *
   */
  public long getStartTime() {
    return this.startTime;
  }

  /**
   * 行军开始时间 *
   */
  public PsStrongHoldArmyInfo setStartTime(long startTime) {
    this.startTime = startTime;
    setStartTimeIsSet(true);
    return this;
  }

  public void unsetStartTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  /** Returns true if field startTime is set (has been assigned a value) and false otherwise */
  public boolean isSetStartTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  public void setStartTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTTIME_ISSET_ID, value);
  }

  /**
   * 行军总时间 *
   */
  public long getTotalTime() {
    return this.totalTime;
  }

  /**
   * 行军总时间 *
   */
  public PsStrongHoldArmyInfo setTotalTime(long totalTime) {
    this.totalTime = totalTime;
    setTotalTimeIsSet(true);
    return this;
  }

  public void unsetTotalTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TOTALTIME_ISSET_ID);
  }

  /** Returns true if field totalTime is set (has been assigned a value) and false otherwise */
  public boolean isSetTotalTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TOTALTIME_ISSET_ID);
  }

  public void setTotalTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TOTALTIME_ISSET_ID, value);
  }

  /**
   * 是否到达 *
   */
  public boolean isArrived() {
    return this.arrived;
  }

  /**
   * 是否到达 *
   */
  public PsStrongHoldArmyInfo setArrived(boolean arrived) {
    this.arrived = arrived;
    setArrivedIsSet(true);
    return this;
  }

  public void unsetArrived() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ARRIVED_ISSET_ID);
  }

  /** Returns true if field arrived is set (has been assigned a value) and false otherwise */
  public boolean isSetArrived() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ARRIVED_ISSET_ID);
  }

  public void setArrivedIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ARRIVED_ISSET_ID, value);
  }

  /**
   * 行军到达时间 *
   */
  public long getArriveTime() {
    return this.arriveTime;
  }

  /**
   * 行军到达时间 *
   */
  public PsStrongHoldArmyInfo setArriveTime(long arriveTime) {
    this.arriveTime = arriveTime;
    setArriveTimeIsSet(true);
    return this;
  }

  public void unsetArriveTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ARRIVETIME_ISSET_ID);
  }

  /** Returns true if field arriveTime is set (has been assigned a value) and false otherwise */
  public boolean isSetArriveTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ARRIVETIME_ISSET_ID);
  }

  public void setArriveTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ARRIVETIME_ISSET_ID, value);
  }

  /**
   * 出战顺序排序用 *
   */
  public long getSortArriveTime() {
    return this.sortArriveTime;
  }

  /**
   * 出战顺序排序用 *
   */
  public PsStrongHoldArmyInfo setSortArriveTime(long sortArriveTime) {
    this.sortArriveTime = sortArriveTime;
    setSortArriveTimeIsSet(true);
    return this;
  }

  public void unsetSortArriveTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SORTARRIVETIME_ISSET_ID);
  }

  /** Returns true if field sortArriveTime is set (has been assigned a value) and false otherwise */
  public boolean isSetSortArriveTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SORTARRIVETIME_ISSET_ID);
  }

  public void setSortArriveTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SORTARRIVETIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ARMY_ID:
      if (value == null) {
        unsetArmyId();
      } else {
        setArmyId((java.lang.Long)value);
      }
      break;

    case DETAIL_INFO:
      if (value == null) {
        unsetDetailInfo();
      } else {
        setDetailInfo((PsStationArmyUnit)value);
      }
      break;

    case START_TIME:
      if (value == null) {
        unsetStartTime();
      } else {
        setStartTime((java.lang.Long)value);
      }
      break;

    case TOTAL_TIME:
      if (value == null) {
        unsetTotalTime();
      } else {
        setTotalTime((java.lang.Long)value);
      }
      break;

    case ARRIVED:
      if (value == null) {
        unsetArrived();
      } else {
        setArrived((java.lang.Boolean)value);
      }
      break;

    case ARRIVE_TIME:
      if (value == null) {
        unsetArriveTime();
      } else {
        setArriveTime((java.lang.Long)value);
      }
      break;

    case SORT_ARRIVE_TIME:
      if (value == null) {
        unsetSortArriveTime();
      } else {
        setSortArriveTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ARMY_ID:
      return getArmyId();

    case DETAIL_INFO:
      return getDetailInfo();

    case START_TIME:
      return getStartTime();

    case TOTAL_TIME:
      return getTotalTime();

    case ARRIVED:
      return isArrived();

    case ARRIVE_TIME:
      return getArriveTime();

    case SORT_ARRIVE_TIME:
      return getSortArriveTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ARMY_ID:
      return isSetArmyId();
    case DETAIL_INFO:
      return isSetDetailInfo();
    case START_TIME:
      return isSetStartTime();
    case TOTAL_TIME:
      return isSetTotalTime();
    case ARRIVED:
      return isSetArrived();
    case ARRIVE_TIME:
      return isSetArriveTime();
    case SORT_ARRIVE_TIME:
      return isSetSortArriveTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsStrongHoldArmyInfo)
      return this.equals((PsStrongHoldArmyInfo)that);
    return false;
  }

  public boolean equals(PsStrongHoldArmyInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_armyId = true;
    boolean that_present_armyId = true;
    if (this_present_armyId || that_present_armyId) {
      if (!(this_present_armyId && that_present_armyId))
        return false;
      if (this.armyId != that.armyId)
        return false;
    }

    boolean this_present_detailInfo = true && this.isSetDetailInfo();
    boolean that_present_detailInfo = true && that.isSetDetailInfo();
    if (this_present_detailInfo || that_present_detailInfo) {
      if (!(this_present_detailInfo && that_present_detailInfo))
        return false;
      if (!this.detailInfo.equals(that.detailInfo))
        return false;
    }

    boolean this_present_startTime = true && this.isSetStartTime();
    boolean that_present_startTime = true && that.isSetStartTime();
    if (this_present_startTime || that_present_startTime) {
      if (!(this_present_startTime && that_present_startTime))
        return false;
      if (this.startTime != that.startTime)
        return false;
    }

    boolean this_present_totalTime = true && this.isSetTotalTime();
    boolean that_present_totalTime = true && that.isSetTotalTime();
    if (this_present_totalTime || that_present_totalTime) {
      if (!(this_present_totalTime && that_present_totalTime))
        return false;
      if (this.totalTime != that.totalTime)
        return false;
    }

    boolean this_present_arrived = true && this.isSetArrived();
    boolean that_present_arrived = true && that.isSetArrived();
    if (this_present_arrived || that_present_arrived) {
      if (!(this_present_arrived && that_present_arrived))
        return false;
      if (this.arrived != that.arrived)
        return false;
    }

    boolean this_present_arriveTime = true && this.isSetArriveTime();
    boolean that_present_arriveTime = true && that.isSetArriveTime();
    if (this_present_arriveTime || that_present_arriveTime) {
      if (!(this_present_arriveTime && that_present_arriveTime))
        return false;
      if (this.arriveTime != that.arriveTime)
        return false;
    }

    boolean this_present_sortArriveTime = true && this.isSetSortArriveTime();
    boolean that_present_sortArriveTime = true && that.isSetSortArriveTime();
    if (this_present_sortArriveTime || that_present_sortArriveTime) {
      if (!(this_present_sortArriveTime && that_present_sortArriveTime))
        return false;
      if (this.sortArriveTime != that.sortArriveTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(armyId);

    hashCode = hashCode * 8191 + ((isSetDetailInfo()) ? 131071 : 524287);
    if (isSetDetailInfo())
      hashCode = hashCode * 8191 + detailInfo.hashCode();

    hashCode = hashCode * 8191 + ((isSetStartTime()) ? 131071 : 524287);
    if (isSetStartTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(startTime);

    hashCode = hashCode * 8191 + ((isSetTotalTime()) ? 131071 : 524287);
    if (isSetTotalTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(totalTime);

    hashCode = hashCode * 8191 + ((isSetArrived()) ? 131071 : 524287);
    if (isSetArrived())
      hashCode = hashCode * 8191 + ((arrived) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetArriveTime()) ? 131071 : 524287);
    if (isSetArriveTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(arriveTime);

    hashCode = hashCode * 8191 + ((isSetSortArriveTime()) ? 131071 : 524287);
    if (isSetSortArriveTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(sortArriveTime);

    return hashCode;
  }

  @Override
  public int compareTo(PsStrongHoldArmyInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetArmyId(), other.isSetArmyId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetArmyId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.armyId, other.armyId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetDetailInfo(), other.isSetDetailInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDetailInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.detailInfo, other.detailInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartTime(), other.isSetStartTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startTime, other.startTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTotalTime(), other.isSetTotalTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTotalTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.totalTime, other.totalTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetArrived(), other.isSetArrived());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetArrived()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.arrived, other.arrived);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetArriveTime(), other.isSetArriveTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetArriveTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.arriveTime, other.arriveTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSortArriveTime(), other.isSetSortArriveTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSortArriveTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sortArriveTime, other.sortArriveTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsStrongHoldArmyInfo(");
    boolean first = true;

    sb.append("armyId:");
    sb.append(this.armyId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("detailInfo:");
    if (this.detailInfo == null) {
      sb.append("null");
    } else {
      sb.append(this.detailInfo);
    }
    first = false;
    if (isSetStartTime()) {
      if (!first) sb.append(", ");
      sb.append("startTime:");
      sb.append(this.startTime);
      first = false;
    }
    if (isSetTotalTime()) {
      if (!first) sb.append(", ");
      sb.append("totalTime:");
      sb.append(this.totalTime);
      first = false;
    }
    if (isSetArrived()) {
      if (!first) sb.append(", ");
      sb.append("arrived:");
      sb.append(this.arrived);
      first = false;
    }
    if (isSetArriveTime()) {
      if (!first) sb.append(", ");
      sb.append("arriveTime:");
      sb.append(this.arriveTime);
      first = false;
    }
    if (isSetSortArriveTime()) {
      if (!first) sb.append(", ");
      sb.append("sortArriveTime:");
      sb.append(this.sortArriveTime);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'armyId' because it's a primitive and you chose the non-beans generator.
    if (detailInfo == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'detailInfo' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
    if (detailInfo != null) {
      detailInfo.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsStrongHoldArmyInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsStrongHoldArmyInfoStandardScheme getScheme() {
      return new PsStrongHoldArmyInfoStandardScheme();
    }
  }

  private static class PsStrongHoldArmyInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsStrongHoldArmyInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsStrongHoldArmyInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ARMY_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.armyId = iprot.readI64();
              struct.setArmyIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // DETAIL_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.detailInfo = new PsStationArmyUnit();
              struct.detailInfo.read(iprot);
              struct.setDetailInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // START_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.startTime = iprot.readI64();
              struct.setStartTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // TOTAL_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.totalTime = iprot.readI64();
              struct.setTotalTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // ARRIVED
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.arrived = iprot.readBool();
              struct.setArrivedIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // ARRIVE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.arriveTime = iprot.readI64();
              struct.setArriveTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // SORT_ARRIVE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sortArriveTime = iprot.readI64();
              struct.setSortArriveTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetArmyId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'armyId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsStrongHoldArmyInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ARMY_ID_FIELD_DESC);
      oprot.writeI64(struct.armyId);
      oprot.writeFieldEnd();
      if (struct.detailInfo != null) {
        oprot.writeFieldBegin(DETAIL_INFO_FIELD_DESC);
        struct.detailInfo.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.isSetStartTime()) {
        oprot.writeFieldBegin(START_TIME_FIELD_DESC);
        oprot.writeI64(struct.startTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetTotalTime()) {
        oprot.writeFieldBegin(TOTAL_TIME_FIELD_DESC);
        oprot.writeI64(struct.totalTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetArrived()) {
        oprot.writeFieldBegin(ARRIVED_FIELD_DESC);
        oprot.writeBool(struct.arrived);
        oprot.writeFieldEnd();
      }
      if (struct.isSetArriveTime()) {
        oprot.writeFieldBegin(ARRIVE_TIME_FIELD_DESC);
        oprot.writeI64(struct.arriveTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetSortArriveTime()) {
        oprot.writeFieldBegin(SORT_ARRIVE_TIME_FIELD_DESC);
        oprot.writeI64(struct.sortArriveTime);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsStrongHoldArmyInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsStrongHoldArmyInfoTupleScheme getScheme() {
      return new PsStrongHoldArmyInfoTupleScheme();
    }
  }

  private static class PsStrongHoldArmyInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsStrongHoldArmyInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsStrongHoldArmyInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI64(struct.armyId);
      struct.detailInfo.write(oprot);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetStartTime()) {
        optionals.set(0);
      }
      if (struct.isSetTotalTime()) {
        optionals.set(1);
      }
      if (struct.isSetArrived()) {
        optionals.set(2);
      }
      if (struct.isSetArriveTime()) {
        optionals.set(3);
      }
      if (struct.isSetSortArriveTime()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetStartTime()) {
        oprot.writeI64(struct.startTime);
      }
      if (struct.isSetTotalTime()) {
        oprot.writeI64(struct.totalTime);
      }
      if (struct.isSetArrived()) {
        oprot.writeBool(struct.arrived);
      }
      if (struct.isSetArriveTime()) {
        oprot.writeI64(struct.arriveTime);
      }
      if (struct.isSetSortArriveTime()) {
        oprot.writeI64(struct.sortArriveTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsStrongHoldArmyInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.armyId = iprot.readI64();
      struct.setArmyIdIsSet(true);
      struct.detailInfo = new PsStationArmyUnit();
      struct.detailInfo.read(iprot);
      struct.setDetailInfoIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.startTime = iprot.readI64();
        struct.setStartTimeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.totalTime = iprot.readI64();
        struct.setTotalTimeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.arrived = iprot.readBool();
        struct.setArrivedIsSet(true);
      }
      if (incoming.get(3)) {
        struct.arriveTime = iprot.readI64();
        struct.setArriveTimeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.sortArriveTime = iprot.readI64();
        struct.setSortArriveTimeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

