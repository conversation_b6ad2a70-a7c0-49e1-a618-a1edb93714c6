package com.lc.billion.icefire.cache;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import com.lc.billion.icefire.web.ScheduleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.game.biz.redis.RedisClient;
import com.lc.billion.icefire.web.bus.api.service.cache.model.impl.JsonCacheMap;
import com.lc.billion.icefire.web.bus.gm.model.Billboard;
import com.lc.billion.icefire.web.mapper.BillboardMapper;
import com.lc.billion.icefire.web.mapper.ServerMapper;
import com.google.common.collect.Lists;

/**
 * TODO 优化为pub/sub方式 将常用的数据进行缓存。包含game 等对象
 */
public class CacheManager {

	private static Logger LOG = LoggerFactory.getLogger(CacheManager.class);

	private static CacheManager instance;

	private JsonCacheMap<Integer, Billboard> billboards;

	private Map<String, String> langKeys = new ConcurrentHashMap<>();


	public CacheManager() {
	}

	public static CacheManager getInstance() {
		if (instance == null) {
			instance = new CacheManager();
		}
		return instance;
	}

	// ----------------------------------公告板数据---------------------------------//
	/**
	 * Spring启动的时候，从数据库捞一下
	 * 
	 * @param list
	 */
	public void loadBillboardFromDB(List<Billboard> list) {
		Map<Integer, Billboard> temp = new HashMap<>();
		for (Billboard b : list) {
			temp.put(b.getId(), b);
		}
		billboards.init(temp);
	}

	public void saveBillboard(Billboard billboard) {
		if (billboards.containsKey(billboard.getId())) {
			Billboard b = billboards.get(billboard.getId());
			billboard.setCreateTime(b.getCreateTime());
		}
		billboards.put(billboard.getId(), billboard);
	}

	public Billboard getBillboardById(int id) {
		return billboards.get(id);
	}

	public void removeBillboard(int id) {
		if (billboards.containsKey(id))
			billboards.remove(id);
	}

	public List<Billboard> getBillboardList() {
		List<Billboard> list = Lists.newArrayList(billboards.values());
		Collections.sort(list, (Comparator.comparingInt(Billboard::getId)).reversed());
		return list;
	}

	// ----------------------------------语言简写对应表---------------------------------//
	@SuppressWarnings("unchecked")
	public Map<String, String> initLangKeys() {
		// 客户端会直接传国家名称过来。需要对名称和简写 转换
		final String json = "{\"Afrikaans\":\"af\",\"Arabic\":\"ar\",\"Bulgarian\":\"bg\",\"Basque\":\"es\" ,\"Catalan\":\"ca\",\"Czech\":\"cs\",\"Danish\":\"da\",\"German\":\"de\",\"Greek\":\"el\",\"English\":\"en\",\"Spanish\":\"es\",\"Estonian\":\"et\",\"Finnish\":\"fi\",\"French\":\"fr\",\"Hebrew\":\"he\",\"Hungarian\":\"hu\",\"Indonesian\":\"id\",\"Italian\":\"it\",\"Japanese\":\"ja\",\"Korean\":\"ko\",\"Lithuanian\":\"lt\",\"Latvian\":\"lv\",\"Norwegian\":\"nb\",\"Dutch\":\"nl\",\"Polish\":\"pl\",\"Portuguese\":\"pt\",\"Romanian\":\"ro\",\"Russian\":\"ru\",\"Slovak\":\"sk\",\"Slovenian\":\"sl\",\"Swedish\":\"sv\",\"Thai\":\"th\",\"Turkish\":\"tr\",\"Ukrainian\":\"uk\",\"Vietnamese\":\"vi\",\"ChineseSimplified\":\"zh_cn\",\"ChineseTraditional\":\"zh_tw\",\"Chinese\":\"zh_cn\"}";

		langKeys = JSONObject.parseObject(json, Map.class);
		return langKeys;
	}

	public String getLanguagebyName(String name) {
		return langKeys.get(name);
	}

	////// init//////////
	public static final String SERVER_MAP_CACHE_KEY = "ALL_GAME_SERVER_MAP";
	private static final String BILLBOARD_MAP_CACHE_KEY = "ALL_BILLBOARD_MAP";

//	public Server convertServer(String json) {
//		Server s = new Server();
//		s.fromJson(json);
//		return s;
//	}

	public Billboard convertBillboard(String json) {
		Billboard b = new Billboard();
		b.fromJson(json);
		return b;
	}

	public void init(ServerMapper serverMapper, BillboardMapper billboardMapper, RedisClient client) {
//		gameServers = new JsonCacheMap<Long, Server>(SERVER_MAP_CACHE_KEY, client, Long::parseLong, this::convertServer);
		billboards = new JsonCacheMap<Integer, Billboard>(BILLBOARD_MAP_CACHE_KEY, client, Integer::parseInt, this::convertBillboard);
//		if (gameServers.needSynchronize()) {
//			LOG.info("init server localCache use cache");
//			gameServers.synchronizeFromCache();
//			refreshDiversionConfiguration();
//		} else {
//			LOG.info("init server localCache use db");
//			loadGameServerData(serverMapper.selectAll());
//		}
		if (billboards.needSynchronize()) {
			LOG.info("init billboard localCache use cache");
			billboards.synchronizeFromCache();
		} else {
			LOG.info("init billboard localCache use db");
			loadBillboardFromDB(billboardMapper.selectAll());
		}
		CacheManager.getInstance().initLangKeys();

		//启动调度，每10秒去尝试同步缓存
		ScheduleService.INSTANCE.scheduleAtFixedRate(()->{
			if (billboards.needSynchronizeFromRedis()) {
				LOG.info("load billboard localCache from redis");
				billboards.synchronizeFromCache();
			}
		},10,10, TimeUnit.SECONDS);
	}

}
