/**
 * 
 */
package com.lc.billion.icefire.web.bus.api.entity;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @sine 2016年3月2日 下午4:09:34
 */
public class Recharge {

	/**
	 * 订单号，与 game server 对账
	 */
	private String id;

	private long userId;

	private long roleId;

	private int serverId;

	private int cityLevel;

	private int platform;

	/**
	 * 1: 下单 2： 充值成功 3: 失效
	 */
	private int state;

	/**
	 * // 0-Test 1-Promo
	 */
	private int isTest;

	// 0-正常订单 1-福利订单
	private int welfare;

	private String productId;

	/**
	 * 货币价格
	 */
	private double currency;

	/**
	 * 本地货币币种
	 */
	private String currencyName;

	/**
	 * 本地货币数量
	 */
	private double localCurrency;

	/**
	 * 充值的钻石数量
	 */
	private int diamond;

	private Timestamp createTime;

	private Timestamp deliveryTime;
	
	private String originId;
	
	private int orderType;

	public String getOriginId() {
		return originId;
	}

	public void setOriginId(String originId) {
		this.originId = originId;
	}

	/**
	 * 是否打折
	 */
	private boolean discount;

	private String plat_orderId;
	private String packageName;
	private String plat_productId;
	private String purchaseTime;
	private String purchaseState;
	private String developerPayload;
	private String purchaseToken;
	/**
	 * 支付收据
	 */
	private String receipt;

	private String receiptBack;

	private int combinationItem;

	private String appsFlyerId;

	private String extData;

	/**
	 * 2021-4-1 sean:
	 * IOS支付订阅的最原始id
	 * 因为ios订阅之后只要订阅组不变，这个id就不会变
	 * 跟谷歌不一样，谷歌是每次新的订阅都会改变
	 * 为了不影响BI统计，所以新加该字段，通过该字段查询最新的recharge
	 */
	private String iosOriginalTID;

	/** 赛季id */
	private int season;

	public Recharge() {

	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getRoleId() {
		return roleId;
	}

	public void setRoleId(long roleId) {
		this.roleId = roleId;
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public int getCityLevel() {
		return cityLevel;
	}

	public void setCityLevel(int cityLevel) {
		this.cityLevel = cityLevel;
	}

	public int getPlatform() {
		return platform;
	}

	public void setPlatform(int platform) {
		this.platform = platform;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public double getCurrency() {
		return currency;
	}

	public void setCurrency(double currency) {
		this.currency = currency;
	}

	public int getDiamond() {
		return diamond;
	}

	public void setDiamond(int diamond) {
		this.diamond = diamond;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public Timestamp getDeliveryTime() {
		return deliveryTime;
	}

	public void setDeliveryTime(Timestamp deliveryTime) {
		this.deliveryTime = deliveryTime;
	}

	public String getRoleIdString() {
		return String.valueOf(roleId);
	}

	public boolean isDiscount() {
		return discount;
	}

	public void setDiscount(boolean discount) {
		this.discount = discount;
	}

	public String getPlat_orderId() {
		return plat_orderId;
	}

	public void setPlat_orderId(String plat_orderId) {
		this.plat_orderId = plat_orderId;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getPlat_productId() {
		return plat_productId;
	}

	public void setPlat_productId(String plat_productId) {
		this.plat_productId = plat_productId;
	}

	public String getPurchaseTime() {
		return purchaseTime;
	}

	public void setPurchaseTime(String purchaseTime) {
		this.purchaseTime = purchaseTime;
	}

	public String getPurchaseState() {
		return purchaseState;
	}

	public void setPurchaseState(String purchaseState) {
		this.purchaseState = purchaseState;
	}

	public String getDeveloperPayload() {
		return developerPayload;
	}

	public void setDeveloperPayload(String developerPayload) {
		this.developerPayload = developerPayload;
	}

	public String getPurchaseToken() {
		return purchaseToken;
	}

	public void setPurchaseToken(String purchaseToken) {
		this.purchaseToken = purchaseToken;
	}

	public int getIsTest() {
		return isTest;
	}

	public void setIsTest(int isTest) {
		this.isTest = isTest;
	}

	public String getCurrencyName() {
		return currencyName;
	}

	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}

	public double getLocalCurrency() {
		return localCurrency;
	}

	public void setLocalCurrency(double localCurrency) {
		this.localCurrency = localCurrency;
	}

	public String getReceipt() {
		return receipt;
	}

	public void setReceipt(String receipt) {
		this.receipt = receipt;
	}

	public int getWelfare() {
		return welfare;
	}

	public void setWelfare(int welfare) {
		this.welfare = welfare;
	}

	public int getCombinationItem() {
		return combinationItem;
	}

	public void setCombinationItem(int combinationItem) {
		this.combinationItem = combinationItem;
	}

	public String getReceiptBack() {
		return receiptBack;
	}

	public void setReceiptBack(String receiptBack) {
		this.receiptBack = receiptBack;
	}

	public int getOrderType() {
		return orderType;
	}

	public void setOrderType(int orderType) {
		this.orderType = orderType;
	}

	public String getAppsFlyerId() {
		return appsFlyerId;
	}

	public void setAppsFlyerId(String appsFlyerId) {
		this.appsFlyerId = appsFlyerId;
	}

	public String getExtData() {
		return extData;
	}

	public void setExtData(String extData) {
		this.extData = extData;
	}

	public String getIosOriginalTID() {
		return iosOriginalTID;
	}

	public void setIosOriginalTID(String iosOriginalTID) {
		this.iosOriginalTID = iosOriginalTID;
	}

	public int getSeason() {
		return season;
	}

	public void setSeason(int season) {
		this.season = season;
	}

	@Override public String toString() {
		return "Recharge{" + "id='" + id + '\'' + ", userId=" + userId + ", roleId=" + roleId + ", serverId=" + serverId + ", cityLevel=" + cityLevel + ", platform=" + platform
				+ ", state=" + state + ", isTest=" + isTest + ", welfare=" + welfare + ", productId='" + productId + '\'' + ", currency=" + currency + ", currencyName='"
				+ currencyName + '\'' + ", localCurrency=" + localCurrency + ", diamond=" + diamond + ", createTime=" + createTime + ", deliveryTime=" + deliveryTime
				+ ", originId='" + originId + '\'' + ", orderType=" + orderType + ", discount=" + discount + ", plat_orderId='" + plat_orderId + '\'' + ", packageName='"
				+ packageName + '\'' + ", plat_productId='" + plat_productId + '\'' + ", purchaseTime='" + purchaseTime + '\'' + ", purchaseState='" + purchaseState + '\''
				+ ", developerPayload='" + developerPayload + '\'' + ", purchaseToken='" + purchaseToken + '\'' + ", receipt='" + receipt + '\'' + ", receiptBack='" + receiptBack
				+ '\'' + ", combinationItem=" + combinationItem + ", appsFlyerId='" + appsFlyerId + '\'' + ", extData='" + extData + '\'' +", iosOriginalTID='" + iosOriginalTID + '\''
				+ '\'' + ", season=" + season + "}";
	}

	public boolean isPayed(){
		return state == RechargeState.PAY || state == RechargeState.GIVE_TOKEN;
	}
}
