package com.lc.billion.icefire.web.bus.api.entity;

/**
 * 订阅
 * date:2020年4月1日
 * <AUTHOR>
 */
public class Subscription {

	private String platOrderid;
	
	private long userId;

	private long roleId;

	private int serverId;
	// 正式订单:-1 测试订单:0
	private int isTest;
	// 普通订单:0  试用:1  订阅:2  续订:3
	private int orderType;
	
	private String rechargeId;
	
	private String originRechargeId;
	
	private String originPlatOrderid;
	
	private String productId;
	
	private long startTime;
	
	private long endTime;
	
	private int state;
	
	private long updateTime;

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getRoleId() {
		return roleId;
	}

	public void setRoleId(long roleId) {
		this.roleId = roleId;
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}


	public String getRechargeId() {
		return rechargeId;
	}

	public void setRechargeId(String rechargeId) {
		this.rechargeId = rechargeId;
	}

	public String getOriginRechargeId() {
		return originRechargeId;
	}

	public void setOriginRechargeId(String originRechargeId) {
		this.originRechargeId = originRechargeId;
	}

	public String getPlatOrderid() {
		return platOrderid;
	}

	public void setPlatOrderid(String platOrderid) {
		this.platOrderid = platOrderid;
	}

	public String getOriginPlatOrderid() {
		return originPlatOrderid;
	}

	public void setOriginPlatOrderid(String originPlatOrderid) {
		this.originPlatOrderid = originPlatOrderid;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public long getStartTime() {
		return startTime;
	}

	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}

	public long getEndTime() {
		return endTime;
	}

	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}

	public int getIsTest() {
		return isTest;
	}

	public void setIsTest(int isTest) {
		this.isTest = isTest;
	}

	public int getOrderType() {
		return orderType;
	}

	public void setOrderType(int orderType) {
		this.orderType = orderType;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public long getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(long updateTime) {
		this.updateTime = updateTime;
	}
}
