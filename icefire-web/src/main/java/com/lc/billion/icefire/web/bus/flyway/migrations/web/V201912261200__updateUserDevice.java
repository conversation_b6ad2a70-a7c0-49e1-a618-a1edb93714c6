package com.lc.billion.icefire.web.bus.flyway.migrations.web;

import com.lc.billion.icefire.web.bus.flyway.AbstractBaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class V201912261200__updateUserDevice extends AbstractBaseJavaMigration {

	@Override
	protected void update(Context context) throws Exception {

		Connection con = context.getConnection();
		String sql = "select SUBSTRING_INDEX(SUBSTRING_INDEX(a.idStr,',',b.help_topic_id + 1),',',-1) as id from( select group_concat(id order by role_main_city_level, create_time) as idStr from user where deviceid in (select deviceid from user group by deviceid having count(deviceid) > 1) GROUP BY deviceid) as a \n" +
				"join mysql.help_topic as b on b.help_topic_id < (length(a.idStr) - LENGTH(REPLACE(a.idStr,',',''))) ORDER BY b.help_topic_id;";
		PreparedStatement ps = con.prepareStatement(sql);
		ResultSet rs = ps.executeQuery();
		List<String> idList = new ArrayList<>();
		while(rs.next()){
			String id = rs.getString("id");
			idList.add(id);
		}
		close(ps);

		if(!idList .isEmpty()){
			sql = "update user set deviceid = null where id = ?";
			ps = con.prepareStatement(sql);
			for(String id : idList){
				ps.setObject(1, id);
				ps.addBatch();
			}
			ps.executeBatch();
			close(ps);

		}
	}

}
