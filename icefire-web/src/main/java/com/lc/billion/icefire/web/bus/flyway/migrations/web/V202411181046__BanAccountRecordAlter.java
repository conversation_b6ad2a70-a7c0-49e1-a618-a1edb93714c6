package com.lc.billion.icefire.web.bus.flyway.migrations.web;

import com.lc.billion.icefire.web.bus.flyway.AbstractBaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.sql.Connection;
import java.sql.PreparedStatement;

public class V202411181046__BanAccountRecordAlter extends AbstractBaseJavaMigration {
    @Override
    protected void update(Context context) throws Exception {
        Connection con = context.getConnection();
        String sql = "ALTER TABLE `ban_acccount_record` CONVERT TO CHARACTER SET utf8mb4;";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.execute();
        close(ps);
    }
}
