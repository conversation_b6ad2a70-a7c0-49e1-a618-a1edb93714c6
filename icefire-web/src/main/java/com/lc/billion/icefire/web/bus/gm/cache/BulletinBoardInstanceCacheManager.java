package com.lc.billion.icefire.web.bus.gm.cache;

import com.lc.billion.icefire.web.bus.gm.model.BulletinBoard;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: huyafei
 * @Date: 2021/9/2 11:03
 * @Description:
 *  公告的缓存管理
 *  因为玩家进入世界，主界面新增了公告预热
 *  以前是点击住城里面的公告才回去请求，每次请求需要查询数据库，其实也不是很好，但是可能很多玩家没有请求
 *  现在客户端改为登录之后就请求，这块每次再去查询数据库可能登录导量会有mysql数据库的压力
 *  所以增加公告缓存，公告本来也很少适合缓存在内存
 *
 *  TODO 应毕老师要求，不采用redis 发布定于机制啦 ，每5分钟查询1次数据库最新数据即可
 **/
public enum BulletinBoardInstanceCacheManager implements BaseCache<Long,BulletinBoard>{
    INSTANCE;
    private BulletinBoardInstanceCacheManager(){
        currentServerUUID=UUID.randomUUID().toString();
    }
    /**
     * 间隔300秒查询1次数据库
     */
    public static final int INTERVAL_SECOND=30;
    /**
     * 当前服务器的uuid 标识当前服务器
     */
    private String currentServerUUID;
    /**
     * 缓存map
     */
    private Map<Long,BulletinBoard> cache=new ConcurrentHashMap<>();

    public String getCurrentServerUUID() {
        return currentServerUUID;
    }

    @Override
    public BulletinBoard findById(Long aLong) {
        return cache.get(aLong);
    }

    @Override
    public List<BulletinBoard> findAll() {
        Collection<BulletinBoard> values = cache.values();
        List<BulletinBoard> _list=new ArrayList<>();
        if(values!=null && values.size()>0){
            _list.addAll(values);
        }
        /**
         * 按照数据库查询的方式排序
         */
        _list.sort((o1, o2) -> -Integer.compare(o1.getWeights(),o2.getWeights()));
        return _list;
    }

    @Override
    public void batchAddCache(List<BulletinBoard> _list) {
        Map<Long,BulletinBoard> newCache = new ConcurrentHashMap<>();
        if(_list!=null && _list.size()>0){
            _list.stream().forEach(e->{
                newCache.put(e.getId(),e);
            });
        }
        this.cache = newCache;
    }

    @Override
    public void addCache(BulletinBoard bulletinBoard) {
        if(bulletinBoard!=null){
            cache.put(bulletinBoard.getId(),bulletinBoard);
        }
    }

    @Override
    public void updateCache(BulletinBoard bulletinBoard) {
        if(bulletinBoard!=null){
            cache.put(bulletinBoard.getId(),bulletinBoard);
        }
    }

    @Override
    public void removeCache(Long aLong) {
        if(aLong!=null){
            cache.remove(aLong);
        }
    }
}
