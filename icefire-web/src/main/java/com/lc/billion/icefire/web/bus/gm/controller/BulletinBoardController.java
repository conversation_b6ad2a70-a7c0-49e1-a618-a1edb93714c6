package com.lc.billion.icefire.web.bus.gm.controller;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.game.biz.redis.RedisClient;
import com.lc.billion.icefire.game.biz.redis.pubsub.refreshServiceChannel.RefreshBulletinBoardChannel;
import com.lc.billion.icefire.web.bus.gm.cache.BulletinBoardInstanceCacheManager;
import com.lc.billion.icefire.web.bus.gm.model.Billboard;
import com.lc.billion.icefire.web.bus.gm.model.BulletinBoard;
import com.lc.billion.icefire.web.bus.gm.model.LanguageEmail;
import com.lc.billion.icefire.web.bus.gm.service.IBillboardService;
import com.lc.billion.icefire.web.bus.gm.service.IBulletinBoardService;
import com.lc.billion.icefire.web.bus.gm.service.ILanguageEmailService;
import com.lc.billion.icefire.web.response.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2020-7-1
 */

@RestController
@RequestMapping(value = "/console")
public class BulletinBoardController {

	private Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	private ILanguageEmailService languageEmailService;

	@Autowired
	private IBulletinBoardService bulletinBoardService;

	@Autowired
	@Qualifier("redisClient2")
	private RedisClient redisClient2;


	@RequestMapping(value = "/bulletin", produces = "text/plain; charset=UTF-8")
	public ModelAndView bulletinBoard() {
		ModelAndView view = new ModelAndView("console/bulletin");
		List<BulletinBoard> list = bulletinBoardService.selectAll();
		view.addObject("bulletins", list);
		return view;
	}

	/**
	 * 处理公告的redis发布
	 */
	@Deprecated
	public void handlerBulletinPublish(Object data,String type){
		JSONObject jsonObject=new JSONObject();
		jsonObject.put(RefreshBulletinBoardChannel.CURRENTSERVERUUID,BulletinBoardInstanceCacheManager.INSTANCE.getCurrentServerUUID());
		jsonObject.put(RefreshBulletinBoardChannel.TYPE,type);
		jsonObject.put(RefreshBulletinBoardChannel.DATA,data);
		redisClient2.publish(RefreshBulletinBoardChannel.class.getSimpleName(),jsonObject.toJSONString());
	}

	@RequestMapping(value = "/bulletin/update", produces = "text/plain; charset=UTF-8")
	public String bulletinUpdate(String params){
		try {

			BulletinBoard bulletin = JsonUtils.readValue(params, BulletinBoard.class);

			LanguageEmail email = languageEmailService.getLanguageEmailByKey(bulletin.getContentKey());
			if (email == null)
				return JsonUtils.toJson(Response.error(-1, "找不到内容！"));

			bulletinBoardService.save(bulletin);
			/**
			 * 这块添加和修改在一块
			 * 由于web是集群的需要通知其他web，内存发生变化
			 * 用redis的发布，订阅处理吧
			 */
			BulletinBoardInstanceCacheManager.INSTANCE.addCache(bulletin);
//			handlerBulletinPublish(bulletin,RefreshBulletinBoardChannel.UPDATE);

			//更新redis的公告更新时间
			redisClient2.set(IBulletinBoardService.BULLETIN_UPDATE_TIME, String.valueOf(System.currentTimeMillis()));
			return JsonUtils.toJson(Response.success(bulletin));
		} catch (Exception e) {
			LOG.error(e.getMessage());

			return JsonUtils.toJson(Response.error(-1, e.getMessage()));
		}
	}

	@RequestMapping(value = "/bulletin/remove", produces = "text/plain; charset=UTF-8")
	public String bulletinRemove(Long id){
		try {
			BulletinBoard b = bulletinBoardService.selectById(id);
			if (b == null)
				return JsonUtils.toJson(Response.error(-1, "没有找到记录"));

			if (!bulletinBoardService.delete(id)) {
				return JsonUtils.toJson(Response.error(-1, "删除失败"));
			}
			BulletinBoardInstanceCacheManager.INSTANCE.removeCache(id);
//			handlerBulletinPublish(id,RefreshBulletinBoardChannel.REMOVE);

			//更新redis的公告更新时间
			redisClient2.set(IBulletinBoardService.BULLETIN_UPDATE_TIME, String.valueOf(System.currentTimeMillis()));
			redisClient2.del(IBulletinBoardService.BULLETIN_VOTE_INFO + id);
			return JsonUtils.toJson(Response.success());
		} catch (Exception e) {
			LOG.error(e.getMessage());
			return JsonUtils.toJson(Response.error(-1, e.getMessage()));
		}
	}

}
