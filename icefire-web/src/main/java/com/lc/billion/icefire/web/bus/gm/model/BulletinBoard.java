package com.lc.billion.icefire.web.bus.gm.model;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;

/**
 * 游戏内公告板
 *
 * <AUTHOR>
 * @date 2020-6-30
 */
public class BulletinBoard {

	private long id;
	private String serverId;
	private String contentKey;
	private String platform;
	private int weights;
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp startTime;
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp expTime;
	private Timestamp createTime;
	private String pictureId;
	private String redirectId;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getContentKey() {
		return contentKey;
	}

	public void setContentKey(String contentKey) {
		this.contentKey = contentKey;
	}

	public int getWeights() {
		return weights;
	}

	public void setWeights(int weights) {
		this.weights = weights;
	}

	public String getServerId() {
		return serverId;
	}

	public void setServerId(String serverId) {
		this.serverId = serverId;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public Timestamp getStartTime() {
		return startTime;
	}

	public void setStartTime(Timestamp startTime) {
		this.startTime = startTime;
	}

	public Timestamp getExpTime() {
		return expTime;
	}

	public void setExpTime(Timestamp expTime) {
		this.expTime = expTime;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getPictureId() {
		return pictureId;
	}

	public void setPictureId(String pictureId) {
		this.pictureId = pictureId;
	}

	public String getRedirectId() {
		return redirectId;
	}

	public void setRedirectId(String redirectId) {
		this.redirectId = redirectId;
	}

	@Override
	public String toString() {
		ObjectMapper mapper = new ObjectMapper();
		String json = "";
		try {
			json = mapper.writeValueAsString(this);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return json;
	}

	public JSONObject toJson(){
		JSONObject json = new JSONObject();
		json.put("id", this.getId());
		json.put("contentKey", this.getContentKey());
		json.put("serverId", this.getServerId());
		json.put("platform", this.getPlatform());
		json.put("weights", this.getWeights());
		json.put("startTime", this.getStartTime().getTime());
		json.put("expTime", this.getExpTime().getTime());
		json.put("pictureId", this.getPictureId());
		json.put("redirectId", this.getRedirectId());
		return json;
	}

}
