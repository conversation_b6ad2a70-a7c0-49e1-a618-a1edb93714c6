package com.lc.billion.icefire.web.bus.gm.model;

import java.sql.Timestamp;

import com.alibaba.fastjson.JSONObject;

/**
 * 邮件
 * 
 * <AUTHOR>
 *
 *         2018年1月4日下午3:30:11
 */
public class Email {

	private String id;
	private int category; // 类别
	private String title;
	private String content;
	private String attachment;
	private String resource;
	private String condition;
	private int expire;
	private Timestamp createTime;

    private Email() {
    }

    public static Email createEmail() {
        return new Email();
    }

    public void fromJson(String json) {
		JSONObject jsonObject = JSONObject.parseObject(json);
		id = jsonObject.getString("i");
		category = jsonObject.getIntValue("c");
		title = jsonObject.getString("t");
		content = jsonObject.getString("ct");
		attachment = jsonObject.getString("a");
		resource = jsonObject.getString("r");
		condition = jsonObject.getString("co");
		expire = jsonObject.getIntValue("e");
	}

	public JSONObject toJson() {
		JSONObject json = new JSONObject();
		json.put("i", id);
		json.put("c", category);
		json.put("t", title);
		json.put("ct", content);
		json.put("a", attachment);
		json.put("r", resource);
		json.put("co", condition);
		json.put("e", expire);
		return json;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public int getCategory() {
		return category;
	}

	public void setCategory(int category) {
		this.category = category;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getAttachment() {
		return attachment;
	}

	public void setAttachment(String attachment) {
		this.attachment = attachment;
	}

	public String getResource() {
		return resource;
	}

	public void setResource(String resource) {
		this.resource = resource;
	}

	public String getCondition() {
		return condition;
	}

	public void setCondition(String condition) {
		this.condition = condition;
	}

	public int getExpire() {
		return expire;
	}

	public void setExpire(int expire) {
		this.expire = expire;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

}
