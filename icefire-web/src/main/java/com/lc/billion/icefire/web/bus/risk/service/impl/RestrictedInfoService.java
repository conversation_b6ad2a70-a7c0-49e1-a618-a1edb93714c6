package com.lc.billion.icefire.web.bus.risk.service.impl;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.web.bus.risk.entity.RestrictedInfo;
import com.lc.billion.icefire.web.bus.risk.service.IRestrictedInfoService;
import com.lc.billion.icefire.web.mapper.RestrictedInfoMapper;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @ClassName RestrictedInfoService
 * @Description
 * <AUTHOR>
 * @Date 2024/11/26 15:19
 * @Version 1.0
 */
@Service
public class RestrictedInfoService implements IRestrictedInfoService {

    @Resource
    private RestrictedInfoMapper restrictedInfoMapper;

    @Override
    public boolean insert(RestrictedInfo restrictedInfo) {
        return restrictedInfoMapper.insert(restrictedInfo);
    }

    @Override
    public boolean deleteOne(RestrictedInfo restrictedInfo) {
        return restrictedInfoMapper.deleteOne(restrictedInfo);
    }

    @Override
    public boolean deleteInfo(String info, int type) {
        RestrictedInfo restrictedInfo = new RestrictedInfo();
        restrictedInfo.setKeyInfo(info);
        restrictedInfo.setTypeId(type);
        return restrictedInfoMapper.deleteOne(restrictedInfo);
    }

    @Override
    public List<RestrictedInfo> getAll() {
        return restrictedInfoMapper.getAll();
    }

    public RestrictedInfo findOne(String msg, int type) {
        RestrictedInfo info = new RestrictedInfo();
        info.setKeyInfo(msg);
        info.setTypeId(type);
        return restrictedInfoMapper.findOne(info);
    }

    @Override
    public boolean addInfo(String info, int type, String auth) {
        RestrictedInfo restrictedInfo = findOne(info, type);
        if (restrictedInfo != null) {
            return false;
        }
        // 插入
        restrictedInfo = new RestrictedInfo();
        restrictedInfo.setKeyInfo(info);
        restrictedInfo.setTypeId(type);
        restrictedInfo.setAuth(auth);
        restrictedInfo.setCreateTime(TimeUtil.getNow());
        return restrictedInfoMapper.insert(restrictedInfo);
    }

    @Override
    public boolean addInfos(List<RestrictedInfo> infos) {
        return restrictedInfoMapper.insertMany(infos);
    }

    @Override
    public boolean hasIp(String ip) {
        RestrictedInfo restrictedInfo = new RestrictedInfo();
        restrictedInfo.setKeyInfo(ip);
        restrictedInfo.setTypeId(RestrictedInfo.IP_TYPE);
        return restrictedInfoMapper.findOne(restrictedInfo) != null;
    }

    @Override
    public RestrictedInfo findInfoByWord(String word) {
        RestrictedInfo info = new RestrictedInfo();
        info.setKeyInfo(word);
        info.setTypeId(RestrictedInfo.WORD_TYPE);
        return restrictedInfoMapper.findOne(info);
    }

    @Override
    public List<RestrictedInfo> searchInfo(String word) {
        RestrictedInfo info = new RestrictedInfo();
        info.setKeyInfo(word);
        return restrictedInfoMapper.searchInfo(info);
    }
}
