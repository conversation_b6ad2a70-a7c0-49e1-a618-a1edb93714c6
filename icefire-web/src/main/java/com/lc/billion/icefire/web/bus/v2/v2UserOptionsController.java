package com.lc.billion.icefire.web.bus.v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.web.bus.gm.model.UserOptions;
import com.lc.billion.icefire.web.bus.gm.service.UserOptionType;
import com.lc.billion.icefire.web.bus.user.entity.Admin;
import com.lc.billion.icefire.web.bus.user.service.IAdminService;
import com.lc.billion.icefire.web.bus.user.service.IUserOptionsService;
import com.lc.billion.icefire.web.response.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping(value = "/console/v2/userOptions")
public class v2UserOptionsController {

    @Autowired
    private IUserOptionsService userOptionService;

    @Autowired
    private IAdminService adminService;

    private Logger LOG = LoggerFactory.getLogger(getClass());

    @RequestMapping(value = "/userList")
    public @ResponseBody String getUserList() {
        List<Admin> userNames = adminService.selectUserNames();
        if (userNames == null) {
            userNames = new ArrayList<>();
        }
        List<String> nameList = new ArrayList<>();
        for (Admin admin : userNames) {
            nameList.add(admin.getName());
        }
        return JsonUtils.toJson(Response.success(nameList));
    }

    @RequestMapping(value = "/list")
    public @ResponseBody String selectUserOptions(String userName, int optionType, String startTime, String endTime) {
        List<UserOptions> options;
        if (StringUtils.isEmpty(userName)) {
            if (optionType > 0) {
                options = userOptionService.findByOptionType(optionType);
            } else {
                options = userOptionService.select();
            }
        } else {
            if (optionType > 0) {
                options = userOptionService.findByNameAndType(userName, optionType);
            } else {
                options = userOptionService.findByUserName(userName);
            }
        }
        JSONObject json = new JSONObject();
        DateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        long startT = 0l;
        long endT = 0l;
        try {
            if (!StringUtils.isEmpty(startTime)) {
                Date startDate = sdf1.parse(startTime);
                startT = startDate.getTime();
            }
            if (!StringUtils.isEmpty(endTime)) {
                Date endDate = sdf1.parse(endTime);
                endT = endDate.getTime();
            }
        } catch (ParseException e) {
        }
        if (!StringUtils.isEmpty(startTime) && startT <= 0) {
            json.put("ret", -1);
            json.put("msg", "time format is error.");
            return json.toJSONString();
        }
        if (!StringUtils.isEmpty(endTime) && endT <= 0) {
            json.put("ret", -1);
            json.put("msg", "time format is error.");
            return json.toJSONString();
        }
        if (startT > 0 && endT > 0 && startT > endT) {
            json.put("ret", -1);
            json.put("msg", "startTime > endTime is error.");
            return json.toJSONString();
        }

        List<UserOptions> os = new ArrayList<>();
        for (UserOptions op : options) {
            if (optionType != 0 && op.getOptionType() != optionType) {
                continue;
            }
            if ((startT > 0 && op.getOptionTime() < startT) || (endT > 0 && op.getOptionTime() > endT)) {
                continue;
            }
            os.add(op);
            // 最多200条
            if (os.size() >= 200) {
                break;
            }
        }

        JSONArray ja = new JSONArray();
        if (os.size() == 0) {
            json.put("ret", 0);
            json.put("data", ja.toJSONString());
            return json.toJSONString();
        }
        DateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (UserOptions option : os) {
            JSONObject jo = new JSONObject();
            jo.put("option_name", UserOptionType.findOptionType(option.getOptionType()).getOptionName());
            jo.put("options", option.getOptions());
            jo.put("server_id", option.getServerId());
            jo.put("user_name", option.getUserName());
            jo.put("option_time", sdf2.format(option.getOptionTime()));
            ja.add(jo);
        }
        json.put("ret", 0);
        json.put("data", ja.toJSONString());
        return json.toJSONString();
    }
}
