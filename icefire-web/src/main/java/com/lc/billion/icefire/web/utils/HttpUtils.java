package com.lc.billion.icefire.web.utils;

import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.lc.billion.icefire.core.monitoring.HttpClientMonitoring;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

public class HttpUtils {

	public static final Logger LOG = LoggerFactory.getLogger(HttpUtils.class);

	private static int connectTimeout = 5000; // 5s
	private static int socketTimeout = 5000; // 5s
	private static int maxTotalConnections = 200; // 最大连接数，可调整

	private static CloseableHttpClient httpClient;

	public static String post(String url, Map<String, String> params) throws Exception {
		List<NameValuePair> nvps = new ArrayList<>();
		for (Map.Entry<String, String> entry : params.entrySet()) {
			nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}

		HttpEntity entity = new UrlEncodedFormEntity(nvps);
		return post(url, entity);
	}

	public static String postJSON(String url, JSONObject params) throws Exception {
		HttpEntity entity = new StringEntity(params.toJSONString(), ContentType.APPLICATION_JSON);
		return post(url, entity);
	}

	public static String post(final String url, final HttpEntity entity) throws Exception {
		if (httpClient == null) {
			init();
		}

		HttpPost httpPost = new HttpPost(url);
		httpPost.setEntity(entity);

		// 设置User-Agent
		httpPost.addHeader("User-Agent", "monster center server");

		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			@Override
			public String handleResponse(final HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status == HttpStatus.SC_OK) {
					HttpEntity entity = response.getEntity();

					return entity != null ? EntityUtils.toString(entity, "UTF-8") : null;
				} else {
					LOG.error("request url: {}, response status: {}", url, status);

					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};

		try {
			String body = httpClient.execute(httpPost, responseHandler);
			return body;
		} catch (IOException e) {
			LOG.error("", e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}

	public static String get(final String url) throws Exception {
		if (httpClient == null) {
			init();
		}

		HttpGet httpPost = new HttpGet(url);

		// 设置User-Agent
		httpPost.addHeader("User-Agent", "monster center server");

		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			@Override
			public String handleResponse(final HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status == HttpStatus.SC_OK) {
					HttpEntity entity = response.getEntity();

					return entity != null ? EntityUtils.toString(entity, "UTF-8") : null;
				} else {
					LOG.error("request url: {}, response status: {}", url, status);

					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};

		try {
			String body = httpClient.execute(httpPost, responseHandler);
			return body;
		} catch (IOException e) {
			LOG.error("", e);
		} finally {
			httpPost.releaseConnection();
		}
		return null;
	}

	/**
	 * 格式化参数，将参数转化成a=b&c=d&e=f的形式
	 * 
	 * @param parameters
	 * @return
	 */
	public static String formatParameters(Map<String, String> parameters) {
		StringBuilder sb = new StringBuilder();
		Iterator<Entry<String, String>> iterator = parameters.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, String> entry = iterator.next();
			sb.append(String.format("%s=%s&", entry.getKey(), entry.getValue()));
		}
		if (parameters.size() > 0) {
			return sb.substring(0, sb.length() - 1);
		}
		return sb.toString();
	}

	/**
	 * 格式化参数，将参数转化成a=b&c=d&e=f的形式
	 * 
	 * @param parameters
	 * @return
	 */
	public static String formatInovationParameters(Map<String, String[]> parameters) {
		StringBuilder sb = new StringBuilder();
		Iterator<Entry<String, String[]>> iterator = parameters.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, String[]> entry = iterator.next();
			sb.append(String.format("%s=%s&", entry.getKey(), join(entry.getValue())));
		}
		if (parameters.size() > 0) {
			return sb.substring(0, sb.length() - 1);
		}
		return sb.toString();
	}

	public static String join(String[] value) {
		if (value == null) {
			return "";
		}
		StringBuilder _sb = new StringBuilder();
		for (String a : value) {
			_sb.append(a);
		}
		return _sb.toString();
	}

	// 初始化， 创建httpclient实例
	@SuppressWarnings("deprecation")
	private static void init() {
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(connectTimeout).setSocketTimeout(socketTimeout).setExpectContinueEnabled(true)
				.setAuthenticationEnabled(true).build();

		HttpRequestRetryHandler retryHandler = new HttpRequestRetryHandler() {
			@Override
			public boolean retryRequest(IOException e, int retryNum, HttpContext httpContext) {
				if (retryNum >= 3) {
					return false;
				}

				if (e instanceof org.apache.http.NoHttpResponseException || e instanceof org.apache.http.client.ClientProtocolException || e instanceof java.net.SocketException) {
					return true;
				}
				return false;
			}
		};

		ConnectionKeepAliveStrategy myStrategy = new ConnectionKeepAliveStrategy() {
			@Override
			public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
				try {

					HeaderElementIterator it = new BasicHeaderElementIterator(response.headerIterator(HTTP.CONN_KEEP_ALIVE));
					while (it.hasNext()) {
						HeaderElement he = it.nextElement();
						String param = he.getName();
						String value = he.getValue();
						if (value != null && param.equalsIgnoreCase("timeout")) {
							return Long.parseLong(value) * 1000;
						}
					}
					return 3 * 1000;

				} catch (Exception e) {
					LOG.error("", e);
				}

				return 0;
			}
		};

		try {
			SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
				// 信任所有
				@Override
				public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
					return true;
				}
			}).build();

			HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
			SSLConnectionSocketFactory sslFactory = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);

			Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
					.register("http", PlainConnectionSocketFactory.getSocketFactory()).register("https", sslFactory).build();

			PoolingHttpClientConnectionManager connMgr = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
			connMgr.setMaxTotal(maxTotalConnections);
			connMgr.setDefaultMaxPerRoute((connMgr.getMaxTotal()));

			HttpClientBuilder builder = HttpClients.custom().setKeepAliveStrategy(myStrategy).setDefaultRequestConfig(requestConfig).setSslcontext(sslContext)
					.setConnectionManager(connMgr).setRetryHandler(retryHandler);

			// 添加监控支持
			builder = HttpClientMonitoring.addMonitoring(builder, "HttpUtils");

			httpClient = builder.build();

		} catch (Exception e) {
			LOG.error("", e);
		}
	}

	public static HttpClient getHttpClient() {
		return httpClient;
	}

	// 销毁
	public static void destroy() {
		if (httpClient != null) {
			try {
				httpClient.close();
				httpClient = null;
			} catch (Exception e) {
				LOG.error("", e);
			}
		}
	}

	/**
	 * 获取真实ip
	 * 
	 * @param request
	 * @return
	 */
	public static String getIpAddr(HttpServletRequest request) {
	    String ip = null;

	    //X-Forwarded-For：Squid 服务代理
	    String ipAddresses = request.getHeader("X-Forwarded-For");

	    if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
	        //Proxy-Client-IP：apache 服务代理
	        ipAddresses = request.getHeader("Proxy-Client-IP");
	    }

	    if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
	        //WL-Proxy-Client-IP：weblogic 服务代理
	        ipAddresses = request.getHeader("WL-Proxy-Client-IP");
	    }

	    if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
	        //HTTP_CLIENT_IP：有些代理服务器
	        ipAddresses = request.getHeader("HTTP_CLIENT_IP");
	    }

	    if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
	        //X-Real-IP：nginx服务代理
	        ipAddresses = request.getHeader("X-Real-IP");
	    }

	    //有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
	    if (ipAddresses != null && ipAddresses.length() != 0) {
	        ip = ipAddresses.split(",")[0];
	    }

	    //还是不能获取到，最后再通过request.getRemoteAddr();获取
	    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
	        ip = request.getRemoteAddr();
	    }
	    return ip;
	}

	public static String getLocalHost() {
		// 根据网卡取本机配置的IP
		InetAddress inet = null;
		try {
			inet = InetAddress.getLocalHost();
			return inet.getHostAddress();
		} catch (UnknownHostException e) {
			LOG.error("", e);
		}
		return "127.0.0.1";
	}

	/**
	 * 格式化参数，将参数转化成a=b&c=d&e=f的形式
	 *
	 * @param parameters
	 * @return
	 */
	public static String sortAndformatParameters(Map<String, String> parameters) {
		StringBuilder sb = new StringBuilder();
		List<String> entryList = parameters.keySet().stream().sorted().collect(Collectors.toList());
		for (String entry : entryList) {
			sb.append(String.format("%s=%s&", entry, parameters.get(entry)));
		}
		if (parameters.size() > 0) {
			return sb.substring(0, sb.length() - 1);
		}
		return sb.toString();
	}

}
