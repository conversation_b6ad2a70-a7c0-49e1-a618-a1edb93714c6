<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<div class="panel panel-primary">
	<div class="panel-heading">
		<h3 class="panel-title">联盟信息查询</h3>
	</div>
	<div class="panel-body">
		<!-- search condition -->
		<div class="row">
			<div class="col-sm-6">
				<div class="panel panel-info mask">
					<div class="panel-heading">
						<h3 class="panel-title">
							<span class="glyphicon glyphicon-search">&nbsp;</span>信息查询
						</h3>
					</div>
					<div class="panel-body">

							<div class="tab-pane" id="byName">
								<form role="form" class="form-horizontal">
									<div class="form-group">
										<!-- <label class="col-sm-2 control-label">选服</label> -->
										<!-- <div class="col-sm-10">
											<select id="serverSelect-role" class="selectpicker"
												data-width="100%">
												<c:forEach var="server" items="${servers}">
													<option value="${server.id}">${server.name}</option>
												</c:forEach>
											</select>
										</div> -->
									</div>
									<div class="form-group">
										<label class="col-sm-2 control-label">联盟id</label>
										<div class="col-sm-10">
											<input type="text" class="form-control" id="allianceId">
										</div>
									</div>
								</form>
							</div>
							<button class="btn btn-info btn-block" id="searchBtn-allianceQuery"
								type="button">查询</button>
						</div>

					</div>
				</div>
			</div>
		</div>
		<!-- search condition -->

		<!-- content  -->
		<div class="row">
			<div class="col-sm-12">
					<div class="panel panel-info">
						<div class="panel-heading">
							<h3 class="panel-title">
								<span class="glyphicon glyphicon-th">&nbsp;</span>联盟基本信息
							</h3>
						</div>
						<div class="panel-body">
							<table class="table">
								<tr>
									<td>玩家所在联盟：</td>
									<td id="alliance-name">0</td>
									<td>联盟ID：</td>
									<td id="alliance-id">0</td>
									<td>联盟简称：</td>
									<td id="alliance-alias">0</td>
								</tr>
								<tr>
									<td>旗帜：</td>
									<td id="alliance-banner">0</td>
									<td>人数上限：</td>
									<td id="alliance-maxMem">0</td>
									<td>盟主ID：</td>
									<td id="alliance-leaderid">0</td>
								</tr>
								<tr>
									<td>总战斗力：</td>
									<td id="alliance-capacity">0</td>
									<td>公告：</td>
									<td id="alliance-notice">0</td>
									<td>宣言：</td>
									<td id="alliance-declaration">0</td>
								</tr>
								<tr>
									<td>总荣誉：</td>
									<td id="alliance-honour">0</td>
									<td>成员数：</td>
									<td id="alliance-mem">0</td>
									<td>创建时间：</td>
									<td id="alliance-createTime">0</td>
								</tr>
                                <tr>
                                    <td><button class="btn btn-info btn-block" id="alliance-reset-alliance-notice" type="button">重置联盟公告</button></td>
                                    <td><button class="btn btn-info btn-block" id="alliance-reset-alliance-announcement" type="button">重置联盟宣言</button></td>
                                </tr>
							</table>
						</div>
					</div>
				</div>
			<div class="col-sm-12">
				<div class="panel panel-info">
					<div class="panel-heading">
						<h3 class="panel-title">
							<span class="glyphicon glyphicon-th">&nbsp;</span>联盟其他信息
						</h3>
					</div>
					<div class="panel-body">
						<input type="hidden" class="form-control" id="role-id">
						<input type="hidden" class="form-control" id="role-server-id">
						<table class="table" id="tabAllianceQuerys">

						</table>
					</div>
				</div>
			</div>
		</div>
		<!-- content  -->
	</div>
</div>

