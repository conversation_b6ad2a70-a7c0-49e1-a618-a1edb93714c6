<%@ page contentType="text/html; charset=UTF-8" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="panel panel-primary mask">
    <div class="panel-heading">
        <h3 class="panel-title">批量添加资源</h3>
    </div>
    <div class="panel-body">
		<div class="row">
			<div class="col-sm-6">
				<div class="panel panel-info">
					<div class="panel-heading">
						<h3 class="panel-title"><span class="glyphicon glyphicon-send">&nbsp;</span>批量添加资源</h3>
					</div>
					<div class="panel-body">
					<input type="hidden" id="username" value="${user.name}">
						<form role="form" class="form-horizontal" enctype="multipart/form-data" id="uploadForm">
							<div class="form-group">
								<div class="col-sm-10">
									<div class="panel panel-info">
										<div class="panel-heading">
											<h3 class="panel-title">
												<nobr><span class="glyphicon glyphicon-th">&nbsp;</span>表格：请按以下格式填写csv文件。注：无表头！！！</nobr>
												<nobr><span class="glyphicon glyphicon-th">&nbsp;</span> 5：绑钻       6：粮食         7：木材     8：水  </nobr>
												<nobr><span class="glyphicon glyphicon-th">&nbsp;</span> 9：铁         10：金币        11：燃料   15：战斗经验</nobr>
												<nobr><span class="glyphicon glyphicon-th">&nbsp;</span> 16：发展经验  17：幸存者货币  22：个人联盟荣誉</nobr>
											</h3>
										</div>
										<div class="panel-body">
											<table border="1">
												<tr>
													<th>serverId</th>
													<th><center>roleId</center></th>
													<th><nobr>资源Id|正负数量;资源Id|正负数量</nobr></th>
												</tr>
												<tr>
                                                    <td><center>1</center></td>
                                                    <td>100000000000001</td>
                                                    <td><center>1|1000;5|-500</center></td>
                                                </tr>
											</table>
										</div>

										<div style="padding-left:16px;padding-right:180px">
												<input type="file"  class="form-control"  id="file" name="file">
										</div>

										<div style="padding-top:8px;padding-left:200px;padding-bottom:8px;">
											  <button  class="btn btn-info" id="modifyResources" style="display: inline-block;padding-left:10px;"  type="button">批量添加</button>
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title"><span class="glyphicon glyphicon-send">&nbsp;</span>添加记录</h3>
                    </div>
                    <div class="panel-body">
                        <table class="table" id="tabAddResourcesRecord">
                            <tr>
                                <th>ID</th>
                                <th>日期</th>
                                <th>备注</th>
                            </tr>
                         </table>

                         <div class="col-sm-6" style="padding-top:8px;padding-left:230px;">
                              <button class="btn btn-info btn-block" id="searchAddResourcesRecord" type="button">查询</button>
                         </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function() {
        var username = document.getElementById('username').value;
        $('#modifyResources').on("click", function () {
            if (!confirm("确定要执行批量添加资源操作吗!")) {
                return;
            }

            var formData = new FormData($("#uploadForm")[0]);
            formData.append('applicant', username);
            $.ajax({
                url: '/console/resources/modify',
                type: 'POST',
                cache: false,
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                },
                complete: function (result) {
                }
            }).done(function (result) {
                var resultStr = JSON.parse(result);
                if(resultStr.ret == 0){
                    alert("批量添加资源成功" );
                }else{
                    alert("批量添加资源失败！");
                }
            });
        });
    });
</script>
