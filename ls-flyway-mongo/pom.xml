<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.longtech.icefire</groupId>
		<artifactId>icefire-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../icefire-parent</relativePath>
	</parent>

	<artifactId>ls-flyway-mongo</artifactId>

	<dependencies>
		<!-- https://mvnrepository.com/artifact/org.mongodb/mongo-java-driver -->
<!-- 		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>mongo-java-driver</artifactId>
			<version>3.12.3</version>
		</dependency>
 -->
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-legacy</artifactId>
        </dependency>
 		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.springframework.data/spring-data-mongodb -->
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-mongodb</artifactId>
			<!-- <version>2.2.13.RELEASE</version>
			 -->
			<version>4.3.1</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.google.android/android -->
		<dependency>
			<groupId>com.google.android</groupId>
			<artifactId>android</artifactId>
			<version>*******</version>
			<scope>provided</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.jboss/jboss-vfs -->
		<dependency>
			<groupId>org.jboss</groupId>
			<artifactId>jboss-vfs</artifactId>
			<version>3.2.15.Final</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.osgi/org.eclipse.osgi -->
		<dependency>
			<groupId>org.eclipse.osgi</groupId>
			<artifactId>org.eclipse.osgi</artifactId>
			<version>3.7.1</version>
		</dependency>

	</dependencies>

	<build>
		<finalName>test</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
				<filtering>false</filtering>
			</resource>
		</resources>
	</build>
</project>