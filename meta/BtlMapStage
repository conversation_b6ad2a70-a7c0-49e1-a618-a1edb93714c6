[{"id": "1", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1001101,1,1|-2~2,22~25,1001102,2,1|-2~2,22~25,1001103,1,1|-2.5~1.5,36~42,1001101,8,1|0~0,40~40,1001107,1,1|-2.5~1.5,43~48,1001102,8,1|-2.5~1.5,50~60,1001103,8,1|0~0,58~58,1001109,1,1|1.5~4,70~75,1001101,11,1|-4~-1.5,70~75,1001102,12,1|-1.5~1.5,70~75,1001103,10,1|1~3,75~75,1001106,1,1", "Building": "0,15,1001201|2.3,15,1001202|0,32,1001203|-2.3,40,1001204|2.3,50,1001205|-2.3,63,1001206|0,63,1001207|2.3,63,1001208"}, {"id": "2", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-3~1,25~31,1004104,5,1|-3~1,32~38,1004102,6,1|-3~1,39~45,1004101,5,1|-3~1,45~50,1004107,2,1|-3~0,62~66,1004103,8,1|0~3,62~66,1004102,8,1|-3~0,66~68,1004109,1,1|0~3,66~68,1004108,1,1|1~3,69~72,1004103,12,1|-1.5~1.5,75~80,1004101,10,1|1.5~4,75~80,1004102,10,1|-4~-1.5,75~80,1004103,10,1|-0.5~0.5,81~83,1004105,1,1", "Building": "2.3,15,1004201|2.3,25,1004202|2.3,30,1004203|2.3,35,1004204|2.3,40,1004205|2.3,45,1004206|2.3,55,1004207|2.3,60,1004208|2.3,65,1004209|2.3,70,1004210"}, {"id": "3", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1003101,1,1|-1.5~1.5,29~40,1003102,12,1|0~0,42~42,1003108,1,1|-1.5~1.5,48~55,1003103,13,1|0~0,51~51,1003109,1,1|-1.5~1.5,58~61,1003103,9,1|0~3,63~66,1003104,10,1|-1.5~1.5,72~75,1003101,10,1|-2.5~-2.5,73~73,1003107,1,1|1.5~3.5,75~78,1003102,12,1|2~2,78~78,1003108,1,1|-4~-1.5,80~84,1003103,10,1|0~0,85~85,1003106,1,1", "Building": "-2.3,15,1003201|0,15,1003202|2.3,15,1003203|0,28,1003204|-2.3,42,1003205|2.3,42,1003206|-2.3,56,1003207|0,56,1003208|2.3,56,1003209|-2.3,70,1003210|0,70,1003211"}, {"id": "4", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-3~1,25~31,1004104,5,1|-3~1,32~38,1004102,6,1|-3~1,39~45,1004101,5,1|-3~1,45~50,1004107,2,1|-3~0,62~66,1004103,8,1|0~3,62~66,1004102,8,1|-3~0,66~68,1004109,1,1|0~3,66~68,1004108,1,1|1~3,69~72,1004103,12,1|-1.5~1.5,75~80,1004101,10,1|1.5~4,75~80,1004102,10,1|-4~-1.5,75~80,1004103,10,1|-0.5~0.5,81~83,1004105,1,1", "Building": "2.3,15,1004201|2.3,25,1004202|2.3,30,1004203|2.3,35,1004204|2.3,40,1004205|2.3,45,1004206|2.3,55,1004207|2.3,60,1004208|2.3,65,1004209|2.3,70,1004210"}, {"id": "5", "FieldType": "2", "PassCondition": "2", "Speed": "3", "MapSize": "8,180", "MapOffSetX": "0", "Monster": "0~0,16~16,1005101,1,0|0~0,20~20,1005101,1,0|-3~3,24~24,1005101,3,0|-1~1,37~40,1005102,12,0|-3~-1.5,44~46,1005103,12,0|-1~1,50~53,1005104,12,0|1.5~3,60~63,1005105,8,0|-1~1,76~79,1005102,12,0|-1~1,83~85,1005103,12,0|1.5~3,86~87,1005104,12,0|1.5~3,92~94,1005105,8,0|-1~1,93~95,1005106,8,0", "Building": "-2.3,20,1005201|2.3,20,1005202|-2.3,40,1005203|2.3,40,1005204|-2.3,62,1005205|2.3,69,1005206|-2.3,75,1005207|-2.3,88,1005208|2.3,88,1005209"}]