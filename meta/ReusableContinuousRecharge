[{"id": "101", "isTest": "0", "configEditor": "", "groupId": "1", "type": "1", "desc": "TopUpReward01", "rewardNew": "3000001", "img": "ui/images/hero_figure/guojia", "needValue": "6800", "showValue": "100000", "needMoney": "", "priceValue": "", "order": "", "priceId": "", "group": "", "showBatch": ""}, {"id": "102", "isTest": "0", "configEditor": "", "groupId": "1", "type": "2", "desc": "TopUpReward02", "rewardNew": "3000002", "img": "ui/images/recharge/Ui_Cumulate_Pic_02", "needValue": "16000", "showValue": "205000", "needMoney": "", "priceValue": "", "order": "", "priceId": "", "group": "", "showBatch": ""}, {"id": "103", "isTest": "0", "configEditor": "", "groupId": "1", "type": "3", "desc": "TopUpReward03", "rewardNew": "3000003", "img": "ui/images/recharge/Ui_Cumulate_Pic_02", "needValue": "27000", "showValue": "210000", "needMoney": "", "priceValue": "", "order": "", "priceId": "", "group": "", "showBatch": ""}, {"id": "104", "isTest": "0", "configEditor": "", "groupId": "1", "type": "4", "desc": "TopUpReward04", "rewardNew": "3000004", "img": "ui/images/recharge/Ui_Cumulate_Pic_02", "needValue": "40000", "showValue": "270000", "needMoney": "", "priceValue": "", "order": "", "priceId": "", "group": "", "showBatch": ""}]