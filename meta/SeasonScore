[{"id": "1", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "1", "para2": "", "para3": "", "score": "1"}, {"id": "2", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "2", "para2": "", "para3": "", "score": "3"}, {"id": "3", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "3", "para2": "", "para3": "", "score": "5"}, {"id": "4", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "4", "para2": "", "para3": "", "score": "15"}, {"id": "5", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "5", "para2": "", "para3": "", "score": "19"}, {"id": "6", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "6", "para2": "", "para3": "", "score": "24"}, {"id": "7", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "7", "para2": "", "para3": "", "score": "30"}, {"id": "8", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "8", "para2": "", "para3": "", "score": "38"}, {"id": "9", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "9", "para2": "", "para3": "", "score": "48"}, {"id": "10", "season": "1", "isTest": "", "desc": "", "type": "1", "para1": "10", "para2": "", "para3": "", "score": "60"}, {"id": "11", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "1", "para2": "", "para3": "", "score": "10"}, {"id": "12", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "2", "para2": "", "para3": "", "score": "30"}, {"id": "13", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "3", "para2": "", "para3": "", "score": "50"}, {"id": "14", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "4", "para2": "", "para3": "", "score": "150"}, {"id": "15", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "5", "para2": "", "para3": "", "score": "190"}, {"id": "16", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "6", "para2": "", "para3": "", "score": "240"}, {"id": "17", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "7", "para2": "", "para3": "", "score": "300"}, {"id": "18", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "8", "para2": "", "para3": "", "score": "380"}, {"id": "19", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "9", "para2": "", "para3": "", "score": "480"}, {"id": "20", "season": "1", "isTest": "", "desc": "", "type": "2", "para1": "10", "para2": "", "para3": "", "score": "600"}, {"id": "21", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "1", "para2": "", "para3": "", "score": "10"}, {"id": "22", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "2", "para2": "", "para3": "", "score": "10"}, {"id": "23", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "3", "para2": "", "para3": "", "score": "10"}, {"id": "24", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "4", "para2": "", "para3": "", "score": "30"}, {"id": "25", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "5", "para2": "", "para3": "", "score": "40"}, {"id": "26", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "6", "para2": "", "para3": "", "score": "50"}, {"id": "27", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "7", "para2": "", "para3": "", "score": "60"}, {"id": "28", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "8", "para2": "", "para3": "", "score": "80"}, {"id": "29", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "9", "para2": "", "para3": "", "score": "100"}, {"id": "30", "season": "1", "isTest": "", "desc": "", "type": "4", "para1": "10", "para2": "", "para3": "", "score": "120"}, {"id": "31", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "1", "para2": "", "para3": "", "score": "1"}, {"id": "32", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "2", "para2": "", "para3": "", "score": "3"}, {"id": "33", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "3", "para2": "", "para3": "", "score": "5"}, {"id": "34", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "4", "para2": "", "para3": "", "score": "15"}, {"id": "35", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "5", "para2": "", "para3": "", "score": "19"}, {"id": "36", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "6", "para2": "", "para3": "", "score": "24"}, {"id": "37", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "7", "para2": "", "para3": "", "score": "30"}, {"id": "38", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "8", "para2": "", "para3": "", "score": "38"}, {"id": "39", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "9", "para2": "", "para3": "", "score": "48"}, {"id": "40", "season": "2", "isTest": "", "desc": "", "type": "1", "para1": "10", "para2": "", "para3": "", "score": "60"}, {"id": "41", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "1", "para2": "", "para3": "", "score": "10"}, {"id": "42", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "2", "para2": "", "para3": "", "score": "30"}, {"id": "43", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "3", "para2": "", "para3": "", "score": "50"}, {"id": "44", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "4", "para2": "", "para3": "", "score": "150"}, {"id": "45", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "5", "para2": "", "para3": "", "score": "190"}, {"id": "46", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "6", "para2": "", "para3": "", "score": "240"}, {"id": "47", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "7", "para2": "", "para3": "", "score": "300"}, {"id": "48", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "8", "para2": "", "para3": "", "score": "380"}, {"id": "49", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "9", "para2": "", "para3": "", "score": "480"}, {"id": "50", "season": "2", "isTest": "", "desc": "", "type": "2", "para1": "10", "para2": "", "para3": "", "score": "600"}, {"id": "51", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "1", "para2": "", "para3": "", "score": "10"}, {"id": "52", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "2", "para2": "", "para3": "", "score": "10"}, {"id": "53", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "3", "para2": "", "para3": "", "score": "10"}, {"id": "54", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "4", "para2": "", "para3": "", "score": "30"}, {"id": "55", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "5", "para2": "", "para3": "", "score": "40"}, {"id": "56", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "6", "para2": "", "para3": "", "score": "50"}, {"id": "57", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "7", "para2": "", "para3": "", "score": "60"}, {"id": "58", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "8", "para2": "", "para3": "", "score": "80"}, {"id": "59", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "9", "para2": "", "para3": "", "score": "100"}, {"id": "60", "season": "2", "isTest": "", "desc": "", "type": "4", "para1": "10", "para2": "", "para3": "", "score": "120"}, {"id": "61", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "1", "para2": "", "para3": "", "score": "1"}, {"id": "62", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "2", "para2": "", "para3": "", "score": "3"}, {"id": "63", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "3", "para2": "", "para3": "", "score": "5"}, {"id": "64", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "4", "para2": "", "para3": "", "score": "15"}, {"id": "65", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "5", "para2": "", "para3": "", "score": "19"}, {"id": "66", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "6", "para2": "", "para3": "", "score": "24"}, {"id": "67", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "7", "para2": "", "para3": "", "score": "30"}, {"id": "68", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "8", "para2": "", "para3": "", "score": "38"}, {"id": "69", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "9", "para2": "", "para3": "", "score": "48"}, {"id": "70", "season": "3", "isTest": "", "desc": "", "type": "1", "para1": "10", "para2": "", "para3": "", "score": "60"}, {"id": "71", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "1", "para2": "", "para3": "", "score": "10"}, {"id": "72", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "2", "para2": "", "para3": "", "score": "30"}, {"id": "73", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "3", "para2": "", "para3": "", "score": "50"}, {"id": "74", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "4", "para2": "", "para3": "", "score": "150"}, {"id": "75", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "5", "para2": "", "para3": "", "score": "190"}, {"id": "76", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "6", "para2": "", "para3": "", "score": "240"}, {"id": "77", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "7", "para2": "", "para3": "", "score": "300"}, {"id": "78", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "8", "para2": "", "para3": "", "score": "380"}, {"id": "79", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "9", "para2": "", "para3": "", "score": "480"}, {"id": "80", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "10", "para2": "", "para3": "", "score": "600"}, {"id": "94", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "11", "para2": "", "para3": "", "score": "700"}, {"id": "95", "season": "3", "isTest": "", "desc": "", "type": "2", "para1": "12", "para2": "", "para3": "", "score": "800"}, {"id": "81", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "1", "para2": "", "para3": "", "score": "10"}, {"id": "82", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "2", "para2": "", "para3": "", "score": "10"}, {"id": "83", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "3", "para2": "", "para3": "", "score": "10"}, {"id": "84", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "4", "para2": "", "para3": "", "score": "30"}, {"id": "85", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "5", "para2": "", "para3": "", "score": "40"}, {"id": "86", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "6", "para2": "", "para3": "", "score": "50"}, {"id": "87", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "7", "para2": "", "para3": "", "score": "60"}, {"id": "88", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "8", "para2": "", "para3": "", "score": "80"}, {"id": "89", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "9", "para2": "", "para3": "", "score": "100"}, {"id": "90", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "10", "para2": "", "para3": "", "score": "120"}, {"id": "96", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "11", "para2": "", "para3": "", "score": "140"}, {"id": "97", "season": "3", "isTest": "", "desc": "", "type": "4", "para1": "12", "para2": "", "para3": "", "score": "160"}, {"id": "91", "season": "1", "isTest": "", "desc": "", "type": "3", "para1": "7", "para2": "", "para3": "", "score": "6"}, {"id": "92", "season": "2", "isTest": "", "desc": "", "type": "3", "para1": "7", "para2": "", "para3": "", "score": "6"}, {"id": "93", "season": "3", "isTest": "", "desc": "", "type": "3", "para1": "7", "para2": "", "para3": "", "score": "6"}]