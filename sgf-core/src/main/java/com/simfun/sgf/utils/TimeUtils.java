package com.simfun.sgf.utils;

import java.util.Calendar;

/**
 * <AUTHOR>
 */
public class TimeUtils {

	/**
	 * 1分钟有多少秒
	 */
	public static final long MINUTE_SECONDS = 60;

	/**
	 * 1小时有多少秒
	 */
	public static final long HOUR_SECONDS = 60 * MINUTE_SECONDS;

	/**
	 * 1天有多少秒
	 */
	public static final long DAY_SECONDS = 24 * HOUR_SECONDS;

	/**
	 * 1秒有多少毫秒
	 */
	public static final long SECONDS_MILLIS = 1000;

	/**
	 * 1分钟有多少毫秒
	 */
	public static final long MINUTE_MILLIS = 60 * SECONDS_MILLIS;

	/**
	 * 1小时有多少毫秒
	 */
	public static final long HOUR_MILLIS = 60 * MINUTE_MILLIS;

	/**
	 * 1小时有多少分钟
	 */
	public static final long HOUR_MINUTES = 60;

	/**
	 * 1天有多少毫秒
	 */
	public static final long DAY_MILLIS = 24 * HOUR_MILLIS;

	/**
	 * 判断指定的两个时间是否在同一天内
	 *
	 * @param time1
	 * @param time2
	 * @return
	 */
	public static boolean isSameDay(long time1, long time2) {
		Calendar cal = Calendar.getInstance();

		cal.setTimeInMillis(time1);
		int year1 = cal.get(Calendar.YEAR);
		int day1 = cal.get(Calendar.DAY_OF_YEAR);

		cal.setTimeInMillis(time2);
		int year2 = cal.get(Calendar.YEAR);
		int day2 = cal.get(Calendar.DAY_OF_YEAR);

		return (year1 == year2) && (day1 == day2);
	}

	/**
	 * 判断指定的两个时间是否在同一天内
	 *
	 * @param time1
	 * @param time2
	 * @return
	 */
	public static boolean isSameWeek(long time1, long time2) {
		Calendar cal = Calendar.getInstance();

		cal.setTimeInMillis(time1);
		int year1 = cal.get(Calendar.YEAR);
		int week1 = cal.get(Calendar.WEEK_OF_YEAR);

		cal.setTimeInMillis(time2);
		int year2 = cal.get(Calendar.YEAR);
		int week2 = cal.get(Calendar.WEEK_OF_YEAR);

		return (year1 == year2) && (week1 == week2);
	}

	/**
	 * 计算指定的两个时间的间隔天数
	 *
	 * @param time1
	 * @param time2
	 * @return
	 */
	public static int betweenDays(long time1, long time2) {
		long beginOfDay1 = getBeginOfDay(time1);
		long beginOfDay2 = getBeginOfDay(time2);

		long d = Math.abs(beginOfDay1 - beginOfDay2);

		return (int) (d / DAY_MILLIS);
	}

	/**
	 * 得到指定时间当天的开始时间，也就是当天的 00:00:00.000
	 *
	 * @param time
	 * @return
	 */
	public static long getBeginOfDay(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}

	/**
	 * 得到指定时间当天的结束时间，也就是当天的 23:59:59.999
	 *
	 * @param time
	 * @return
	 */
	public static long getEndOfDay(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		cal.set(Calendar.MILLISECOND, 999);
		return cal.getTimeInMillis();
	}

	/**
	 * 获得指定时间的下一个小时的时间
	 *
	 * <p>
	 * 比如现在是2016.10.17 17:50，那么返回的时间是2016.10.17 18:00
	 *
	 * @param time 指定的时间
	 * @return
	 */
	public static long getNextHour(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.add(Calendar.HOUR_OF_DAY, 1);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}

	/**
	 * 获得指定时间的下一天的时间
	 *
	 * <p>
	 * 比如现在是2016.10.17 17:50，那么返回的时间是2016.10.18 00:00
	 *
	 * @param time 指定的时间
	 * @return
	 */
	public static long getNextDay(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.add(Calendar.DATE, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}

	/**
	 * 本周开启时间
	 */
	public static long getWeekStartTime(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		// 指定周一为起始时间
		int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
		if (dayOfWeek == Calendar.SUNDAY) {
			cal.setTimeInMillis(time - DAY_MILLIS);
		}
		cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);

		return cal.getTimeInMillis();
	}

	/**
	 * 下周开启时间
	 */
	public static long getNextWeekStartTime(long time) {
		long weekStartTime = getWeekStartTime(time);
		return weekStartTime + 7 * TimeUtils.DAY_MILLIS;
	}

	/**
	 * 获取月份
	 */
	public static int getMonth(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		return cal.get(Calendar.MONTH) + 1;
	}

	/**
	 * 获取月份第几日
	 */
	public static int getMonthDay(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		return cal.get(Calendar.DAY_OF_MONTH);
	}

	public static long getBeginOfMonth(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}

	public static long getBeginOfMonthDay(long time, int day) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.DAY_OF_MONTH, day);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}

	public static long getEndOfMonthDay(long time, int day) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.DAY_OF_MONTH, day);
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		cal.set(Calendar.MILLISECOND, 999);
		return cal.getTimeInMillis();
	}

}
