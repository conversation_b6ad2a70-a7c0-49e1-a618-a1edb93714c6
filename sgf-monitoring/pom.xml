<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.simfun.sgf</groupId>
        <artifactId>sgf-parent</artifactId>
        <version>1.2.0-SNAPSHOT</version>
        <relativePath>../sgf-parent</relativePath>
    </parent>

    <artifactId>sgf-monitoring</artifactId>
    <name>Simple Game Framework Monitoring</name>
    <description>轻量级监控模块，基于Micrometer和Prometheus</description>

    <properties>
        <micrometer.version>1.15.1</micrometer.version>
    </properties>

    <dependencies>
        <!-- Micrometer核心 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
            <version>${micrometer.version}</version>
        </dependency>

        <!-- Prometheus注册表 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>${micrometer.version}</version>
        </dependency>

        <!-- Java 11 HttpClient 监控 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-java11</artifactId>
            <version>${micrometer.version}</version>
        </dependency>

        <!-- Apache HttpComponents 4.x -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
            <scope>provided</scope>
        </dependency>

        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
            <version>3.3.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>


        <!-- AspectJ -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.9.22</version>
        </dependency>

        <!-- Annotation API -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>


        <!-- 日志 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.13.2</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>