package com.simfun.sgf.monitoring.annotation;

import com.simfun.sgf.monitoring.config.MonitoringConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用监控功能
 * <p>
 * 在Spring配置类上添加此注解以启用监控功能
 * </p>
 *
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(MonitoringConfiguration.class)
public @interface EnableMonitoring {
    
    /**
     * 应用名称，用于指标标签
     */
    String applicationName() default "";
    
    /**
     * 是否启用JVM指标
     */
    boolean enableJvmMetrics() default true;
    
}