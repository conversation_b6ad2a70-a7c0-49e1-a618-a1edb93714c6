package com.simfun.sgf.monitoring.aop;

import com.simfun.sgf.monitoring.annotation.Counted;
import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;


/**
 * 计数监控切面
 */
@Aspect
@Order(2)
public class CountedAspect {
    
    private static final Logger log = LoggerFactory.getLogger(CountedAspect.class);
    
    private final MeterRegistryManager meterRegistryManager;
    
    public CountedAspect(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }
    
    @Around("@annotation(counted)")
    public Object countMethod(ProceedingJoinPoint joinPoint, Counted counted) throws Throwable {
        return processCountedExecution(joinPoint, counted);
    }
    
    private Object processCountedExecution(ProceedingJoinPoint joinPoint, Counted counted) throws Throwable {
        var metricName = buildMetricName(joinPoint, counted);
        var description = counted.description().isEmpty() ?
                "调用次数: " + joinPoint.getSignature().toShortString() : counted.description();

        boolean success = false;
        Throwable exception = null;
        
        try {
            var result = joinPoint.proceed();
            success = true;
            return result;
        } catch (Throwable t) {
            exception = t;
            throw t;
        } finally {
            recordCountingMetrics(metricName, description, success, exception, counted);
        }
    }
    
    private void recordCountingMetrics(String metricName, String description, boolean success,
                                     Throwable exception, Counted counted) {
        meterRegistryManager.safeExecuteAsync(() -> {
            if (counted.recordFailuresOnly() && success) {
                return;
            }
            
            var tags = buildTags(counted, success, exception);
            var counter = meterRegistryManager.counter(metricName, description, tags);
            counter.increment();
        });
    }
    
    private String buildMetricName(ProceedingJoinPoint joinPoint, Counted counted) {
        if (!counted.name().isEmpty()) {
            return counted.name();
        }
        
        var className = joinPoint.getTarget().getClass().getSimpleName();
        var methodName = joinPoint.getSignature().getName();
        
        return String.format("method.invocations.%s.%s", 
                className.toLowerCase(), methodName);
    }
    
    private String[] buildTags(Counted counted, boolean success, Throwable exception) {
        var extraTags = counted.extraTags();
        String[] baseTags = {"status", success ? "success" : "failure"};
        
        if (exception != null) {
            String[] exceptionTags = {"exception_type", exception.getClass().getSimpleName()};
            return combineArrays(extraTags, baseTags, exceptionTags);
        }
        
        return combineArrays(extraTags, baseTags);
    }
    
    private String[] combineArrays(String[]... arrays) {
        int totalLength = 0;
        for (String[] array : arrays) {
            totalLength += array.length;
        }
        
        String[] result = new String[totalLength];
        int index = 0;
        
        for (String[] array : arrays) {
            System.arraycopy(array, 0, result, index, array.length);
            index += array.length;
        }
        
        return result;
    }
} 