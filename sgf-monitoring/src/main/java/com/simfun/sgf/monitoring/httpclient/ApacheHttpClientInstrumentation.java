package com.simfun.sgf.monitoring.httpclient;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Timer;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpRequestWrapper;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.protocol.HttpContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Apache HttpComponents 4.x 监控装饰器
 * <p>
 * 为 Apache HttpComponents 4.x HttpClient 添加 Micrometer 监控指标
 * </p>
 */
@Component
public class ApacheHttpClientInstrumentation {

    private static final Logger log = LoggerFactory.getLogger(ApacheHttpClientInstrumentation.class);

    private final MeterRegistryManager meterRegistryManager;

    public ApacheHttpClientInstrumentation(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }

    /**
     * 为 HttpClientBuilder 添加监控拦截器
     *
     * @param builder    HttpClientBuilder
     * @param clientName 客户端名称，用于指标标签
     * @return 添加了监控的 HttpClientBuilder
     */
    public HttpClientBuilder addMonitoring(HttpClientBuilder builder, String clientName) {
        try {
            // 添加请求拦截器
            builder.addInterceptorFirst((HttpRequest request, HttpContext context) -> {
                context.setAttribute("monitoring.start.time", System.nanoTime());
                context.setAttribute("monitoring.client.name", clientName);
            });

            // 添加响应拦截器
            builder.addInterceptorLast((HttpResponse response, HttpContext context) -> {
                recordMetrics(context, response, null);
            });

            log.info("为 Apache HttpClient 添加监控拦截器，客户端: {}", clientName);
            return builder;

        } catch (Exception e) {
            log.error("添加 Apache HttpClient 监控失败，客户端: {}，使用原始 builder", clientName, e);
            return builder;
        }
    }

    /**
     * 记录监控指标
     */
    private void recordMetrics(HttpContext context, HttpResponse response, Throwable exception) {
        try {
            Long startTime = (Long) context.getAttribute("monitoring.start.time");
            String clientName = (String) context.getAttribute("monitoring.client.name");

            if (startTime == null || clientName == null) {
                return;
            }

            long durationNanos = System.nanoTime() - startTime;
            int statusCode = response != null ? response.getStatusLine().getStatusCode() : 0;
            String method = extractMethod(context);
            String uri = extractUri(context);

            // 记录计时器指标
            String[] tags = {
                    "client.name", clientName,
                    "method", method,
                    "status", String.valueOf(statusCode),
                    "uri", uri,
                    "outcome", determineOutcome(statusCode, exception)
            };

            Timer timer = meterRegistryManager.timer(
                    "http.client.requests",
                    "Apache HttpClient 请求计时",
                    tags
            );
            timer.record(durationNanos, TimeUnit.NANOSECONDS);

            // 记录计数器指标
            meterRegistryManager.incrementCounter(
                    "http.client.requests.total",
                    "client.name", clientName,
                    "method", method,
                    "status", String.valueOf(statusCode),
                    "outcome", determineOutcome(statusCode, exception)
            );

        } catch (Exception e) {
            log.warn("记录 Apache HttpClient 监控指标失败", e);
        }
    }

    /**
     * 提取HTTP方法
     */
    private String extractMethod(HttpContext context) {
        try {
            HttpRequest request = (HttpRequest) context.getAttribute("http.request");
            if (request != null) {
                return request.getRequestLine().getMethod();
            }
        } catch (Exception e) {
            log.debug("提取HTTP方法失败", e);
        }
        return "UNKNOWN";
    }

    /**
     * 提取URI模式
     */
    private String extractUri(HttpContext context) {
        try {
            HttpRequest request = (HttpRequest) context.getAttribute("http.request");
            if (request instanceof HttpUriRequest) {
                URI uri = ((HttpUriRequest) request).getURI();
                return extractUriPattern(uri.toString());
            } else if (request instanceof HttpRequestWrapper) {
                HttpRequestWrapper wrapper = (HttpRequestWrapper) request;
                if (wrapper.getOriginal() instanceof HttpUriRequest) {
                    URI uri = ((HttpUriRequest) wrapper.getOriginal()).getURI();
                    return extractUriPattern(uri.toString());
                }
            }
        } catch (Exception e) {
            log.debug("提取URI失败", e);
        }
        return "UNKNOWN";
    }

    /**
     * 提取URI模式，减少标签基数
     */
    private String extractUriPattern(String uri) {
        if (uri == null) {
            return "UNKNOWN";
        }

        try {
            // 移除查询参数
            int queryIndex = uri.indexOf('?');
            if (queryIndex > 0) {
                uri = uri.substring(0, queryIndex);
            }

            // 移除协议和主机部分，只保留路径
            if (uri.startsWith("http://") || uri.startsWith("https://")) {
                int pathStart = uri.indexOf('/', uri.indexOf("://") + 3);
                if (pathStart > 0) {
                    uri = uri.substring(pathStart);
                }
            }

            // 替换数字ID为占位符，减少标签基数
            uri = uri.replaceAll("/\\d+", "/{id}");

            return uri.isEmpty() ? "/" : uri;

        } catch (Exception e) {
            log.debug("处理URI模式失败: {}", uri, e);
            return "UNKNOWN";
        }
    }

    /**
     * 确定请求结果
     */
    private String determineOutcome(int statusCode, Throwable exception) {
        if (exception != null) {
            return "ERROR";
        }

        if (statusCode >= 200 && statusCode < 300) {
            return "SUCCESS";
        } else if (statusCode >= 300 && statusCode < 400) {
            return "REDIRECTION";
        } else if (statusCode >= 400 && statusCode < 500) {
            return "CLIENT_ERROR";
        } else if (statusCode >= 500) {
            return "SERVER_ERROR";
        } else {
            return "UNKNOWN";
        }
    }
}
