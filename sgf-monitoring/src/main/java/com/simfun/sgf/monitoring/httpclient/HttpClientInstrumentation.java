package com.simfun.sgf.monitoring.httpclient;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.java11.instrument.binder.jdk.MicrometerHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.time.Duration;
import java.util.concurrent.Executor;

/**
 * HttpClient 监控装饰器
 * <p>
 * 为 Java 11 HttpClient 添加 Micrometer 监控指标
 * </p>
 */
@Component
public class HttpClientInstrumentation {

    private static final Logger log = LoggerFactory.getLogger(HttpClientInstrumentation.class);

    private final MeterRegistryManager meterRegistryManager;

    public HttpClientInstrumentation(MeterRegistryManager meterRegistryManager) {
        this.meterRegistryManager = meterRegistryManager;
    }

    /**
     * 装饰 HttpClient 以添加监控指标
     *
     * @param httpClient 原始 HttpClient
     * @param clientName 客户端名称，用于指标标签
     * @return 装饰后的 HttpClient
     */
    public HttpClient instrument(HttpClient httpClient, String clientName) {
        try {
            MeterRegistry meterRegistry = meterRegistryManager.getRegistry();
            
            HttpClient instrumentedClient = MicrometerHttpClient
                    .instrumentationBuilder(httpClient, meterRegistry)
                    .uriMapper(this::extractUriPattern)
                    .build();
            
            log.info("使用 MicrometerHttpClient 装饰客户端: {}", clientName);
            return instrumentedClient;
            
        } catch (NoClassDefFoundError e) {
            log.warn("micrometer-java11 依赖不可用，客户端: {}，使用原始 HttpClient", clientName);
            return httpClient;
        } catch (Exception e) {
            log.error("装饰 HttpClient 失败，客户端: {}，使用原始 HttpClient", clientName, e);
            return httpClient;
        }
    }

    /**
     * 提取 URI 模式，用于减少指标标签基数
     * 
     * @param request HTTP 请求
     * @return URI 模式字符串
     */
    private String extractUriPattern(HttpRequest request) {
        // 首先检查是否有显式设置的 URI 模式头
        String uriPattern = request.headers()
                .firstValue(MicrometerHttpClient.URI_PATTERN_HEADER)
                .orElse(null);
        
        if (uriPattern != null) {
            return uriPattern;
        }
        
        // 从 URI 路径中提取模式
        String path = request.uri().getPath();
        if (path == null || path.isEmpty()) {
            return "UNKNOWN";
        }
        
        return path;
    }

    /**
     * 创建并装饰 HttpClient
     *
     * @param clientName     客户端名称
     * @param executor       执行器
     * @param connectTimeout 连接超时时间
     * @return 装饰后的 HttpClient
     */
    public HttpClient createInstrumentedClient(String clientName, Executor executor, Duration connectTimeout) {
        try {
            HttpClient.Builder builder = HttpClient.newBuilder();
            
            if (executor != null) {
                builder.executor(executor);
            }
            
            if (connectTimeout != null) {
                builder.connectTimeout(connectTimeout);
            }
            
            HttpClient httpClient = builder.build();
            
            return instrument(httpClient, clientName);
            
        } catch (Exception e) {
            log.error("创建监控 HttpClient 失败，{}，降级到原始客户端", clientName, e);
            
            // 降级：创建原始 HttpClient
            HttpClient.Builder fallbackBuilder = HttpClient.newBuilder();
            if (executor != null) {
                fallbackBuilder.executor(executor);
            }
            if (connectTimeout != null) {
                fallbackBuilder.connectTimeout(connectTimeout);
            }
            return fallbackBuilder.build();
        }
    }


}