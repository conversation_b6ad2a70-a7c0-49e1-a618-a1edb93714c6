package com.simfun.sgf.monitoring.init;

import com.simfun.sgf.monitoring.httpclient.ApacheHttpClientInstrumentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * HttpClient 监控初始化器
 * <p>
 * 负责将 sgf-monitoring 的监控能力注入到 icefire-core 模块
 * </p>
 */
@Component
public class HttpClientMonitoringInitializer {

    private static final Logger log = LoggerFactory.getLogger(HttpClientMonitoringInitializer.class);

    @Autowired
    private ApacheHttpClientInstrumentation apacheHttpClientInstrumentation;

    /**
     * 应用启动完成后初始化监控
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializeHttpClientMonitoring() {
        try {
            // 通过反射调用 icefire-core 的监控集成类
            Class<?> monitoringClass = Class.forName("com.lc.billion.icefire.core.monitoring.HttpClientMonitoring");
            Method setDecoratorMethod = monitoringClass.getMethod("setMonitoringDecorator", 
                    java.util.function.BiFunction.class);

            // 创建监控装饰器函数
            java.util.function.BiFunction<org.apache.http.impl.client.HttpClientBuilder, String, org.apache.http.impl.client.HttpClientBuilder> decorator = 
                    (builder, clientName) -> apacheHttpClientInstrumentation.addMonitoring(builder, clientName);

            // 注入监控装饰器
            setDecoratorMethod.invoke(null, decorator);

            log.info("HttpClient 监控初始化完成");

        } catch (ClassNotFoundException e) {
            log.info("icefire-core 模块不可用，跳过 HttpClient 监控初始化");
        } catch (Exception e) {
            log.error("初始化 HttpClient 监控失败", e);
        }
    }
}
