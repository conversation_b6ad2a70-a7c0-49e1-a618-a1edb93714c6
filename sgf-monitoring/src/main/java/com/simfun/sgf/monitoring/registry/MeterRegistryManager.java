package com.simfun.sgf.monitoring.registry;

import io.micrometer.core.instrument.*;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.function.ToDoubleFunction;

/**
 * 指标注册表管理器
 * <p>
 * 提供统一的指标创建和管理接口，支持异步处理以减少性能影响
 * </p>
 */
public class MeterRegistryManager {

    private static final Logger log = LoggerFactory.getLogger(MeterRegistryManager.class);

    private final PrometheusMeterRegistry meterRegistry;
    private final String applicationName;
    private final ExecutorService metricsExecutor;

    // 缓存已创建的 Timer，避免重复创建
    private final ConcurrentHashMap<String, Timer> timerCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Counter> counterCache = new ConcurrentHashMap<>();


    public MeterRegistryManager(PrometheusMeterRegistry meterRegistry, String applicationName) {
        this.meterRegistry = meterRegistry;
        this.applicationName = applicationName;

        // 使用虚拟线程执行器处理指标记录
        this.metricsExecutor = Executors.newVirtualThreadPerTaskExecutor();

        // 添加通用标签
        meterRegistry.config().commonTags("application", applicationName);

        log.info("监控注册表管理器初始化完成，应用名: {}, 使用虚拟线程执行器", applicationName);
    }

    /**
     * 获取原始注册表
     */
    public PrometheusMeterRegistry getRegistry() {
        return meterRegistry;
    }

    /**
     * 创建计时器
     */
    public Timer timer(String name, String description, String... tags) {
        return Timer.builder(name)
                .description(description)
                .tags(tags)
                .publishPercentileHistogram(false)
                .register(meterRegistry);
    }

    /**
     * 创建计数器
     */
    public Counter counter(String name, String description, String... tags) {
        return Counter.builder(name)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }

    /**
     * 创建测量仪
     */
    public <T> Gauge gauge(String name, String description, T stateObject, ToDoubleFunction<T> valueFunction, String... tags) {
        return Gauge.builder(name, stateObject, valueFunction)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }

    /**
     * 创建测量仪
     */
    public Gauge gauge(String name, String description, Supplier<Number> supplier, String... tags) {
        return Gauge.builder(name, supplier)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }

    /**
     * 创建长任务计时器
     */
    public LongTaskTimer longTaskTimer(String name, String description, String... tags) {
        return LongTaskTimer.builder(name)
                .description(description)
                .tags(tags)
                .register(meterRegistry);
    }

    /**
     * 记录计时器样本
     */
    public void recordTimer(String name, long duration, TimeUnit unit, String... tags) {
        try {
            String cacheKey = buildCacheKey(name, tags);
            Timer timer = timerCache.computeIfAbsent(cacheKey,
                    k -> timer(name, "", tags));
            timer.record(duration, unit);
        } catch (Exception e) {
            log.warn("记录计时器指标失败: {}", name, e);
        }
    }

    /**
     * 增加计数器
     */
    public void incrementCounter(String name, String... tags) {
        try {
            String cacheKey = buildCacheKey(name, tags);
            Counter counter = counterCache.computeIfAbsent(cacheKey,
                    k -> counter(name, "", tags));
            counter.increment();
        } catch (Exception e) {
            log.warn("增加计数器指标失败: {}", name, e);
        }
    }

    /**
     * 记录数值
     */
    public void recordValue(String name, double value, String... tags) {
        try {
            DistributionSummary.builder(name)
                    .tags(tags)
                    .register(meterRegistry)
                    .record(value);
        } catch (Exception e) {
            log.warn("记录数值指标失败: {}", name, e);
        }
    }

    /**
     * 安全执行指标记录（同步执行的）
     */
    public void safeExecute(Runnable metricsAction) {
        try {
            metricsAction.run();
        } catch (Exception e) {
            log.warn("执行指标记录时发生异常", e);
        }
    }

    /**
     * 基于虚拟线程异步安全执行指标记录
     */
    public void safeExecuteAsync(Runnable metricsAction) {
        // 如果已关闭则忽略
        if (metricsExecutor.isShutdown()) {
            log.debug("指标执行器已关闭，跳过异步指标记录");
            return;
        }

        try {
            metricsExecutor.submit(() -> {
                try {
                    metricsAction.run();
                } catch (Throwable e) {
                    log.warn("异步执行指标记录时发生异常", e);
                }
            });
        } catch (RejectedExecutionException e) {
            log.debug("指标执行器拒绝任务，可能正在关闭: {}", e.getMessage());
        } catch (Exception e) {
            log.warn("提交异步指标记录任务失败", e);
        }
    }

    /**
     * 获取应用名称
     */
    public String getApplicationName() {
        return applicationName;
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String name, String... tags) {
        if (tags == null || tags.length == 0) {
            return name;
        }
        StringBuilder sb = new StringBuilder(name);
        for (String tag : tags) {
            sb.append(":").append(tag);
        }
        return sb.toString();
    }

    /**
     * 关闭资源
     */
    public void shutdown() {
        if (metricsExecutor != null && !metricsExecutor.isShutdown()) {
            metricsExecutor.shutdown();
            log.info("监控指标执行器已关闭");
        }
        // 清理缓存
        timerCache.clear();
        counterCache.clear();
        log.info("监控指标缓存已清理");
    }
} 