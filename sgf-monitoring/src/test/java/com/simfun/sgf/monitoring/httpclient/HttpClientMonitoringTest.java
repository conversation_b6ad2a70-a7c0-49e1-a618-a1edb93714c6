package com.simfun.sgf.monitoring.httpclient;

import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.micrometer.prometheusmetrics.PrometheusConfig;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;

/**
 * HttpClient 监控功能测试
 */
public class HttpClientMonitoringTest {

    private MeterRegistryManager meterRegistryManager;
    private ApacheHttpClientInstrumentation instrumentation;
    private ClientAndServer mockServer;

    @BeforeEach
    public void setUp() {
        // 创建测试用的注册表
        PrometheusMeterRegistry registry = new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
        meterRegistryManager = new MeterRegistryManager(registry, "test-app");
        instrumentation = new ApacheHttpClientInstrumentation(meterRegistryManager);

        // 启动模拟服务器
        mockServer = ClientAndServer.startClientAndServer(1080);
        mockServer
                .when(request().withMethod("GET").withPath("/test"))
                .respond(response().withStatusCode(200).withBody("OK"));
    }

    @Test
    public void testHttpClientMonitoring() throws Exception {
        // 创建带监控的HttpClient
        CloseableHttpClient httpClient = instrumentation.addMonitoring(
                HttpClients.custom(), "test-client").build();

        // 执行HTTP请求
        HttpGet request = new HttpGet("http://localhost:1080/test");
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            assertEquals(200, response.getStatusLine().getStatusCode());
        }

        // 等待指标记录
        Thread.sleep(100);

        // 验证指标是否被记录
        Timer timer = meterRegistryManager.getRegistry().find("http.client.requests").timer();
        assertNotNull(timer, "Timer 指标应该被创建");
        assertEquals(1, timer.count(), "应该记录一次请求");

        Counter counter = meterRegistryManager.getRegistry().find("http.client.requests.total").counter();
        assertNotNull(counter, "Counter 指标应该被创建");
        assertEquals(1.0, counter.count(), "应该计数一次请求");

        // 关闭资源
        httpClient.close();
        mockServer.stop();
    }

    @Test
    public void testMonitoringWithError() throws Exception {
        // 配置模拟服务器返回错误
        mockServer.reset();
        mockServer
                .when(request().withMethod("GET").withPath("/error"))
                .respond(response().withStatusCode(500).withBody("Internal Server Error"));

        // 创建带监控的HttpClient
        CloseableHttpClient httpClient = instrumentation.addMonitoring(
                HttpClients.custom(), "test-client-error").build();

        // 执行HTTP请求
        HttpGet request = new HttpGet("http://localhost:1080/error");
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            assertEquals(500, response.getStatusLine().getStatusCode());
        }

        // 等待指标记录
        Thread.sleep(100);

        // 验证错误指标
        Timer timer = meterRegistryManager.getRegistry().find("http.client.requests")
                .tag("outcome", "SERVER_ERROR")
                .timer();
        assertNotNull(timer, "错误 Timer 指标应该被创建");
        assertEquals(1, timer.count(), "应该记录一次错误请求");

        // 关闭资源
        httpClient.close();
        mockServer.stop();
    }

    @Test
    public void testPrometheusMetricsOutput() throws Exception {
        // 创建带监控的HttpClient并执行请求
        CloseableHttpClient httpClient = instrumentation.addMonitoring(
                HttpClients.custom(), "prometheus-test").build();

        HttpGet request = new HttpGet("http://localhost:1080/test");
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            assertEquals(200, response.getStatusLine().getStatusCode());
        }

        // 等待指标记录
        Thread.sleep(100);

        // 获取Prometheus格式的指标
        String metrics = meterRegistryManager.getRegistry().scrape();
        
        // 验证指标格式
        assertTrue(metrics.contains("http_client_requests_seconds"), "应该包含计时器指标");
        assertTrue(metrics.contains("http_client_requests_total"), "应该包含计数器指标");
        assertTrue(metrics.contains("client_name=\"prometheus-test\""), "应该包含客户端名称标签");

        // 关闭资源
        httpClient.close();
        mockServer.stop();
    }
}
