package com.simfun.sgf.net.msg;

import com.simfun.sgf.net.msg.MessageConfig.MessageMeta;
import com.simfun.sgf.utils.JavaUtils;
import org.simpleframework.xml.Serializer;
import org.simpleframework.xml.core.Persister;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

/**
 * 默认的消息配置管理器的实现
 * 
 * <ul>
 * <li>采用xml格式文件</li> <br>
 * 格式如下
 * 
 * <pre>
 * &ltmessages&gt
 *     &ltmessage type="1" name="CgLogin" /&gt
 * &lt/messages&gt
 * </pre>
 * 
 * <li>如果不符合实际需求，另行独自实现MessageConfigManager即可</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class DefaultMessageConfigManager extends AbstractMessageConfigManager<MessageMeta> {

	public DefaultMessageConfigManager() {

	}

	public DefaultMessageConfigManager(String configFileName, String messagePackage) {
		super(configFileName, messagePackage);
	}

	protected List<MessageMeta> loadMessageMeta() {
		Serializer serializer = new Persister();

		InputStream is = null;
		try {
			is = this.getClass().getClassLoader().getResourceAsStream(getConfigFileName());
			MessageConfig messageCfg = serializer.read(MessageConfig.class, is, false);

			List<MessageMeta> messageMetas = messageCfg.getMessageMetas();
			if (messageMetas == null) {
				messageMetas = Collections.emptyList();
			}
			return messageMetas;
		} catch (Exception e) {
			throw JavaUtils.sneakyThrow(e);
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					log.error("", e);
				}
			}
		}
	}

}
